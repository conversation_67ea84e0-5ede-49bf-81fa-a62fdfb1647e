2025-08-11 12:21:43.251 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 13422 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-11 12:21:43.254 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "dev"
2025-08-11 12:21:43.254 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Loading source class com.fozmo.ym.server.YmServerApplication
2025-08-11 12:21:43.250 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 13507 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-11 12:21:43.252 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "dev"
2025-08-11 12:21:43.253 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Loading source class com.fozmo.ym.server.YmServerApplication
2025-08-11 12:21:43.719 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@bcb09a6
2025-08-11 12:21:43.721 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@bcb09a6
2025-08-11 12:21:50.816 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-11 12:21:50.816 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-11 12:21:53.007 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.w.e.t.TomcatServletWebServerFactory[0;39m | Code archive: /home/<USER>/work/maven/rep/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar
2025-08-11 12:21:53.008 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.w.e.t.TomcatServletWebServerFactory[0;39m | Code archive: /home/<USER>/work/maven/rep/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar
2025-08-11 12:21:53.010 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.w.e.t.TomcatServletWebServerFactory[0;39m | None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-08-11 12:21:53.010 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.w.e.t.TomcatServletWebServerFactory[0;39m | Code archive: /home/<USER>/work/maven/rep/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar
2025-08-11 12:21:53.011 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.w.e.t.TomcatServletWebServerFactory[0;39m | Code archive: /home/<USER>/work/maven/rep/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar
2025-08-11 12:21:53.012 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.w.e.t.TomcatServletWebServerFactory[0;39m | None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-08-11 12:21:53.118 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-11 12:21:53.119 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-11 12:21:53.156 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-11 12:21:53.155 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-11 12:21:53.157 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 12:21:53.156 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 12:21:53.366 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-11 12:21:53.367 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-08-11 12:21:53.368 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 9647 ms
2025-08-11 12:21:53.379 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-11 12:21:53.379 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-08-11 12:21:53.382 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 9663 ms
2025-08-11 12:21:56.043 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-11 12:21:56.044 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-11 12:21:56.045 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 12:21:56.048 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-11 12:21:56.049 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-11 12:21:56.050 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 12:22:00.452 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-11 12:22:00.461 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-11 12:22:01.188 | [34m INFO 13507[0;39m | [1;33mredisson-netty-1-5 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for 192.168.110.160/192.168.110.160:6379
2025-08-11 12:22:01.188 | [34m INFO 13422[0;39m | [1;33mredisson-netty-1-5 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for 192.168.110.160/192.168.110.160:6379
2025-08-11 12:22:01.254 | [34m INFO 13507[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for 192.168.110.160/192.168.110.160:6379
2025-08-11 12:22:01.255 | [34m INFO 13422[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for 192.168.110.160/192.168.110.160:6379
2025-08-11 12:22:01.753 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.ServletContextInitializerBeans[0;39m | Mapping filters: corsFilterBean urls=[/*] order=-2147483648, traceFilter urls=[/*] order=-2147483647, webMvcObservationFilter urls=[/*] order=-2147483647, requestBodyCacheFilter urls=[/*] order=-2147483148, tenantContextWebFilter urls=[/*] order=-104, springSecurityFilterChain urls=[/*] order=-100, tenantSecurityWebFilter urls=[/*] order=-99, webStatFilterRegistrationBean urls=[/*] order=2147483647, druidAdRemoveFilterFilter urls=[/druid/js/common.js] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, authenticationTokenFilter urls=[/*] order=2147483647, homepageForwardFilter urls=[/*] order=2147483647
2025-08-11 12:22:01.753 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.ServletContextInitializerBeans[0;39m | Mapping servlets: dispatcherServlet urls=[/], statViewServletRegistrationBean urls=[/druid/*]
2025-08-11 12:22:01.761 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.ServletContextInitializerBeans[0;39m | Mapping filters: corsFilterBean urls=[/*] order=-2147483648, traceFilter urls=[/*] order=-2147483647, webMvcObservationFilter urls=[/*] order=-2147483647, requestBodyCacheFilter urls=[/*] order=-2147483148, tenantContextWebFilter urls=[/*] order=-104, springSecurityFilterChain urls=[/*] order=-100, tenantSecurityWebFilter urls=[/*] order=-99, webStatFilterRegistrationBean urls=[/*] order=2147483647, druidAdRemoveFilterFilter urls=[/druid/js/common.js] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, authenticationTokenFilter urls=[/*] order=2147483647, homepageForwardFilter urls=[/*] order=2147483647
2025-08-11 12:22:01.761 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.ServletContextInitializerBeans[0;39m | Mapping servlets: dispatcherServlet urls=[/], statViewServletRegistrationBean urls=[/druid/*]
2025-08-11 12:22:01.816 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.f.OrderedRequestContextFilter [0;39m | Filter 'requestContextFilter' configured for use
2025-08-11 12:22:01.818 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.f.OrderedRequestContextFilter [0;39m | Filter 'requestContextFilter' configured for use
2025-08-11 12:22:01.827 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32ms.b.w.s.f.OrderedCharacterEncodingFilter[0;39m | Filter 'characterEncodingFilter' configured for use
2025-08-11 12:22:01.827 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32ms.b.w.s.f.OrderedCharacterEncodingFilter[0;39m | Filter 'characterEncodingFilter' configured for use
2025-08-11 12:22:01.828 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.springframework.web.filter.CorsFilter [0;39m | Filter 'corsFilterBean' configured for use
2025-08-11 12:22:01.828 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.springframework.web.filter.CorsFilter [0;39m | Filter 'corsFilterBean' configured for use
2025-08-11 12:22:01.830 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.w.f.ServerHttpObservationFilter     [0;39m | Filter 'webMvcObservationFilter' configured for use
2025-08-11 12:22:01.830 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.w.f.ServerHttpObservationFilter     [0;39m | Filter 'webMvcObservationFilter' configured for use
2025-08-11 12:22:01.830 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.DelegatingFilterProxyRegistrationBean$1[0;39m | Filter 'springSecurityFilterChain' configured for use
2025-08-11 12:22:01.830 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.f.OrderedFormContentFilter    [0;39m | Filter 'formContentFilter' configured for use
2025-08-11 12:22:01.830 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.DelegatingFilterProxyRegistrationBean$1[0;39m | Filter 'springSecurityFilterChain' configured for use
2025-08-11 12:22:01.830 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.s.f.OrderedFormContentFilter    [0;39m | Filter 'formContentFilter' configured for use
2025-08-11 12:22:05.314 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-11 12:22:05.320 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-11 12:22:05.341 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@7b6c6005, ORIGINAL=[Ljava.lang.String;@2ac50ab0, PIC_CLICK=[Ljava.lang.String;@2388307]
2025-08-11 12:22:05.342 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@69d142f6, ORIGINAL=[Ljava.lang.String;@555856fa, PIC_CLICK=[Ljava.lang.String;@5766f830]
2025-08-11 12:22:05.343 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-11 12:22:05.344 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-11 12:22:05.360 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-11 12:22:05.361 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-11 12:22:05.360 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-11 12:22:05.361 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-11 12:22:06.600 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-11 12:22:06.660 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.AutoConfigurationPackages       [0;39m | @EnableAutoConfiguration was declared on a class in the package 'com.fozmo.ym.server'. Automatic @Repository and @Entity scanning is enabled.
2025-08-11 12:22:06.664 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-11 12:22:06.727 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.AutoConfigurationPackages       [0;39m | @EnableAutoConfiguration was declared on a class in the package 'com.fozmo.ym.server'. Automatic @Repository and @Entity scanning is enabled.
2025-08-11 12:22:07.100 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerMapping[0;39m | 387 mappings in 'requestMappingHandlerMapping'
2025-08-11 12:22:07.202 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerMapping[0;39m | 387 mappings in 'requestMappingHandlerMapping'
2025-08-11 12:22:07.225 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.w.s.handler.SimpleUrlHandlerMapping [0;39m | Patterns [/webjars/**, /**, /admin/monitor/**, /admin/monitor/extensions/**] in 'resourceHandlerMapping'
2025-08-11 12:22:07.298 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.w.s.handler.SimpleUrlHandlerMapping [0;39m | Patterns [/webjars/**, /**, /admin/monitor/**, /admin/monitor/extensions/**] in 'resourceHandlerMapping'
2025-08-11 12:22:07.329 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | ControllerAdvice beans: 1 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice
2025-08-11 12:22:07.393 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | ControllerAdvice beans: 1 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice
2025-08-11 12:22:07.512 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.m.m.a.ExceptionHandlerExceptionResolver[0;39m | ControllerAdvice beans: 2 @ExceptionHandler, 2 ResponseBodyAdvice
2025-08-11 12:22:07.558 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.m.m.a.ExceptionHandlerExceptionResolver[0;39m | ControllerAdvice beans: 2 @ExceptionHandler, 2 ResponseBodyAdvice
2025-08-11 12:22:07.810 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.w.s.s.s.WebSocketHandlerMapping     [0;39m | Patterns [/space/ws] in 'webSocketHandlerMapping'
2025-08-11 12:22:07.848 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.w.s.s.s.WebSocketHandlerMapping     [0;39m | Patterns [/space/ws] in 'webSocketHandlerMapping'
2025-08-11 12:22:08.254 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-11 12:22:08.260 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-11 12:22:08.754 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@bcb09a6
2025-08-11 12:22:08.780 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@bcb09a6
2025-08-11 12:22:09.341 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-11 12:22:09.350 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-11 12:22:09.602 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-11 12:22:09.613 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-11 12:22:09.614 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-11 12:22:09.624 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-11 12:22:09.627 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-11 12:22:09.638 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-11 12:22:09.793 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-11 12:22:09.793 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-11 12:22:09.793 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-11 12:22:09.793 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-11 12:22:09.794 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-11 12:22:09.794 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-11 12:22:09.794 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-11 12:22:09.811 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-11 12:22:09.811 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-11 12:22:09.811 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-11 12:22:09.812 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-11 12:22:09.812 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-11 12:22:09.812 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-11 12:22:09.812 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-11 12:22:14.150 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-11 12:22:14.351 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-11 12:22:14.842 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-11 12:22:14.941 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-11 12:22:15.150 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-11 12:22:15.443 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-11 12:22:15.473 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-11 12:22:15.504 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-11 12:22:15.595 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-11 12:22:15.623 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-11 12:22:15.698 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-11 12:22:15.882 | [34m INFO 13422[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-11 12:22:15.892 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-11 12:22:15.953 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-11 12:22:16.013 | [34m INFO 13507[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-11 12:22:16.068 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-11 12:22:17.313 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@40fd8aa1[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-11 12:22:17.314 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@341df52[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-11 12:22:17.403 | [34m INFO 13422[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-08-11 12:22:17.423 | [1;31mERROR 13422[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 12:22:17.428 | [34m INFO 13422[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 12:22:17.487 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-11 12:22:17.511 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9f1f42c[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-11 12:22:17.512 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e2544e9[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-11 12:22:17.590 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AdminServerAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.config.AdminServerMarkerConfiguration$Marker; SearchStrategy: all) found bean 'adminServerMarker' (OnBeanCondition)

   AdminServerAutoConfiguration#applicationRegistry matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.ApplicationRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#endpointDetectionTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.EndpointDetectionTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#endpointDetector matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.EndpointDetector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#eventStore matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.eventstore.InstanceEventStore; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#infoUpdateTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InfoUpdateTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#infoUpdater matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InfoUpdater; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceFilter matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InstanceFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceIdGenerator matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InstanceIdGenerator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceRegistry matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InstanceRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceRepository matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.domain.entities.InstanceRepository; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#statusUpdateTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.StatusUpdateTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#statusUpdater matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.StatusUpdater; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration#instanceWebClientBuilder matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.InstanceWebClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.CookieStoreConfiguration#cookieStore matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.cookies.PerInstanceCookieStore; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.CookieStoreConfiguration#cookieStoreCleanupTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.cookies.CookieStoreCleanupTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.HttpHeadersProviderConfiguration#basicAuthHttpHeadersProvider matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.BasicAuthHttpHeaderProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration#filterInstanceWebClientCustomizer matched:
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.InstanceExchangeFilterFunction; SearchStrategy: all) found beans 'addHeadersInstanceExchangeFilter', 'rewriteEndpointUrlInstanceExchangeFilter', 'setDefaultAcceptHeaderInstanceExchangeFilter', 'logfileAcceptWorkaround', 'timeoutInstanceExchangeFilter', 'retryInstanceExchangeFilter', 'legacyEndpointConverterInstanceExchangeFilter', 'cookieHandlingInstanceExchangeFilter'; @ConditionalOnMissingBean (names: filterInstanceWebClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#addHeadersInstanceExchangeFilter matched:
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.HttpHeadersProvider; SearchStrategy: all) found bean 'basicAuthHttpHeadersProvider'; @ConditionalOnMissingBean (names: addHeadersInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#cookieHandlingInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: cookieHandlingInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#legacyEndpointConverterInstanceExchangeFilter matched:
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.LegacyEndpointConverter; SearchStrategy: all) found beans 'infoLegacyEndpointConverter', 'threaddumpLegacyEndpointConverter', 'mappingsLegacyEndpointConverter', 'startupLegacyEndpointConverter', 'httptraceLegacyEndpointConverter', 'envLegacyEndpointConverter', 'healthLegacyEndpointConverter', 'beansLegacyEndpointConverter', 'liquibaseLegacyEndpointConverter', 'flywayLegacyEndpointConverter', 'configpropsLegacyEndpointConverter'; @ConditionalOnMissingBean (names: legacyEndpointConverterInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#logfileAcceptWorkaround matched:
      - @ConditionalOnMissingBean (names: logfileAcceptWorkaround; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#retryInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: retryInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#rewriteEndpointUrlInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: rewriteEndpointUrlInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#setDefaultAcceptHeaderInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: setDefaultAcceptHeaderInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#timeoutInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: timeoutInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#beansLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: beansLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#configpropsLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: configpropsLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#envLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: envLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#flywayLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: flywayLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#healthLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: healthLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#httptraceLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: httptraceLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#infoLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: infoLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#liquibaseLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: liquibaseLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#mappingsLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: mappingsLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#startupLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: startupLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#threaddumpLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: threaddumpLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerNotifierAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)

   AdminServerUiAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.config.AdminServerMarkerConfiguration$Marker; SearchStrategy: all) found bean 'adminServerMarker' (OnBeanCondition)

   AdminServerUiAutoConfiguration#homeUiController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.ui.web.UiController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerUiAutoConfiguration.ServletUiConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   AdminServerUiAutoConfiguration.ServletUiConfiguration.AdminUiWebMvcConfig#homepageForwardFilter matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.ui.web.servlet.HomepageForwardingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerWebConfiguration#applicationsController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.ApplicationsController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerWebConfiguration#instancesController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.InstancesController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerWebConfiguration.ServletRestApiConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   AdminServerWebConfiguration.ServletRestApiConfiguration#instancesProxyController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.servlet.InstancesProxyController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AjCaptchaServiceAutoConfiguration#captchaService matched:
      - @ConditionalOnMissingBean (types: com.anji.captcha.service.CaptchaService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AopAutoConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   ApplicationAvailabilityAutoConfiguration#applicationAvailability matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.availability.ApplicationAvailability; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AuditEventsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration#beansEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.beans.BeansEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CacheMeterBinderProvidersConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.MeterBinder' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.CaffeineCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.cache.caffeine.CaffeineCache', 'com.github.benmanes.caffeine.cache.Cache' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.JCacheCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.cache.jcache.JCacheCache', 'javax.cache.CacheManager' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.RedisCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.cache.RedisCache' (OnClassCondition)

   CacheMetricsAutoConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans 'tenantRedisCacheManager', 'redisCacheManager' (OnBeanCondition)

   CacheMetricsRegistrarConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.metrics.cache.CacheMeterBinderProvider,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'caffeineCacheMeterBinderProvider', 'redisCacheMeterBinderProvider', 'jCacheCacheMeterBinderProvider' (OnBeanCondition)

   CachesEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   CachesEndpointAutoConfiguration#cachesEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CachesEndpointAutoConfiguration#cachesEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) found bean 'cachesEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   org.springframework.boot.autoconfigure.http.client.reactive.ClientHttpConnectorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.http.client.reactive.ClientHttpConnector', 'reactor.core.publisher.Mono' (OnClassCondition)
      - Detected ClientHttpConnectorBuilder (ConditionalOnClientHttpConnectorBuilderDetection)

   org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnector matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.client.reactive.ClientHttpConnector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnectorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.reactive.ClientHttpConnectorBuilder<?>; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnectorSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.reactive.ClientHttpConnectorSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   org.springframework.boot.autoconfigure.http.client.reactive.ClientHttpConnectorAutoConfiguration$ReactorNetty matched:
      - @ConditionalOnClass found required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration$ReactorNetty matched:
      - @ConditionalOnClass found required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.http.codec.CodecConfigurer', 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   CompositeMeterRegistryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.composite.CompositeMeterRegistry' (OnClassCondition)

   ConditionsReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ConditionsReportEndpointAutoConfiguration#conditionsReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.condition.ConditionsReportEndpoint; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) found bean 'configurationPropertiesReportEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)
      - @ConditionalOnMissingBean (types: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)

   DataSourceHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found bean 'dataSource' (OnBeanCondition)

   DataSourceHealthContributorAutoConfiguration#dbHealthContributor matched:
      - @ConditionalOnMissingBean (names: dbHealthIndicator,dbHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceInitializationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer,org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'dataSource' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.DataSourcePoolMetadataMetricsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.jdbc.metadata.DataSourcePoolMetadataProvider; SearchStrategy: all) found bean 'hikariPoolDataSourceMetadataProvider' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.HikariDataSourceMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DdlAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.baomidou.mybatisplus.extension.ddl.IDdl' (OnClassCondition)

   DiskSpaceHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   DiskSpaceHealthContributorAutoConfiguration#diskSpaceHealthIndicator matched:
      - @ConditionalOnMissingBean (names: diskSpaceHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.alibaba.druid.pool.DruidDataSource) matched (OnPropertyCondition)

   DruidDynamicDataSourceConfiguration matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure' (OnClassCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAopConfiguration#dsProcessor matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.processor.DsProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAopConfiguration#dynamicDatasourceAnnotationAdvisor matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.aop.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAopConfiguration#dynamicTransactionAdvisor matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.seata=false) matched (OnPropertyCondition)

   DynamicDataSourceAssistConfiguration#dataSourceCreator matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAssistConfiguration#dataSourceInitEvent matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.event.DataSourceInitEvent; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAssistConfiguration.DsTxEventListenerFactoryConfiguration#dsTxEventListenerFactory matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.tx.DsTxEventListenerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceCreatorAutoConfiguration.DruidDataSourceCreatorConfiguration matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.HikariDataSourceCreatorConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnWarDeployment the application is not deployed as a WAR file. (OnWarDeploymentCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   EndpointAutoConfiguration#endpointCachingOperationInvokerAdvisor matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoker.cache.CachingOperationInvokerAdvisor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#endpointOperationParameterMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoke.ParameterValueMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#propertiesEndpointAccessResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.EndpointAccessResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) found bean 'environmentEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnBooleanProperty (server.error.whitelabel.enabled=true) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)

   GsonAutoConfiguration#gson matched:
      - @ConditionalOnMissingBean (types: com.google.gson.Gson; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonAutoConfiguration#gsonBuilder matched:
      - @ConditionalOnMissingBean (types: com.google.gson.GsonBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonHttpMessageConvertersConfiguration matched:
      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)

   HealthContributorAutoConfiguration#pingHealthContributor matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   HealthEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HealthEndpointConfiguration#healthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpointGroupMembershipValidator matched:
      - @ConditionalOnBooleanProperty (management.endpoint.health.validate-group-membership=true) matched (OnPropertyCondition)

   HealthEndpointConfiguration#healthEndpointGroups matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointGroups; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthHttpCodeStatusMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HttpCodeStatusMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthStatusAggregator matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.StatusAggregator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration#healthEndpointWebExtension matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration.MvcAdditionalHealthEndpointPathsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   HttpClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.client.ClientHttpRequestFactory' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (NotReactiveWebApplicationCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder<?>; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactorySettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactorySettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientObservationsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.observation.Observation' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   HttpClientObservationsAutoConfiguration.MeterFilterConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (server.servlet.encoding.enabled=true) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpExchangesEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   InfoEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   InfoEndpointAutoConfiguration#infoEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.info.InfoEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonEndpointAutoConfiguration#endpointObjectMapper matched:
      - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.ObjectMapper', 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)
      - @ConditionalOnBooleanProperty (management.endpoints.jackson.isolated-object-mapper=true) matched (OnPropertyCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnPreferredJsonMapper JACKSON no property was configured and Jackson is the default (OnPreferredJsonMapperCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcClientAutoConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate; SearchStrategy: all) found a single bean 'namedParameterJdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.simple.JdbcClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   JvmMetricsAutoConfiguration#classLoaderMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmCompilationMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmCompilationMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmGcMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmGcMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmHeapPressureMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmHeapPressureMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmInfoMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmInfoMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmMemoryMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmThreadMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:
      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   LogFileWebEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   LogFileWebEndpointAutoConfiguration#logFileWebEndpoint matched:
      - Log File found logging.file.name ./logs/ym-server.log (LogFileWebEndpointAutoConfiguration.LogFileCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LogFileWebEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#diffItemsToLogContentService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.starter.diff.IDiffItemsToLogContentService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#functionService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.IFunctionService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#logRecordPerformanceMonitor matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.ILogRecordPerformanceMonitor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#operatorGetService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.IOperatorGetService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#recordService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.ILogRecordService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogbackMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'ch.qos.logback.classic.LoggerContext', 'org.slf4j.LoggerFactory' (OnClassCondition)
      - LogbackLoggingCondition ILoggerFactory is a Logback LoggerContext (LogbackMetricsAutoConfiguration.LogbackLoggingCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   LogbackMetricsAutoConfiguration#logbackMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.logging.LogbackMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LoggersEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   LoggersEndpointAutoConfiguration#loggersEndpoint matched:
      - Logging System enabled (LoggersEndpointAutoConfiguration.OnEnabledLoggingSystemCondition)
      - @ConditionalOnBean (types: org.springframework.boot.logging.LoggingSystem; SearchStrategy: all) found bean 'springBootLoggingSystem'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LoggersEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ManagementContextAutoConfiguration.SameManagementContextConfiguration matched:
      - Management Port actual port type (SAME) matched required type (OnManagementPortCondition)

   MappingsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration.SpringMvcConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   MetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)

   MetricsAutoConfiguration#micrometerClock matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MetricsEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   MetricsEndpointAutoConfiguration#metricsEndpoint matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.MetricsEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'jakarta.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (spring.servlet.multipart.enabled=true) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: jakarta.servlet.MultipartConfigElement; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipleOpenApiSupportConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 2 matched 1 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnListGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi' (MultipleOpenApiSupportCondition)

   MultipleOpenApiSupportConfiguration#multipleOpenApiResource matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   MybatisPlusAutoConfiguration#mybatisPlusSpringApplicationContextAware matched:
      - @ConditionalOnMissingBean (types: com.baomidou.mybatisplus.extension.spring.MybatisPlusApplicationContextAware; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration#mpjInterceptor matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusJoinAutoConfiguration#mpjInterceptorConfig matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusJoinAutoConfiguration#mpjSqlInjectorOnMiss matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnMissingBean (types: com.baomidou.mybatisplus.core.injector.ISqlInjector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration.MPJMappingConfig matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusJoinAutoConfiguration.MPJSpringContext matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NamedParameterJdbcTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a single bean 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NettyAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.netty.util.NettyRuntime' (OnClassCondition)

   NoticeClientConfiguration#primaryNoticeClient matched:
      - @ConditionalOnMissingBean (names: primaryNoticeClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.observation.ObservationRegistry' (OnClassCondition)

   ObservationAutoConfiguration#observationRegistry matched:
      - @ConditionalOnMissingBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.observation.MeterObservationHandler; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration.OnlyMetricsMeterObservationHandlerConfiguration matched:
      - @ConditionalOnMissingBean (types: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.OnlyMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry'; @ConditionalOnMissingClass did not find unwanted class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnBooleanProperty (spring.dao.exceptiontranslation.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.prometheusmetrics.PrometheusMeterRegistry' (OnClassCondition)
      - @ConditionalOnEnabledMetricsExport management.defaults.metrics.export.enabled is considered true (OnMetricsExportEnabledCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) found bean 'micrometerClock' (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusConfig matched:
      - @ConditionalOnMissingBean (types: io.micrometer.prometheusmetrics.PrometheusConfig; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusMeterRegistry matched:
      - @ConditionalOnMissingBean (types: io.micrometer.prometheusmetrics.PrometheusMeterRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusRegistry matched:
      - @ConditionalOnMissingBean (types: io.prometheus.metrics.model.registry.PrometheusRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusScrapeEndpointConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusScrapeEndpointConfiguration#prometheusEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.export.prometheus.PrometheusScrapeEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ReactiveHealthEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   ReactiveHealthEndpointConfiguration#reactiveHealthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.ReactiveHealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ReactiveSecurityAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'reactor.core.publisher.Flux', 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity', 'org.springframework.security.web.server.WebFilterChainProxy', 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Hooks' (OnClassCondition)

   ReactorNettyConfigurations.ReactorResourceFactoryConfiguration#reactorResourceFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.client.ReactorResourceFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisAutoConfiguration#redisConnectionDetails matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.data.redis.RedisConnectionDetails; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisCacheConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - Cache org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration REDIS cache type (CacheCondition)

   RedisHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory' (OnBeanCondition)

   RedisReactiveAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'org.springframework.data.redis.core.ReactiveRedisTemplate', 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisReactiveAutoConfiguration#reactiveRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (names: reactiveRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveAutoConfiguration#reactiveStringRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (names: reactiveStringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory' (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration#redisHealthContributor matched:
      - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redisson matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonConnectionFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonReactive matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonReactiveClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonRxJava matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonRxClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#stringRedisTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfigurationV2 matched:
      - @ConditionalOnClass found required classes 'org.redisson.Redisson', 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RepositoryMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.repository.Repository' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#metricsRepositoryMethodInvocationListener matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.MetricsRepositoryMethodInvocationListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#repositoryTagsProvider matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.RepositoryTagsProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.VirtualThreadsExecutorEnabled found non-matching nested conditions @ConditionalOnThreading did not find VIRTUAL; NestedCondition on NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.NotReactiveWebApplication NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition)

   RestClientAutoConfiguration#httpMessageConvertersRestClientCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found bean 'restClientBuilder' (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) found bean 'restTemplateBuilder' (OnBeanCondition)

   SbomEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   SbomEndpointAutoConfiguration#sbomEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.sbom.SbomEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SbomEndpointAutoConfiguration#sbomEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.sbom.SbomEndpoint; SearchStrategy: all) found bean 'sbomEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.sbom.SbomEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration#scheduledTasksEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.scheduling.ScheduledTasksEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ScheduledTasksObservabilityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   SecurityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityAutoConfiguration#authenticationEventPublisher matched:
      - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationEventPublisher; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SecurityFilterAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer', 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityFilterAutoConfiguration#securityFilterChainRegistration matched:
      - @ConditionalOnBean (names: springSecurityFilterChain; SearchStrategy: all) found bean 'springSecurityFilterChain' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath; SearchStrategy: all) found bean 'dispatcherServletRegistration' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration#requestMatcherProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.security.servlet.RequestMatcherProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletEndpointManagementContextConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   ServletEndpointManagementContextConfiguration.WebMvcServletEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)

   ServletManagementContextAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.Servlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SpaceWebSocketAutoConfiguration matched:
      - @ConditionalOnProperty (ym.space.websocket.enable) matched (OnPropertyCondition)

   SpaceWebSocketAutoConfiguration.RedisWebSocketMessageSenderConfiguration matched:
      - @ConditionalOnProperty (ym.space.websocket.sender-type=redis) matched (OnPropertyCondition)

   SpringBootAdminClientAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - matched (SpringBootAdminClientEnabledCondition)

   SpringBootAdminClientAutoConfiguration#registrationListener matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.RegistrationApplicationListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration#registrator matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.ApplicationRegistrator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration#startupDateMetadataContributor matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.metadata.StartupDateMetadataContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.BlockingRegistrationClientConfig matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) found bean 'restTemplateBuilder' (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.RestClientRegistrationClientConfig matched:
      - @ConditionalOnBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found bean 'restClientBuilder' (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.RestClientRegistrationClientConfig#registrationClient matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.RegistrationClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.ServletConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   SpringBootAdminClientAutoConfiguration.ServletConfiguration#applicationFactory matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.ApplicationFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootWebSecurityConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   SpringBootWebSecurityConfiguration.WebSecurityEnablerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)
      - @ConditionalOnMissingBean (names: springSecurityFilterChain; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#pageableCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#sortCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#springDataWebSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SpringDataWebSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfigProperties matched:
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   SpringDocConfiguration#fileSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.FileSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#globalOpenApiCustomizer matched:
      - @ConditionalOnMissingBean (names: globalOpenApiCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#initExtraSchemas matched:
      - @ConditionalOnProperty (springdoc.enable-extra-schemas) matched (OnPropertyCondition)

   SpringDocConfiguration#operationBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.OperationService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#parameterBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.GenericParameterService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#parameterObjectNamingStrategyCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.ParameterObjectNamingStrategyCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#polymorphicModelConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.polymorphic-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PolymorphicModelConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#requestBodyBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.RequestBodyService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#responseSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.ResponseSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#schemaPropertyDeprecatingConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.deprecating-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SchemaPropertyDeprecatingConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#securityParser matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.SecurityService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.SpringDocCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocProviders matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDocProviders; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on CacheOrGroupedOpenApiCondition.OnCacheDisabled found non-matching nested conditions @ConditionalOnProperty (springdoc.cache.disabled) did not find property 'springdoc.cache.disabled', @ConditionalOnMissingBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans of type 'org.springdoc.core.models.GroupedOpenApi' systemGroupedOpenApi, infraGroupedOpenApi, spaceGroupedOpenApi, logGroupedOpenApi, smsGroupedOpenApi, accountGroupedOpenApi, tenantGroupedOpenApi, fileGroupedOpenApi, marketGroupedOpenApi, authGroupedOpenApi, socialGroupedOpenApi, rightsGroupedOpenApi, allGroupedOpenApi; NestedCondition on CacheOrGroupedOpenApiCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 1 matched 1 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 2 matched 1 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnListGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi' (CacheOrGroupedOpenApiCondition)

   SpringDocConfiguration#springdocObjectMapperProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.ObjectMapperProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.QuerydslProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.data.querydsl.binding.QuerydslBindingsFactory' (OnClassCondition)

   SpringDocConfiguration.QuerydslProvider#queryDslQuerydslPredicateOperationCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.QuerydslPredicateOperationCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider#springDataWebPropertiesProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDataWebPropertiesProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration#webFluxSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.WebFluxSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.WebConversionServiceConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.web.format.WebConversionService' (OnClassCondition)

   SpringDocGroovyConfiguration matched:
      - @ConditionalOnClass found required class 'groovy.lang.MetaClass' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true} and ${springdoc.enable-groovy:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocPageableConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Pageable' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocPageableConfiguration#dataRestDelegatingMethodParameterCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DataRestDelegatingMethodParameterCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration#pageOpenAPIConverter matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PagedModel', 'org.springframework.data.web.config.SpringDataWebSettings' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration#pageableOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.pageable-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageableOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocSecurityConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.SecurityFilterChain' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true} and ${springdoc.enable-spring-security:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocSecurityConfiguration.SpringSecurityLoginEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.Filter' (OnClassCondition)

   SpringDocSortConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Sort' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocSortConfiguration#sortOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.sort-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SortOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocWebMvcConfiguration#openApiResource matched:
      - @ConditionalOnExpression (#{(${springdoc.use-management-port:false} == false ) and ${springdoc.enable-default-api-docs:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.api.OpenApiWebMvcResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#requestBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.service.RequestService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#responseBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.GenericResponseService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#springWebProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringWebProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.function.RouterFunction' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration#routerFunctionProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.providers.RouterFunctionWebMvcProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringNativeClientAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - matched (SpringBootAdminClientEnabledCondition)

   SpringNativeServerAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.config.AdminServerMarkerConfiguration$Marker; SearchStrategy: all) found bean 'adminServerMarker' (OnBeanCondition)

   SqlInitializationAutoConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.sql.init.enabled=true) matched (OnPropertyCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'spring.sql.init.mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)

   SslAutoConfiguration#sslBundleRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.ssl.SslBundleRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   SslHealthContributorAutoConfiguration#sslHealthIndicator matched:
      - @ConditionalOnMissingBean (names: sslHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslHealthContributorAutoConfiguration#sslInfo matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslObservabilityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'sslBundleRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration#startupTimeMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.startup.StartupTimeMetricsListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   SystemMetricsAutoConfiguration#diskSpaceMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.system.DiskSpaceMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#fileDescriptorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.FileDescriptorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#processorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.ProcessorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#uptimeMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.UptimeMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutorConfigurations.AsyncConfigurerConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.scheduling.annotation.AsyncConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.AsyncConfigurerConfiguration#applicationTaskExecutorAsyncConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.scheduling.annotation.AsyncConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration matched:
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on TaskExecutorConfigurations.OnExecutorCondition.ModelCondition @ConditionalOnProperty (spring.task.execution.mode=force) did not find property 'spring.task.execution.mode'; NestedCondition on TaskExecutorConfigurations.OnExecutorCondition.ExecutorBeanCondition @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (TaskExecutorConfigurations.OnExecutorCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.ThreadPoolTaskExecutorBuilderConfiguration#threadPoolTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics' (OnClassCondition)
      - @ConditionalOnBean (types: java.util.concurrent.Executor,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'applicationTaskExecutor', 'taskScheduler' (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor' (OnBeanCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'; @ConditionalOnMissingBean (types: org.springframework.scheduling.TaskScheduler,java.util.concurrent.ScheduledExecutorService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration#taskScheduler matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.ThreadPoolTaskSchedulerBuilderConfiguration#threadPoolTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TemplateEngineConfigurations.DefaultTemplateEngineConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring6.ISpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThreadDumpEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ThreadDumpEndpointAutoConfiguration#dumpEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.management.ThreadDumpEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.thymeleaf.templatemode.TemplateMode', 'org.thymeleaf.spring6.SpringTemplateEngine' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (spring.thymeleaf.enabled=true) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.view.AbstractCachingViewResolver' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TomcatMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.binder.tomcat.TomcatMetrics', 'org.apache.catalina.Manager' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   TomcatMetricsAutoConfiguration#tomcatMetricsBinder matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.tomcat.TomcatMetrics,org.springframework.boot.actuate.metrics.web.tomcat.TomcatMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransServiceConfig#restTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransServiceConfig#rpcTransService matched:
      - @ConditionalOnBean (types: com.fhs.trans.service.impl.SimpleTransService$SimpleTransDiver; SearchStrategy: all) found bean 'MybatisPlusSimpleTransDiver' (OnBeanCondition)

   TransServiceConfig#simpleTransService matched:
      - @ConditionalOnBean (types: com.fhs.trans.service.impl.SimpleTransService$SimpleTransDiver; SearchStrategy: all) found bean 'MybatisPlusSimpleTransDiver' (OnBeanCondition)

   TransServiceConfig#transProxyController matched:
      - @ConditionalOnBean (types: com.fhs.trans.service.impl.SimpleTransService$SimpleTransDiver; SearchStrategy: all) found bean 'MybatisPlusSimpleTransDiver' (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a single bean 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionManagerCustomizationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionManagerCustomizationAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/jakarta.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: jakarta.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   WebClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebClientAutoConfiguration#webClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.reactive.function.client.WebClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebClientAutoConfiguration#webClientHttpConnectorCustomizer matched:
      - @ConditionalOnBean (types: org.springframework.http.client.reactive.ClientHttpConnector; SearchStrategy: all) found bean 'clientHttpConnector' (OnBeanCondition)

   WebClientAutoConfiguration#webClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebClientAutoConfiguration.WebClientCodecsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.codec.CodecCustomizer; SearchStrategy: all) found beans 'jacksonCodecCustomizer', 'defaultCodecCustomizer' (OnBeanCondition)

   WebClientAutoConfiguration.WebClientCodecsConfiguration#exchangeStrategiesCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientCodecCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebClientObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebEndpointAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration#controllerEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#endpointMediaTypes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.EndpointMediaTypes; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#pathMappedEndpoints matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.PathMappedEndpoints; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#webEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration#servletEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnBooleanProperty (spring.mvc.formcontent.filter.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:
      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:
      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:
      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#viewNameTranslator matched:
      - @ConditionalOnMissingBean (names: viewNameTranslator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet,org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) found beans 'webEndpointDiscoverer', 'dispatcherServlet' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#controllerEndpointHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.ControllerEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#endpointObjectMapperWebMvcConfigurer matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.endpoint.jackson.EndpointObjectMapper; SearchStrategy: all) found bean 'endpointObjectMapper' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#webEndpointServletHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcObservationAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.web.servlet.DispatcherServlet', 'io.micrometer.observation.Observation' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   WebMvcObservationAutoConfiguration#webMvcObservationFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.ServerHttpObservationFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcObservationAutoConfiguration.MeterFilterConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   WebSocketMessagingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMaInRedisTemplateConfigStorageConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.StringRedisTemplate' (OnClassCondition)
      - @ConditionalOnProperty (wx.miniapp.config-storage.type=redistemplate) matched (OnPropertyCondition)

   WxMaInRedisTemplateConfigStorageConfiguration#wxMaConfig matched:
      - @ConditionalOnMissingBean (types: cn.binarywang.wx.miniapp.config.WxMaConfig; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMaServiceAutoConfiguration#wxMaService matched:
      - @ConditionalOnBean (types: cn.binarywang.wx.miniapp.config.WxMaConfig; SearchStrategy: all) found bean 'wxMaConfig'; @ConditionalOnMissingBean (types: cn.binarywang.wx.miniapp.api.WxMaService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMpServiceAutoConfiguration#wxMpService matched:
      - @ConditionalOnMissingBean (types: me.chanjar.weixin.mp.api.WxMpService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMpStorageAutoConfiguration#wxMpConfigStorage matched:
      - @ConditionalOnMissingBean (types: me.chanjar.weixin.mp.config.WxMpConfigStorage; SearchStrategy: all) did not find any beans (OnBeanCondition)

   YmDataSourceAutoConfiguration#druidAdRemoveFilterFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   YmDeptDataPermissionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fozmo.ym.framework.security.core.LoginUser' (OnClassCondition)
      - @ConditionalOnBean (types: com.fozmo.ym.module.auth.api.permission.PermissionApi,com.fozmo.ym.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer; SearchStrategy: all) found beans 'sysDeptDataPermissionRuleCustomizer', 'permissionApiImpl' (OnBeanCondition)

   YmMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer' (OnClassCondition)
      - @ConditionalOnProperty (ym.metrics.enable) matched (OnPropertyCondition)

   YmRedisMQConsumerAutoConfiguration#redisMessageListenerContainer matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.pubsub.AbstractRedisChannelMessageListener; SearchStrategy: all) found bean 'spaceRedisWebSocketMessageConsumer' (OnBeanCondition)

   YmRedisMQConsumerAutoConfiguration#redisPendingMessageResendJob matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener; SearchStrategy: all) found beans 'worksDeleteConsumer', 'spaceDeleteConsumer', 'worksCommentConsumer', 'spaceCommentConsumer', 'socialUserConsumer', 'smsSendConsumer', 'noticeConsumer' (OnBeanCondition)

   YmRedisMQConsumerAutoConfiguration#redisStreamMessageCleanupJob matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener; SearchStrategy: all) found beans 'worksDeleteConsumer', 'spaceDeleteConsumer', 'worksCommentConsumer', 'spaceCommentConsumer', 'socialUserConsumer', 'smsSendConsumer', 'noticeConsumer' (OnBeanCondition)

   YmRedisMQConsumerAutoConfiguration#redisStreamMessageListenerContainer matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener; SearchStrategy: all) found beans 'worksDeleteConsumer', 'spaceDeleteConsumer', 'worksCommentConsumer', 'spaceCommentConsumer', 'socialUserConsumer', 'smsSendConsumer', 'noticeConsumer' (OnBeanCondition)

   YmSwaggerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.swagger.v3.oas.models.OpenAPI' (OnClassCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled=true) matched (OnPropertyCondition)

   YmTenantAutoConfiguration matched:
      - @ConditionalOnProperty (ym.tenant.enable) matched (OnPropertyCondition)

   YmTracerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fozmo.ym.framework.tracer.core.aop.BizTraceAspect' (OnClassCondition)
      - @ConditionalOnProperty (ym.tracer.enable) matched (OnPropertyCondition)

   YmXxlJobAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.xxl.job.core.executor.impl.XxlJobSpringExecutor' (OnClassCondition)
      - @ConditionalOnProperty (xxl.job.enabled=true) matched (OnPropertyCondition)

   YmXxlJobAutoConfiguration#xxlJobExecutor matched:
      - @ConditionalOnMissingBean (types: com.xxl.job.core.executor.XxlJobExecutor; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AdminServerCloudFoundryAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)

   AdminServerHazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'com.hazelcast.core.HazelcastInstance' (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#addReactiveHeadersInstanceExchangeFilter:
      Did not match:
         - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.reactive.ReactiveHttpHeadersProvider; SearchStrategy: all) did not find any beans of type de.codecentric.boot.admin.server.web.client.reactive.ReactiveHttpHeadersProvider (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.CompositeNotifierConfiguration:
      Did not match:
         - @ConditionalOnBean (types: de.codecentric.boot.admin.server.notify.Notifier; SearchStrategy: all) did not find any beans of type de.codecentric.boot.admin.server.notify.Notifier (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.DingTalkNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.dingtalk.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.DiscordNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.discord.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.FeiShuNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.feishu.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.FilteringNotifierWebConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate (types: de.codecentric.boot.admin.server.notify.filter.FilteringNotifier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.HipchatNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.hipchat.url) did not find property 'url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.LetsChatNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.letschat.url) did not find property 'url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.MailNotifierConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.mail.MailSender; SearchStrategy: all) did not find any beans of type org.springframework.mail.MailSender (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.MicrosoftTeamsNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.ms-teams.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.NotifierTriggerConfiguration:
      Did not match:
         - @ConditionalOnBean (types: de.codecentric.boot.admin.server.notify.Notifier; SearchStrategy: all) did not find any beans of type de.codecentric.boot.admin.server.notify.Notifier (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.OpsGenieNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.opsgenie.api-key) did not find property 'api-key' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.PagerdutyNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.pagerduty.service-key) did not find property 'service-key' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.RocketChatNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.rocketchat.url) did not find property 'url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.SlackNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.slack.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.TelegramNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.telegram.auth-token) did not find property 'auth-token' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.WebexNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.webex.auth-token) did not find property 'auth-token' (OnPropertyCondition)

   AdminServerUiAutoConfiguration.ReactiveUiConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   AdminServerWebConfiguration.ReactiveRestApiConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   AjCaptchaStorageAutoConfiguration#captchaCacheService:
      Did not match:
         - @ConditionalOnMissingBean (types: com.anji.captcha.service.CaptchaCacheService; SearchStrategy: all) found beans of type 'com.anji.captcha.service.CaptchaCacheService' AjCaptchaCacheService (OnBeanCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=false) did not find property 'spring.aop.proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AppOpticsMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.appoptics.AppOpticsMeterRegistry' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AtlasMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.atlas.AtlasMeterRegistry' (OnClassCondition)

   AuditAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnBooleanProperty (management.auditevents.enabled=true) matched (OnPropertyCondition)

   AuditEventsEndpointAutoConfiguration#auditEventsEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)

   AvailabilityHealthContributorAutoConfiguration#livenessStateHealthIndicator:
      Did not match:
         - @ConditionalOnBooleanProperty (management.health.livenessstate.enabled=true) did not find property 'management.health.livenessstate.enabled' (OnPropertyCondition)

   AvailabilityHealthContributorAutoConfiguration#readinessStateHealthIndicator:
      Did not match:
         - @ConditionalOnBooleanProperty (management.health.readinessstate.enabled=true) did not find property 'management.health.readinessstate.enabled' (OnPropertyCondition)

   AvailabilityProbesAutoConfiguration:
      Did not match:
         - Probes availability not running on a supported cloud platform (AvailabilityProbesAutoConfiguration.ProbesCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   BatchObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.configuration.annotation.BatchObservabilityBeanPostProcessor' (OnClassCondition)

   BraveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'brave.Tracer' (OnClassCondition)

   Cache2kCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.cache2k.Cache2kBuilder' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (names: cacheResolver types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans of type 'org.springframework.cache.CacheManager' redisCacheManager, tenantRedisCacheManager (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CacheMeterBinderProvidersConfiguration.Cache2kCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.cache2k.Cache2kBuilder', 'org.cache2k.extra.spring.SpringCache2kCache', 'org.cache2k.extra.micrometer.Cache2kCacheMetrics' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.HazelcastCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.spring.cache.HazelcastCache', 'com.hazelcast.core.Hazelcast' (OnClassCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.CaffeineCacheConfiguration unknown cache type (CacheCondition)
      Matched:
         - @ConditionalOnClass found required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CloudFoundryActuatorAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnBooleanProperty (management.cloudfoundry.enabled=true) matched (OnPropertyCondition)

   CompositeMeterRegistryConfiguration:
      Did not match:
         - NoneNestedConditions 1 matched 1 did not; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.SingleInjectableMeterRegistry @ConditionalOnSingleCandidate (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found a single bean 'prometheusMeterRegistry'; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.NoMeterRegistryCondition @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition)

   ConnectionFactoryHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   ConnectionPoolMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.pool.ConnectionPool' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceCheckpointRestoreConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.crac.Resource' (OnClassCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceConfiguration.OracleUcp:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DatadogMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.datadog.DatadogMeterRegistry' (OnClassCondition)

   DdlAutoConfiguration#ddlApplicationRunner:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.mybatisplus.extension.ddl.IDdl; SearchStrategy: all) did not find any beans of type com.baomidou.mybatisplus.extension.ddl.IDdl (OnBeanCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidDataSourceAutoConfigure#dataSource:
      Did not match:
         - @ConditionalOnMissingBean (types: com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceWrapper,com.alibaba.druid.pool.DruidDataSource,javax.sql.DataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   DynamicDataSourceCreatorAutoConfiguration.AtomikosDataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.atomikos.jdbc.AtomikosDataSourceBean' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.BeeCpDataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'cn.beecp.BeeDataSource' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.Dbcp2DataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DynatraceMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.dynatrace.DynatraceMeterRegistry' (OnClassCondition)

   EasyTransMybatisPlusConfig#mybatisPlusTransableRegister:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-auto=true) did not find property 'easy-trans.is-enable-auto' (OnPropertyCondition)

   EasyTransResponseBodyAdvice:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-global=true) found different value in property 'easy-trans.is-enable-global' (OnPropertyCondition)

   ElasticMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.elastic.ElasticMeterRegistry' (OnClassCondition)

   ElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.elasticsearch.ElasticsearchClient' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate' (OnClassCondition)

   ElasticsearchReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.repository.ElasticsearchRepository' (OnClassCondition)

   ElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClientBuilder' (OnClassCondition)

   ElasticsearchRestHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration#tomcatVirtualThreadsProtocolHandlerCustomizer:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FlywayEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GangliaMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.ganglia.GangliaMeterRegistry' (OnClassCondition)

   GenericCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration unknown cache type (CacheCondition)

   GraphQlAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlRSocketAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphiteMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.graphite.GraphiteMeterRegistry' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration.GsonHttpMessageConverterConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.JacksonJsonbUnavailable NoneNestedConditions 1 matched 1 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JsonbPreferred @ConditionalOnPreferredJsonMapper JSONB no property was configured and Jackson is the default; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JacksonAvailable @ConditionalOnBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) found bean 'mappingJackson2HttpMessageConverter'; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.GsonPreferred @ConditionalOnPreferredJsonMapper GSON no property was configured and Jackson is the default (GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.JakartaWebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HealthEndpointReactiveWebExtensionConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   HealthEndpointWebExtensionConfiguration.JerseyAdditionalHealthEndpointPathsConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   HeapDumpWebEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnAvailableEndpoint the configured access for endpoint 'heapdump' is NONE (OnAvailableEndpointCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.persistence.EntityManager' (OnClassCondition)

   HibernateMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.persistence.EntityManagerFactory' (OnClassCondition)

   HttpExchangesAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnBooleanProperty (management.httpexchanges.recording.enabled=true) matched (OnPropertyCondition)

   HttpExchangesAutoConfiguration.ReactiveHttpExchangesConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
         - Ancestor org.springframework.boot.actuate.autoconfigure.web.exchanges.HttpExchangesAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   HttpExchangesAutoConfiguration.ServletHttpExchangesConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.actuate.autoconfigure.web.exchanges.HttpExchangesAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   HttpExchangesEndpointAutoConfiguration#httpExchangesEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository (OnBeanCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.web.reactive.DispatcherHandler', 'org.springframework.http.server.reactive.HttpHandler' (OnClassCondition)

   HumioMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.humio.HumioMeterRegistry' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   I18nAspect:
      Did not match:
         - @ConditionalOnProperty (aj.captcha.i18n.enabled) did not find property 'aj.captcha.i18n.enabled' (OnPropertyCondition)

   IdentifierGeneratorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.commons.util.InetUtils' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.influx.InfluxMeterRegistry' (OnClassCondition)

   InfoContributorAutoConfiguration#buildInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.BuildProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#envInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.env.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#gitInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.GitProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#javaInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.java.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#osInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.os.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#processInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.process.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#sslInfo:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.ssl.enabled is not true (OnEnabledInfoContributorCondition)
      Matched:
         - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) did not find any beans (OnBeanCondition)

   InfoContributorAutoConfiguration#sslInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.ssl.enabled is not true (OnEnabledInfoContributorCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   IntegrationGraphEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.graph.IntegrationGraphServer' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.JCacheCacheConfiguration unknown cache type (CacheCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JedisConnectionConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found beans of type 'org.springframework.data.redis.connection.RedisConnectionFactory' redissonConnectionFactory (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.apache.commons.pool2.impl.GenericObjectPool', 'org.springframework.data.redis.connection.jedis.JedisConnection', 'redis.clients.jedis.Jedis' (OnClassCondition)
         - @ConditionalOnProperty (spring.data.redis.client-type=jedis) matched (OnPropertyCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JerseySameManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JerseyServerMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.micrometer.server.ObservationApplicationEventListener' (OnClassCondition)

   JerseyWebEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JettyMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.server.Server' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.Message' (OnClassCondition)

   JmsHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   JmxAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)

   JmxEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)

   JmxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.jmx.JmxMeterRegistry' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'spring.datasource.jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.transaction.Transaction' (OnClassCondition)

   JvmMetricsAutoConfiguration#virtualThreadMetrics:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.java21.instrument.binder.jdk.VirtualThreadMetrics' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   KafkaMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.ProducerFactory' (OnClassCondition)

   KairosMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.kairos.KairosMeterRegistry' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.LdapOperations' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LettuceConnectionConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.lettuce.core.RedisClient' (OnClassCondition)

   LettuceMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.lettuce.core.metrics.MicrometerCommandLatencyRecorder' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   LiquibaseEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.integration.spring.SpringLiquibase' (OnClassCondition)

   Log4J2MetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.logging.log4j.core.LoggerContext' (OnClassCondition)

   LogRecordProxyAutoConfiguration#parseFunction:
      Did not match:
         - @ConditionalOnMissingBean (types: com.mzt.logapi.service.IParseFunction; SearchStrategy: all) found beans of type 'com.mzt.logapi.service.IParseFunction' adminUserParseFunction, areaParseFunction, booleanParseFunction, deptParseFunction, postParseFunction, sexParseFunction (OnBeanCondition)

   MailHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.mail.javamail.JavaMailSenderImpl; SearchStrategy: all) did not find any beans of type org.springframework.mail.javamail.JavaMailSenderImpl (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnClassCondition)
         - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on MailSenderAutoConfiguration.MailSenderCondition.JndiNameProperty @ConditionalOnProperty (spring.mail.jndi-name) did not find property 'spring.mail.jndi-name'; NestedCondition on MailSenderAutoConfiguration.MailSenderCondition.HostProperty @ConditionalOnProperty (spring.mail.host) did not find property 'spring.mail.host' (MailSenderAutoConfiguration.MailSenderCondition)
      Matched:
         - @ConditionalOnClass found required classes 'jakarta.mail.internet.MimeMessage', 'jakarta.activation.MimeType', 'org.springframework.mail.MailSender' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mail.test-connection=true) did not find property 'spring.mail.test-connection' (OnPropertyCondition)

   ManagementContextAutoConfiguration.DifferentManagementContextConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   ManagementWebSecurityAutoConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' filterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ReactiveWebConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MetricsAspectsAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.observations.annotations.enabled=true) did not find property 'management.observations.annotations.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'org.aspectj.weaver.Advice' (OnClassCondition)

   MicrometerTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.MongoTemplate' (OnClassCondition)

   MongoMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClientSettings' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.ReactiveMongoTemplate' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MultipleOpenApiSupportConfiguration.SpringDocWebMvcActuatorDifferentConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisPlusAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) found beans of type 'org.mybatis.spring.mapper.MapperScannerConfigurer' com.fozmo.ym.framework.mybatis.config.YmMybatisAutoConfiguration#MapperScannerRegistrar#0 (OnBeanCondition)

   MybatisPlusInnerInterceptorAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor; SearchStrategy: all) did not find any beans of type com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration#mpjSqlInjector:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.mybatisplus.core.injector.ISqlInjector; SearchStrategy: all) did not find any beans of type com.baomidou.mybatisplus.core.injector.ISqlInjector (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   NewRelicMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.newrelic.NewRelicMeterRegistry' (OnClassCondition)

   NoOpCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration unknown cache type (CacheCondition)

   NoOpMeterRegistryConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (OnBeanCondition)

   NoopTracerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   OAuth2AuthorizationServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2AuthorizationServerJwtAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.nimbusds.jose.jwk.source.JWKSource' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   OAuth2ClientWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.web.OAuth2AuthorizedClientRepository' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken' (OnClassCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration.TracingAndMetricsObservationHandlerConfiguration:
      Did not match:
         - @ConditionalOnBean did not find required type 'io.micrometer.tracing.Tracer' (OnBeanCondition)
         - @ConditionalOnBean (types: ?; SearchStrategy: all) did not find any beans of type ? (OnBeanCondition)

   ObservationAutoConfiguration.MetricsWithTracingConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   ObservationAutoConfiguration.ObservedAspectConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.observations.annotations.enabled=true) did not find property 'management.observations.annotations.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   ObservationAutoConfiguration.OnlyTracingConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   OpenTelemetryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.sdk.OpenTelemetrySdk' (OnClassCondition)

   OpenTelemetryLoggingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.sdk.logs.SdkLoggerProvider' (OnClassCondition)

   OpenTelemetryTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.otel.bridge.OtelTracer' (OnClassCondition)

   OtlpLoggingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.exporter.otlp.http.logs.OtlpHttpLogRecordExporter' (OnClassCondition)

   OtlpMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.registry.otlp.OtlpMeterRegistry' (OnClassCondition)

   OtlpTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.otel.bridge.OtelTracer' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   PrometheusExemplarsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusPushGatewayConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.prometheus.metrics.exporter.pushgateway.PushGateway' (OnClassCondition)

   PulsarAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   PulsarReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   QuartzEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   R2dbcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)

   R2dbcInitializationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)

   R2dbcObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcProxyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcTransactionManagerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)

   RSocketGraphQlClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   RabbitHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   RabbitMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.ConnectionFactory' (OnClassCondition)

   ReactiveCloudFoundryActuatorAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ReactiveElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.transport.ElasticsearchTransport' (OnClassCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient' (OnClassCondition)

   ReactiveManagementContextAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveManagementWebSecurityAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity', 'org.springframework.security.web.server.WebFilterChainProxy' (OnClassCondition)

   ReactiveMultipartAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.http.codec.multipart.DefaultPartHttpMessageReader', 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   ReactiveOAuth2ClientWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration.SpringBootWebFluxSecurityConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication.ReactiveWebApplicationCondition not a reactive web application; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication.RSocketSecurityEnabledCondition @ConditionalOnBean (types: org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler; SearchStrategy: all) did not find any beans of type org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler (ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)
         - AnyNestedCondition 1 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.PasswordConfigured @ConditionalOnProperty (spring.security.user.password) did not find property 'spring.security.user.password'; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.NameConfigured @ConditionalOnProperty (spring.security.user.name) did not find property 'spring.security.user.name'; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.MissingAlternative @ConditionalOnMissingClass did not find unwanted classes 'org.springframework.security.oauth2.client.registration.ClientRegistrationRepository', 'org.springframework.security.oauth2.server.resource.introspection.ReactiveOpaqueTokenIntrospector' (ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.http.ReactiveHttpInputMessage' (OnClassCondition)

   RedisAutoConfiguration#redisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) found beans named redisTemplate (OnBeanCondition)

   RedisAutoConfiguration#stringRedisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) found beans of type 'org.springframework.data.redis.core.StringRedisTemplate' stringRedisTemplate (OnBeanCondition)

   RedisHealthContributorAutoConfiguration#redisHealthContributor:
      Did not match:
         - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) found beans named redisHealthContributor (OnBeanCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.data.redis.repositories.enabled=true) found different value in property 'spring.data.redis.repositories.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RedissonAutoConfiguration#redisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) found beans named redisTemplate (OnBeanCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityDataConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.data.repository.query.SecurityEvaluationContextExtension' (OnClassCondition)

   SecurityRequestMatchersManagementContextConfiguration.JerseyRequestMatcherConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletEndpointManagementContextConfiguration.JerseyServletEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   ServletManagementContextAutoConfiguration.ApplicationContextFilterConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.server.add-application-context-header=true) did not find property 'management.server.add-application-context-header' (OnPropertyCondition)

   ServletWebServerFactoryAutoConfiguration.ForwardedHeaderFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SessionsEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   ShutdownEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnAvailableEndpoint the configured access for endpoint 'shutdown' is NONE (OnAvailableEndpointCondition)

   SignalFxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.signalfx.SignalFxMeterRegistry' (OnClassCondition)

   SimpleCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration unknown cache type (CacheCondition)

   SimpleMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledMetricsExport management.defaults.metrics.export.enabled is considered true (OnMetricsExportEnabledCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.application.admin.enabled=true) did not find property 'spring.application.admin.enabled' (OnPropertyCondition)

   SpringBootAdminClientAutoConfiguration.BlockingRegistrationClientConfig#registrationClient:
      Did not match:
         - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.RegistrationClient; SearchStrategy: all) found beans of type 'de.codecentric.boot.admin.client.registration.RegistrationClient' registrationClient (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.ReactiveConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   SpringBootAdminClientAutoConfiguration.ReactiveRegistrationClientConfig:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder,org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found beans of type 'org.springframework.web.client.RestClient$Builder' restClientBuilder and found beans of type 'org.springframework.boot.web.client.RestTemplateBuilder' restTemplateBuilder (OnBeanCondition)

   SpringBootAdminClientCloudFoundryAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
         - matched (SpringBootAdminClientEnabledCondition)

   SpringBootWebSecurityConfiguration.SecurityFilterChainConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' filterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)

   SpringDocConfiguration#oas31ModelConverter:
      Did not match:
         - @ConditionalOnProperty (springdoc.explicit-object-schema=true) did not find property 'springdoc.explicit-object-schema' (OnPropertyCondition)

   SpringDocConfiguration#openAPIBuilder:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.service.OpenAPIService; SearchStrategy: all) found beans of type 'org.springdoc.core.service.OpenAPIService' openApiBuilder (OnBeanCondition)

   SpringDocConfiguration#propertiesResolverForSchema:
      Did not match:
         - @ConditionalOnProperty (springdoc.api-docs.resolve-schema-properties) did not find property 'springdoc.api-docs.resolve-schema-properties' (OnPropertyCondition)

   SpringDocConfiguration#propertyCustomizingConverter:
      Did not match:
         - @ConditionalOnBean (types: org.springdoc.core.customizers.PropertyCustomizer; SearchStrategy: all) did not find any beans of type org.springdoc.core.customizers.PropertyCustomizer (OnBeanCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor2:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)

   SpringDocConfiguration.SpringDocActuatorConfiguration:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocRepositoryRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocDataRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocFunctionCatalogConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.function.web.function.FunctionEndpointInitializer' (OnClassCondition)

   SpringDocHateoasConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.server.LinkRelationProvider' (OnClassCondition)

   SpringDocJacksonKotlinModuleConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.module.kotlin.KotlinModule' (OnClassCondition)

   SpringDocJavadocConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.therapi.runtimejavadoc.CommentFormatter' (OnClassCondition)

   SpringDocKotlinConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'kotlin.coroutines.Continuation' (OnClassCondition)

   SpringDocKotlinxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'kotlinx.coroutines.flow.Flow' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringDocSecurityOAuth2ClientConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.annotation.RegisteredOAuth2AuthorizedClient' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringDocSecurityOAuth2Configuration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringSecurityLoginEndpointConfiguration#springSecurityLoginEndpointCustomizer:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-login-endpoint) did not find property 'springdoc.show-login-endpoint' (OnPropertyCondition)

   SpringDocSortConfiguration#dataRestDelegatingMethodParameterCustomizer:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DataRestDelegatingMethodParameterCustomizer; SearchStrategy: all) found beans of type 'org.springdoc.core.customizers.DataRestDelegatingMethodParameterCustomizer' dataRestDelegatingMethodParameterCustomizer (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#actuatorProvider:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.show-actuator:false} or ${springdoc.use-management-port:false}}) resulted in false (OnExpressionCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#openApiActuatorResource:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.use-management-port:false} and ${springdoc.enable-default-api-docs:true}}) resulted in false (OnExpressionCondition)

   SslObservabilityAutoConfiguration#sslInfoProvider:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) found beans of type 'org.springframework.boot.info.SslInfo' sslInfo (OnBeanCondition)

   StackdriverMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.stackdriver.StackdriverMeterRegistry' (OnClassCondition)

   StartupEndpointAutoConfiguration:
      Did not match:
         - ApplicationStartup configured applicationStartup is of type class org.springframework.core.metrics.DefaultApplicationStartup, expected BufferingApplicationStartup. (StartupEndpointAutoConfiguration.ApplicationStartupCondition)

   StatsdMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.statsd.StatsdMeterRegistry' (OnClassCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder' simpleAsyncTaskExecutorBuilder (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutorVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder' simpleAsyncTaskSchedulerBuilder (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration#taskSchedulerVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TemplateEngineConfigurations.ReactiveTemplateEngineConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity6.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.layoutdialect.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   TransServiceConfig#container:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransServiceConfig#listenerAdapter:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransServiceConfig#redisCacheService:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransServiceConfig#transMessageListener:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.AspectJTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnBean did not find required type 'org.springframework.transaction.aspectj.AbstractTransactionAspect' (OnBeanCondition)
         - @ConditionalOnBean (types: ?; SearchStrategy: all) did not find any beans of type ? (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) found beans of type 'org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration' org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=false) did not find property 'spring.aop.proxy-target-class' (OnPropertyCondition)
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationManager,org.springframework.security.authentication.AuthenticationProvider,org.springframework.security.core.userdetails.UserDetailsService,org.springframework.security.authentication.AuthenticationManagerResolver,?; SearchStrategy: all) found beans of type 'org.springframework.security.authentication.AuthenticationManager' authenticationManagerBean (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - AnyNestedCondition 1 matched 2 did not; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.PasswordConfigured @ConditionalOnProperty (spring.security.user.password) did not find property 'spring.security.user.password'; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.NameConfigured @ConditionalOnProperty (spring.security.user.name) did not find property 'spring.security.user.name'; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.MissingAlternative @ConditionalOnMissingClass did not find unwanted classes 'org.springframework.security.oauth2.client.registration.ClientRegistrationRepository', 'org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector', 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured)

   WavefrontAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.application.ApplicationTags' (OnClassCondition)

   WavefrontMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WavefrontTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebFluxEndpointManagementContextConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.web.reactive.DispatcherHandler', 'org.springframework.http.server.reactive.HttpHandler' (OnClassCondition)

   WebFluxObservationAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'io.micrometer.observation.Observation', 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mvc.hiddenmethod.filter.enabled=true) did not find property 'spring.mvc.hiddenmethod.filter.enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ProblemDetailsErrorHandlingConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mvc.problemdetails.enabled=true) did not find property 'spring.mvc.problemdetails.enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#managementHealthEndpointWebMvcHandlerMapping:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.client.core.WebServiceTemplate' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSessionIdResolverAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.web.server.session.WebSessionManager', 'reactor.core.publisher.Mono' (OnClassCondition)

   WebSocketMessagingAutoConfiguration.WebSocketMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.socket.config.annotation.DelegatingWebSocketMessageBrokerConfiguration,com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans of type org.springframework.web.socket.config.annotation.DelegatingWebSocketMessageBrokerConfiguration (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.ObjectMapper', 'org.springframework.messaging.simp.config.AbstractMessageBrokerConfiguration' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.ee10.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   WxMaInJedisConfigStorageConfiguration:
      Did not match:
         - @ConditionalOnProperty (wx.miniapp.config-storage.type=jedis) found different value in property 'type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'redis.clients.jedis.JedisPool', 'redis.clients.jedis.JedisPoolConfig' (OnClassCondition)

   WxMaInMemoryConfigStorageConfiguration:
      Did not match:
         - @ConditionalOnProperty (wx.miniapp.config-storage.type=memory) found different value in property 'type' (OnPropertyCondition)

   WxMaInRedissonConfigStorageConfiguration:
      Did not match:
         - @ConditionalOnProperty (wx.miniapp.config-storage.type=redisson) found different value in property 'type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.redisson.Redisson', 'org.redisson.api.RedissonClient' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.transaction.TransactionManager' (OnClassCondition)

   YmApiLogAutoConfiguration#apiAccessLogFilter:
      Did not match:
         - @ConditionalOnProperty (ym.access-log.enable) found different value in property 'enable' (OnPropertyCondition)

   YmLock4jConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.baomidou.lock.annotation.Lock4j' (OnClassCondition)

   YmMybatisAutoConfiguration#keyGenerator:
      Did not match:
         - @ConditionalOnProperty (mybatis-plus.global-config.db-config.id-type=INPUT) found different value in property 'id-type' (OnPropertyCondition)

   YmRabbitMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   YmTenantAutoConfiguration#tenantRabbitMQInitializer:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   YmTenantAutoConfiguration#tenantRocketMQInitializer:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.rocketmq.spring.core.RocketMQTemplate' (OnClassCondition)

   YmWebAutoConfiguration#restTemplate:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.client.RestTemplate; SearchStrategy: all) found beans of type 'org.springframework.web.client.RestTemplate' restTemplate (OnBeanCondition)

   YmXssAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (ym.xss.enable=true) found different value in property 'enable' (OnPropertyCondition)

   ZipkinAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'zipkin2.reporter.Encoding' (OnClassCondition)


Exclusions:
-----------

    org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration


Unconditional classes:
----------------------

    org.springframework.boot.actuate.autoconfigure.info.InfoContributorAutoConfiguration

    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.metrics.integration.IntegrationMetricsAutoConfiguration

    com.fozmo.ym.framework.web.config.YmWebAutoConfiguration

    com.fozmo.ym.framework.mq.redis.config.YmRedisMQConsumerAutoConfiguration

    com.fozmo.ym.framework.apilog.config.YmApiLogAutoConfiguration

    de.codecentric.boot.admin.server.config.AdminServerWebConfiguration

    com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration

    com.fozmo.ym.framework.signature.config.YmApiSignatureAutoConfiguration

    com.fozmo.ym.framework.redis.config.YmRedisAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration

    com.fozmo.ym.framework.mybatis.config.YmMybatisAutoConfiguration

    de.codecentric.boot.admin.server.config.AdminServerInstanceWebClientConfiguration

    com.fozmo.ym.framework.ratelimiter.config.YmRateLimiterConfiguration

    com.fozmo.ym.framework.jackson.config.YmJacksonAutoConfiguration

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.availability.AvailabilityHealthContributorAutoConfiguration

    com.binarywang.spring.starter.wxjava.mp.config.WxMpAutoConfiguration

    org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration

    com.fozmo.ym.framework.mq.redis.config.YmRedisMQProducerAutoConfiguration

    com.fozmo.ym.framework.banner.config.YmBannerAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration

    com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration

    com.fhs.trans.config.TransServiceConfig

    com.fozmo.ym.framework.operatelog.config.YmOperateLogConfiguration

    org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration

    com.fozmo.ym.framework.redis.config.YmCacheAutoConfiguration

    com.fozmo.ym.framework.dict.config.YmDictAutoConfiguration

    com.fozmo.ym.framework.idempotent.config.YmIdempotentConfiguration

    com.fozmo.ym.framework.security.config.YmSecurityAutoConfiguration

    com.anji.captcha.config.AjCaptchaAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.jackson.JacksonEndpointAutoConfiguration

    com.fozmo.ym.framework.security.config.YmWebSecurityConfigurerAdapter

    com.fozmo.ym.framework.translate.config.YmTranslateAutoConfiguration

    cn.hutool.extra.spring.SpringUtil

    org.springdoc.core.configuration.SpringDocSpecPropertiesConfiguration

    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration

    com.binarywang.spring.starter.wxjava.miniapp.config.WxMaAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration

    com.fozmo.ym.framework.datasource.config.YmDataSourceAutoConfiguration

    com.fhs.trans.config.EasyTransMybatisPlusConfig



2025-08-11 12:22:17.608 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-11 12:22:17.618 | [34m INFO 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 35.984 seconds (process running for 37.544)
2025-08-11 12:22:17.637 | [34m INFO 13507[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-11 12:22:17.637 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.ApplicationAvailabilityBean     [0;39m | Application availability state LivenessState changed to CORRECT
2025-08-11 12:22:17.652 | [34m INFO 13422[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-11 12:22:17.657 | [1;31mERROR 13507[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 12:22:17.664 | [34m INFO 13507[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:10000/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 12:22:17.679 | [39mDEBUG 13422[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.ApplicationAvailabilityBean     [0;39m | Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
2025-08-11 12:22:17.728 | [31m WARN 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-11 12:22:17.745 | [34m INFO 13507[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-08-11 12:22:17.746 | [1;31mERROR 13507[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registryRemove(AdminBizClient.java:51)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:84)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 12:22:17.746 | [34m INFO 13507[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:10000/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registryRemove, content=null]
2025-08-11 12:22:17.746 | [34m INFO 13507[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-11 12:22:17.746 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-11 12:22:17.746 | [34m INFO 13507[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-08-11 12:22:17.747 | [34m INFO 13507[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-11 12:22:17.747 | [34m INFO 13507[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-11 12:22:17.752 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-11 12:22:17.753 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-11 12:22:17.837 | [39mDEBUG 13422[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://*********:11000/actuator, healthUrl=http://*********:11000/actuator/health, serviceUrl=http://*********:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 12:22:18.013 | [34m INFO 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 12:22:18.013 | [34m INFO 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-11 12:22:18.013 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Detected StandardServletMultipartResolver
2025-08-11 12:22:18.013 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Detected AcceptHeaderLocaleResolver
2025-08-11 12:22:18.013 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Detected FixedThemeResolver
2025-08-11 12:22:18.016 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6c249eb5
2025-08-11 12:22:18.016 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Detected org.springframework.web.servlet.support.SessionFlashMapManager@4a3e5552
2025-08-11 12:22:18.017 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-11 12:22:18.017 | [34m INFO 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 4 ms
2025-08-11 12:22:18.107 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-11 12:22:18.111 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-11 12:22:18.114 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 12:22:18.120 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-11 12:22:18.120 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-11 12:22:18.120 | [34m INFO 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-11 12:22:18.171 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://*********:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 12:22:18.176 | [39mDEBUG 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AdminServerAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.config.AdminServerMarkerConfiguration$Marker; SearchStrategy: all) found bean 'adminServerMarker' (OnBeanCondition)

   AdminServerAutoConfiguration#applicationRegistry matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.ApplicationRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#endpointDetectionTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.EndpointDetectionTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#endpointDetector matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.EndpointDetector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#eventStore matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.eventstore.InstanceEventStore; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#infoUpdateTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InfoUpdateTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#infoUpdater matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InfoUpdater; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceFilter matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InstanceFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceIdGenerator matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InstanceIdGenerator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceRegistry matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.InstanceRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#instanceRepository matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.domain.entities.InstanceRepository; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#statusUpdateTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.StatusUpdateTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerAutoConfiguration#statusUpdater matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.services.StatusUpdater; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration#instanceWebClientBuilder matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.InstanceWebClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.CookieStoreConfiguration#cookieStore matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.cookies.PerInstanceCookieStore; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.CookieStoreConfiguration#cookieStoreCleanupTrigger matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.cookies.CookieStoreCleanupTrigger; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.HttpHeadersProviderConfiguration#basicAuthHttpHeadersProvider matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.client.BasicAuthHttpHeaderProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration#filterInstanceWebClientCustomizer matched:
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.InstanceExchangeFilterFunction; SearchStrategy: all) found beans 'addHeadersInstanceExchangeFilter', 'rewriteEndpointUrlInstanceExchangeFilter', 'setDefaultAcceptHeaderInstanceExchangeFilter', 'logfileAcceptWorkaround', 'timeoutInstanceExchangeFilter', 'retryInstanceExchangeFilter', 'legacyEndpointConverterInstanceExchangeFilter', 'cookieHandlingInstanceExchangeFilter'; @ConditionalOnMissingBean (names: filterInstanceWebClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#addHeadersInstanceExchangeFilter matched:
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.HttpHeadersProvider; SearchStrategy: all) found bean 'basicAuthHttpHeadersProvider'; @ConditionalOnMissingBean (names: addHeadersInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#cookieHandlingInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: cookieHandlingInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#legacyEndpointConverterInstanceExchangeFilter matched:
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.LegacyEndpointConverter; SearchStrategy: all) found beans 'infoLegacyEndpointConverter', 'threaddumpLegacyEndpointConverter', 'mappingsLegacyEndpointConverter', 'startupLegacyEndpointConverter', 'httptraceLegacyEndpointConverter', 'envLegacyEndpointConverter', 'healthLegacyEndpointConverter', 'beansLegacyEndpointConverter', 'liquibaseLegacyEndpointConverter', 'flywayLegacyEndpointConverter', 'configpropsLegacyEndpointConverter'; @ConditionalOnMissingBean (names: legacyEndpointConverterInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#logfileAcceptWorkaround matched:
      - @ConditionalOnMissingBean (names: logfileAcceptWorkaround; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#retryInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: retryInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#rewriteEndpointUrlInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: rewriteEndpointUrlInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#setDefaultAcceptHeaderInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: setDefaultAcceptHeaderInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#timeoutInstanceExchangeFilter matched:
      - @ConditionalOnMissingBean (names: timeoutInstanceExchangeFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#beansLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: beansLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#configpropsLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: configpropsLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#envLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: envLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#flywayLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: flywayLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#healthLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: healthLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#httptraceLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: httptraceLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#infoLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: infoLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#liquibaseLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: liquibaseLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#mappingsLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: mappingsLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#startupLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: startupLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.LegaycEndpointConvertersConfiguration#threaddumpLegacyEndpointConverter matched:
      - @ConditionalOnMissingBean (names: threaddumpLegacyEndpointConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerNotifierAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)

   AdminServerUiAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.config.AdminServerMarkerConfiguration$Marker; SearchStrategy: all) found bean 'adminServerMarker' (OnBeanCondition)

   AdminServerUiAutoConfiguration#homeUiController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.ui.web.UiController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerUiAutoConfiguration.ServletUiConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   AdminServerUiAutoConfiguration.ServletUiConfiguration.AdminUiWebMvcConfig#homepageForwardFilter matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.ui.web.servlet.HomepageForwardingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerWebConfiguration#applicationsController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.ApplicationsController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerWebConfiguration#instancesController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.InstancesController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerWebConfiguration.ServletRestApiConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   AdminServerWebConfiguration.ServletRestApiConfiguration#instancesProxyController matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.server.web.servlet.InstancesProxyController; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AjCaptchaServiceAutoConfiguration#captchaService matched:
      - @ConditionalOnMissingBean (types: com.anji.captcha.service.CaptchaService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AopAutoConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   ApplicationAvailabilityAutoConfiguration#applicationAvailability matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.availability.ApplicationAvailability; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AuditEventsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration#beansEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.beans.BeansEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CacheMeterBinderProvidersConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.MeterBinder' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.CaffeineCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.cache.caffeine.CaffeineCache', 'com.github.benmanes.caffeine.cache.Cache' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.JCacheCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.cache.jcache.JCacheCache', 'javax.cache.CacheManager' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.RedisCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.cache.RedisCache' (OnClassCondition)

   CacheMetricsAutoConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans 'tenantRedisCacheManager', 'redisCacheManager' (OnBeanCondition)

   CacheMetricsRegistrarConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.metrics.cache.CacheMeterBinderProvider,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'caffeineCacheMeterBinderProvider', 'redisCacheMeterBinderProvider', 'jCacheCacheMeterBinderProvider' (OnBeanCondition)

   CachesEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   CachesEndpointAutoConfiguration#cachesEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CachesEndpointAutoConfiguration#cachesEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) found bean 'cachesEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   org.springframework.boot.autoconfigure.http.client.reactive.ClientHttpConnectorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.http.client.reactive.ClientHttpConnector', 'reactor.core.publisher.Mono' (OnClassCondition)
      - Detected ClientHttpConnectorBuilder (ConditionalOnClientHttpConnectorBuilderDetection)

   org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnector matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.client.reactive.ClientHttpConnector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnectorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.reactive.ClientHttpConnectorBuilder<?>; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnectorSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.reactive.ClientHttpConnectorSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   org.springframework.boot.autoconfigure.http.client.reactive.ClientHttpConnectorAutoConfiguration$ReactorNetty matched:
      - @ConditionalOnClass found required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration$ReactorNetty matched:
      - @ConditionalOnClass found required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.http.codec.CodecConfigurer', 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   CompositeMeterRegistryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.composite.CompositeMeterRegistry' (OnClassCondition)

   ConditionsReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ConditionsReportEndpointAutoConfiguration#conditionsReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.condition.ConditionsReportEndpoint; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) found bean 'configurationPropertiesReportEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)
      - @ConditionalOnMissingBean (types: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)

   DataSourceHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found bean 'dataSource' (OnBeanCondition)

   DataSourceHealthContributorAutoConfiguration#dbHealthContributor matched:
      - @ConditionalOnMissingBean (names: dbHealthIndicator,dbHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceInitializationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer,org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'dataSource' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.DataSourcePoolMetadataMetricsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.jdbc.metadata.DataSourcePoolMetadataProvider; SearchStrategy: all) found bean 'hikariPoolDataSourceMetadataProvider' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.HikariDataSourceMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DdlAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.baomidou.mybatisplus.extension.ddl.IDdl' (OnClassCondition)

   DiskSpaceHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   DiskSpaceHealthContributorAutoConfiguration#diskSpaceHealthIndicator matched:
      - @ConditionalOnMissingBean (names: diskSpaceHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.alibaba.druid.pool.DruidDataSource) matched (OnPropertyCondition)

   DruidDynamicDataSourceConfiguration matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure' (OnClassCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAopConfiguration#dsProcessor matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.processor.DsProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAopConfiguration#dynamicDatasourceAnnotationAdvisor matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.aop.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAopConfiguration#dynamicTransactionAdvisor matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.seata=false) matched (OnPropertyCondition)

   DynamicDataSourceAssistConfiguration#dataSourceCreator matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAssistConfiguration#dataSourceInitEvent matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.event.DataSourceInitEvent; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAssistConfiguration.DsTxEventListenerFactoryConfiguration#dsTxEventListenerFactory matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.tx.DsTxEventListenerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceCreatorAutoConfiguration.DruidDataSourceCreatorConfiguration matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.HikariDataSourceCreatorConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnWarDeployment the application is not deployed as a WAR file. (OnWarDeploymentCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   EndpointAutoConfiguration#endpointCachingOperationInvokerAdvisor matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoker.cache.CachingOperationInvokerAdvisor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#endpointOperationParameterMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoke.ParameterValueMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#propertiesEndpointAccessResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.EndpointAccessResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) found bean 'environmentEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnBooleanProperty (server.error.whitelabel.enabled=true) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)

   GsonAutoConfiguration#gson matched:
      - @ConditionalOnMissingBean (types: com.google.gson.Gson; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonAutoConfiguration#gsonBuilder matched:
      - @ConditionalOnMissingBean (types: com.google.gson.GsonBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonHttpMessageConvertersConfiguration matched:
      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)

   HealthContributorAutoConfiguration#pingHealthContributor matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   HealthEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HealthEndpointConfiguration#healthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpointGroupMembershipValidator matched:
      - @ConditionalOnBooleanProperty (management.endpoint.health.validate-group-membership=true) matched (OnPropertyCondition)

   HealthEndpointConfiguration#healthEndpointGroups matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointGroups; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthHttpCodeStatusMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HttpCodeStatusMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthStatusAggregator matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.StatusAggregator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration#healthEndpointWebExtension matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration.MvcAdditionalHealthEndpointPathsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   HttpClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.client.ClientHttpRequestFactory' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (NotReactiveWebApplicationCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder<?>; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactorySettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactorySettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientObservationsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.observation.Observation' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   HttpClientObservationsAutoConfiguration.MeterFilterConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (server.servlet.encoding.enabled=true) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpExchangesEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   InfoEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   InfoEndpointAutoConfiguration#infoEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.info.InfoEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonEndpointAutoConfiguration#endpointObjectMapper matched:
      - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.ObjectMapper', 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)
      - @ConditionalOnBooleanProperty (management.endpoints.jackson.isolated-object-mapper=true) matched (OnPropertyCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnPreferredJsonMapper JACKSON no property was configured and Jackson is the default (OnPreferredJsonMapperCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcClientAutoConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate; SearchStrategy: all) found a single bean 'namedParameterJdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.simple.JdbcClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   JvmMetricsAutoConfiguration#classLoaderMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmCompilationMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmCompilationMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmGcMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmGcMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmHeapPressureMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmHeapPressureMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmInfoMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmInfoMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmMemoryMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmThreadMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:
      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   LogFileWebEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   LogFileWebEndpointAutoConfiguration#logFileWebEndpoint matched:
      - Log File found logging.file.name ./logs/ym-server.log (LogFileWebEndpointAutoConfiguration.LogFileCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LogFileWebEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#diffItemsToLogContentService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.starter.diff.IDiffItemsToLogContentService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#functionService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.IFunctionService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#logRecordPerformanceMonitor matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.ILogRecordPerformanceMonitor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#operatorGetService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.IOperatorGetService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogRecordProxyAutoConfiguration#recordService matched:
      - @ConditionalOnMissingBean (types: com.mzt.logapi.service.ILogRecordService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogbackMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'ch.qos.logback.classic.LoggerContext', 'org.slf4j.LoggerFactory' (OnClassCondition)
      - LogbackLoggingCondition ILoggerFactory is a Logback LoggerContext (LogbackMetricsAutoConfiguration.LogbackLoggingCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   LogbackMetricsAutoConfiguration#logbackMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.logging.LogbackMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LoggersEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   LoggersEndpointAutoConfiguration#loggersEndpoint matched:
      - Logging System enabled (LoggersEndpointAutoConfiguration.OnEnabledLoggingSystemCondition)
      - @ConditionalOnBean (types: org.springframework.boot.logging.LoggingSystem; SearchStrategy: all) found bean 'springBootLoggingSystem'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LoggersEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ManagementContextAutoConfiguration.SameManagementContextConfiguration matched:
      - Management Port actual port type (SAME) matched required type (OnManagementPortCondition)

   MappingsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration.SpringMvcConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   MetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)

   MetricsAutoConfiguration#micrometerClock matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MetricsEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   MetricsEndpointAutoConfiguration#metricsEndpoint matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.MetricsEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'jakarta.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (spring.servlet.multipart.enabled=true) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: jakarta.servlet.MultipartConfigElement; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipleOpenApiSupportConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 2 matched 1 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnListGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi' (MultipleOpenApiSupportCondition)

   MultipleOpenApiSupportConfiguration#multipleOpenApiResource matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   MybatisPlusAutoConfiguration#mybatisPlusSpringApplicationContextAware matched:
      - @ConditionalOnMissingBean (types: com.baomidou.mybatisplus.extension.spring.MybatisPlusApplicationContextAware; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration#mpjInterceptor matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusJoinAutoConfiguration#mpjInterceptorConfig matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusJoinAutoConfiguration#mpjSqlInjectorOnMiss matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnMissingBean (types: com.baomidou.mybatisplus.core.injector.ISqlInjector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration.MPJMappingConfig matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusJoinAutoConfiguration.MPJSpringContext matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NamedParameterJdbcTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a single bean 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NettyAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.netty.util.NettyRuntime' (OnClassCondition)

   NoticeClientConfiguration#primaryNoticeClient matched:
      - @ConditionalOnMissingBean (names: primaryNoticeClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.observation.ObservationRegistry' (OnClassCondition)

   ObservationAutoConfiguration#observationRegistry matched:
      - @ConditionalOnMissingBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.observation.MeterObservationHandler; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration.OnlyMetricsMeterObservationHandlerConfiguration matched:
      - @ConditionalOnMissingBean (types: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.OnlyMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry'; @ConditionalOnMissingClass did not find unwanted class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnBooleanProperty (spring.dao.exceptiontranslation.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.prometheusmetrics.PrometheusMeterRegistry' (OnClassCondition)
      - @ConditionalOnEnabledMetricsExport management.defaults.metrics.export.enabled is considered true (OnMetricsExportEnabledCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) found bean 'micrometerClock' (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusConfig matched:
      - @ConditionalOnMissingBean (types: io.micrometer.prometheusmetrics.PrometheusConfig; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusMeterRegistry matched:
      - @ConditionalOnMissingBean (types: io.micrometer.prometheusmetrics.PrometheusMeterRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusRegistry matched:
      - @ConditionalOnMissingBean (types: io.prometheus.metrics.model.registry.PrometheusRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusScrapeEndpointConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusScrapeEndpointConfiguration#prometheusEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.export.prometheus.PrometheusScrapeEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ReactiveHealthEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   ReactiveHealthEndpointConfiguration#reactiveHealthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.ReactiveHealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ReactiveSecurityAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'reactor.core.publisher.Flux', 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity', 'org.springframework.security.web.server.WebFilterChainProxy', 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Hooks' (OnClassCondition)

   ReactorNettyConfigurations.ReactorResourceFactoryConfiguration#reactorResourceFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.client.ReactorResourceFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisAutoConfiguration#redisConnectionDetails matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.data.redis.RedisConnectionDetails; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisCacheConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - Cache org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration REDIS cache type (CacheCondition)

   RedisHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory' (OnBeanCondition)

   RedisReactiveAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'org.springframework.data.redis.core.ReactiveRedisTemplate', 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisReactiveAutoConfiguration#reactiveRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (names: reactiveRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveAutoConfiguration#reactiveStringRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (names: reactiveStringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory' (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration#redisHealthContributor matched:
      - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redisson matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonConnectionFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonReactive matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonReactiveClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonRxJava matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonRxClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#stringRedisTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfigurationV2 matched:
      - @ConditionalOnClass found required classes 'org.redisson.Redisson', 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RepositoryMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.repository.Repository' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#metricsRepositoryMethodInvocationListener matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.MetricsRepositoryMethodInvocationListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#repositoryTagsProvider matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.RepositoryTagsProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.VirtualThreadsExecutorEnabled found non-matching nested conditions @ConditionalOnThreading did not find VIRTUAL; NestedCondition on NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.NotReactiveWebApplication NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition)

   RestClientAutoConfiguration#httpMessageConvertersRestClientCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found bean 'restClientBuilder' (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication not a reactive web application (NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) found bean 'restTemplateBuilder' (OnBeanCondition)

   SbomEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   SbomEndpointAutoConfiguration#sbomEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.sbom.SbomEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SbomEndpointAutoConfiguration#sbomEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.sbom.SbomEndpoint; SearchStrategy: all) found bean 'sbomEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.sbom.SbomEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration#scheduledTasksEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.scheduling.ScheduledTasksEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ScheduledTasksObservabilityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   SecurityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityAutoConfiguration#authenticationEventPublisher matched:
      - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationEventPublisher; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SecurityFilterAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer', 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityFilterAutoConfiguration#securityFilterChainRegistration matched:
      - @ConditionalOnBean (names: springSecurityFilterChain; SearchStrategy: all) found bean 'springSecurityFilterChain' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath; SearchStrategy: all) found bean 'dispatcherServletRegistration' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration#requestMatcherProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.security.servlet.RequestMatcherProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletEndpointManagementContextConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   ServletEndpointManagementContextConfiguration.WebMvcServletEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)

   ServletManagementContextAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.Servlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SpaceWebSocketAutoConfiguration matched:
      - @ConditionalOnProperty (ym.space.websocket.enable) matched (OnPropertyCondition)

   SpaceWebSocketAutoConfiguration.RedisWebSocketMessageSenderConfiguration matched:
      - @ConditionalOnProperty (ym.space.websocket.sender-type=redis) matched (OnPropertyCondition)

   SpringBootAdminClientAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - matched (SpringBootAdminClientEnabledCondition)

   SpringBootAdminClientAutoConfiguration#registrationListener matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.RegistrationApplicationListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration#registrator matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.ApplicationRegistrator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration#startupDateMetadataContributor matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.metadata.StartupDateMetadataContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.BlockingRegistrationClientConfig matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) found bean 'restTemplateBuilder' (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.RestClientRegistrationClientConfig matched:
      - @ConditionalOnBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found bean 'restClientBuilder' (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.RestClientRegistrationClientConfig#registrationClient matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.RegistrationClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.ServletConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   SpringBootAdminClientAutoConfiguration.ServletConfiguration#applicationFactory matched:
      - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.ApplicationFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootWebSecurityConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   SpringBootWebSecurityConfiguration.WebSecurityEnablerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)
      - @ConditionalOnMissingBean (names: springSecurityFilterChain; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#pageableCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#sortCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#springDataWebSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SpringDataWebSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfigProperties matched:
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   SpringDocConfiguration#fileSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.FileSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#globalOpenApiCustomizer matched:
      - @ConditionalOnMissingBean (names: globalOpenApiCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#initExtraSchemas matched:
      - @ConditionalOnProperty (springdoc.enable-extra-schemas) matched (OnPropertyCondition)

   SpringDocConfiguration#operationBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.OperationService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#parameterBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.GenericParameterService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#parameterObjectNamingStrategyCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.ParameterObjectNamingStrategyCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#polymorphicModelConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.polymorphic-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PolymorphicModelConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#requestBodyBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.RequestBodyService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#responseSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.ResponseSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#schemaPropertyDeprecatingConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.deprecating-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SchemaPropertyDeprecatingConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#securityParser matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.SecurityService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.SpringDocCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocProviders matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDocProviders; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on CacheOrGroupedOpenApiCondition.OnCacheDisabled found non-matching nested conditions @ConditionalOnProperty (springdoc.cache.disabled) did not find property 'springdoc.cache.disabled', @ConditionalOnMissingBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans of type 'org.springdoc.core.models.GroupedOpenApi' systemGroupedOpenApi, infraGroupedOpenApi, spaceGroupedOpenApi, logGroupedOpenApi, smsGroupedOpenApi, accountGroupedOpenApi, tenantGroupedOpenApi, fileGroupedOpenApi, marketGroupedOpenApi, authGroupedOpenApi, socialGroupedOpenApi, rightsGroupedOpenApi, allGroupedOpenApi; NestedCondition on CacheOrGroupedOpenApiCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 1 matched 1 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 2 matched 1 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnListGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) found beans 'systemGroupedOpenApi', 'tenantGroupedOpenApi', 'infraGroupedOpenApi', 'accountGroupedOpenApi', 'logGroupedOpenApi', 'authGroupedOpenApi', 'smsGroupedOpenApi', 'allGroupedOpenApi', 'socialGroupedOpenApi', 'rightsGroupedOpenApi', 'spaceGroupedOpenApi', 'marketGroupedOpenApi', 'fileGroupedOpenApi' (CacheOrGroupedOpenApiCondition)

   SpringDocConfiguration#springdocObjectMapperProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.ObjectMapperProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.QuerydslProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.data.querydsl.binding.QuerydslBindingsFactory' (OnClassCondition)

   SpringDocConfiguration.QuerydslProvider#queryDslQuerydslPredicateOperationCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.QuerydslPredicateOperationCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider#springDataWebPropertiesProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDataWebPropertiesProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration#webFluxSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.WebFluxSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.WebConversionServiceConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.web.format.WebConversionService' (OnClassCondition)

   SpringDocGroovyConfiguration matched:
      - @ConditionalOnClass found required class 'groovy.lang.MetaClass' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true} and ${springdoc.enable-groovy:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocPageableConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Pageable' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocPageableConfiguration#dataRestDelegatingMethodParameterCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DataRestDelegatingMethodParameterCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration#pageOpenAPIConverter matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PagedModel', 'org.springframework.data.web.config.SpringDataWebSettings' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration#pageableOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.pageable-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageableOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocSecurityConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.SecurityFilterChain' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true} and ${springdoc.enable-spring-security:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocSecurityConfiguration.SpringSecurityLoginEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.Filter' (OnClassCondition)

   SpringDocSortConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Sort' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocSortConfiguration#sortOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.sort-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SortOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocWebMvcConfiguration#openApiResource matched:
      - @ConditionalOnExpression (#{(${springdoc.use-management-port:false} == false ) and ${springdoc.enable-default-api-docs:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.api.OpenApiWebMvcResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#requestBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.service.RequestService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#responseBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.GenericResponseService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#springWebProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringWebProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.function.RouterFunction' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration#routerFunctionProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.providers.RouterFunctionWebMvcProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringNativeClientAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - matched (SpringBootAdminClientEnabledCondition)

   SpringNativeServerAutoConfiguration matched:
      - matched (SpringBootAdminServerEnabledCondition)
      - @ConditionalOnBean (types: de.codecentric.boot.admin.server.config.AdminServerMarkerConfiguration$Marker; SearchStrategy: all) found bean 'adminServerMarker' (OnBeanCondition)

   SqlInitializationAutoConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.sql.init.enabled=true) matched (OnPropertyCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'spring.sql.init.mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)

   SslAutoConfiguration#sslBundleRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.ssl.SslBundleRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   SslHealthContributorAutoConfiguration#sslHealthIndicator matched:
      - @ConditionalOnMissingBean (names: sslHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslHealthContributorAutoConfiguration#sslInfo matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslObservabilityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'sslBundleRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration#startupTimeMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.startup.StartupTimeMetricsListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   SystemMetricsAutoConfiguration#diskSpaceMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.system.DiskSpaceMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#fileDescriptorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.FileDescriptorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#processorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.ProcessorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#uptimeMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.UptimeMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutorConfigurations.AsyncConfigurerConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.scheduling.annotation.AsyncConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.AsyncConfigurerConfiguration#applicationTaskExecutorAsyncConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.scheduling.annotation.AsyncConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration matched:
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on TaskExecutorConfigurations.OnExecutorCondition.ModelCondition @ConditionalOnProperty (spring.task.execution.mode=force) did not find property 'spring.task.execution.mode'; NestedCondition on TaskExecutorConfigurations.OnExecutorCondition.ExecutorBeanCondition @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (TaskExecutorConfigurations.OnExecutorCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.ThreadPoolTaskExecutorBuilderConfiguration#threadPoolTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics' (OnClassCondition)
      - @ConditionalOnBean (types: java.util.concurrent.Executor,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'applicationTaskExecutor', 'taskScheduler' (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor' (OnBeanCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'; @ConditionalOnMissingBean (types: org.springframework.scheduling.TaskScheduler,java.util.concurrent.ScheduledExecutorService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration#taskScheduler matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.ThreadPoolTaskSchedulerBuilderConfiguration#threadPoolTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TemplateEngineConfigurations.DefaultTemplateEngineConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring6.ISpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThreadDumpEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ThreadDumpEndpointAutoConfiguration#dumpEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.management.ThreadDumpEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.thymeleaf.templatemode.TemplateMode', 'org.thymeleaf.spring6.SpringTemplateEngine' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (spring.thymeleaf.enabled=true) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.view.AbstractCachingViewResolver' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TomcatMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.binder.tomcat.TomcatMetrics', 'org.apache.catalina.Manager' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   TomcatMetricsAutoConfiguration#tomcatMetricsBinder matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.tomcat.TomcatMetrics,org.springframework.boot.actuate.metrics.web.tomcat.TomcatMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransServiceConfig#restTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransServiceConfig#rpcTransService matched:
      - @ConditionalOnBean (types: com.fhs.trans.service.impl.SimpleTransService$SimpleTransDiver; SearchStrategy: all) found bean 'MybatisPlusSimpleTransDiver' (OnBeanCondition)

   TransServiceConfig#simpleTransService matched:
      - @ConditionalOnBean (types: com.fhs.trans.service.impl.SimpleTransService$SimpleTransDiver; SearchStrategy: all) found bean 'MybatisPlusSimpleTransDiver' (OnBeanCondition)

   TransServiceConfig#transProxyController matched:
      - @ConditionalOnBean (types: com.fhs.trans.service.impl.SimpleTransService$SimpleTransDiver; SearchStrategy: all) found bean 'MybatisPlusSimpleTransDiver' (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a single bean 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionManagerCustomizationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionManagerCustomizationAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/jakarta.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: jakarta.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   WebClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebClientAutoConfiguration#webClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.reactive.function.client.WebClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebClientAutoConfiguration#webClientHttpConnectorCustomizer matched:
      - @ConditionalOnBean (types: org.springframework.http.client.reactive.ClientHttpConnector; SearchStrategy: all) found bean 'clientHttpConnector' (OnBeanCondition)

   WebClientAutoConfiguration#webClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebClientAutoConfiguration.WebClientCodecsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.codec.CodecCustomizer; SearchStrategy: all) found beans 'jacksonCodecCustomizer', 'defaultCodecCustomizer' (OnBeanCondition)

   WebClientAutoConfiguration.WebClientCodecsConfiguration#exchangeStrategiesCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientCodecCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebClientObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebEndpointAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration#controllerEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#endpointMediaTypes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.EndpointMediaTypes; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#pathMappedEndpoints matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.PathMappedEndpoints; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#webEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration#servletEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnBooleanProperty (spring.mvc.formcontent.filter.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:
      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:
      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:
      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#viewNameTranslator matched:
      - @ConditionalOnMissingBean (names: viewNameTranslator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet,org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) found beans 'webEndpointDiscoverer', 'dispatcherServlet' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#controllerEndpointHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.ControllerEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#endpointObjectMapperWebMvcConfigurer matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.endpoint.jackson.EndpointObjectMapper; SearchStrategy: all) found bean 'endpointObjectMapper' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#webEndpointServletHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcObservationAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.web.servlet.DispatcherServlet', 'io.micrometer.observation.Observation' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   WebMvcObservationAutoConfiguration#webMvcObservationFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.ServerHttpObservationFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcObservationAutoConfiguration.MeterFilterConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   WebSocketMessagingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMaInRedisTemplateConfigStorageConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.StringRedisTemplate' (OnClassCondition)
      - @ConditionalOnProperty (wx.miniapp.config-storage.type=redistemplate) matched (OnPropertyCondition)

   WxMaInRedisTemplateConfigStorageConfiguration#wxMaConfig matched:
      - @ConditionalOnMissingBean (types: cn.binarywang.wx.miniapp.config.WxMaConfig; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMaServiceAutoConfiguration#wxMaService matched:
      - @ConditionalOnBean (types: cn.binarywang.wx.miniapp.config.WxMaConfig; SearchStrategy: all) found bean 'wxMaConfig'; @ConditionalOnMissingBean (types: cn.binarywang.wx.miniapp.api.WxMaService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMpServiceAutoConfiguration#wxMpService matched:
      - @ConditionalOnMissingBean (types: me.chanjar.weixin.mp.api.WxMpService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WxMpStorageAutoConfiguration#wxMpConfigStorage matched:
      - @ConditionalOnMissingBean (types: me.chanjar.weixin.mp.config.WxMpConfigStorage; SearchStrategy: all) did not find any beans (OnBeanCondition)

   YmDataSourceAutoConfiguration#druidAdRemoveFilterFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   YmDeptDataPermissionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fozmo.ym.framework.security.core.LoginUser' (OnClassCondition)
      - @ConditionalOnBean (types: com.fozmo.ym.module.auth.api.permission.PermissionApi,com.fozmo.ym.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer; SearchStrategy: all) found beans 'sysDeptDataPermissionRuleCustomizer', 'permissionApiImpl' (OnBeanCondition)

   YmMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer' (OnClassCondition)
      - @ConditionalOnProperty (ym.metrics.enable) matched (OnPropertyCondition)

   YmRedisMQConsumerAutoConfiguration#redisMessageListenerContainer matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.pubsub.AbstractRedisChannelMessageListener; SearchStrategy: all) found bean 'spaceRedisWebSocketMessageConsumer' (OnBeanCondition)

   YmRedisMQConsumerAutoConfiguration#redisPendingMessageResendJob matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener; SearchStrategy: all) found beans 'worksDeleteConsumer', 'spaceDeleteConsumer', 'worksCommentConsumer', 'spaceCommentConsumer', 'socialUserConsumer', 'smsSendConsumer', 'noticeConsumer' (OnBeanCondition)

   YmRedisMQConsumerAutoConfiguration#redisStreamMessageCleanupJob matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener; SearchStrategy: all) found beans 'worksDeleteConsumer', 'spaceDeleteConsumer', 'worksCommentConsumer', 'spaceCommentConsumer', 'socialUserConsumer', 'smsSendConsumer', 'noticeConsumer' (OnBeanCondition)

   YmRedisMQConsumerAutoConfiguration#redisStreamMessageListenerContainer matched:
      - @ConditionalOnBean (types: com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener; SearchStrategy: all) found beans 'worksDeleteConsumer', 'spaceDeleteConsumer', 'worksCommentConsumer', 'spaceCommentConsumer', 'socialUserConsumer', 'smsSendConsumer', 'noticeConsumer' (OnBeanCondition)

   YmSwaggerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.swagger.v3.oas.models.OpenAPI' (OnClassCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled=true) matched (OnPropertyCondition)

   YmTenantAutoConfiguration matched:
      - @ConditionalOnProperty (ym.tenant.enable) matched (OnPropertyCondition)

   YmTracerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fozmo.ym.framework.tracer.core.aop.BizTraceAspect' (OnClassCondition)
      - @ConditionalOnProperty (ym.tracer.enable) matched (OnPropertyCondition)

   YmXxlJobAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.xxl.job.core.executor.impl.XxlJobSpringExecutor' (OnClassCondition)
      - @ConditionalOnProperty (xxl.job.enabled=true) matched (OnPropertyCondition)

   YmXxlJobAutoConfiguration#xxlJobExecutor matched:
      - @ConditionalOnMissingBean (types: com.xxl.job.core.executor.XxlJobExecutor; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AdminServerCloudFoundryAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)

   AdminServerHazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'com.hazelcast.core.HazelcastInstance' (OnBeanCondition)

   AdminServerInstanceWebClientConfiguration.InstanceExchangeFiltersConfiguration.DefaultInstanceExchangeFiltersConfiguration#addReactiveHeadersInstanceExchangeFilter:
      Did not match:
         - @ConditionalOnBean (types: de.codecentric.boot.admin.server.web.client.reactive.ReactiveHttpHeadersProvider; SearchStrategy: all) did not find any beans of type de.codecentric.boot.admin.server.web.client.reactive.ReactiveHttpHeadersProvider (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.CompositeNotifierConfiguration:
      Did not match:
         - @ConditionalOnBean (types: de.codecentric.boot.admin.server.notify.Notifier; SearchStrategy: all) did not find any beans of type de.codecentric.boot.admin.server.notify.Notifier (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.DingTalkNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.dingtalk.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.DiscordNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.discord.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.FeiShuNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.feishu.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.FilteringNotifierWebConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate (types: de.codecentric.boot.admin.server.notify.filter.FilteringNotifier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.HipchatNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.hipchat.url) did not find property 'url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.LetsChatNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.letschat.url) did not find property 'url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.MailNotifierConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.mail.MailSender; SearchStrategy: all) did not find any beans of type org.springframework.mail.MailSender (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.MicrosoftTeamsNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.ms-teams.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.NotifierTriggerConfiguration:
      Did not match:
         - @ConditionalOnBean (types: de.codecentric.boot.admin.server.notify.Notifier; SearchStrategy: all) did not find any beans of type de.codecentric.boot.admin.server.notify.Notifier (OnBeanCondition)

   AdminServerNotifierAutoConfiguration.OpsGenieNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.opsgenie.api-key) did not find property 'api-key' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.PagerdutyNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.pagerduty.service-key) did not find property 'service-key' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.RocketChatNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.rocketchat.url) did not find property 'url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.SlackNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.slack.webhook-url) did not find property 'webhook-url' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.TelegramNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.telegram.auth-token) did not find property 'auth-token' (OnPropertyCondition)

   AdminServerNotifierAutoConfiguration.WebexNotifierConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.boot.admin.notify.webex.auth-token) did not find property 'auth-token' (OnPropertyCondition)

   AdminServerUiAutoConfiguration.ReactiveUiConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   AdminServerWebConfiguration.ReactiveRestApiConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   AjCaptchaStorageAutoConfiguration#captchaCacheService:
      Did not match:
         - @ConditionalOnMissingBean (types: com.anji.captcha.service.CaptchaCacheService; SearchStrategy: all) found beans of type 'com.anji.captcha.service.CaptchaCacheService' AjCaptchaCacheService (OnBeanCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=false) did not find property 'spring.aop.proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AppOpticsMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.appoptics.AppOpticsMeterRegistry' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AtlasMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.atlas.AtlasMeterRegistry' (OnClassCondition)

   AuditAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnBooleanProperty (management.auditevents.enabled=true) matched (OnPropertyCondition)

   AuditEventsEndpointAutoConfiguration#auditEventsEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)

   AvailabilityHealthContributorAutoConfiguration#livenessStateHealthIndicator:
      Did not match:
         - @ConditionalOnBooleanProperty (management.health.livenessstate.enabled=true) did not find property 'management.health.livenessstate.enabled' (OnPropertyCondition)

   AvailabilityHealthContributorAutoConfiguration#readinessStateHealthIndicator:
      Did not match:
         - @ConditionalOnBooleanProperty (management.health.readinessstate.enabled=true) did not find property 'management.health.readinessstate.enabled' (OnPropertyCondition)

   AvailabilityProbesAutoConfiguration:
      Did not match:
         - Probes availability not running on a supported cloud platform (AvailabilityProbesAutoConfiguration.ProbesCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   BatchObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.configuration.annotation.BatchObservabilityBeanPostProcessor' (OnClassCondition)

   BraveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'brave.Tracer' (OnClassCondition)

   Cache2kCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.cache2k.Cache2kBuilder' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (names: cacheResolver types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans of type 'org.springframework.cache.CacheManager' redisCacheManager, tenantRedisCacheManager (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CacheMeterBinderProvidersConfiguration.Cache2kCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.cache2k.Cache2kBuilder', 'org.cache2k.extra.spring.SpringCache2kCache', 'org.cache2k.extra.micrometer.Cache2kCacheMetrics' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.HazelcastCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.spring.cache.HazelcastCache', 'com.hazelcast.core.Hazelcast' (OnClassCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.CaffeineCacheConfiguration unknown cache type (CacheCondition)
      Matched:
         - @ConditionalOnClass found required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CloudFoundryActuatorAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnBooleanProperty (management.cloudfoundry.enabled=true) matched (OnPropertyCondition)

   CompositeMeterRegistryConfiguration:
      Did not match:
         - NoneNestedConditions 1 matched 1 did not; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.SingleInjectableMeterRegistry @ConditionalOnSingleCandidate (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found a single bean 'prometheusMeterRegistry'; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.NoMeterRegistryCondition @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition)

   ConnectionFactoryHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   ConnectionPoolMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.pool.ConnectionPool' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceCheckpointRestoreConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.crac.Resource' (OnClassCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceConfiguration.OracleUcp:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DatadogMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.datadog.DatadogMeterRegistry' (OnClassCondition)

   DdlAutoConfiguration#ddlApplicationRunner:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.mybatisplus.extension.ddl.IDdl; SearchStrategy: all) did not find any beans of type com.baomidou.mybatisplus.extension.ddl.IDdl (OnBeanCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidDataSourceAutoConfigure#dataSource:
      Did not match:
         - @ConditionalOnMissingBean (types: com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceWrapper,com.alibaba.druid.pool.DruidDataSource,javax.sql.DataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   DynamicDataSourceCreatorAutoConfiguration.AtomikosDataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.atomikos.jdbc.AtomikosDataSourceBean' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.BeeCpDataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'cn.beecp.BeeDataSource' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.Dbcp2DataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DynatraceMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.dynatrace.DynatraceMeterRegistry' (OnClassCondition)

   EasyTransMybatisPlusConfig#mybatisPlusTransableRegister:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-auto=true) did not find property 'easy-trans.is-enable-auto' (OnPropertyCondition)

   EasyTransResponseBodyAdvice:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-global=true) found different value in property 'easy-trans.is-enable-global' (OnPropertyCondition)

   ElasticMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.elastic.ElasticMeterRegistry' (OnClassCondition)

   ElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.elasticsearch.ElasticsearchClient' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate' (OnClassCondition)

   ElasticsearchReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.repository.ElasticsearchRepository' (OnClassCondition)

   ElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClientBuilder' (OnClassCondition)

   ElasticsearchRestHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration#tomcatVirtualThreadsProtocolHandlerCustomizer:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FlywayEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GangliaMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.ganglia.GangliaMeterRegistry' (OnClassCondition)

   GenericCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration unknown cache type (CacheCondition)

   GraphQlAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlRSocketAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphiteMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.graphite.GraphiteMeterRegistry' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration.GsonHttpMessageConverterConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.JacksonJsonbUnavailable NoneNestedConditions 1 matched 1 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JsonbPreferred @ConditionalOnPreferredJsonMapper JSONB no property was configured and Jackson is the default; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JacksonAvailable @ConditionalOnBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) found bean 'mappingJackson2HttpMessageConverter'; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.GsonPreferred @ConditionalOnPreferredJsonMapper GSON no property was configured and Jackson is the default (GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.JakartaWebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HealthEndpointReactiveWebExtensionConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   HealthEndpointWebExtensionConfiguration.JerseyAdditionalHealthEndpointPathsConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   HeapDumpWebEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnAvailableEndpoint the configured access for endpoint 'heapdump' is NONE (OnAvailableEndpointCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.persistence.EntityManager' (OnClassCondition)

   HibernateMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.persistence.EntityManagerFactory' (OnClassCondition)

   HttpExchangesAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnBooleanProperty (management.httpexchanges.recording.enabled=true) matched (OnPropertyCondition)

   HttpExchangesAutoConfiguration.ReactiveHttpExchangesConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
         - Ancestor org.springframework.boot.actuate.autoconfigure.web.exchanges.HttpExchangesAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   HttpExchangesAutoConfiguration.ServletHttpExchangesConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.actuate.autoconfigure.web.exchanges.HttpExchangesAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   HttpExchangesEndpointAutoConfiguration#httpExchangesEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository (OnBeanCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.web.reactive.DispatcherHandler', 'org.springframework.http.server.reactive.HttpHandler' (OnClassCondition)

   HumioMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.humio.HumioMeterRegistry' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   I18nAspect:
      Did not match:
         - @ConditionalOnProperty (aj.captcha.i18n.enabled) did not find property 'aj.captcha.i18n.enabled' (OnPropertyCondition)

   IdentifierGeneratorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.commons.util.InetUtils' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.influx.InfluxMeterRegistry' (OnClassCondition)

   InfoContributorAutoConfiguration#buildInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.BuildProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#envInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.env.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#gitInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.GitProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#javaInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.java.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#osInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.os.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#processInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.process.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#sslInfo:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.ssl.enabled is not true (OnEnabledInfoContributorCondition)
      Matched:
         - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) did not find any beans (OnBeanCondition)

   InfoContributorAutoConfiguration#sslInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.ssl.enabled is not true (OnEnabledInfoContributorCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   IntegrationGraphEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.graph.IntegrationGraphServer' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.JCacheCacheConfiguration unknown cache type (CacheCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JedisConnectionConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found beans of type 'org.springframework.data.redis.connection.RedisConnectionFactory' redissonConnectionFactory (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.apache.commons.pool2.impl.GenericObjectPool', 'org.springframework.data.redis.connection.jedis.JedisConnection', 'redis.clients.jedis.Jedis' (OnClassCondition)
         - @ConditionalOnProperty (spring.data.redis.client-type=jedis) matched (OnPropertyCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JerseySameManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JerseyServerMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.micrometer.server.ObservationApplicationEventListener' (OnClassCondition)

   JerseyWebEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JettyMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.server.Server' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.Message' (OnClassCondition)

   JmsHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   JmxAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)

   JmxEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)

   JmxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.jmx.JmxMeterRegistry' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'spring.datasource.jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.transaction.Transaction' (OnClassCondition)

   JvmMetricsAutoConfiguration#virtualThreadMetrics:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.java21.instrument.binder.jdk.VirtualThreadMetrics' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   KafkaMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.ProducerFactory' (OnClassCondition)

   KairosMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.kairos.KairosMeterRegistry' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.LdapOperations' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LettuceConnectionConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.lettuce.core.RedisClient' (OnClassCondition)

   LettuceMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.lettuce.core.metrics.MicrometerCommandLatencyRecorder' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   LiquibaseEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.integration.spring.SpringLiquibase' (OnClassCondition)

   Log4J2MetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.logging.log4j.core.LoggerContext' (OnClassCondition)

   LogRecordProxyAutoConfiguration#parseFunction:
      Did not match:
         - @ConditionalOnMissingBean (types: com.mzt.logapi.service.IParseFunction; SearchStrategy: all) found beans of type 'com.mzt.logapi.service.IParseFunction' adminUserParseFunction, areaParseFunction, booleanParseFunction, deptParseFunction, postParseFunction, sexParseFunction (OnBeanCondition)

   MailHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.mail.javamail.JavaMailSenderImpl; SearchStrategy: all) did not find any beans of type org.springframework.mail.javamail.JavaMailSenderImpl (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnClassCondition)
         - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on MailSenderAutoConfiguration.MailSenderCondition.JndiNameProperty @ConditionalOnProperty (spring.mail.jndi-name) did not find property 'spring.mail.jndi-name'; NestedCondition on MailSenderAutoConfiguration.MailSenderCondition.HostProperty @ConditionalOnProperty (spring.mail.host) did not find property 'spring.mail.host' (MailSenderAutoConfiguration.MailSenderCondition)
      Matched:
         - @ConditionalOnClass found required classes 'jakarta.mail.internet.MimeMessage', 'jakarta.activation.MimeType', 'org.springframework.mail.MailSender' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mail.test-connection=true) did not find property 'spring.mail.test-connection' (OnPropertyCondition)

   ManagementContextAutoConfiguration.DifferentManagementContextConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   ManagementWebSecurityAutoConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' filterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ReactiveWebConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MetricsAspectsAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.observations.annotations.enabled=true) did not find property 'management.observations.annotations.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'org.aspectj.weaver.Advice' (OnClassCondition)

   MicrometerTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.MongoTemplate' (OnClassCondition)

   MongoMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClientSettings' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.ReactiveMongoTemplate' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MultipleOpenApiSupportConfiguration.SpringDocWebMvcActuatorDifferentConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisPlusAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) found beans of type 'org.mybatis.spring.mapper.MapperScannerConfigurer' com.fozmo.ym.framework.mybatis.config.YmMybatisAutoConfiguration#MapperScannerRegistrar#0 (OnBeanCondition)

   MybatisPlusInnerInterceptorAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor; SearchStrategy: all) did not find any beans of type com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor (OnBeanCondition)

   MybatisPlusJoinAutoConfiguration#mpjSqlInjector:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.mybatisplus.core.injector.ISqlInjector; SearchStrategy: all) did not find any beans of type com.baomidou.mybatisplus.core.injector.ISqlInjector (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   NewRelicMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.newrelic.NewRelicMeterRegistry' (OnClassCondition)

   NoOpCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration unknown cache type (CacheCondition)

   NoOpMeterRegistryConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (OnBeanCondition)

   NoopTracerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   OAuth2AuthorizationServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2AuthorizationServerJwtAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.nimbusds.jose.jwk.source.JWKSource' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   OAuth2ClientWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.web.OAuth2AuthorizedClientRepository' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken' (OnClassCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration.TracingAndMetricsObservationHandlerConfiguration:
      Did not match:
         - @ConditionalOnBean did not find required type 'io.micrometer.tracing.Tracer' (OnBeanCondition)
         - @ConditionalOnBean (types: ?; SearchStrategy: all) did not find any beans of type ? (OnBeanCondition)

   ObservationAutoConfiguration.MetricsWithTracingConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   ObservationAutoConfiguration.ObservedAspectConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.observations.annotations.enabled=true) did not find property 'management.observations.annotations.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   ObservationAutoConfiguration.OnlyTracingConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   OpenTelemetryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.sdk.OpenTelemetrySdk' (OnClassCondition)

   OpenTelemetryLoggingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.sdk.logs.SdkLoggerProvider' (OnClassCondition)

   OpenTelemetryTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.otel.bridge.OtelTracer' (OnClassCondition)

   OtlpLoggingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.exporter.otlp.http.logs.OtlpHttpLogRecordExporter' (OnClassCondition)

   OtlpMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.registry.otlp.OtlpMeterRegistry' (OnClassCondition)

   OtlpTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.otel.bridge.OtelTracer' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   PrometheusExemplarsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusPushGatewayConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.prometheus.metrics.exporter.pushgateway.PushGateway' (OnClassCondition)

   PulsarAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   PulsarReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   QuartzEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   R2dbcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)

   R2dbcInitializationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)

   R2dbcObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcProxyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcTransactionManagerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)

   RSocketGraphQlClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   RabbitHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   RabbitMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.ConnectionFactory' (OnClassCondition)

   ReactiveCloudFoundryActuatorAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ReactiveElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.transport.ElasticsearchTransport' (OnClassCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient' (OnClassCondition)

   ReactiveManagementContextAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveManagementWebSecurityAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity', 'org.springframework.security.web.server.WebFilterChainProxy' (OnClassCondition)

   ReactiveMultipartAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.http.codec.multipart.DefaultPartHttpMessageReader', 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   ReactiveOAuth2ClientWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration.SpringBootWebFluxSecurityConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication.ReactiveWebApplicationCondition not a reactive web application; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication.RSocketSecurityEnabledCondition @ConditionalOnBean (types: org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler; SearchStrategy: all) did not find any beans of type org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler (ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)
         - AnyNestedCondition 1 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.PasswordConfigured @ConditionalOnProperty (spring.security.user.password) did not find property 'spring.security.user.password'; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.NameConfigured @ConditionalOnProperty (spring.security.user.name) did not find property 'spring.security.user.name'; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.MissingAlternative @ConditionalOnMissingClass did not find unwanted classes 'org.springframework.security.oauth2.client.registration.ClientRegistrationRepository', 'org.springframework.security.oauth2.server.resource.introspection.ReactiveOpaqueTokenIntrospector' (ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.http.ReactiveHttpInputMessage' (OnClassCondition)

   RedisAutoConfiguration#redisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) found beans named redisTemplate (OnBeanCondition)

   RedisAutoConfiguration#stringRedisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) found beans of type 'org.springframework.data.redis.core.StringRedisTemplate' stringRedisTemplate (OnBeanCondition)

   RedisHealthContributorAutoConfiguration#redisHealthContributor:
      Did not match:
         - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) found beans named redisHealthContributor (OnBeanCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.data.redis.repositories.enabled=true) found different value in property 'spring.data.redis.repositories.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RedissonAutoConfiguration#redisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) found beans named redisTemplate (OnBeanCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityDataConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.data.repository.query.SecurityEvaluationContextExtension' (OnClassCondition)

   SecurityRequestMatchersManagementContextConfiguration.JerseyRequestMatcherConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletEndpointManagementContextConfiguration.JerseyServletEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   ServletManagementContextAutoConfiguration.ApplicationContextFilterConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.server.add-application-context-header=true) did not find property 'management.server.add-application-context-header' (OnPropertyCondition)

   ServletWebServerFactoryAutoConfiguration.ForwardedHeaderFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SessionsEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   ShutdownEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnAvailableEndpoint the configured access for endpoint 'shutdown' is NONE (OnAvailableEndpointCondition)

   SignalFxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.signalfx.SignalFxMeterRegistry' (OnClassCondition)

   SimpleCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration unknown cache type (CacheCondition)

   SimpleMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledMetricsExport management.defaults.metrics.export.enabled is considered true (OnMetricsExportEnabledCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.application.admin.enabled=true) did not find property 'spring.application.admin.enabled' (OnPropertyCondition)

   SpringBootAdminClientAutoConfiguration.BlockingRegistrationClientConfig#registrationClient:
      Did not match:
         - @ConditionalOnMissingBean (types: de.codecentric.boot.admin.client.registration.RegistrationClient; SearchStrategy: all) found beans of type 'de.codecentric.boot.admin.client.registration.RegistrationClient' registrationClient (OnBeanCondition)

   SpringBootAdminClientAutoConfiguration.ReactiveConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   SpringBootAdminClientAutoConfiguration.ReactiveRegistrationClientConfig:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder,org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found beans of type 'org.springframework.web.client.RestClient$Builder' restClientBuilder and found beans of type 'org.springframework.boot.web.client.RestTemplateBuilder' restTemplateBuilder (OnBeanCondition)

   SpringBootAdminClientCloudFoundryAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
         - matched (SpringBootAdminClientEnabledCondition)

   SpringBootWebSecurityConfiguration.SecurityFilterChainConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' filterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)

   SpringDocConfiguration#oas31ModelConverter:
      Did not match:
         - @ConditionalOnProperty (springdoc.explicit-object-schema=true) did not find property 'springdoc.explicit-object-schema' (OnPropertyCondition)

   SpringDocConfiguration#openAPIBuilder:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.service.OpenAPIService; SearchStrategy: all) found beans of type 'org.springdoc.core.service.OpenAPIService' openApiBuilder (OnBeanCondition)

   SpringDocConfiguration#propertiesResolverForSchema:
      Did not match:
         - @ConditionalOnProperty (springdoc.api-docs.resolve-schema-properties) did not find property 'springdoc.api-docs.resolve-schema-properties' (OnPropertyCondition)

   SpringDocConfiguration#propertyCustomizingConverter:
      Did not match:
         - @ConditionalOnBean (types: org.springdoc.core.customizers.PropertyCustomizer; SearchStrategy: all) did not find any beans of type org.springdoc.core.customizers.PropertyCustomizer (OnBeanCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor2:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)

   SpringDocConfiguration.SpringDocActuatorConfiguration:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocRepositoryRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocDataRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocFunctionCatalogConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.function.web.function.FunctionEndpointInitializer' (OnClassCondition)

   SpringDocHateoasConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.server.LinkRelationProvider' (OnClassCondition)

   SpringDocJacksonKotlinModuleConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.module.kotlin.KotlinModule' (OnClassCondition)

   SpringDocJavadocConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.therapi.runtimejavadoc.CommentFormatter' (OnClassCondition)

   SpringDocKotlinConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'kotlin.coroutines.Continuation' (OnClassCondition)

   SpringDocKotlinxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'kotlinx.coroutines.flow.Flow' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringDocSecurityOAuth2ClientConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.annotation.RegisteredOAuth2AuthorizedClient' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringDocSecurityOAuth2Configuration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringSecurityLoginEndpointConfiguration#springSecurityLoginEndpointCustomizer:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-login-endpoint) did not find property 'springdoc.show-login-endpoint' (OnPropertyCondition)

   SpringDocSortConfiguration#dataRestDelegatingMethodParameterCustomizer:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DataRestDelegatingMethodParameterCustomizer; SearchStrategy: all) found beans of type 'org.springdoc.core.customizers.DataRestDelegatingMethodParameterCustomizer' dataRestDelegatingMethodParameterCustomizer (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#actuatorProvider:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.show-actuator:false} or ${springdoc.use-management-port:false}}) resulted in false (OnExpressionCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#openApiActuatorResource:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.use-management-port:false} and ${springdoc.enable-default-api-docs:true}}) resulted in false (OnExpressionCondition)

   SslObservabilityAutoConfiguration#sslInfoProvider:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) found beans of type 'org.springframework.boot.info.SslInfo' sslInfo (OnBeanCondition)

   StackdriverMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.stackdriver.StackdriverMeterRegistry' (OnClassCondition)

   StartupEndpointAutoConfiguration:
      Did not match:
         - ApplicationStartup configured applicationStartup is of type class org.springframework.core.metrics.DefaultApplicationStartup, expected BufferingApplicationStartup. (StartupEndpointAutoConfiguration.ApplicationStartupCondition)

   StatsdMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.statsd.StatsdMeterRegistry' (OnClassCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder' simpleAsyncTaskExecutorBuilder (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutorVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder' simpleAsyncTaskSchedulerBuilder (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration#taskSchedulerVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TemplateEngineConfigurations.ReactiveTemplateEngineConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity6.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.layoutdialect.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   TransServiceConfig#container:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransServiceConfig#listenerAdapter:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransServiceConfig#redisCacheService:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransServiceConfig#transMessageListener:
      Did not match:
         - @ConditionalOnProperty (easy-trans.is-enable-redis=true) did not find property 'easy-trans.is-enable-redis' (OnPropertyCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.AspectJTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnBean did not find required type 'org.springframework.transaction.aspectj.AbstractTransactionAspect' (OnBeanCondition)
         - @ConditionalOnBean (types: ?; SearchStrategy: all) did not find any beans of type ? (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) found beans of type 'org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration' org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=false) did not find property 'spring.aop.proxy-target-class' (OnPropertyCondition)
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationManager,org.springframework.security.authentication.AuthenticationProvider,org.springframework.security.core.userdetails.UserDetailsService,org.springframework.security.authentication.AuthenticationManagerResolver,?; SearchStrategy: all) found beans of type 'org.springframework.security.authentication.AuthenticationManager' authenticationManagerBean (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - AnyNestedCondition 1 matched 2 did not; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.PasswordConfigured @ConditionalOnProperty (spring.security.user.password) did not find property 'spring.security.user.password'; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.NameConfigured @ConditionalOnProperty (spring.security.user.name) did not find property 'spring.security.user.name'; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.MissingAlternative @ConditionalOnMissingClass did not find unwanted classes 'org.springframework.security.oauth2.client.registration.ClientRegistrationRepository', 'org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector', 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured)

   WavefrontAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.application.ApplicationTags' (OnClassCondition)

   WavefrontMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WavefrontTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebFluxEndpointManagementContextConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.web.reactive.DispatcherHandler', 'org.springframework.http.server.reactive.HttpHandler' (OnClassCondition)

   WebFluxObservationAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'io.micrometer.observation.Observation', 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mvc.hiddenmethod.filter.enabled=true) did not find property 'spring.mvc.hiddenmethod.filter.enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ProblemDetailsErrorHandlingConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mvc.problemdetails.enabled=true) did not find property 'spring.mvc.problemdetails.enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#managementHealthEndpointWebMvcHandlerMapping:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.client.core.WebServiceTemplate' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSessionIdResolverAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.springframework.web.server.session.WebSessionManager', 'reactor.core.publisher.Mono' (OnClassCondition)

   WebSocketMessagingAutoConfiguration.WebSocketMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.socket.config.annotation.DelegatingWebSocketMessageBrokerConfiguration,com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans of type org.springframework.web.socket.config.annotation.DelegatingWebSocketMessageBrokerConfiguration (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.ObjectMapper', 'org.springframework.messaging.simp.config.AbstractMessageBrokerConfiguration' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - not a reactive web application (OnWebApplicationCondition)
      Matched:
         - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.ee10.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   WxMaInJedisConfigStorageConfiguration:
      Did not match:
         - @ConditionalOnProperty (wx.miniapp.config-storage.type=jedis) found different value in property 'type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'redis.clients.jedis.JedisPool', 'redis.clients.jedis.JedisPoolConfig' (OnClassCondition)

   WxMaInMemoryConfigStorageConfiguration:
      Did not match:
         - @ConditionalOnProperty (wx.miniapp.config-storage.type=memory) found different value in property 'type' (OnPropertyCondition)

   WxMaInRedissonConfigStorageConfiguration:
      Did not match:
         - @ConditionalOnProperty (wx.miniapp.config-storage.type=redisson) found different value in property 'type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.redisson.Redisson', 'org.redisson.api.RedissonClient' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.transaction.TransactionManager' (OnClassCondition)

   YmApiLogAutoConfiguration#apiAccessLogFilter:
      Did not match:
         - @ConditionalOnProperty (ym.access-log.enable) found different value in property 'enable' (OnPropertyCondition)

   YmLock4jConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.baomidou.lock.annotation.Lock4j' (OnClassCondition)

   YmMybatisAutoConfiguration#keyGenerator:
      Did not match:
         - @ConditionalOnProperty (mybatis-plus.global-config.db-config.id-type=INPUT) found different value in property 'id-type' (OnPropertyCondition)

   YmRabbitMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   YmTenantAutoConfiguration#tenantRabbitMQInitializer:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   YmTenantAutoConfiguration#tenantRocketMQInitializer:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.rocketmq.spring.core.RocketMQTemplate' (OnClassCondition)

   YmWebAutoConfiguration#restTemplate:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.client.RestTemplate; SearchStrategy: all) found beans of type 'org.springframework.web.client.RestTemplate' restTemplate (OnBeanCondition)

   YmXssAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (ym.xss.enable=true) found different value in property 'enable' (OnPropertyCondition)

   ZipkinAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'zipkin2.reporter.Encoding' (OnClassCondition)


Exclusions:
-----------

    org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration

    org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration


Unconditional classes:
----------------------

    org.springframework.boot.actuate.autoconfigure.info.InfoContributorAutoConfiguration

    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.metrics.integration.IntegrationMetricsAutoConfiguration

    com.fozmo.ym.framework.web.config.YmWebAutoConfiguration

    com.fozmo.ym.framework.mq.redis.config.YmRedisMQConsumerAutoConfiguration

    com.fozmo.ym.framework.apilog.config.YmApiLogAutoConfiguration

    de.codecentric.boot.admin.server.config.AdminServerWebConfiguration

    com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration

    com.fozmo.ym.framework.signature.config.YmApiSignatureAutoConfiguration

    com.fozmo.ym.framework.redis.config.YmRedisAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration

    com.fozmo.ym.framework.mybatis.config.YmMybatisAutoConfiguration

    de.codecentric.boot.admin.server.config.AdminServerInstanceWebClientConfiguration

    com.fozmo.ym.framework.ratelimiter.config.YmRateLimiterConfiguration

    com.fozmo.ym.framework.jackson.config.YmJacksonAutoConfiguration

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.availability.AvailabilityHealthContributorAutoConfiguration

    com.binarywang.spring.starter.wxjava.mp.config.WxMpAutoConfiguration

    org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration

    com.fozmo.ym.framework.mq.redis.config.YmRedisMQProducerAutoConfiguration

    com.fozmo.ym.framework.banner.config.YmBannerAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration

    com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration

    com.fhs.trans.config.TransServiceConfig

    com.fozmo.ym.framework.operatelog.config.YmOperateLogConfiguration

    org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration

    com.fozmo.ym.framework.redis.config.YmCacheAutoConfiguration

    com.fozmo.ym.framework.dict.config.YmDictAutoConfiguration

    com.fozmo.ym.framework.idempotent.config.YmIdempotentConfiguration

    com.fozmo.ym.framework.security.config.YmSecurityAutoConfiguration

    com.anji.captcha.config.AjCaptchaAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.jackson.JacksonEndpointAutoConfiguration

    com.fozmo.ym.framework.security.config.YmWebSecurityConfigurerAdapter

    com.fozmo.ym.framework.translate.config.YmTranslateAutoConfiguration

    cn.hutool.extra.spring.SpringUtil

    org.springdoc.core.configuration.SpringDocSpecPropertiesConfiguration

    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration

    com.binarywang.spring.starter.wxjava.miniapp.config.WxMaAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration

    com.fozmo.ym.framework.datasource.config.YmDataSourceAutoConfiguration

    com.fhs.trans.config.EasyTransMybatisPlusConfig



2025-08-11 12:22:18.210 | [1;31mERROR 13507[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Application run failed

org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:408)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:394)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:586)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:364)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:310)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:1006)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:630)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.fozmo.ym.server.YmServerApplication.main(YmServerApplication.java:13)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat server
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:251)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:44)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:405)
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: standardService.connector.startFailed
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:222)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.addPreviouslyRemovedConnectors(TomcatWebServer.java:310)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:236)
	... 16 common frames omitted
Caused by: org.apache.catalina.LifecycleException: Protocol handler start failed
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1106)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:219)
	... 18 common frames omitted
Caused by: java.net.BindException: 地址已在使用
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:555)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
	at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:266)
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:221)
	at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1399)
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:1482)
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:644)
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1103)
	... 20 common frames omitted

2025-08-11 12:22:18.298 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [32e20036] HTTP GET http://*********:11000/actuator/health
2025-08-11 12:22:18.409 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 12:22:18.416 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 12:22:18.416 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 12:22:18.421 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 12:22:18.440 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 12:22:18.445 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 12:22:18.457 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 12:22:18.458 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 12:22:18.478 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 12:22:18.498 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 12:22:18.499 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 12:22:18.501 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 12:22:18.504 | [39mDEBUG 13422[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 12:22:18.520 | [34m INFO 13422[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as cdca45f9a050
2025-08-11 12:22:18.586 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 12:22:18.598 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@6cbfca49]
2025-08-11 12:22:18.601 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 12:22:18.618 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [32e20036] [f794c5d7-1, L:/127.0.0.1:49246 - R:*********/*********:11000] Response 200 OK
2025-08-11 12:22:18.666 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [32e20036] [f794c5d7-1, L:/127.0.0.1:49246 - R:*********/*********:11000] Decoded [{status=UP}]
2025-08-11 12:22:18.674 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2fe4d90b] HTTP GET http://*********:11000/actuator
2025-08-11 12:22:18.688 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator", parameters={}
2025-08-11 12:22:18.689 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator root web endpoint
2025-08-11 12:22:18.692 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 12:22:18.698 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{_links={self=[Link@3acc95fb href = 'http://*********:11000/actuator'], beans=[Link@20ff74b4 href =  (truncated)...]
2025-08-11 12:22:18.703 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 12:22:18.703 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2fe4d90b] [9bb69435-1, L:/127.0.0.1:49258 - R:*********/*********:11000] Response 200 OK
2025-08-11 12:22:18.743 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2fe4d90b] [9bb69435-1, L:/127.0.0.1:49258 - R:*********/*********:11000] Decoded [QueryIndexEndpointStrategy.Response(links={self=QueryIndexEndpointStrategy.Response.EndpointRef(href (truncated)...]
2025-08-11 12:22:18.749 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5ee1679] HTTP GET http://*********:11000/actuator/info
2025-08-11 12:22:18.754 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 12:22:18.754 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 12:22:18.755 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 12:22:18.758 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 12:22:18.758 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 12:22:18.759 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 12:22:18.760 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5ee1679] [f794c5d7-2, L:/127.0.0.1:49246 - R:*********/*********:11000] Response 200 OK
2025-08-11 12:22:18.762 | [39mDEBUG 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5ee1679] [f794c5d7-2, L:/127.0.0.1:49246 - R:*********/*********:11000] Decoded [{}]
2025-08-11 12:22:27.680 | [39mDEBUG 13422[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://*********:11000/actuator, healthUrl=http://*********:11000/actuator/health, serviceUrl=http://*********:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 12:22:27.694 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 12:22:27.697 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://*********:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 12:22:27.699 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 12:22:27.699 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 12:22:27.699 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 12:22:27.699 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 12:22:27.702 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 12:22:27.704 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 12:22:27.704 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 12:22:27.704 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 12:22:27.705 | [39mDEBUG 13422[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 12:22:27.705 | [39mDEBUG 13422[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 12:22:35.104 | [39mDEBUG 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.a.ApplicationAvailabilityBean     [0;39m | Application availability state ReadinessState changed from ACCEPTING_TRAFFIC to REFUSING_TRAFFIC
2025-08-11 12:22:35.104 | [39mDEBUG 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@bcb09a6, started on Mon Aug 11 12:21:43 CST 2025
2025-08-11 12:22:35.113 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 12:22:35.115 | [34m INFO 13422[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-11 12:22:36.665 | [39mDEBUG 13422[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [7d836385] HTTP GET http://*********:11000/actuator/health
2025-08-11 12:22:36.673 | [34m INFO 13422[0;39m | [1;33mreactor-http-epoll-2 [TID: N/A][0;39m [1;32md.c.b.a.server.services.StatusUpdater   [0;39m | Couldn't retrieve status for Instance(id=cdca45f9a050, version=2, registration=Registration(name=ym-server, managementUrl=http://*********:11000/actuator, healthUrl=http://*********:11000/actuator/health, serviceUrl=http://*********:11000/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={}), statusTimestamp=2025-08-11T04:22:18.668770283Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://*********:11000/actuator/caches), loggers=Endpoint(id=loggers, url=http://*********:11000/actuator/loggers), logfile=Endpoint(id=logfile, url=http://*********:11000/actuator/logfile), health=Endpoint(id=health, url=http://*********:11000/actuator/health), env=Endpoint(id=env, url=http://*********:11000/actuator/env), scheduledtasks=Endpoint(id=scheduledtasks, url=http://*********:11000/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://*********:11000/actuator/mappings), beans=Endpoint(id=beans, url=http://*********:11000/actuator/beans), configprops=Endpoint(id=configprops, url=http://*********:11000/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://*********:11000/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://*********:11000/actuator/sbom), prometheus=Endpoint(id=prometheus, url=http://*********:11000/actuator/prometheus), metrics=Endpoint(id=metrics, url=http://*********:11000/actuator/metrics), conditions=Endpoint(id=conditions, url=http://*********:11000/actuator/conditions), info=Endpoint(id=info, url=http://*********:11000/actuator/info)}), buildVersion=null, tags=Tags(values={}))

org.springframework.web.reactive.function.client.WebClientRequestException: finishConnect(..) failed: 连接被拒绝: /*********:11000
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:600)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:546)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:262)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:603)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:596)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:572)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:505)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:649)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:642)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:131)
		at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.fulfillConnectPromise(AbstractEpollChannel.java:725)
		at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.finishConnect(AbstractEpollChannel.java:744)
		at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.epollOutReady(AbstractEpollChannel.java:608)
		at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.handle(AbstractEpollChannel.java:469)
		at io.netty.channel.epoll.EpollIoHandler$DefaultEpollIoRegistration.handle(EpollIoHandler.java:307)
		at io.netty.channel.epoll.EpollIoHandler.processReady(EpollIoHandler.java:489)
		at io.netty.channel.epoll.EpollIoHandler.run(EpollIoHandler.java:444)
		at io.netty.channel.SingleThreadIoEventLoop.runIo(SingleThreadIoEventLoop.java:206)
		at io.netty.channel.SingleThreadIoEventLoop.run(SingleThreadIoEventLoop.java:177)
		at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:1073)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: finishConnect(..) failed: 连接被拒绝: /*********:11000
Caused by: java.net.ConnectException: finishConnect(..) failed: 连接被拒绝
	at io.netty.channel.unix.Errors.newConnectException0(Errors.java:166)
	at io.netty.channel.unix.Errors.handleConnectErrno(Errors.java:131)
	at io.netty.channel.unix.Socket.finishConnect(Socket.java:360)
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.doFinishConnect(AbstractEpollChannel.java:761)
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.finishConnect(AbstractEpollChannel.java:738)
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.epollOutReady(AbstractEpollChannel.java:608)
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.handle(AbstractEpollChannel.java:469)
	at io.netty.channel.epoll.EpollIoHandler$DefaultEpollIoRegistration.handle(EpollIoHandler.java:307)
	at io.netty.channel.epoll.EpollIoHandler.processReady(EpollIoHandler.java:489)
	at io.netty.channel.epoll.EpollIoHandler.run(EpollIoHandler.java:444)
	at io.netty.channel.SingleThreadIoEventLoop.runIo(SingleThreadIoEventLoop.java:206)
	at io.netty.channel.SingleThreadIoEventLoop.run(SingleThreadIoEventLoop.java:177)
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:1073)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 12:22:37.148 | [34m INFO 13422[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-08-11 12:22:37.148 | [1;31mERROR 13422[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registryRemove(AdminBizClient.java:51)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:84)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 12:22:37.149 | [34m INFO 13422[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registryRemove, content=null]
2025-08-11 12:22:37.149 | [34m INFO 13422[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-11 12:22:37.149 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-11 12:22:37.149 | [34m INFO 13422[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-08-11 12:22:37.149 | [34m INFO 13422[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-11 12:22:37.150 | [34m INFO 13422[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-11 12:22:37.153 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-11 12:22:37.153 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-11 12:22:37.184 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-11 12:22:37.188 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-11 12:22:37.193 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-11 12:22:37.193 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-11 12:22:37.194 | [34m INFO 13422[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye

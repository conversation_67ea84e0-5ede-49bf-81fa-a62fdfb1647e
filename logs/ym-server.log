2025-07-28 15:05:46.856 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 25099 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by l<PERSON>qi<PERSON> in /home/<USER>/work/project/fozmo/ym)
2025-07-28 15:05:46.859 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-07-28 15:05:49.008 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 25243 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-07-28 15:05:49.010 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-07-28 15:05:51.647 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-07-28 15:05:52.550 | [31m WARN 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:52.611 | [31m WARN 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:52.632 | [31m WARN 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:52.684 | [31m WARN 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:52.689 | [31m WARN 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:52.750 | [31m WARN 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-28 15:05:53.682 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-07-28 15:05:53.726 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-07-28 15:05:53.729 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-28 15:05:53.918 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-07-28 15:05:53.919 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 6619 ms
2025-07-28 15:05:54.876 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-07-28 15:05:55.452 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-07-28 15:05:55.453 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-07-28 15:05:55.455 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-28 15:05:55.512 | [31m WARN 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:55.569 | [31m WARN 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:55.589 | [31m WARN 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:55.647 | [31m WARN 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:55.660 | [31m WARN 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:05:55.732 | [31m WARN 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-28 15:05:56.878 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-07-28 15:05:56.923 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-07-28 15:05:56.924 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-28 15:05:57.066 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-07-28 15:05:57.066 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 7724 ms
2025-07-28 15:05:58.854 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-07-28 15:05:58.855 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-07-28 15:05:58.858 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-28 15:06:00.871 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-07-28 15:06:01.339 | [34m INFO 25099[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-07-28 15:06:01.827 | [34m INFO 25099[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-07-28 15:06:02.511 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-07-28 15:06:02.991 | [34m INFO 25243[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-07-28 15:06:03.535 | [34m INFO 25243[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-07-28 15:06:05.597 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-28 15:06:05.628 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@76f65a8b, ORIGINAL=[Ljava.lang.String;@34341b2a, PIC_CLICK=[Ljava.lang.String;@4e826e33]
2025-07-28 15:06:05.630 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-07-28 15:06:05.637 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-28 15:06:05.637 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-07-28 15:06:05.771 | [1;31mERROR 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | load font error:

java.io.IOException: Problem reading font data.
	at java.desktop/java.awt.Font.createFont0(Font.java:1205)
	at java.desktop/java.awt.Font.createFont(Font.java:1076)
	at com.anji.captcha.service.impl.AbstractCaptchaService.loadWaterMarkFont(AbstractCaptchaService.java:204)
	at com.anji.captcha.service.impl.AbstractCaptchaService.init(AbstractCaptchaService.java:93)
	at com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl.init(BlockPuzzleCaptchaServiceImpl.java:34)
	at com.anji.captcha.service.impl.CaptchaServiceFactory.getInstance(CaptchaServiceFactory.java:36)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration.captchaService(AjCaptchaServiceAutoConfiguration.java:67)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.CGLIB$captchaService$0(<generated>)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.captchaService(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:468)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:606)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.fozmo.ym.server.YmServerApplication.main(YmServerApplication.java:13)

2025-07-28 15:06:07.691 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-28 15:06:07.708 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@2c7ddfa0, ORIGINAL=[Ljava.lang.String;@53b52f30, PIC_CLICK=[Ljava.lang.String;@1b250e51]
2025-07-28 15:06:07.710 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-07-28 15:06:07.715 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-28 15:06:07.715 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-07-28 15:06:07.819 | [1;31mERROR 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | load font error:

java.io.IOException: Problem reading font data.
	at java.desktop/java.awt.Font.createFont0(Font.java:1205)
	at java.desktop/java.awt.Font.createFont(Font.java:1076)
	at com.anji.captcha.service.impl.AbstractCaptchaService.loadWaterMarkFont(AbstractCaptchaService.java:204)
	at com.anji.captcha.service.impl.AbstractCaptchaService.init(AbstractCaptchaService.java:93)
	at com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl.init(BlockPuzzleCaptchaServiceImpl.java:34)
	at com.anji.captcha.service.impl.CaptchaServiceFactory.getInstance(CaptchaServiceFactory.java:36)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration.captchaService(AjCaptchaServiceAutoConfiguration.java:67)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.CGLIB$captchaService$0(<generated>)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.captchaService(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:468)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:606)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.fozmo.ym.server.YmServerApplication.main(YmServerApplication.java:13)

2025-07-28 15:06:08.483 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-07-28 15:06:09.328 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-07-28 15:06:09.831 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-07-28 15:06:09.859 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 25835 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-07-28 15:06:09.862 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-07-28 15:06:10.094 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-07-28 15:06:10.109 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-07-28 15:06:10.123 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-07-28 15:06:10.332 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 15:06:10.333 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 15:06:10.333 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 15:06:10.333 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 15:06:10.334 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 15:06:10.334 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 15:06:10.510 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-07-28 15:06:11.161 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-07-28 15:06:11.511 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-07-28 15:06:11.781 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-07-28 15:06:11.797 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-07-28 15:06:11.818 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-07-28 15:06:11.953 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 15:06:11.953 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 15:06:11.954 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 15:06:11.954 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 15:06:11.954 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 15:06:11.954 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 15:06:13.220 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-07-28 15:06:13.712 | [31m WARN 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:06:13.759 | [31m WARN 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:06:13.777 | [31m WARN 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:06:13.815 | [31m WARN 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:06:13.820 | [31m WARN 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 15:06:13.868 | [31m WARN 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-28 15:06:14.373 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-07-28 15:06:14.393 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-07-28 15:06:14.393 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-28 15:06:14.423 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 15:06:14.505 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-07-28 15:06:14.505 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4352 ms
2025-07-28 15:06:14.948 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 15:06:15.243 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 15:06:15.281 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-07-28 15:06:15.282 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-07-28 15:06:15.283 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-28 15:06:15.323 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 15:06:15.480 | [34m INFO 25099[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 15:06:15.844 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 15:06:15.901 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-07-28 15:06:16.658 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 15:06:16.949 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 15:06:17.186 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 15:06:17.198 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 15:06:17.298 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4969b90d[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-07-28 15:06:17.299 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@287213ac[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-07-28 15:06:17.399 | [34m INFO 25099[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-07-28 15:06:17.461 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-07-28 15:06:17.534 | [34m INFO 25099[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 31.575 seconds (process running for 31.982)
2025-07-28 15:06:17.554 | [34m INFO 25099[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-07-28 15:06:17.557 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 15:06:17.560 | [34m INFO 25099[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-07-28 15:06:17.593 | [34m INFO 25099[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-07-28 15:06:17.604 | [34m INFO 25099[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-28 15:06:17.605 | [34m INFO 25099[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-28 15:06:17.606 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-28 15:06:17.606 | [34m INFO 25099[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-28 15:06:17.606 | [34m INFO 25099[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-28 15:06:17.607 | [34m INFO 25099[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-28 15:06:17.612 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-07-28 15:06:17.613 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-07-28 15:06:17.737 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 15:06:17.754 | [31m WARN 25099[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Failed to register application as Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor) at spring-boot-admin ([http://127.0.0.1:11000/admin/monitor/instances]): null. Further attempts are logged on DEBUG level

java.util.concurrent.CancellationException: null
	at org.apache.hc.core5.concurrent.BasicFuture.getResult(BasicFuture.java:87)
	at org.apache.hc.core5.concurrent.BasicFuture.get(BasicFuture.java:115)
	at org.apache.hc.core5.pool.StrictConnPool$1.get(StrictConnPool.java:183)
	at org.apache.hc.core5.pool.StrictConnPool$1.get(StrictConnPool.java:177)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager$3.get(PoolingHttpClientConnectionManager.java:344)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.acquireEndpoint(InternalExecRuntime.java:111)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:127)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:195)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:576)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:533)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:680)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:758)
	at de.codecentric.boot.admin.client.registration.RestClientRegistrationClient.register(RestClientRegistrationClient.java:45)
	at de.codecentric.boot.admin.client.registration.DefaultApplicationRegistrator.register(DefaultApplicationRegistrator.java:80)
	at de.codecentric.boot.admin.client.registration.DefaultApplicationRegistrator.register(DefaultApplicationRegistrator.java:61)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-07-28 15:06:17.919 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-07-28 15:06:17.921 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-07-28 15:06:17.928 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-07-28 15:06:17.928 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-07-28 15:06:17.928 | [34m INFO 25099[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-07-28 15:06:17.985 | [34m INFO 25243[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 15:06:18.025 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-07-28 15:06:18.293 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-07-28 15:06:18.807 | [34m INFO 25835[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-07-28 15:06:19.344 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@208d5110[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-07-28 15:06:19.345 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4beac695[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-07-28 15:06:19.365 | [34m INFO 25835[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-07-28 15:06:19.412 | [34m INFO 25243[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-07-28 15:06:19.451 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-07-28 15:06:19.510 | [34m INFO 25243[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 31.531 seconds (process running for 32.126)
2025-07-28 15:06:19.524 | [34m INFO 25243[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-07-28 15:06:19.526 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 15:06:19.528 | [34m INFO 25243[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-07-28 15:06:19.551 | [34m INFO 25243[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-07-28 15:06:19.565 | [34m INFO 25243[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-28 15:06:19.566 | [34m INFO 25243[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-28 15:06:19.566 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-28 15:06:19.566 | [34m INFO 25243[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-28 15:06:19.566 | [34m INFO 25243[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-28 15:06:19.566 | [34m INFO 25243[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-28 15:06:19.571 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-07-28 15:06:19.571 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-07-28 15:06:19.637 | [31m WARN 25243[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Failed to register application as Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor) at spring-boot-admin ([http://127.0.0.1:11000/admin/monitor/instances]): null. Further attempts are logged on DEBUG level

java.util.concurrent.CancellationException: null
	at org.apache.hc.core5.concurrent.BasicFuture.getResult(BasicFuture.java:87)
	at org.apache.hc.core5.concurrent.BasicFuture.get(BasicFuture.java:115)
	at org.apache.hc.core5.pool.StrictConnPool$1.get(StrictConnPool.java:183)
	at org.apache.hc.core5.pool.StrictConnPool$1.get(StrictConnPool.java:177)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager$3.get(PoolingHttpClientConnectionManager.java:344)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.acquireEndpoint(InternalExecRuntime.java:111)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:127)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:195)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:576)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:533)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:680)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:758)
	at de.codecentric.boot.admin.client.registration.RestClientRegistrationClient.register(RestClientRegistrationClient.java:45)
	at de.codecentric.boot.admin.client.registration.DefaultApplicationRegistrator.register(DefaultApplicationRegistrator.java:80)
	at de.codecentric.boot.admin.client.registration.DefaultApplicationRegistrator.register(DefaultApplicationRegistrator.java:61)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-07-28 15:06:20.036 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-07-28 15:06:20.038 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-07-28 15:06:20.044 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-07-28 15:06:20.044 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-07-28 15:06:20.045 | [34m INFO 25243[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-07-28 15:06:22.505 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-28 15:06:22.523 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@72c743f4, ORIGINAL=[Ljava.lang.String;@4bb4c147, PIC_CLICK=[Ljava.lang.String;@2c7ddfa0]
2025-07-28 15:06:22.526 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-07-28 15:06:22.531 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-28 15:06:22.531 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-07-28 15:06:24.703 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-07-28 15:06:25.137 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-07-28 15:06:25.622 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-07-28 15:06:25.859 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-07-28 15:06:25.869 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-07-28 15:06:25.880 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-07-28 15:06:26.041 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 15:06:26.041 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 15:06:26.042 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 15:06:26.042 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 15:06:26.042 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 15:06:26.042 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 15:06:30.711 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 15:06:30.835 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 15:06:30.849 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 15:06:31.378 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 15:06:31.468 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 15:06:31.750 | [34m INFO 25835[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 15:06:31.801 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-07-28 15:06:33.006 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7b836cf6[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-07-28 15:06:33.007 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59412c[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-07-28 15:06:33.087 | [34m INFO 25835[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-07-28 15:06:33.166 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-07-28 15:06:33.266 | [34m INFO 25835[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 24.547 seconds (process running for 25.23)
2025-07-28 15:06:33.289 | [34m INFO 25835[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-07-28 15:06:33.604 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 15:06:33.604 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-07-28 15:06:33.607 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-07-28 15:06:34.069 | [34m INFO 25835[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-07-28 15:06:51.256 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{content-length=122, ym-token=test4, host=127.0.0.1:11000, content-type=application/json, connection=keep-alive, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=Apifox/1.0.0 (https://apifox.com), accept=*/*},请求体：{
    "worksId": 536818,
    "shareChannel": 0,
    "sharePage": "in esse",
    "env": "develop",
    "scene": "12C4567"
}
2025-07-28 15:06:51.625 | [1;31mERROR 25835[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.c.e.util.ServiceExceptionUtil   [0;39m | 错误码(401000)|错误内容(访问令牌不存在)|参数([])
2025-07-28 15:06:51.701 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/social/worksshare/create) 参数({
    "worksId": 536818,
    "shareChannel": 0,
    "sharePage": "in esse",
    "env": "develop",
    "scene": "12C4567"
})]
2025-07-28 15:06:52.037 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.m.a.util.oauth2.WechatAppletUtil  [0;39m | 获取小程序二维码-url:https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=94_PV0BgCBzoPnfTftBMaQ4ouJTlc1-HAHhjs05wOuuFI3uWshQCrycE74L5colHC43DeNHFr7C0gVs9vtvyhk9agbvkmqt2_cw8RAllfxhyifqYJFGvr8DoB1Wm4YIEPgAJABXI,参数：{"page":"in esse","scene":"12C4567","env_version":"develop","check_path":false}
2025-07-28 15:06:52.886 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.m.a.util.oauth2.WechatAppletUtil  [0;39m | 获取小程序二维码返回
2025-07-28 15:06:53.063 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/social/worksshare/create) 耗时(1360 ms)]
2025-07-28 15:21:25.490 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{sec-fetch-mode=navigate, sec-fetch-site=none, accept-language=zh-CN,zh;q=0.9, sec-fetch-user=?1, accept=text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7, sec-ch-ua="Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138", sec-ch-ua-mobile=?0, sec-ch-ua-platform="Linux", host=*********:11000, upgrade-insecure-requests=1, connection=keep-alive, cache-control=max-age=0, accept-encoding=gzip, deflate, br, zstd, user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, sec-fetch-dest=document},请求体：null
2025-07-28 15:21:25.904 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/admin/monitor/assets/sba-Jg4iGIJt.css) 无参数]
2025-07-28 15:21:25.904 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/admin/monitor/assets/index-Dqy3wWp6.js) 无参数]
2025-07-28 15:21:25.904 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/admin/monitor/assets/index-DqTM08dA.css) 无参数]
2025-07-28 15:21:25.905 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/admin/monitor/assets/sba-BViw7H6_.js) 无参数]
2025-07-28 15:21:25.907 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/admin/monitor/assets/index-DqTM08dA.css) 耗时(2 ms)]
2025-07-28 15:21:25.909 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/admin/monitor/assets/sba-Jg4iGIJt.css) 耗时(4 ms)]
2025-07-28 15:21:25.910 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/admin/monitor/assets/index-Dqy3wWp6.js) 耗时(6 ms)]
2025-07-28 15:21:25.919 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/admin/monitor/assets/sba-BViw7H6_.js) 耗时(13 ms)]
2025-07-28 15:21:26.101 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/admin/monitor/assets/img/icon-spring-boot-admin.svg) 无参数]
2025-07-28 15:21:26.106 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/admin/monitor/assets/img/icon-spring-boot-admin.svg) 耗时(5 ms)]
2025-07-28 15:38:26.296 | [34m INFO 25835[0;39m | [1;33mSimpleAsyncTaskExecutor-1 [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.s.c.SmsSendConsumer       [0;39m | [onMessage][消息内容(SmsSendMessage(logId=1402, mobile=15088618717, channelId=8, apiTemplateId=4372216, templateParams=[KeyValue(key=code, value=2890)]))]
2025-07-28 15:48:56.148 | [1;31mERROR 25835[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.w.c.h.GlobalExceptionHandler    [0;39m | [defaultExceptionHandler]

org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to flush: java.io.IOException: 断开的管道
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:346)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:418)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:137)
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:128)
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44)
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:235)
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$DefaultSseEmitterHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:310)
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$DefaultSseEmitterHandler.send(ResponseBodyEmitterReturnValueHandler.java:297)
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:226)
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:217)
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$SseEmitterSubscriber.send(ReactiveTypeHandler.java:437)
	at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$AbstractEmitterSubscriber.run(ReactiveTypeHandler.java:359)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.catalina.connector.ClientAbortException: java.io.IOException: 断开的管道
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:266)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:524)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:415)
	... 15 common frames omitted
Caused by: java.io.IOException: 断开的管道
	at java.base/sun.nio.ch.FileDispatcherImpl.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:62)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:132)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:97)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:53)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:532)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:125)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1411)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:732)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:698)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:683)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1271)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:408)
	at org.apache.coyote.Response.action(Response.java:208)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:300)
	... 19 common frames omitted

2025-07-28 15:48:56.157 | [31m WARN 25835[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32m.m.m.a.ExceptionHandlerExceptionResolver[0;39m | Failure in @ExceptionHandler com.fozmo.ym.framework.web.core.handler.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Throwable)

org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.fozmo.ym.framework.common.pojo.CommonResult] with preset Content-Type 'text/event-stream;charset=UTF-8'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:365)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:471)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:73)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:182)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1359)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1161)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:91)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
	at org.apache.catalina.core.ApplicationDispatcher.doDispatch(ApplicationDispatcher.java:540)
	at org.apache.catalina.core.ApplicationDispatcher.dispatch(ApplicationDispatcher.java:511)
	at org.apache.catalina.core.AsyncContextImpl$AsyncRunnable.run(AsyncContextImpl.java:599)
	at org.apache.catalina.core.AsyncContextImpl.doInternalDispatch(AsyncContextImpl.java:342)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:165)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:239)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:243)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-07-28 15:52:39.411 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{content-length=24, ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=1a0155b4-0c21-4d6d-a9f0-5abf7091b68a, host=*************:11000, content-type=application/json, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：{   
    "humanId":5
}
2025-07-28 15:52:39.446 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/account/human/updateHuman) 参数({   
    "humanId":5
})]
2025-07-28 15:52:39.626 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/account/human/updateHuman) 耗时(180 ms)]
2025-07-28 16:22:10.176 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=ac28090e-5e4e-417c-b40d-f4fe741eddcf, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 16:22:10.190 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/account/myAccount) 无参数]
2025-07-28 16:22:10.425 | [34m INFO 25835[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/account/myAccount) 耗时(234 ms)]
2025-07-28 18:56:16.269 | [34m INFO 25835[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 18:56:16.271 | [34m INFO 25835[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-07-28 18:56:22.441 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 124307 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-07-28 18:56:22.443 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-07-28 18:56:25.613 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-07-28 18:56:26.131 | [31m WARN 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:56:26.170 | [31m WARN 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:56:26.184 | [31m WARN 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:56:26.226 | [31m WARN 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:56:26.231 | [31m WARN 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:56:26.275 | [31m WARN 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-28 18:56:26.839 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-07-28 18:56:26.863 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-07-28 18:56:26.864 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-28 18:56:26.963 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-07-28 18:56:26.963 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4285 ms
2025-07-28 18:56:27.945 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-07-28 18:56:27.946 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-07-28 18:56:27.947 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-28 18:56:30.545 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-07-28 18:56:30.920 | [34m INFO 124307[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-07-28 18:56:31.351 | [34m INFO 124307[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-07-28 18:56:34.509 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-28 18:56:34.528 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@63792a7a, ORIGINAL=[Ljava.lang.String;@6911005b, PIC_CLICK=[Ljava.lang.String;@6b7d363c]
2025-07-28 18:56:34.530 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-07-28 18:56:34.536 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-28 18:56:34.536 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-07-28 18:56:36.810 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-07-28 18:56:37.271 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-07-28 18:56:37.750 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-07-28 18:56:37.983 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-07-28 18:56:37.994 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-07-28 18:56:38.006 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-07-28 18:56:38.157 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 18:56:38.158 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 18:56:38.158 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 18:56:38.158 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 18:56:38.158 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 18:56:38.158 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 18:56:43.178 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 18:56:43.336 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 18:56:43.427 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 18:56:43.813 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 18:56:43.951 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 18:56:44.221 | [34m INFO 124307[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 18:56:44.270 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-07-28 18:56:45.689 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6051e328[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-07-28 18:56:45.690 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d2775ae[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-07-28 18:56:45.777 | [34m INFO 124307[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-07-28 18:56:45.880 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-07-28 18:56:45.979 | [34m INFO 124307[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 24.423 seconds (process running for 24.876)
2025-07-28 18:56:46.015 | [34m INFO 124307[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-07-28 18:56:46.486 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 18:56:46.487 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-07-28 18:56:46.491 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 4 ms
2025-07-28 18:56:47.366 | [34m INFO 124307[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-07-28 18:57:02.884 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{accountId=28846, pageNo=1, pageSize=100},头部信息：{host=127.0.0.1:11000, connection=keep-alive, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=Apifox/1.0.0 (https://apifox.com), accept=*/*},请求体：null
2025-07-28 18:57:03.146 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({accountId=28846, pageNo=1, pageSize=100})]
2025-07-28 18:57:05.488 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(2340 ms)]
2025-07-28 18:59:36.194 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=20c841ee-ef5b-44f1-9803-550e9a5a69e7, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 18:59:36.230 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 18:59:37.992 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1761 ms)]
2025-07-28 18:59:48.558 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=b356b3a2-beb6-4125-b38c-17de3a13301b, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 18:59:48.571 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 18:59:56.525 | [34m INFO 124307[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(7953 ms)]
2025-07-28 19:05:42.173 | [34m INFO 124307[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 19:05:42.175 | [34m INFO 124307[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-07-28 19:05:46.391 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 125917 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-07-28 19:05:46.393 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-07-28 19:05:49.512 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-07-28 19:05:50.113 | [31m WARN 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:05:50.152 | [31m WARN 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:05:50.168 | [31m WARN 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:05:50.203 | [31m WARN 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:05:50.209 | [31m WARN 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:05:50.252 | [31m WARN 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-28 19:05:50.747 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-07-28 19:05:50.770 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-07-28 19:05:50.770 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-28 19:05:50.864 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-07-28 19:05:50.864 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4255 ms
2025-07-28 19:05:51.746 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-07-28 19:05:51.746 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-07-28 19:05:51.748 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-28 19:05:54.337 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-07-28 19:05:54.680 | [34m INFO 125917[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-07-28 19:05:55.163 | [34m INFO 125917[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-07-28 19:05:58.109 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-28 19:05:58.126 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@2c7ddfa0, ORIGINAL=[Ljava.lang.String;@53b52f30, PIC_CLICK=[Ljava.lang.String;@1b250e51]
2025-07-28 19:05:58.129 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-07-28 19:05:58.134 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-28 19:05:58.135 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-07-28 19:06:00.355 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-07-28 19:06:00.821 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-07-28 19:06:01.129 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-07-28 19:06:01.297 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-07-28 19:06:01.306 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-07-28 19:06:01.314 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-07-28 19:06:01.418 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 19:06:01.418 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 19:06:01.418 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 19:06:01.419 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 19:06:01.419 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 19:06:01.419 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 19:06:06.084 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 19:06:06.178 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 19:06:06.755 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 19:06:06.806 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 19:06:07.527 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 19:06:07.670 | [34m INFO 125917[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 19:06:07.705 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-07-28 19:06:08.702 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@287213ac[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-07-28 19:06:08.703 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@74b8bc34[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-07-28 19:06:08.769 | [34m INFO 125917[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-07-28 19:06:08.806 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-07-28 19:06:08.860 | [34m INFO 125917[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 23.314 seconds (process running for 23.753)
2025-07-28 19:06:08.874 | [34m INFO 125917[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-07-28 19:06:09.103 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 19:06:09.104 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-07-28 19:06:09.107 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-07-28 19:06:09.417 | [34m INFO 125917[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-07-28 19:06:11.233 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=c6f758a7-65fc-4c96-a444-0b624bbd512e, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:06:11.456 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:06:17.926 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(6469 ms)]
2025-07-28 19:06:21.958 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=d6596fce-a167-4697-93a8-b49b3f25bf0b, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:06:21.972 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:06:28.104 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(6131 ms)]
2025-07-28 19:06:30.565 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=8d565cfa-9358-40d3-8b29-d493b46c95df, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:06:30.577 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:06:36.482 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(5904 ms)]
2025-07-28 19:06:39.493 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=70c96509-3aa4-4051-b658-79b422b12f07, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:06:39.507 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:06:45.373 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(5865 ms)]
2025-07-28 19:07:23.394 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=2049a1fc-faa5-419c-81c5-58b848fcc5dd, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:07:23.414 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:07:24.598 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1183 ms)]
2025-07-28 19:07:26.996 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=06d46be0-58d7-4003-8589-b1f342ed4675, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:07:27.008 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:07:28.221 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1213 ms)]
2025-07-28 19:07:29.465 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=50d1af0e-d883-48f1-a81a-2d23182df44a, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:07:29.476 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:07:30.653 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1177 ms)]
2025-07-28 19:07:35.299 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=e4e30a6e-41f4-45cd-a57c-71c236f79fc8, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:07:35.313 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:07:36.560 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1246 ms)]
2025-07-28 19:07:39.395 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=191f09e6-72eb-4c67-9311-0d39fe35219f, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:07:39.406 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:07:40.585 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1178 ms)]
2025-07-28 19:07:59.261 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=cc52d7e0-9d40-490b-adec-4973db82f6de, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:07:59.272 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:08:00.496 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1224 ms)]
2025-07-28 19:08:13.641 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{accountId=28846, pageNo=1, pageSize=100},头部信息：{host=127.0.0.1:11000, connection=keep-alive, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=Apifox/1.0.0 (https://apifox.com), accept=*/*},请求体：null
2025-07-28 19:08:13.643 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({accountId=28846, pageNo=1, pageSize=100})]
2025-07-28 19:08:15.128 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1485 ms)]
2025-07-28 19:08:30.739 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=1e514c55-6371-4cfa-8141-b10e31da4580, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:08:30.750 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:08:31.963 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1212 ms)]
2025-07-28 19:08:43.333 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=b0e8cae6-c143-4c67-9cf9-32db7abcd20f, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:08:43.343 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:08:44.539 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(1195 ms)]
2025-07-28 19:09:19.956 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=10},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=57c9f4cf-6a76-4afd-985b-01c6816ba5f9, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:09:19.968 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=10})]
2025-07-28 19:09:20.651 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(682 ms)]
2025-07-28 19:09:22.721 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=10},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=cfc613d4-3e97-4a1f-8488-43f67ea7cc97, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:09:22.733 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=10})]
2025-07-28 19:09:23.385 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(651 ms)]
2025-07-28 19:09:24.565 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=10},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=86e4bf83-0898-41e1-a9dd-96cfdd183c87, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:09:24.579 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=10})]
2025-07-28 19:09:25.218 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(639 ms)]
2025-07-28 19:09:26.481 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=10},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=bf5dd5d2-27b6-4edd-9232-c1dd2b9b7594, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:09:26.495 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=10})]
2025-07-28 19:09:27.096 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(601 ms)]
2025-07-28 19:09:29.784 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=10},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=1f802cf1-15ef-4395-b022-fa3f19c00eed, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:09:29.792 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=10})]
2025-07-28 19:09:30.435 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(642 ms)]
2025-07-28 19:09:31.569 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=10},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=c7d80fb6-555a-4046-af69-3d05c4985331, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:09:31.582 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=10})]
2025-07-28 19:09:32.256 | [34m INFO 125917[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(673 ms)]
2025-07-28 19:20:26.720 | [34m INFO 125917[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 19:20:26.722 | [34m INFO 125917[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-07-28 19:20:32.795 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 128626 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-07-28 19:20:32.797 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-07-28 19:20:35.971 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-07-28 19:20:36.479 | [31m WARN 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:20:36.519 | [31m WARN 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:20:36.532 | [31m WARN 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:20:36.561 | [31m WARN 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:20:36.566 | [31m WARN 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 19:20:36.613 | [31m WARN 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-28 19:20:37.091 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-07-28 19:20:37.114 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-07-28 19:20:37.114 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-28 19:20:37.207 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-07-28 19:20:37.208 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4187 ms
2025-07-28 19:20:38.409 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-07-28 19:20:38.409 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-07-28 19:20:38.411 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-28 19:20:40.989 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-07-28 19:20:41.349 | [34m INFO 128626[0;39m | [1;33mredisson-netty-1-5 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-07-28 19:20:41.779 | [34m INFO 128626[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-07-28 19:20:44.441 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-28 19:20:44.457 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@2c7ddfa0, ORIGINAL=[Ljava.lang.String;@53b52f30, PIC_CLICK=[Ljava.lang.String;@1b250e51]
2025-07-28 19:20:44.458 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-07-28 19:20:44.463 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-28 19:20:44.463 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-07-28 19:20:46.239 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-07-28 19:20:46.713 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-07-28 19:20:47.026 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-07-28 19:20:47.186 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-07-28 19:20:47.193 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-07-28 19:20:47.201 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-07-28 19:20:47.314 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 19:20:47.314 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 19:20:47.314 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 19:20:47.314 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 19:20:47.314 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 19:20:47.314 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 19:20:52.057 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-07-28 19:20:52.279 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-07-28 19:20:52.601 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-07-28 19:20:53.288 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-07-28 19:20:53.578 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-07-28 19:20:53.690 | [34m INFO 128626[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-07-28 19:20:53.724 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-07-28 19:20:54.713 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@287213ac[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-07-28 19:20:54.714 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@74b8bc34[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-07-28 19:20:54.782 | [34m INFO 128626[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-07-28 19:20:54.827 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-07-28 19:20:54.887 | [34m INFO 128626[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 22.969 seconds (process running for 23.372)
2025-07-28 19:20:54.901 | [34m INFO 128626[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-07-28 19:20:55.109 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 19:20:55.109 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-07-28 19:20:55.112 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-07-28 19:20:55.423 | [34m INFO 128626[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-07-28 19:21:04.479 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=89f9a4f8-76b2-436a-abf5-02966eaf073d, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:04.715 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:05.686 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(970 ms)]
2025-07-28 19:21:13.616 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=89a0f13e-ebb6-42e9-a7f3-d34b2b472f5c, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:13.633 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:14.473 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(839 ms)]
2025-07-28 19:21:16.062 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=30c1036e-1085-4ae0-9a1d-a3fd45173454, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:16.075 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:16.832 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(757 ms)]
2025-07-28 19:21:18.299 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=fa26c84b-1b89-4af7-a06f-3448b824b253, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:18.319 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:19.132 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(813 ms)]
2025-07-28 19:21:20.300 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=4e0291ce-c5bf-43eb-aed3-e15400a7014b, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:20.316 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:21.110 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(794 ms)]
2025-07-28 19:21:22.899 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=b474de19-70e2-479f-87f8-592ea10ed92c, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:22.908 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:23.708 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(799 ms)]
2025-07-28 19:21:25.071 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=20},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=d13ff634-e329-4a11-ab01-eea15886978a, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:25.109 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=20})]
2025-07-28 19:21:25.891 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(781 ms)]
2025-07-28 19:21:43.197 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=65003455-4f40-4a10-8c09-c4caf16c443d, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:43.209 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:21:46.766 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(3555 ms)]
2025-07-28 19:21:50.213 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=6fc148a3-c63b-4e12-b9a6-8998ea5be6d9, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:50.224 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:21:53.703 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(3478 ms)]
2025-07-28 19:21:55.880 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=0cee448a-ef66-43f6-bc74-d2d45205e69b, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:21:55.892 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:21:59.381 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(3489 ms)]
2025-07-28 19:22:12.645 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{pageNo=1, pageSize=100},头部信息：{ym-token=ef5ee48a51924e13a938ffbe1d7dfdbb, postman-token=8547929d-c3c5-4308-b926-d5815377ebdd, host=*************:11000, connection=keep-alive, cache-control=no-cache, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=PostmanRuntime/7.44.1, accept=*/*},请求体：null
2025-07-28 19:22:12.664 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/space/works/page) 参数({pageNo=1, pageSize=100})]
2025-07-28 19:22:16.024 | [34m INFO 128626[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/space/works/page) 耗时(3359 ms)]

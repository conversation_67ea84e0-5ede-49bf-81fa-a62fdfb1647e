2025-08-11 17:34:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:34:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:34:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:34:18.092 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:34:18.092 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:34:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2d39bd31] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:34:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:34:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:34:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:34:26.981 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:34:26.981 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@33ed1318]
2025-08-11 17:34:26.981 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:34:26.981 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2d39bd31] [5a6fdeeb-90, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:34:26.981 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2d39bd31] [5a6fdeeb-90, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:34:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:34:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:34:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:34:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:34:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:34:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:34:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:34:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:34:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:34:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:34:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:34:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:34:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:34:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:34:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:34:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:34:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:34:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:34:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:34:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:34:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:34:37.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:34:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [482c4507] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:34:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:34:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:34:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:34:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5b5a5a37] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:34:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:34:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:34:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:34:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:34:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:34:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:34:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5b5a5a37] [3a2e29dc-37, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:34:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5b5a5a37] [3a2e29dc-37, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:34:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:34:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2c45f79a]
2025-08-11 17:34:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:34:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [482c4507] [c8271542-29, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:34:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [482c4507] [c8271542-29, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:34:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:34:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:34:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:34:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:34:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:34:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:34:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:34:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:34:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:34:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:34:47.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:34:48.093 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:34:48.093 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:34:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:34:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:34:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:34:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:34:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:34:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:34:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:34:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:34:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:34:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:34:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:34:57.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:35:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1d9fc85d] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:35:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:35:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:35:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:35:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:35:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@78df9575]
2025-08-11 17:35:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:35:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1d9fc85d] [5a6fdeeb-91, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:35:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [1d9fc85d] [5a6fdeeb-91, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:35:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:35:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:35:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:35:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:35:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:35:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:35:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:35:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:35:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:35:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:35:07.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:35:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:35:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:35:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:35:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:35:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:35:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:35:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:35:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:35:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:35:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:35:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:35:18.094 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:35:18.094 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:35:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [63f4dc07] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:35:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:35:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:35:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:35:26.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:35:26.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2f1207d6]
2025-08-11 17:35:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:35:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [63f4dc07] [3a2e29dc-38, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:35:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [63f4dc07] [3a2e29dc-38, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:35:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:35:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:35:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:35:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:35:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:35:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:35:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:35:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:35:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:35:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:35:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:35:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:35:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:35:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:35:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:35:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:35:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:35:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:35:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:35:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:35:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:35:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:35:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [600e3268] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:35:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:35:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:35:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:35:46.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:35:46.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@738e2804]
2025-08-11 17:35:46.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:35:46.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [600e3268] [c8271542-30, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:35:46.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [600e3268] [c8271542-30, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:35:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:35:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:35:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:35:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:35:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:35:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:35:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:35:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:35:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:35:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:35:47.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:35:48.095 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:35:48.095 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:35:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:35:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:35:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:35:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:35:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:35:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:35:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:35:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:35:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:35:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:35:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:35:57.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:36:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1fc574f2] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:36:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:36:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:36:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:36:06.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:36:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@767658e1]
2025-08-11 17:36:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:36:06.943 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1fc574f2] [5a6fdeeb-92, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:36:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [1fc574f2] [5a6fdeeb-92, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:36:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:36:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:36:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:36:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:36:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:36:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:36:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:36:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:36:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:36:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:36:07.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:36:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:36:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:36:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:36:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:36:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:36:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:36:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:36:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:36:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:36:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:36:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:36:18.096 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:36:18.096 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:36:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [33ae6c79] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:36:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:36:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:36:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:36:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:36:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1652d2a8]
2025-08-11 17:36:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:36:26.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [33ae6c79] [3a2e29dc-39, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:36:26.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [33ae6c79] [3a2e29dc-39, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:36:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:36:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:36:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:36:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:36:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:36:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:36:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:36:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:36:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:36:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:36:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:36:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:36:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:36:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:36:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:36:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:36:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:36:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:36:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:36:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:36:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:36:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:36:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [67aa87fc] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:36:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:36:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:36:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:36:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [390433d2] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:36:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:36:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:36:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:36:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:36:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:36:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:36:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [390433d2] [5a6fdeeb-93, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:36:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [390433d2] [5a6fdeeb-93, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:36:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:36:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@21f4e2b7]
2025-08-11 17:36:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:36:46.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [67aa87fc] [c8271542-31, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:36:46.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [67aa87fc] [c8271542-31, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:36:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:36:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:36:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:36:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:36:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:36:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:36:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:36:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:36:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:36:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:36:47.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:36:48.097 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:36:48.097 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:36:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:36:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:36:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:36:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:36:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:36:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:36:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:36:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:36:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:36:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:36:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:36:57.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:37:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [50a0a72] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:37:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:37:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:37:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:37:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:37:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@6b52d14e]
2025-08-11 17:37:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:37:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [50a0a72] [3a2e29dc-40, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:37:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [50a0a72] [3a2e29dc-40, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:37:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:37:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:37:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:37:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:37:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:37:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:37:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:37:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:37:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:37:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:37:07.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:37:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:37:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:37:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:37:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:37:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:37:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:37:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:37:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:37:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:37:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:37:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:37:18.097 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:37:18.098 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:37:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [24b63543] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:37:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:37:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:37:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:37:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:37:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1d68db52]
2025-08-11 17:37:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:37:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [24b63543] [5a6fdeeb-94, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:37:26.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [24b63543] [5a6fdeeb-94, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:37:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:37:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:37:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:37:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:37:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:37:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:37:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:37:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:37:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:37:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:37:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:37:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:37:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:37:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:37:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:37:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:37:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:37:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:37:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:37:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:37:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:37:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:37:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [7dcbfcb9] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:37:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:37:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:37:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:37:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:37:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@172a205]
2025-08-11 17:37:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:37:46.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [7dcbfcb9] [c8271542-32, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:37:46.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [7dcbfcb9] [c8271542-32, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:37:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:37:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:37:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:37:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:37:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:37:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:37:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:37:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:37:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:37:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:37:47.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:37:48.098 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:37:48.098 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:37:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:37:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:37:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:37:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:37:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:37:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:37:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:37:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:37:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:37:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:37:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:37:57.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:38:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [49eac96c] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:38:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:38:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:38:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:38:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:38:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@514422f8]
2025-08-11 17:38:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:38:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [49eac96c] [3a2e29dc-41, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:38:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [49eac96c] [3a2e29dc-41, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:38:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:38:07.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:38:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:38:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:38:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:38:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:38:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:38:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:38:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:38:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:38:07.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:38:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:38:17.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:38:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:38:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:38:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:38:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:38:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:38:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:38:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:38:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:38:17.823 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:38:18.099 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:38:18.099 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:38:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4948ec93] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:38:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:38:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:38:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:38:26.938 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:38:26.938 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1fa8c29d]
2025-08-11 17:38:26.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:38:26.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4948ec93] [5a6fdeeb-95, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:38:26.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4948ec93] [5a6fdeeb-95, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:38:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:38:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:38:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:38:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:38:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:38:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:38:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:38:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:38:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:38:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:38:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:38:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:38:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:38:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:38:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:38:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:38:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:38:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:38:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:38:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:38:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:38:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:38:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4790e0a3] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:38:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:38:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:38:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:38:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2578270c] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:38:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:38:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:38:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:38:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:38:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:38:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:38:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2578270c] [3a2e29dc-42, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:38:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2578270c] [3a2e29dc-42, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:38:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:38:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7cd58d1a]
2025-08-11 17:38:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:38:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4790e0a3] [c8271542-33, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:38:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4790e0a3] [c8271542-33, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:38:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:38:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:38:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:38:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:38:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:38:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:38:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:38:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:38:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:38:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:38:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:38:48.100 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:38:48.100 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:38:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:38:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:38:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:38:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:38:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:38:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:38:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:38:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:38:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:38:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:38:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:38:57.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:39:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4644ddb7] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:39:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:39:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:39:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:39:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:39:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@26bf393a]
2025-08-11 17:39:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:39:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4644ddb7] [5a6fdeeb-96, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:39:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4644ddb7] [5a6fdeeb-96, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:39:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:39:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:39:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:39:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:39:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:39:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:39:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:39:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:39:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:39:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:39:07.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:39:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:39:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:39:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:39:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:39:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:39:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:39:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:39:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:39:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:39:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:39:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:39:18.100 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:39:18.100 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:39:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [33b000fa] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:39:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:39:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:39:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:39:26.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:39:26.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@18e5693f]
2025-08-11 17:39:26.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:39:26.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [33b000fa] [3a2e29dc-43, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:39:26.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [33b000fa] [3a2e29dc-43, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:39:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:39:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:39:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:39:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:39:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:39:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:39:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:39:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:39:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:39:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:39:27.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:39:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:39:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:39:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:39:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:39:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:39:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:39:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:39:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:39:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:39:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:39:37.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:39:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6e1f2102] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:39:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:39:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:39:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:39:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:39:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@4e1fb5a0]
2025-08-11 17:39:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:39:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6e1f2102] [c8271542-34, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:39:46.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6e1f2102] [c8271542-34, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:39:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:39:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:39:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:39:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:39:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:39:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:39:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:39:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:39:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:39:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:39:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:39:48.101 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:39:48.101 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:39:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:39:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:39:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:39:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:39:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:39:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:39:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:39:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:39:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:39:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:39:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:39:57.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:40:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [519726fe] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:40:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:40:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:40:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:40:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:40:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@3c62cee2]
2025-08-11 17:40:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:40:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [519726fe] [5a6fdeeb-97, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:40:06.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [519726fe] [5a6fdeeb-97, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:40:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:40:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:40:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:40:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:40:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:40:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:40:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:40:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:40:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:40:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:40:07.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:40:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:40:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:40:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:40:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:40:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:40:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:40:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:40:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:40:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:40:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:40:17.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:40:18.102 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:40:18.102 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:40:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [609b171e] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:40:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:40:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:40:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:40:26.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:40:26.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2895eab2]
2025-08-11 17:40:26.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:40:26.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [609b171e] [3a2e29dc-44, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:40:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [609b171e] [3a2e29dc-44, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:40:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:40:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:40:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:40:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:40:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:40:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:40:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:40:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:40:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:40:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:40:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:40:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:40:37.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:40:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:40:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:40:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:40:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:40:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:40:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:40:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:40:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:40:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:40:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [768cdc27] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:40:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:40:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:40:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:40:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [38d11147] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:40:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:40:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:40:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:40:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:40:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:40:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:40:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [38d11147] [5a6fdeeb-98, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:40:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [38d11147] [5a6fdeeb-98, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:40:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:40:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1f757177]
2025-08-11 17:40:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:40:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [768cdc27] [c8271542-35, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:40:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [768cdc27] [c8271542-35, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:40:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:40:47.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:40:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:40:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:40:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:40:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:40:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:40:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:40:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:40:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:40:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:40:48.103 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:40:48.103 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:40:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:40:57.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:40:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:40:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:40:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:40:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:40:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:40:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:40:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:40:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:40:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:40:57.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:41:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [20bd6fdf] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:41:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:41:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:41:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:41:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:41:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@3c65f59f]
2025-08-11 17:41:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:41:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [20bd6fdf] [3a2e29dc-45, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:41:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [20bd6fdf] [3a2e29dc-45, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:41:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:41:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:41:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:41:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:41:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:41:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:41:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:41:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:41:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:41:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:41:07.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:41:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:41:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:41:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:41:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:41:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:41:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:41:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:41:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:41:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:41:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:41:17.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:41:18.104 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:41:18.104 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:41:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2d4494dd] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:41:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:41:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:41:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:41:26.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:41:26.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1bc6c12e]
2025-08-11 17:41:26.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:41:26.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2d4494dd] [5a6fdeeb-99, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:41:26.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2d4494dd] [5a6fdeeb-99, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:41:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:41:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:41:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:41:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:41:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:41:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:41:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:41:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:41:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:41:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:41:27.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:41:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:41:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:41:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:41:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:41:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:41:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:41:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:41:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:41:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:41:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:41:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:41:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [66cbb83f] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:41:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:41:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:41:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:41:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:41:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@360d338b]
2025-08-11 17:41:46.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:41:46.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [66cbb83f] [c8271542-36, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:41:46.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [66cbb83f] [c8271542-36, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:41:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:41:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:41:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:41:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:41:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:41:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:41:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:41:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:41:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:41:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:41:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:41:48.105 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:41:48.105 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:41:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:41:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:41:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:41:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:41:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:41:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:41:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:41:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:41:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:41:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:41:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:41:57.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:42:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4f2d9cd4] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:42:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:42:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:42:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:42:06.938 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:42:06.938 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@69c12268]
2025-08-11 17:42:06.939 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:42:06.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4f2d9cd4] [3a2e29dc-46, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:42:06.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4f2d9cd4] [3a2e29dc-46, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:42:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:42:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:42:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:42:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:42:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:42:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:42:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:42:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:42:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:42:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:42:07.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:42:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:42:17.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:42:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:42:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:42:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:42:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:42:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:42:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:42:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:42:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:42:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:42:18.106 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:42:18.106 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:42:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6211b882] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:42:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:42:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:42:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:42:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:42:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@79628ddf]
2025-08-11 17:42:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:42:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6211b882] [5a6fdeeb-100, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:42:26.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6211b882] [5a6fdeeb-100, L:/127.0.0.1:34608 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:42:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:42:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:42:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:42:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:42:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:42:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:42:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:42:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:42:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:42:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:42:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:42:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:42:37.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:42:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:42:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:42:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:42:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:42:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:42:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:42:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:42:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:42:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:42:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [e009cab] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:42:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:42:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:42:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:42:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [97f13db] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:42:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:42:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:42:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:42:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:42:46.938 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:42:46.938 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:42:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [97f13db] [3a2e29dc-47, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:42:46.939 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [97f13db] [3a2e29dc-47, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:42:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:42:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7b89d345]
2025-08-11 17:42:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:42:46.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [e009cab] [c8271542-37, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:42:46.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [e009cab] [c8271542-37, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:42:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:42:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:42:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:42:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:42:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:42:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:42:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:42:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:42:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:42:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:42:47.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:42:48.107 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:42:48.107 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:42:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:42:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:42:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:42:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:42:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:42:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:42:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:42:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:42:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:42:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:42:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:42:57.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:43:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [41c0439a] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:43:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:43:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:43:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:43:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:43:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1a8e8a9b]
2025-08-11 17:43:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:43:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [41c0439a] [3a2e29dc-48, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:43:06.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [41c0439a] [3a2e29dc-48, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:43:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:43:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:43:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:43:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:43:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:43:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:43:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:43:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:43:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:43:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:43:07.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:43:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:43:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:43:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:43:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:43:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:43:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:43:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:43:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:43:17.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:43:17.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:43:17.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:43:18.108 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:43:18.108 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:43:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1e0a37de] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:43:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:43:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:43:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:43:26.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:43:26.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@c961b49]
2025-08-11 17:43:26.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:43:26.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1e0a37de] [c8271542-38, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:43:26.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [1e0a37de] [c8271542-38, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:43:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:43:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:43:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:43:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:43:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:43:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:43:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:43:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:43:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:43:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:43:27.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:43:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:43:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:43:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:43:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:43:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:43:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:43:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:43:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:43:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:43:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:43:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:43:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [23f862c3] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:43:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:43:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:43:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:43:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:43:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@b2ce69a]
2025-08-11 17:43:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:43:46.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [23f862c3] [3a2e29dc-49, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:43:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [23f862c3] [3a2e29dc-49, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:43:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:43:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:43:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:43:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:43:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:43:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:43:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:43:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:43:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:43:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:43:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:43:48.109 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:43:48.109 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:43:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:43:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:43:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:43:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:43:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:43:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:43:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:43:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:43:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:43:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:43:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:43:57.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:44:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [19d0d177] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:44:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:44:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:44:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:44:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:44:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@73e15c6c]
2025-08-11 17:44:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:44:06.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [19d0d177] [c8271542-39, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:44:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [19d0d177] [c8271542-39, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:44:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:44:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:44:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:44:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:44:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:44:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:44:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:44:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:44:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:44:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:44:07.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:44:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:44:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:44:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:44:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:44:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:44:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:44:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:44:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:44:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:44:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:44:17.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:44:18.109 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:44:18.110 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:44:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [70c9455c] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:44:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:44:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:44:26.928 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:44:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:44:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@63a45eaf]
2025-08-11 17:44:26.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:44:26.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [70c9455c] [3a2e29dc-50, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:44:26.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [70c9455c] [3a2e29dc-50, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:44:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:44:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:44:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:44:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:44:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:44:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:44:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:44:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:44:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:44:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:44:27.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:44:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:44:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:44:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:44:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:44:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:44:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:44:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:44:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:44:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:44:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:44:37.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:44:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [d496aad] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:44:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:44:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:44:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:44:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [621f8cf8] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:44:46.936 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [621f8cf8] [3a2e29dc-51, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:44:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [621f8cf8] [3a2e29dc-51, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:44:46.992 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:44:46.992 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@94d5c47]
2025-08-11 17:44:46.992 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:44:46.992 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [d496aad] [c8271542-40, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:44:46.993 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [d496aad] [c8271542-40, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:44:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:44:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:44:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:44:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:44:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:44:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:44:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:44:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:44:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:44:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:44:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:44:48.110 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:44:48.110 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:44:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:44:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:44:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:44:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:44:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:44:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:44:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:44:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:44:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:44:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:44:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:44:57.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:45:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [17b49fa1] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:45:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:45:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:45:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:45:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:45:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@3f5bafcf]
2025-08-11 17:45:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:45:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [17b49fa1] [3a2e29dc-52, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:45:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [17b49fa1] [3a2e29dc-52, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:45:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:45:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:45:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:45:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:45:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:45:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:45:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:45:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:45:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:45:07.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:45:07.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:45:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:45:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:45:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:45:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:45:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:45:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:45:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:45:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:45:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:45:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:45:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:45:18.111 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:45:18.111 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:45:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6c533c31] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:45:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:45:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:45:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:45:26.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:45:26.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@413060aa]
2025-08-11 17:45:26.942 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:45:26.943 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6c533c31] [c8271542-41, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:45:26.943 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6c533c31] [c8271542-41, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:45:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:45:27.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:45:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:45:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:45:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:45:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:45:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:45:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:45:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:45:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:45:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:45:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:45:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:45:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:45:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:45:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:45:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:45:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:45:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:45:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:45:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:45:37.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:45:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [689f5ae3] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:45:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:45:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:45:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:45:47.001 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:45:47.001 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@3388aecb]
2025-08-11 17:45:47.001 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:45:47.001 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [689f5ae3] [3a2e29dc-53, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:45:47.002 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [689f5ae3] [3a2e29dc-53, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:45:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:45:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:45:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:45:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:45:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:45:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:45:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:45:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:45:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:45:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:45:47.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:45:48.112 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:45:48.112 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:45:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:45:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:45:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:45:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:45:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:45:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:45:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:45:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:45:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:45:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:45:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:45:57.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:46:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6a3d528a] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:46:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:46:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:46:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:46:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:46:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2c535e56]
2025-08-11 17:46:06.943 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:46:06.943 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6a3d528a] [c8271542-42, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:46:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6a3d528a] [c8271542-42, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:46:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:46:07.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:46:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:46:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:46:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:46:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:46:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:46:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:46:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:46:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:46:07.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:46:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:46:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:46:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:46:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:46:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:46:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:46:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:46:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:46:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:46:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:46:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:46:18.113 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:46:18.113 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:46:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5242be1d] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:46:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:46:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:46:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:46:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:46:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1c71acc6]
2025-08-11 17:46:26.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:46:26.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5242be1d] [3a2e29dc-54, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:46:26.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5242be1d] [3a2e29dc-54, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:46:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:46:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:46:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:46:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:46:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:46:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:46:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:46:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:46:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:46:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:46:27.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:46:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:46:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:46:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:46:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:46:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:46:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:46:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:46:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:46:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:46:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:46:37.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:46:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [40e85923] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:46:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:46:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:46:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:46:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [442a0c9f] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:46:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [442a0c9f] [3a2e29dc-55, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:46:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [442a0c9f] [3a2e29dc-55, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:46:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:46:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@8d4060f]
2025-08-11 17:46:46.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:46:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [40e85923] [c8271542-43, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:46:46.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [40e85923] [c8271542-43, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:46:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:46:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:46:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:46:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:46:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:46:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:46:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:46:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:46:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:46:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:46:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:46:48.114 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:46:48.114 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:46:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:46:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:46:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:46:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:46:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:46:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:46:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:46:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:46:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:46:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:46:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:46:57.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:47:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6ec72ac6] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:47:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:47:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:47:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:47:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:47:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@5f495211]
2025-08-11 17:47:06.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:47:06.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6ec72ac6] [3a2e29dc-56, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:47:06.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6ec72ac6] [3a2e29dc-56, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:47:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:47:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:47:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:47:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:47:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:47:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:47:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:47:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:47:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:47:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:47:07.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:47:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:47:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:47:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:47:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:47:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:47:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:47:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:47:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:47:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:47:17.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:47:17.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:47:18.115 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:47:18.115 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:47:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [14b37979] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:47:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:47:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:47:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:47:26.946 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:47:26.946 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@3998e524]
2025-08-11 17:47:26.946 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:47:26.946 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [14b37979] [c8271542-44, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:47:26.946 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [14b37979] [c8271542-44, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:47:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:47:27.821 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:47:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:47:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:47:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:47:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:47:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:47:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:47:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:47:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:47:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:47:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:47:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:47:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:47:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:47:37.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:47:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:47:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:47:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:47:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:47:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:47:37.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:47:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [3f9837da] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:47:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:47:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:47:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:47:46.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:47:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@6dc41210]
2025-08-11 17:47:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:47:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [3f9837da] [3a2e29dc-57, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:47:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [3f9837da] [3a2e29dc-57, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:47:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:47:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:47:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:47:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:47:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:47:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:47:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:47:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:47:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:47:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:47:47.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:47:48.116 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:47:48.116 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:47:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:47:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:47:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:47:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:47:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:47:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:47:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:47:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:47:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:47:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:47:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:47:57.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:48:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2ca0a846] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:48:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:48:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:48:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:48:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:48:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@430fa07e]
2025-08-11 17:48:06.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:48:06.944 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2ca0a846] [c8271542-45, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:48:06.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2ca0a846] [c8271542-45, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:48:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:48:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:48:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:48:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:48:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:48:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:48:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:48:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:48:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:48:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:48:07.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:48:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:48:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:48:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:48:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:48:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:48:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:48:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:48:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:48:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:48:17.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:48:17.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:48:18.116 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:48:18.117 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:48:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2d3940bb] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:48:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:48:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:48:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:48:26.946 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:48:26.946 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7a2624de]
2025-08-11 17:48:26.946 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:48:26.946 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2d3940bb] [3a2e29dc-58, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:48:26.946 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2d3940bb] [3a2e29dc-58, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:48:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:48:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:48:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:48:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:48:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:48:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:48:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:48:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:48:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:48:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:48:27.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:48:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:48:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:48:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:48:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:48:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:48:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:48:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:48:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:48:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:48:37.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:48:37.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:48:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6a65c3dd] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:48:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:48:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:48:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:48:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [61bd08d0] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:48:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:48:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:48:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:48:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:48:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:48:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:48:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [61bd08d0] [3a2e29dc-59, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:48:46.938 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [61bd08d0] [3a2e29dc-59, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:48:46.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:48:46.944 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1da25393]
2025-08-11 17:48:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:48:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6a65c3dd] [c8271542-46, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:48:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6a65c3dd] [c8271542-46, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:48:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:48:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:48:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:48:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:48:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:48:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:48:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:48:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:48:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:48:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:48:47.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:48:48.117 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:48:48.117 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:48:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:48:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:48:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:48:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:48:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:48:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:48:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:48:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:48:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:48:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:48:57.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:48:57.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:49:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4a88b35a] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:49:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:49:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:49:06.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:49:06.990 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:49:06.990 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@14e867f0]
2025-08-11 17:49:06.990 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:49:06.990 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4a88b35a] [3a2e29dc-60, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:49:06.991 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4a88b35a] [3a2e29dc-60, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:49:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:49:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:49:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:49:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:49:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:49:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:49:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:49:07.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:49:07.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:49:07.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:49:07.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:49:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:49:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:49:17.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:49:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:49:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:49:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:49:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:49:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:49:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:49:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:49:17.824 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:49:18.118 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:49:18.118 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:49:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1712761] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:49:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:49:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:49:26.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:49:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:49:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@cd6bcf8]
2025-08-11 17:49:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:49:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1712761] [c8271542-47, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:49:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [1712761] [c8271542-47, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:49:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:49:27.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:49:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:49:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:49:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:49:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:49:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:49:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:49:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:49:27.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:49:27.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:49:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:49:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:49:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:49:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:49:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:49:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:49:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:37.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:49:37.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:49:37.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:49:37.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:49:37.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:49:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [414a929e] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:49:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:49:46.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:49:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:49:46.960 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:49:46.960 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2c9532b2]
2025-08-11 17:49:46.960 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:49:46.960 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [414a929e] [3a2e29dc-61, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:49:46.961 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [414a929e] [3a2e29dc-61, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:49:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:49:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:49:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:49:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:49:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:49:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:49:47.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:49:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:49:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:49:47.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:49:47.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:49:48.119 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:49:48.119 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:49:57.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:49:57.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:49:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:49:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:49:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:49:57.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:49:57.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:49:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:49:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:49:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:49:57.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:49:57.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:50:06.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [68f4ca7] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:50:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:50:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:50:06.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:50:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:50:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7666b825]
2025-08-11 17:50:06.941 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:50:06.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [68f4ca7] [c8271542-48, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:50:06.942 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [68f4ca7] [c8271542-48, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:50:07.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:50:07.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:50:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:50:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:50:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:50:07.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:50:07.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:50:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:50:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:50:07.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:50:07.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:50:17.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:50:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:17.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:50:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:50:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:50:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:50:17.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:50:17.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:50:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:50:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:50:17.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:50:17.826 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:50:18.119 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:50:18.120 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]
2025-08-11 17:50:26.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [16878f7b] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:50:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:50:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:50:26.926 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:50:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:50:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@3a9321e8]
2025-08-11 17:50:26.940 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:50:26.940 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [16878f7b] [3a2e29dc-62, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:50:26.941 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [16878f7b] [3a2e29dc-62, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:50:27.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:50:27.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:50:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:50:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:50:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:50:27.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:50:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:50:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:50:27.826 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:50:27.827 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:50:27.827 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:50:37.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:50:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:37.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:50:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:50:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:50:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:50:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:50:37.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:50:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:50:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:50:37.825 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:50:37.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:50:46.925 | [39mDEBUG 24466[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [33c8c5a5] HTTP GET http://127.0.1.1:11000/actuator/health
2025-08-11 17:50:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-08-11 17:50:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-08-11 17:50:46.927 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:50:46.935 | [39mDEBUG 24466[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [755187] HTTP GET http://127.0.1.1:11000/actuator/info
2025-08-11 17:50:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-08-11 17:50:46.936 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-08-11 17:50:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-08-11 17:50:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:50:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-08-11 17:50:46.937 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:50:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [755187] [3a2e29dc-63, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:50:46.937 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-8 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [755187] [3a2e29dc-63, L:/127.0.0.1:44334 - R:127.0.1.1/127.0.1.1:11000] Decoded [{}]
2025-08-11 17:50:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-08-11 17:50:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7c5756d9]
2025-08-11 17:50:46.945 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-08-11 17:50:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [33c8c5a5] [c8271542-49, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Response 200 OK
2025-08-11 17:50:46.945 | [39mDEBUG 24466[0;39m | [1;33mreactor-http-epoll-7 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [33c8c5a5] [c8271542-49, L:/127.0.0.1:58772 - R:127.0.1.1/127.0.1.1:11000] Decoded [{status=UP}]
2025-08-11 17:50:47.819 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1.1:11000/actuator/health, serviceUrl=http://127.0.1.1:11000/)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-08-11 17:50:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:47.822 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.1.1:11000/actuator, healthUrl=http://127.0.1 (truncated)...]
2025-08-11 17:50:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-08-11 17:50:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-08-11 17:50:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-08-11 17:50:47.823 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-08-11 17:50:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-08-11 17:50:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=cdca45f9a050},[Location:"http://127.0.0.1:11000/instances/cdca45f9a050"]>]
2025-08-11 17:50:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-08-11 17:50:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=cdca45f9a050}]
2025-08-11 17:50:47.824 | [39mDEBUG 24466[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-08-11 17:50:47.825 | [39mDEBUG 24466[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-08-11 17:50:48.120 | [1;31mERROR 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | 连接被拒绝

java.net.ConnectException: 连接被拒绝
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1143)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1072)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-11 17:50:48.120 | [34m INFO 24466[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.1.167:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(连接被拒绝), for url : http://127.0.0.1:8018/xxl-job-admin/api/registry, content=null]

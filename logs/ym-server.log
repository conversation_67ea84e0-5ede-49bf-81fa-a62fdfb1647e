2025-08-04 12:18:56.078 | [34m INFO 1772098[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 12:18:56.080 | [34m INFO 1772098[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-04 12:19:03.603 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 255983 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by l<PERSON><PERSON><PERSON> in /home/<USER>/work/project/fozmo/ym)
2025-08-04 12:19:03.605 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 12:19:06.809 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 12:19:07.399 | [31m WARN 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:19:07.441 | [31m WARN 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:19:07.458 | [31m WARN 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:19:07.499 | [31m WARN 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:19:07.507 | [31m WARN 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:19:07.562 | [31m WARN 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 12:19:08.189 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 12:19:08.219 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 12:19:08.219 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 12:19:08.345 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 12:19:08.346 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4524 ms
2025-08-04 12:19:09.462 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 12:19:09.463 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 12:19:09.465 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 12:19:12.273 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 12:19:12.710 | [34m INFO 255983[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 12:19:13.204 | [34m INFO 255983[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 12:19:16.517 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 12:19:16.535 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@750c242e, ORIGINAL=[Ljava.lang.String;@3e0bbd36, PIC_CLICK=[Ljava.lang.String;@1e287867]
2025-08-04 12:19:16.537 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 12:19:16.542 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 12:19:16.542 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 12:19:18.862 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 12:19:19.342 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 12:19:19.823 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 12:19:20.061 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 12:19:20.072 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 12:19:20.084 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 12:19:20.257 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 12:19:20.258 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 12:19:20.258 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 12:19:20.258 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 12:19:20.258 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 12:19:20.258 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 12:19:25.032 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 12:19:25.067 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 12:19:25.269 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 12:19:25.337 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 12:19:25.794 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 12:19:26.168 | [34m INFO 255983[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 12:19:26.211 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 12:19:27.463 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@48821e3c[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 12:19:27.463 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@8d16b81[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 12:19:27.526 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 12:19:27.549 | [34m INFO 255983[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-04 12:19:27.624 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-04 12:19:27.707 | [34m INFO 255983[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 24.922 seconds (process running for 25.509)
2025-08-04 12:19:27.733 | [34m INFO 255983[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-04 12:19:28.049 | [34m INFO 255983[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 12:19:28.049 | [34m INFO 255983[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-04 12:19:28.053 | [34m INFO 255983[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 4 ms
2025-08-04 12:19:28.564 | [34m INFO 255983[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-08-04 12:59:04.914 | [34m INFO 255983[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 12:59:04.916 | [34m INFO 255983[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-04 12:59:09.396 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 309239 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 12:59:09.398 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 12:59:12.567 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 12:59:13.094 | [31m WARN 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:59:13.135 | [31m WARN 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:59:13.147 | [31m WARN 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:59:13.180 | [31m WARN 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:59:13.184 | [31m WARN 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 12:59:13.224 | [31m WARN 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 12:59:13.706 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 12:59:13.734 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 12:59:13.734 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 12:59:13.867 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 12:59:13.868 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4187 ms
2025-08-04 12:59:15.023 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 12:59:15.024 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 12:59:15.025 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 12:59:17.803 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 12:59:18.179 | [34m INFO 309239[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 12:59:18.669 | [34m INFO 309239[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 12:59:21.763 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 12:59:21.780 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@5e584389, ORIGINAL=[Ljava.lang.String;@42491839, PIC_CLICK=[Ljava.lang.String;@732af580]
2025-08-04 12:59:21.782 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 12:59:21.787 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 12:59:21.788 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 12:59:23.924 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 12:59:24.366 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 12:59:24.867 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 12:59:25.096 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 12:59:25.106 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 12:59:25.118 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 12:59:25.270 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 12:59:25.270 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 12:59:25.270 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 12:59:25.270 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 12:59:25.270 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 12:59:25.271 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 12:59:29.596 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 12:59:30.081 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 12:59:30.503 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 12:59:30.644 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 12:59:30.737 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 12:59:31.200 | [34m INFO 309239[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 12:59:31.249 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 12:59:32.600 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@8779d78[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 12:59:32.601 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3afbbcf[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 12:59:32.661 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 12:59:32.683 | [34m INFO 309239[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-04 12:59:32.754 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-04 12:59:32.833 | [34m INFO 309239[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 24.29 seconds (process running for 25.138)
2025-08-04 12:59:32.861 | [34m INFO 309239[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-04 12:59:33.185 | [34m INFO 309239[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 12:59:33.185 | [34m INFO 309239[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-04 12:59:33.188 | [34m INFO 309239[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-08-04 12:59:33.673 | [34m INFO 309239[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-08-04 14:00:52.290 | [34m INFO 309239[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 14:00:52.292 | [34m INFO 309239[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-04 14:02:13.978 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 389644 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:02:13.982 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:02:16.714 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 389815 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:02:16.718 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:02:19.008 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:02:19.824 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:19.920 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:19.951 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:20.033 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:20.045 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:20.151 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:02:21.090 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:02:21.123 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:02:21.124 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:02:21.298 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:02:21.300 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 6967 ms
2025-08-04 14:02:22.720 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:02:23.302 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:23.336 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:23.348 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:23.392 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:23.402 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:02:23.457 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:02:24.049 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:02:24.074 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:02:24.075 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:02:24.217 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:02:24.218 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 7116 ms
2025-08-04 14:02:27.728 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:02:27.729 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:02:27.729 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:02:27.730 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:02:27.730 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:02:27.732 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:02:32.588 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:02:32.804 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:02:33.053 | [34m INFO 389815[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:02:33.256 | [34m INFO 389644[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:02:33.589 | [34m INFO 389815[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:02:33.776 | [34m INFO 389644[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:02:35.702 | [31m WARN 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'spaceApiImpl': Injection of resource dependencies failed
2025-08-04 14:02:35.780 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:02:35.788 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:02:35.806 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:02:35.807 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:02:35.807 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:02:35.832 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:02:35.890 | [34m INFO 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:02:35.969 | [1;31mERROR 389815[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.api.NoticeMessageApi' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.api.NoticeMessageApi' in your configuration.

2025-08-04 14:02:36.076 | [31m WARN 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'spaceApiImpl': Injection of resource dependencies failed
2025-08-04 14:02:36.146 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:02:36.153 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:02:36.173 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:02:36.174 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:02:36.174 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:02:36.194 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:02:36.250 | [34m INFO 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:02:36.357 | [1;31mERROR 389644[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.api.NoticeMessageApi' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.api.NoticeMessageApi' in your configuration.

2025-08-04 14:03:54.553 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 392106 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:03:54.555 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:03:57.732 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:03:58.253 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:03:58.293 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:03:58.305 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:03:58.337 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:03:58.341 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:03:58.378 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:03:58.821 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:03:58.841 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:03:58.842 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:03:58.957 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:03:58.958 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4085 ms
2025-08-04 14:03:59.891 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:03:59.892 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:03:59.893 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:04:02.396 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:04:02.748 | [34m INFO 392106[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:04:03.220 | [34m INFO 392106[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:04:04.534 | [31m WARN 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'spaceApiImpl': Injection of resource dependencies failed
2025-08-04 14:04:04.563 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:04:04.568 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:04:04.578 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:04:04.578 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:04:04.578 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:04:04.589 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:04:04.630 | [34m INFO 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:04:04.666 | [1;31mERROR 392106[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.api.NoticeMessageApi' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.api.NoticeMessageApi' in your configuration.

2025-08-04 14:07:59.670 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 398201 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:07:59.677 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:08:02.362 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 398368 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:08:02.364 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:08:05.761 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:08:07.056 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:07.118 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:07.148 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:07.242 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:07.265 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:07.410 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:08:08.475 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:08:08.709 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 8559 ms
2025-08-04 14:08:09.740 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:08:10.060 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:08:10.061 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:08:10.063 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:08:10.328 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:10.407 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:10.440 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:10.520 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:10.531 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:08:10.628 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:08:11.600 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:08:11.651 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:08:11.653 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:08:11.850 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:08:11.851 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 8907 ms
2025-08-04 14:08:13.196 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:08:13.197 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:08:13.199 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:08:15.020 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:08:15.528 | [34m INFO 398201[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:08:16.092 | [34m INFO 398201[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:08:16.807 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:08:17.232 | [34m INFO 398368[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:08:17.816 | [34m INFO 398368[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:08:19.554 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:08:19.570 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@65af4162, ORIGINAL=[Ljava.lang.String;@46b0fb24, PIC_CLICK=[Ljava.lang.String;@13dabbb9]
2025-08-04 14:08:19.572 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:08:19.578 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:08:19.579 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:08:19.678 | [1;31mERROR 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | load font error:

java.io.IOException: Problem reading font data.
	at java.desktop/java.awt.Font.createFont0(Font.java:1205)
	at java.desktop/java.awt.Font.createFont(Font.java:1076)
	at com.anji.captcha.service.impl.AbstractCaptchaService.loadWaterMarkFont(AbstractCaptchaService.java:204)
	at com.anji.captcha.service.impl.AbstractCaptchaService.init(AbstractCaptchaService.java:93)
	at com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl.init(BlockPuzzleCaptchaServiceImpl.java:34)
	at com.anji.captcha.service.impl.CaptchaServiceFactory.getInstance(CaptchaServiceFactory.java:36)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration.captchaService(AjCaptchaServiceAutoConfiguration.java:67)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.CGLIB$captchaService$0(<generated>)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.captchaService(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:468)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:606)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.fozmo.ym.server.YmServerApplication.main(YmServerApplication.java:13)

2025-08-04 14:08:20.454 | [31m WARN 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'noticeConsumer': Injection of resource dependencies failed
2025-08-04 14:08:20.473 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:08:20.473 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:08:20.498 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:08:20.500 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:08:20.505 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:08:20.505 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:08:20.505 | [34m INFO 398201[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:08:21.290 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:08:21.307 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@3217aada, ORIGINAL=[Ljava.lang.String;@34f14035, PIC_CLICK=[Ljava.lang.String;@72efba55]
2025-08-04 14:08:21.310 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:08:21.316 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:08:21.316 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:08:22.523 | [31m WARN 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'noticeConsumer': Injection of resource dependencies failed
2025-08-04 14:08:22.523 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:08:22.523 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:08:22.552 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:08:22.556 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:08:22.566 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:08:22.566 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:08:22.566 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:08:22.579 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:08:22.614 | [34m INFO 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:08:22.649 | [1;31mERROR 398368[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' in your configuration.

2025-08-04 14:09:56.692 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 401327 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:09:56.694 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:10:00.241 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 401542 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:10:00.250 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:10:02.047 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:10:02.605 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:02.646 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:02.660 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:02.720 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:02.732 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:02.789 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:10:03.474 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:10:03.510 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:10:03.511 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:10:03.658 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:10:03.659 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 6706 ms
2025-08-04 14:10:05.010 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:10:05.011 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:10:05.014 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:10:05.702 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:10:06.775 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:06.871 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:06.903 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:06.990 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:07.000 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:10:07.145 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:10:08.070 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:10:08.112 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:10:08.114 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:10:08.287 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:10:08.288 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 7600 ms
2025-08-04 14:10:09.818 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:10:10.155 | [34m INFO 401327[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:10:10.612 | [34m INFO 401327[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:10:13.814 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:10:13.834 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@3217aada, ORIGINAL=[Ljava.lang.String;@34f14035, PIC_CLICK=[Ljava.lang.String;@72efba55]
2025-08-04 14:10:13.836 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:10:13.842 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:10:13.842 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:10:14.385 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:10:14.385 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:10:14.387 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:10:15.036 | [31m WARN 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'noticeConsumer': Injection of resource dependencies failed
2025-08-04 14:10:15.036 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:10:15.037 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:10:15.086 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:10:15.092 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:10:15.106 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:10:15.106 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:10:15.106 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:10:15.126 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:10:15.189 | [34m INFO 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:10:15.257 | [1;31mERROR 401327[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' in your configuration.

2025-08-04 14:10:17.981 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:10:18.352 | [34m INFO 401542[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:10:18.826 | [34m INFO 401542[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:10:22.006 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:10:22.024 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@6aa2ffd3, ORIGINAL=[Ljava.lang.String;@12e7664b, PIC_CLICK=[Ljava.lang.String;@5c77cb42]
2025-08-04 14:10:22.026 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:10:22.032 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:10:22.032 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:10:23.611 | [31m WARN 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'noticeConsumer': Injection of resource dependencies failed
2025-08-04 14:10:23.611 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:10:23.611 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:10:23.640 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:10:23.643 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:10:23.651 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:10:23.651 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:10:23.651 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:10:23.662 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:10:23.697 | [34m INFO 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:10:23.732 | [1;31mERROR 401542[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' in your configuration.

2025-08-04 14:26:05.296 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 421817 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:26:05.305 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:26:07.578 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 421984 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:26:07.581 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:26:10.103 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:26:11.035 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:11.134 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:11.170 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:11.279 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:11.288 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:11.393 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:26:12.481 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:26:12.825 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 7154 ms
2025-08-04 14:26:14.168 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:26:14.986 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:15.039 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:15.055 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:15.096 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:15.102 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:26:15.150 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:26:15.769 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:26:15.792 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:26:15.793 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:26:15.918 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:26:15.918 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 8056 ms
2025-08-04 14:26:19.499 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:26:19.500 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:26:19.501 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:26:19.505 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:26:19.505 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:26:19.507 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:26:24.237 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:26:24.242 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:26:24.674 | [34m INFO 421817[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:26:24.708 | [34m INFO 421984[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:26:25.244 | [34m INFO 421817[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:26:25.260 | [34m INFO 421984[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:26:28.645 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:26:28.662 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@35fe64ca, ORIGINAL=[Ljava.lang.String;@74d6d08a, PIC_CLICK=[Ljava.lang.String;@3217aada]
2025-08-04 14:26:28.664 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:26:28.668 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:26:28.668 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:26:28.774 | [1;31mERROR 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | load font error:

java.io.IOException: Problem reading font data.
	at java.desktop/java.awt.Font.createFont0(Font.java:1205)
	at java.desktop/java.awt.Font.createFont(Font.java:1076)
	at com.anji.captcha.service.impl.AbstractCaptchaService.loadWaterMarkFont(AbstractCaptchaService.java:204)
	at com.anji.captcha.service.impl.AbstractCaptchaService.init(AbstractCaptchaService.java:93)
	at com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl.init(BlockPuzzleCaptchaServiceImpl.java:34)
	at com.anji.captcha.service.impl.CaptchaServiceFactory.getInstance(CaptchaServiceFactory.java:36)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration.captchaService(AjCaptchaServiceAutoConfiguration.java:67)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.CGLIB$captchaService$0(<generated>)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.anji.captcha.config.AjCaptchaServiceAutoConfiguration$$SpringCGLIB$$0.captchaService(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:468)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:606)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.fozmo.ym.server.YmServerApplication.main(YmServerApplication.java:13)

2025-08-04 14:26:29.140 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:26:29.167 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@38e10ff0, ORIGINAL=[Ljava.lang.String;@30df67b8, PIC_CLICK=[Ljava.lang.String;@58d8059b]
2025-08-04 14:26:29.171 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:26:29.180 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:26:29.180 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:26:29.641 | [31m WARN 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'noticeConsumer': Injection of resource dependencies failed
2025-08-04 14:26:29.642 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:26:29.644 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:26:29.679 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:26:29.681 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:26:29.688 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:26:29.688 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:26:29.688 | [34m INFO 421817[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:26:30.383 | [31m WARN 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'noticeConsumer': Injection of resource dependencies failed
2025-08-04 14:26:30.397 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:26:30.397 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:26:30.432 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:26:30.436 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:26:30.444 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:26:30.444 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:26:30.444 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:26:30.457 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Stopping service [Tomcat]
2025-08-04 14:26:30.496 | [34m INFO 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:26:30.533 | [1;31mERROR 421984[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m | 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' that could not be found.


Action:

Consider defining a bean of type 'com.fozmo.ym.module.notice.framwork.client.NoticeClient' in your configuration.

2025-08-04 14:34:05.888 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 432363 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:34:05.895 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:34:08.278 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 432525 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:34:08.283 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:34:11.148 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:34:12.136 | [31m WARN 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:12.188 | [31m WARN 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:12.209 | [31m WARN 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:12.293 | [31m WARN 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:12.301 | [31m WARN 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:12.368 | [31m WARN 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:34:13.180 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:34:13.211 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:34:13.212 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:34:13.399 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:34:13.399 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 7165 ms
2025-08-04 14:34:14.259 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:34:14.973 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:15.003 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:15.016 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:15.047 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:15.052 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:15.117 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:34:15.711 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:34:15.731 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:34:15.732 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:34:15.820 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:34:15.821 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 7147 ms
2025-08-04 14:34:20.001 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:34:20.002 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:34:20.002 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:34:20.003 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:34:20.003 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:34:20.004 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:34:24.645 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:34:24.650 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:34:25.135 | [34m INFO 432363[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:34:25.140 | [34m INFO 432525[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:34:25.617 | [34m INFO 432363[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:34:25.628 | [34m INFO 432525[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:34:29.443 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:34:29.459 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:34:29.462 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@6aa2ffd3, ORIGINAL=[Ljava.lang.String;@12e7664b, PIC_CLICK=[Ljava.lang.String;@5c77cb42]
2025-08-04 14:34:29.464 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:34:29.471 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:34:29.471 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:34:29.478 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@3dddef98, ORIGINAL=[Ljava.lang.String;@1e9bc8, PIC_CLICK=[Ljava.lang.String;@28b992e0]
2025-08-04 14:34:29.481 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:34:29.487 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:34:29.487 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:34:30.512 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-04 14:34:30.590 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-04 14:34:32.275 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 14:34:32.364 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 14:34:32.811 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 14:34:32.876 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 14:34:33.352 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 14:34:33.433 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 14:34:33.617 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 14:34:33.629 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 14:34:33.642 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 14:34:33.694 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 14:34:33.705 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 14:34:33.718 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 14:34:33.810 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:34:33.811 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:34:33.811 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:34:33.811 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:34:33.811 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:34:33.811 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:34:33.811 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:34:33.856 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:34:33.899 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:34:33.900 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:34:33.900 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:34:33.900 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:34:33.900 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:34:33.900 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:34:33.900 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:34:38.468 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:34:38.817 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:34:38.817 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:34:38.904 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:34:38.934 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:34:39.025 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:34:39.202 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:34:39.204 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:34:39.205 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:34:39.206 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:34:39.546 | [34m INFO 432525[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:34:39.789 | [34m INFO 432363[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:34:39.829 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 14:34:39.947 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:34:40.005 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 14:34:41.426 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@74e506a8[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 14:34:41.427 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2f8b4c20[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 14:34:41.508 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 14:34:41.533 | [34m INFO 432363[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-04 14:34:41.640 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-04 14:34:41.662 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e3d6d79[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 14:34:41.663 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@db70c9b[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 14:34:41.738 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 14:34:41.738 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[10000] is in use.
2025-08-04 14:34:41.747 | [34m INFO 432363[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 36.832 seconds (process running for 37.441)
2025-08-04 14:34:41.786 | [34m INFO 432525[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10001
2025-08-04 14:34:41.795 | [34m INFO 432363[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-04 14:34:41.904 | [31m WARN 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-04 14:34:41.920 | [34m INFO 432525[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-08-04 14:34:41.933 | [34m INFO 432525[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.110.54:10001/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-04 14:34:41.934 | [34m INFO 432525[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-04 14:34:41.934 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-04 14:34:41.934 | [34m INFO 432525[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-08-04 14:34:41.934 | [34m INFO 432525[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-04 14:34:41.935 | [34m INFO 432525[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-04 14:34:41.939 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:34:41.940 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:34:42.026 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:34:42.031 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:34:42.039 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:34:42.039 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:34:42.039 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:34:42.086 | [34m INFO 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.s.b.a.l.ConditionEvaluationReportLogger[0;39m | 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:34:42.123 | [1;31mERROR 432525[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Application run failed

org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:408)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:394)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:586)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:364)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:310)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:1006)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:630)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.fozmo.ym.server.YmServerApplication.main(YmServerApplication.java:13)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat server
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:251)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:44)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:405)
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: standardService.connector.startFailed
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:222)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.addPreviouslyRemovedConnectors(TomcatWebServer.java:310)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:236)
	... 16 common frames omitted
Caused by: org.apache.catalina.LifecycleException: Protocol handler start failed
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1106)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:219)
	... 18 common frames omitted
Caused by: java.net.BindException: 地址已在使用
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:555)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
	at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:266)
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:221)
	at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1399)
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:1482)
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:644)
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1103)
	... 20 common frames omitted

2025-08-04 14:34:42.189 | [34m INFO 432363[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 14:34:42.189 | [34m INFO 432363[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-04 14:34:42.193 | [34m INFO 432363[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-08-04 14:34:42.707 | [34m INFO 432363[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-08-04 14:34:47.700 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 14:34:47.702 | [34m INFO 432363[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-04 14:34:49.740 | [34m INFO 432363[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-08-04 14:34:49.751 | [34m INFO 432363[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.110.54:10000/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-04 14:34:49.752 | [34m INFO 432363[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-04 14:34:49.752 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-04 14:34:49.753 | [34m INFO 432363[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-08-04 14:34:49.753 | [34m INFO 432363[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-04 14:34:49.753 | [34m INFO 432363[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-04 14:34:49.762 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:34:49.763 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:34:49.814 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:34:49.816 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:34:49.825 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:34:49.825 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:34:49.825 | [34m INFO 432363[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:34:51.699 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 434027 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:34:51.701 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:34:54.837 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:34:55.365 | [31m WARN 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:55.395 | [31m WARN 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:55.412 | [31m WARN 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:55.456 | [31m WARN 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:55.461 | [31m WARN 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:34:55.511 | [31m WARN 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:34:56.082 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:34:56.114 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:34:56.114 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:34:56.251 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:34:56.252 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4298 ms
2025-08-04 14:34:57.374 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:34:57.374 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:34:57.376 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:35:00.074 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:35:00.411 | [34m INFO 434027[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:35:01.012 | [34m INFO 434027[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:35:04.123 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:35:04.141 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@6969e079, ORIGINAL=[Ljava.lang.String;@4dd139e0, PIC_CLICK=[Ljava.lang.String;@4f63343b]
2025-08-04 14:35:04.143 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:35:04.149 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:35:04.149 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:35:05.104 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-04 14:35:06.501 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 14:35:06.953 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 14:35:07.535 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 14:35:07.839 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 14:35:07.853 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 14:35:07.867 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 14:35:08.043 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:35:08.044 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:35:08.044 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:35:08.044 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:35:08.044 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:35:08.044 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:35:08.044 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:35:12.178 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:35:12.607 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:35:13.344 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:35:13.422 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:35:13.665 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:35:13.827 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:35:13.883 | [34m INFO 434027[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:35:13.928 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 14:35:15.838 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@25123ce4[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 14:35:15.839 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a234d98[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 14:35:15.913 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 14:35:15.933 | [34m INFO 434027[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-04 14:35:16.007 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-04 14:35:16.099 | [34m INFO 434027[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 25.515 seconds (process running for 26.074)
2025-08-04 14:35:16.124 | [34m INFO 434027[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-04 14:35:16.447 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 14:35:16.447 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-04 14:35:16.450 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-08-04 14:35:16.929 | [34m INFO 434027[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-08-04 14:35:19.282 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{content-length=25, ym-token=test4, host=127.0.0.1:11000, content-type=application/json, connection=keep-alive, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=Apifox/1.0.0 (https://apifox.com), accept=*/*},请求体：{
    "spaceId": 301773
}
2025-08-04 14:35:19.662 | [1;31mERROR 434027[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.c.e.util.ServiceExceptionUtil   [0;39m | 错误码(401000)|错误内容(访问令牌不存在)|参数([])
2025-08-04 14:35:19.733 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/social/like/create) 参数({
    "spaceId": 301773
})]
2025-08-04 14:35:20.167 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.m.notice.api.NoticeMessageApiImpl [0;39m | createMessage:NoticeMessageDTO(channelId=1, templateId=1, params=[4, 20250725我的第211个测试空间], sendResource=spaceLike, sendApp=ym, toUser=[23728])
2025-08-04 14:35:20.198 | [34m INFO 434027[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/social/like/create) 耗时(462 ms)]
2025-08-04 14:35:20.234 | [34m INFO 434027[0;39m | [1;33mSimpleAsyncTaskExecutor-1 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.i.NoticeClientFactoryImpl [0;39m | Created notice client for channel: 1
2025-08-04 14:35:20.387 | [1;31mERROR 434027[0;39m | [1;33mSimpleAsyncTaskExecutor-1 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.impl.DefaultNoticeClient  [0;39m | Failed to send notice via channelId: 1

jakarta.validation.ConstraintViolationException: createMessage.createReqVO.sendContent: 发送内容不能为空, createMessage.createReqVO.sendTime: 发送时间不能为空
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:170)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.fozmo.ym.module.notice.service.message.NoticeMessageServiceImpl$$SpringCGLIB$$0.createMessage(<generated>)
	at com.fozmo.ym.module.notice.framwork.client.impl.NoticeSystemClient.sendNoticeMessage(NoticeSystemClient.java:116)
	at com.fozmo.ym.module.notice.framwork.client.impl.NoticeSystemClient.sendNotice(NoticeSystemClient.java:58)
	at com.fozmo.ym.module.notice.framwork.client.impl.DefaultNoticeClient.sendNotice(DefaultNoticeClient.java:48)
	at com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer.processMessage(NoticeConsumer.java:38)
	at com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer.onMessage(NoticeConsumer.java:31)
	at com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer.onMessage(NoticeConsumer.java:14)
	at com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener.onMessage(AbstractRedisStreamMessageListener.java:58)
	at com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener.onMessage(AbstractRedisStreamMessageListener.java:19)
	at org.springframework.data.redis.stream.StreamPollTask.deserializeAndEmitRecords(StreamPollTask.java:157)
	at org.springframework.data.redis.stream.StreamPollTask.doLoop(StreamPollTask.java:128)
	at org.springframework.data.redis.stream.StreamPollTask.run(StreamPollTask.java:112)
	at java.base/java.lang.Thread.run(Thread.java:840)

2025-08-04 14:35:20.392 | [1;31mERROR 434027[0;39m | [1;33mSimpleAsyncTaskExecutor-1 [TID: N/A][0;39m [1;32mr.c.s.AbstractRedisStreamMessageListener[0;39m | redis 消息队列异常| 队列名称=class com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage | stream=NoticeSendMessage id=1754289320160-0 error=jakarta.validation.ConstraintViolationException: createMessage.createReqVO.sendContent: 发送内容不能为空, createMessage.createReqVO.sendTime: 发送时间不能为空
2025-08-04 14:35:35.098 | [34m INFO 434027[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@434027) 消息数量(1)]
2025-08-04 14:36:35.071 | [34m INFO 434027[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@434027) 消息数量(1)]
2025-08-04 14:37:35.077 | [34m INFO 434027[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@434027) 消息数量(1)]
2025-08-04 14:37:58.596 | [34m INFO 434027[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 14:37:58.597 | [34m INFO 434027[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-04 14:38:03.162 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 438288 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:38:03.163 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:38:06.870 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:38:07.413 | [31m WARN 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:38:07.452 | [31m WARN 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:38:07.469 | [31m WARN 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:38:07.509 | [31m WARN 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:38:07.515 | [31m WARN 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:38:07.566 | [31m WARN 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:38:08.252 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:38:08.283 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:38:08.284 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:38:08.438 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:38:08.439 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 5051 ms
2025-08-04 14:38:14.459 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:38:14.460 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:38:14.461 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:38:17.256 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:38:17.596 | [34m INFO 438288[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:38:18.042 | [34m INFO 438288[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:38:21.611 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:38:21.658 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@6969e079, ORIGINAL=[Ljava.lang.String;@4dd139e0, PIC_CLICK=[Ljava.lang.String;@4f63343b]
2025-08-04 14:38:21.677 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:38:21.688 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:38:21.688 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:38:22.954 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-04 14:38:24.388 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 14:38:24.845 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 14:38:25.370 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 14:38:25.619 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 14:38:25.631 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 14:38:25.645 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 14:38:25.798 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:38:25.799 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:38:25.799 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:38:25.799 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:38:25.799 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:38:25.799 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:38:25.799 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:38:31.003 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:38:31.003 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:38:31.068 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:38:31.108 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:38:31.244 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:38:31.409 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:38:31.465 | [34m INFO 438288[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:38:31.508 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 14:38:32.784 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@15f67a4f[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 14:38:32.785 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@75d9aae4[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 14:38:32.851 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 14:38:32.875 | [34m INFO 438288[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-04 14:38:32.962 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-04 14:38:33.058 | [34m INFO 438288[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 30.727 seconds (process running for 31.471)
2025-08-04 14:38:33.087 | [34m INFO 438288[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-04 14:38:33.487 | [34m INFO 438288[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 14:38:33.487 | [34m INFO 438288[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-04 14:38:33.490 | [34m INFO 438288[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-08-04 14:38:34.052 | [34m INFO 438288[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-08-04 14:39:04.648 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 14:39:04.650 | [34m INFO 438288[0;39m | [1;33mtomcat-shutdown [TID: N/A][0;39m [1;32mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m | Graceful shutdown complete
2025-08-04 14:39:06.676 | [34m INFO 438288[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server stop.
2025-08-04 14:39:06.690 | [34m INFO 438288[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='ym-server', registryValue='http://192.168.110.54:10000/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-04 14:39:06.691 | [34m INFO 438288[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.x.j.c.thread.ExecutorRegistryThread   [0;39m | >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-04 14:39:06.691 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-04 14:39:06.691 | [34m INFO 438288[0;39m | [1;33mxxl-job, executor JobLogFileCleanThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.JobLogFileCleanThread [0;39m | >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-08-04 14:39:06.691 | [34m INFO 438288[0;39m | [1;33mxxl-job, executor TriggerCallbackThread [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-04 14:39:06.691 | [34m INFO 438288[0;39m | [1;33mThread-5 [TID: N/A][0;39m [1;32mc.x.j.core.thread.TriggerCallbackThread [0;39m | >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-04 14:39:06.695 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.works.WorksDeleteConsumer [0;39m | Shutting down works delete executor
2025-08-04 14:39:06.695 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.f.y.m.s.c.m.space.SpaceDeleteConsumer [0;39m | Shutting down space delete executor
2025-08-04 14:39:06.714 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-08-04 14:39:06.716 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-08-04 14:39:06.720 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-08-04 14:39:06.720 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-08-04 14:39:06.720 | [34m INFO 438288[0;39m | [1;33mSpringApplicationShutdownHook [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-08-04 14:39:10.499 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Starting YmServerApplication using Java 17.0.15 with PID 440075 (/home/<USER>/work/project/fozmo/ym/ym-web/target/classes started by liuqian in /home/<USER>/work/project/fozmo/ym)
2025-08-04 14:39:10.501 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | The following 1 profile is active: "test"
2025-08-04 14:39:13.724 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-04 14:39:14.261 | [31m WARN 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:39:14.302 | [31m WARN 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:39:14.316 | [31m WARN 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:39:14.351 | [31m WARN 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration' of type [com.fozmo.ym.framework.datapermission.config.YmDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:39:14.356 | [31m WARN 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'dataPermissionAnnotationAdvisor' of type [com.fozmo.ym.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-04 14:39:14.404 | [31m WARN 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration' of type [com.fozmo.ym.framework.quartz.config.YmAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04 14:39:14.989 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port 11000 (http)
2025-08-04 14:39:15.015 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-08-04 14:39:15.016 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.apache.catalina.core.StandardEngine   [0;39m | Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 14:39:15.144 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring embedded WebApplicationContext
2025-08-04 14:39:15.145 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4415 ms
2025-08-04 14:39:16.580 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-08-04 14:39:16.581 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-08-04 14:39:16.583 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-04 14:39:19.168 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.49.0
2025-08-04 14:39:19.515 | [34m INFO 440075[0;39m | [1;33mredisson-netty-1-6 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for *************/*************:6379
2025-08-04 14:39:19.930 | [34m INFO 440075[0;39m | [1;33mredisson-netty-1-19 [TID: N/A][0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for *************/*************:6379
2025-08-04 14:39:23.056 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32m.a.c.c.AjCaptchaServiceAutoConfiguration[0;39m | 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='classpath:images/jigsaw', picClick='classpath:images/pic-click', waterMark='元美', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-04 14:39:23.074 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.anji.captcha.util.ImageUtils        [0;39m | 自定义resource底图:[SLIDING_BLOCK=[Ljava.lang.String;@32561cd5, ORIGINAL=[Ljava.lang.String;@78ef5801, PIC_CLICK=[Ljava.lang.String;@6969e079]
2025-08-04 14:39:23.077 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaCache-service:[local]
2025-08-04 14:39:23.082 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.impl.CaptchaServiceFactory      [0;39m | supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-04 14:39:23.082 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.a.c.s.i.BlockPuzzleCaptchaServiceImpl [0;39m | --->>>初始化验证码底图<<<---blockPuzzle
2025-08-04 14:39:24.068 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.NoticeClientConfiguration [0;39m | Creating primary NoticeClient bean
2025-08-04 14:39:25.471 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.a.e.web.EndpointLinksResolver     [0;39m | Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 14:39:25.907 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.e.s.MybatisPlusApplicationContextAware[0;39m | Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5baaae4c
2025-08-04 14:39:26.408 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fhs.common.spring.SpringContextUtil   [0;39m | ------SpringContextUtil setApplicationContext-------
2025-08-04 14:39:26.635 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.dict.core.DictFrameworkUtils    [0;39m | [init][初始化 DictFrameworkUtils 成功]
2025-08-04 14:39:26.645 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.j.c.YmJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-08-04 14:39:26.656 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisMessageListenerContainer][注册 Channel(SpaceRedisWebSocketMessage) 对应的监听器(com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer)]
2025-08-04 14:39:26.804 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:39:26.805 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:39:26.805 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:39:26.805 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:39:26.805 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:39:26.805 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:39:26.805 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][开始注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:39:31.494 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.WorksCommentConsumer)]
2025-08-04 14:39:31.864 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-1 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(WorksDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.works.WorksDeleteConsumer)]
2025-08-04 14:39:32.408 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-4 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SocialUserMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.social.SocialUserConsumer)]
2025-08-04 14:39:32.430 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-2 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(NoticeSendMessage) 对应的监听器(com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer)]
2025-08-04 14:39:32.483 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-5 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceDeleteMessage) 对应的监听器(com.fozmo.ym.module.space.core.mq.space.SpaceDeleteConsumer)]
2025-08-04 14:39:32.700 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-6 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SmsSendMessage) 对应的监听器(com.fozmo.ym.module.sms.core.mq.sms.consumer.SmsSendConsumer)]
2025-08-04 14:39:33.184 | [34m INFO 440075[0;39m | [1;33mForkJoinPool.commonPool-worker-3 [TID: N/A][0;39m [1;32mm.r.c.YmRedisMQConsumerAutoConfiguration[0;39m | [redisStreamMessageListenerContainer][完成注册 StreamKey(SpaceCommentMessage) 对应的监听器(com.fozmo.ym.module.social.core.mq.comment.SpaceCommentConsumer)]
2025-08-04 14:39:33.227 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.f.y.f.q.c.YmXxlJobAutoConfiguration   [0;39m | [xxlJobExecutor][初始化 XXL-Job 执行器的配置]
2025-08-04 14:39:34.499 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:spaceCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60b4dacc[class com.fozmo.ym.module.social.core.job.comment.SyncSpaceCommentTask$$SpringCGLIB$$0#spaceCommentSyncToDB]
2025-08-04 14:39:34.500 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.xxl.job.core.executor.XxlJobExecutor  [0;39m | >>>>>>>>>>> xxl-job register jobhandler success, name:worksCommentSyncToDB, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71e28a63[class com.fozmo.ym.module.social.core.job.comment.SyncWorksCommentTask$$SpringCGLIB$$0#worksCommentSyncToDB]
2025-08-04 14:39:34.571 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mcom.xxl.job.core.util.NetUtil           [0;39m | >>>>>>>>>>> xxl-job, port[9999] is in use.
2025-08-04 14:39:34.596 | [34m INFO 440075[0;39m | [1;33mThread-6 [TID: N/A][0;39m [1;32mcom.xxl.job.core.server.EmbedServer     [0;39m | >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2025-08-04 14:39:34.670 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port 11000 (http) with context path '/'
2025-08-04 14:39:34.756 | [34m INFO 440075[0;39m | [1;33mmain [TID: N/A][0;39m [1;32mc.fozmo.ym.server.YmServerApplication   [0;39m | Started YmServerApplication in 25.105 seconds (process running for 25.633)
2025-08-04 14:39:34.778 | [34m INFO 440075[0;39m | [1;33mpool-10-thread-1 [TID: N/A][0;39m [1;32mc.f.y.f.b.core.BannerApplicationRunner  [0;39m | 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://127.0.0.1:11000/doc.html/ 
	----------------------------------------------------------
2025-08-04 14:39:35.084 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@434027) 消息数量(1)]
2025-08-04 14:39:35.096 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 14:39:35.096 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-08-04 14:39:35.099 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 3 ms
2025-08-04 14:39:35.570 | [34m INFO 440075[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32md.c.b.a.c.r.ApplicationRegistrator      [0;39m | Application registered itself as e50ef2fda688
2025-08-04 14:39:39.647 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{content-length=25, ym-token=test4, host=127.0.0.1:11000, content-type=application/json, connection=keep-alive, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=Apifox/1.0.0 (https://apifox.com), accept=*/*},请求体：{
    "spaceId": 314777
}
2025-08-04 14:39:40.100 | [1;31mERROR 440075[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.c.e.util.ServiceExceptionUtil   [0;39m | 错误码(401000)|错误内容(访问令牌不存在)|参数([])
2025-08-04 14:39:40.175 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/social/like/create) 参数({
    "spaceId": 314777
})]
2025-08-04 14:39:40.525 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.m.notice.api.NoticeMessageApiImpl [0;39m | createMessage:NoticeMessageDTO(channelId=1, templateId=1, params=[4, 20250725我的第331个测试空间], sendResource=spaceLike, sendApp=ym, toUser=[23728])
2025-08-04 14:39:40.558 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/social/like/create) 耗时(381 ms)]
2025-08-04 14:39:40.598 | [34m INFO 440075[0;39m | [1;33mSimpleAsyncTaskExecutor-4 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.i.NoticeClientFactoryImpl [0;39m | Created notice client for channel: 1
2025-08-04 14:39:40.947 | [1;31mERROR 440075[0;39m | [1;33mSimpleAsyncTaskExecutor-4 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.impl.DefaultNoticeClient  [0;39m | Failed to send notice via channelId: 1

org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:254)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:149)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:130)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:118)
	at com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils.execute(MybatisBatchUtils.java:175)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.insert(BaseMapper.java:511)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:732)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy271.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.toolkit.Db.lambda$saveBatch$223719e8$1(Db.java:89)
	at com.baomidou.mybatisplus.extension.toolkit.SqlHelper.execute(SqlHelper.java:323)
	at com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch(Db.java:89)
	at com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch(Db.java:75)
	at com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX.insertBatch(BaseMapperX.java:179)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:732)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy271.insertBatch(Unknown Source)
	at com.fozmo.ym.module.notice.service.message.NoticeMessageServiceImpl.createMessage(NoticeMessageServiceImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.fozmo.ym.module.notice.service.message.NoticeMessageServiceImpl$$SpringCGLIB$$0.createMessage(<generated>)
	at com.fozmo.ym.module.notice.framwork.client.impl.NoticeSystemClient.sendNoticeMessage(NoticeSystemClient.java:116)
	at com.fozmo.ym.module.notice.framwork.client.impl.NoticeSystemClient.sendNotice(NoticeSystemClient.java:56)
	at com.fozmo.ym.module.notice.framwork.client.impl.DefaultNoticeClient.sendNotice(DefaultNoticeClient.java:48)
	at com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer.processMessage(NoticeConsumer.java:38)
	at com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer.onMessage(NoticeConsumer.java:31)
	at com.fozmo.ym.module.notice.framwork.mq.NoticeConsumer.onMessage(NoticeConsumer.java:14)
	at com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener.onMessage(AbstractRedisStreamMessageListener.java:58)
	at com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener.onMessage(AbstractRedisStreamMessageListener.java:19)
	at org.springframework.data.redis.stream.StreamPollTask.deserializeAndEmitRecords(StreamPollTask.java:157)
	at org.springframework.data.redis.stream.StreamPollTask.doLoop(StreamPollTask.java:128)
	at org.springframework.data.redis.stream.StreamPollTask.run(StreamPollTask.java:112)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.executor.BatchExecutorException: com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:148)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:129)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:122)
	at org.apache.ibatis.executor.CachingExecutor.flushStatements(CachingExecutor.java:115)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy304.flushStatements(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy304.flushStatements(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:252)
	... 42 common frames omitted
Caused by: java.sql.BatchUpdateException: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:214)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchWithMultiValuesClause(ClientPreparedStatement.java:784)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:457)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:858)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3117)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3115)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:198)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeBatch(DruidPooledPreparedStatement.java:551)
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:126)
	... 58 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:96)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:990)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1168)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1103)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1450)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchWithMultiValuesClause(ClientPreparedStatement.java:745)
	... 67 common frames omitted

2025-08-04 14:39:40.950 | [1;31mERROR 440075[0;39m | [1;33mSimpleAsyncTaskExecutor-4 [TID: N/A][0;39m [1;32mr.c.s.AbstractRedisStreamMessageListener[0;39m | redis 消息队列异常| 队列名称=class com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage | stream=NoticeSendMessage id=1754289580517-0 error=org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'notice_message.send_content'.
2025-08-04 14:41:35.053 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@434027) 消息数量(1)]
2025-08-04 14:41:35.090 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消息(1754289320160-0)重新投递成功]
2025-08-04 14:41:35.090 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@440075) 消息数量(1)]
2025-08-04 14:41:35.220 | [34m INFO 440075[0;39m | [1;33mSimpleAsyncTaskExecutor-4 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.impl.DefaultNoticeClient  [0;39m | Notice sent successfully via client: NoticeSystemClient
2025-08-04 14:41:58.961 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.filter.ApiAccessLogFilter   [0;39m | 请求参数：{},头部信息：{content-length=25, ym-token=test4, host=127.0.0.1:11000, content-type=application/json, connection=keep-alive, tenant-id=1, accept-encoding=gzip, deflate, br, user-agent=Apifox/1.0.0 (https://apifox.com), accept=*/*},请求体：{
    "spaceId": 338856
}
2025-08-04 14:41:59.024 | [1;31mERROR 440075[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.c.e.util.ServiceExceptionUtil   [0;39m | 错误码(401000)|错误内容(访问令牌不存在)|参数([])
2025-08-04 14:41:59.078 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/app/social/like/create) 参数({
    "spaceId": 338856
})]
2025-08-04 14:41:59.246 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.m.notice.api.NoticeMessageApiImpl [0;39m | createMessage:NoticeMessageDTO(channelId=1, templateId=1, params=[4, 20250725我的第366个测试空间], sendResource=spaceLike, sendApp=ym, toUser=[23728])
2025-08-04 14:41:59.254 | [34m INFO 440075[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mc.f.y.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/app/social/like/create) 耗时(176 ms)]
2025-08-04 14:41:59.402 | [34m INFO 440075[0;39m | [1;33mSimpleAsyncTaskExecutor-4 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.impl.DefaultNoticeClient  [0;39m | Notice sent successfully via client: NoticeSystemClient
2025-08-04 14:42:35.062 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@440075) 消息数量(1)]
2025-08-04 14:43:35.059 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@440075) 消息数量(1)]
2025-08-04 14:44:35.083 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@440075) 消息数量(1)]
2025-08-04 14:45:35.150 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消费者(**********@440075) 消息数量(1)]
2025-08-04 14:45:35.182 | [34m INFO 440075[0;39m | [1;33mscheduling-1 [TID: N/A][0;39m [1;32my.f.m.r.c.j.RedisPendingMessageResendJob[0;39m | [processPendingMessage][消息(1754289580517-0)重新投递成功]
2025-08-04 14:45:35.310 | [34m INFO 440075[0;39m | [1;33mSimpleAsyncTaskExecutor-4 [TID: N/A][0;39m [1;32mc.f.y.m.n.f.c.impl.DefaultNoticeClient  [0;39m | Notice sent successfully via client: NoticeSystemClient

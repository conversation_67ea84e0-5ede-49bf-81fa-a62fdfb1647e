version: "3.4"

name: ym-system

services:
  mysql:
    container_name: ym-mysql
    image: mysql:8
    restart: unless-stopped
    tty: true
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ruoyi-vue-pro}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
    volumes:
      - mysql:/var/lib/mysql/
      - ./sql/mysql/ruoyi-vue-pro.sql:/docker-entrypoint-initdb.d/ruoyi-vue-pro.sql:ro

  redis:
    container_name: ym-redis
    image: redis:6-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis:/data

  server:
    container_name: ym-server
    build:
      context: ./ym-server/
    image: ym-server
    restart: unless-stopped
    ports:
      - "48080:48080"
    environment:
      # https://github.com/polovyivan/docker-pass-configs-to-container
      SPRING_PROFILES_ACTIVE: local
      JAVA_OPTS:
        ${JAVA_OPTS:-
          -Xms512m
          -Xmx512m
          -Djava.security.egd=file:/dev/./urandom
        }
      ARGS:
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-********************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.datasource.dynamic.datasource.slave.url=${SLAVE_DATASOURCE_URL:-********************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.slave.username=${SLAVE_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.slave.password=${SLAVE_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-ym-redis}
    depends_on:
      - mysql
      - redis

  admin:
    container_name: ym-admin
    build:
      context: ./ym-ui-admin
      args:
        NODE_ENV:
          ENV=${NODE_ENV:-production}
          PUBLIC_PATH=${PUBLIC_PATH:-/}
          VUE_APP_TITLE=${VUE_APP_TITLE:-元美系统}
          VUE_APP_BASE_API=${VUE_APP_BASE_API:-/prod-api}
          VUE_APP_APP_NAME=${VUE_APP_APP_NAME:-/}
          VUE_APP_TENANT_ENABLE=${VUE_APP_TENANT_ENABLE:-true}
          VUE_APP_CAPTCHA_ENABLE=${VUE_APP_CAPTCHA_ENABLE:-true}
          VUE_APP_DOC_ENABLE=${VUE_APP_DOC_ENABLE:-true}
          VUE_APP_BAIDU_CODE=${VUE_APP_BAIDU_CODE:-fadc1bd5db1a1d6f581df60a1807f8ab}
    image: ym-admin
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      - server

volumes:
  mysql:
    driver: local
  redis:
    driver: local

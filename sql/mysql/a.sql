DROP TABLE IF EXISTS `market_activity`;
CREATE TABLE `market_activity`  (
                                    `id` bigint(20) NOT NULL,
                                    `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                    `activity_type` tinyint(4) NOT NULL,
                                    `status` tinyint(4) NOT NULL,
                                    `start_time` datetime(0) NOT NULL,
                                    `end_time` datetime(0) NOT NULL,
                                    `invalid_time` datetime(0) NULL DEFAULT NULL,
                                    `delete_time` datetime(0) NULL DEFAULT NULL,
                                    `time_limited_discount` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                    `full_privilege` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                    `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                    `deleted` bit(1) NOT NULL DEFAULT b'0',
                                    `tenant_id` bigint(20) NOT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '促销活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_user
-- ----------------------------
DROP TABLE IF EXISTS `member_user`;
CREATE TABLE `member_user`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                `nickname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
                                `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
                                `status` tinyint(4) NOT NULL COMMENT '状态',
                                `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
                                `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
                                `register_ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注册 IP',
                                `login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '最后登录IP',
                                `login_date` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
                                `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE INDEX `uk_mobile`(`mobile`) USING BTREE COMMENT '手机号'
) ENGINE = InnoDB AUTO_INCREMENT = 248 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_app
-- ----------------------------
DROP TABLE IF EXISTS `pay_app`;
CREATE TABLE `pay_app`  (
                            `id` bigint(20) NOT NULL,
                            `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                            `status` tinyint(4) NOT NULL,
                            `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                            `order_notify_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                            `refund_notify_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                            `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                            `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                            `deleted` bit(1) NOT NULL DEFAULT b'0',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付应用' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_channel
-- ----------------------------
DROP TABLE IF EXISTS `pay_channel`;
CREATE TABLE `pay_channel`  (
                                `id` bigint(20) NOT NULL,
                                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `status` tinyint(4) NOT NULL,
                                `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `fee_rate` double NOT NULL DEFAULT 0,
                                `app_id` bigint(20) NOT NULL,
                                `config` varchar(10240) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                `deleted` bit(1) NOT NULL DEFAULT b'0',
                                `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付渠道' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_notify_log
-- ----------------------------
DROP TABLE IF EXISTS `pay_notify_log`;
CREATE TABLE `pay_notify_log`  (
                                   `id` bigint(20) NOT NULL,
                                   `task_id` bigint(20) NOT NULL,
                                   `notify_times` int(11) NOT NULL,
                                   `response` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                   `status` tinyint(4) NOT NULL,
                                   `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                   `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                   `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                   `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                   `deleted` bit(1) NOT NULL DEFAULT b'0',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付通知日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_notify_task
-- ----------------------------
DROP TABLE IF EXISTS `pay_notify_task`;
CREATE TABLE `pay_notify_task`  (
                                    `id` bigint(20) NOT NULL,
                                    `app_id` bigint(20) NOT NULL,
                                    `type` tinyint(4) NOT NULL,
                                    `data_id` bigint(20) NOT NULL,
                                    `merchant_order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                    `status` tinyint(4) NOT NULL,
                                    `next_notify_time` datetime(0) NULL DEFAULT NULL,
                                    `last_execute_time` datetime(0) NULL DEFAULT NULL,
                                    `notify_times` int(11) NOT NULL,
                                    `max_notify_times` int(11) NOT NULL,
                                    `notify_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                    `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                    `deleted` bit(1) NOT NULL DEFAULT b'0',
                                    `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付通知任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_order
-- ----------------------------
DROP TABLE IF EXISTS `pay_order`;
CREATE TABLE `pay_order`  (
                              `id` bigint(20) NOT NULL,
                              `app_id` bigint(20) NOT NULL,
                              `channel_id` bigint(20) NULL DEFAULT NULL,
                              `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `merchant_order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `subject` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `body` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `notify_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `price` bigint(20) NOT NULL,
                              `channel_fee_rate` double NULL DEFAULT 0,
                              `channel_fee_price` bigint(20) NULL DEFAULT 0,
                              `status` tinyint(4) NOT NULL,
                              `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `expire_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `success_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `notify_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `extension_id` bigint(20) NULL DEFAULT NULL,
                              `no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `refund_price` bigint(20) NOT NULL,
                              `channel_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `channel_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                              `deleted` bit(1) NOT NULL DEFAULT b'0',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_order_extension
-- ----------------------------
DROP TABLE IF EXISTS `pay_order_extension`;
CREATE TABLE `pay_order_extension`  (
                                        `id` bigint(20) NOT NULL,
                                        `no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                        `order_id` bigint(20) NOT NULL,
                                        `channel_id` bigint(20) NOT NULL,
                                        `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                        `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                        `status` tinyint(4) NOT NULL,
                                        `channel_extras` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                        `channel_error_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                        `channel_error_msg` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                        `channel_notify_data` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                        `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                        `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                        `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                        `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                        `deleted` bit(1) NOT NULL DEFAULT b'0',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付订单拓展' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_refund
-- ----------------------------
DROP TABLE IF EXISTS `pay_refund`;
CREATE TABLE `pay_refund`  (
                               `id` bigint(20) NOT NULL,
                               `no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `app_id` bigint(20) NOT NULL,
                               `channel_id` bigint(20) NOT NULL,
                               `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `order_id` bigint(20) NOT NULL,
                               `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `merchant_order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `merchant_refund_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `notify_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `status` tinyint(4) NOT NULL,
                               `pay_price` bigint(20) NOT NULL,
                               `refund_price` bigint(20) NOT NULL,
                               `reason` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `channel_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `channel_refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `success_time` datetime(0) NULL DEFAULT NULL,
                               `channel_error_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `channel_error_msg` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `channel_notify_data` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                               `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                               `deleted` bit(1) NOT NULL DEFAULT b'0',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_brand
-- ----------------------------
DROP TABLE IF EXISTS `product_brand`;
CREATE TABLE `product_brand`  (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '品牌编号',
                                  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
                                  `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌图片',
                                  `sort` int(11) NULL DEFAULT 0 COMMENT '品牌排序',
                                  `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌描述',
                                  `status` tinyint(4) NOT NULL COMMENT '状态',
                                  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                  `deleted` bit(1) NOT NULL DEFAULT b'0',
                                  `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品品牌' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类编号',
                                     `parent_id` bigint(20) NOT NULL COMMENT '父分类编号',
                                     `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
                                     `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '移动端分类图',
                                     `big_pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'PC 端分类图',
                                     `sort` int(11) NULL DEFAULT 0 COMMENT '分类排序',
                                     `status` tinyint(4) NOT NULL COMMENT '开启状态',
                                     `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `deleted` bit(1) NOT NULL DEFAULT b'0',
                                     `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_comment
-- ----------------------------
DROP TABLE IF EXISTS `product_comment`;
CREATE TABLE `product_comment`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论编号，主键自增',
                                    `user_id` bigint(20) NULL DEFAULT NULL COMMENT '评价人的用户编号关联 MemberUserDO 的 id 编号',
                                    `user_nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '评价人名称',
                                    `user_avatar` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '评价人头像',
                                    `anonymous` bit(1) NULL DEFAULT NULL COMMENT '是否匿名',
                                    `order_id` bigint(20) NULL DEFAULT NULL COMMENT '交易订单编号关联 TradeOrderDO 的 id 编号',
                                    `order_item_id` bigint(20) NULL DEFAULT NULL COMMENT '交易订单项编号关联 TradeOrderItemDO 的 id 编号',
                                    `spu_id` bigint(20) NULL DEFAULT NULL COMMENT '商品 SPU 编号关联 ProductSpuDO 的 id',
                                    `spu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品 SPU 名称',
                                    `sku_id` bigint(20) NULL DEFAULT NULL COMMENT '商品 SKU 编号关联 ProductSkuDO 的 id 编号',
                                    `visible` bit(1) NULL DEFAULT NULL COMMENT '是否可见true:显示false:隐藏',
                                    `scores` tinyint(4) NULL DEFAULT NULL COMMENT '评分星级1-5分',
                                    `description_scores` tinyint(4) NULL DEFAULT NULL COMMENT '描述星级1-5 星',
                                    `benefit_scores` tinyint(4) NULL DEFAULT NULL COMMENT '服务星级1-5 星',
                                    `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '评论内容',
                                    `pic_urls` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '评论图片地址数组',
                                    `reply_status` bit(1) NULL DEFAULT NULL COMMENT '商家是否回复',
                                    `reply_user_id` bigint(20) NULL DEFAULT NULL COMMENT '回复管理员编号关联 AdminUserDO 的 id 编号',
                                    `reply_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商家回复内容',
                                    `reply_time` datetime(0) NULL DEFAULT NULL COMMENT '商家回复时间',
                                    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '商品评论' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_property
-- ----------------------------
DROP TABLE IF EXISTS `product_property`;
CREATE TABLE `product_property`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名称',
                                     `status` tinyint(4) NULL DEFAULT NULL COMMENT '状态： 0 开启 ，1 禁用',
                                     `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `deleted` bit(1) NOT NULL DEFAULT b'0',
                                     `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                     `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格名称' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_property_value
-- ----------------------------
DROP TABLE IF EXISTS `product_property_value`;
CREATE TABLE `product_property_value`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `property_id` bigint(20) NULL DEFAULT NULL COMMENT '规格键id',
                                           `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格值名字',
                                           `status` tinyint(4) NULL DEFAULT NULL COMMENT '状态： 1 开启 ，2 禁用',
                                           `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                           `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                           `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                           `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                           `deleted` bit(1) NOT NULL DEFAULT b'0',
                                           `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                           `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格值' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_sku`;
CREATE TABLE `product_sku`  (
                                `id` bigint(20) NOT NULL,
                                `spu_id` bigint(20) NOT NULL COMMENT 'spu编号',
                                `properties` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性数组，JSON 格式',
                                `price` int(11) NOT NULL DEFAULT -1 COMMENT '商品价格，单位：分',
                                `market_price` int(11) NULL DEFAULT NULL COMMENT '市场价，单位：分',
                                `cost_price` int(11) NOT NULL DEFAULT -1 COMMENT '成本价，单位： 分',
                                `bar_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU 的条形码',
                                `pic_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
                                `stock` int(11) NULL DEFAULT NULL COMMENT '库存',
                                `weight` double NULL DEFAULT NULL COMMENT '商品重量，单位：kg 千克',
                                `volume` double NULL DEFAULT NULL COMMENT '商品体积，单位：m^3 平米',
                                `sub_commission_first_price` int(11) NULL DEFAULT NULL COMMENT '一级分销的佣金，单位：分',
                                `sub_commission_second_price` int(11) NULL DEFAULT NULL COMMENT '二级分销的佣金，单位：分',
                                `sales_count` int(11) NULL DEFAULT NULL COMMENT '商品销量',
                                `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                `deleted` bit(1) NOT NULL DEFAULT b'0',
                                `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品sku' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_spu
-- ----------------------------
DROP TABLE IF EXISTS `product_spu`;
CREATE TABLE `product_spu`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品 SPU 编号，自增',
                                `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
                                `keyword` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关键字',
                                `introduction` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品简介',
                                `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品详情',
                                `bar_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '条形码',
                                `category_id` bigint(20) NOT NULL COMMENT '商品分类编号',
                                `brand_id` int(11) NULL DEFAULT NULL COMMENT '商品品牌编号',
                                `pic_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品封面图',
                                `slider_pic_urls` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品轮播图地址\n 数组，以逗号分隔\n 最多上传15张',
                                `video_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品视频',
                                `unit` tinyint(4) NOT NULL COMMENT '单位',
                                `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序字段',
                                `status` tinyint(4) NOT NULL COMMENT '商品状态: 0 上架（开启） 1 下架（禁用）-1 回收',
                                `spec_type` bit(1) NOT NULL COMMENT '规格类型：0 单规格 1 多规格',
                                `price` int(11) NOT NULL DEFAULT -1 COMMENT '商品价格，单位使用：分',
                                `market_price` int(11) NOT NULL COMMENT '市场价，单位使用：分',
                                `cost_price` int(11) NOT NULL DEFAULT -1 COMMENT '成本价，单位： 分',
                                `stock` int(11) NOT NULL DEFAULT 0 COMMENT '库存',
                                `delivery_template_id` bigint(20) NOT NULL COMMENT '物流配置模板编号',
                                `recommend_hot` bit(1) NOT NULL COMMENT '是否热卖推荐: 0 默认 1 热卖',
                                `recommend_benefit` bit(1) NOT NULL COMMENT '是否优惠推荐: 0 默认 1 优选',
                                `recommend_best` bit(1) NOT NULL COMMENT '是否精品推荐: 0 默认 1 精品',
                                `recommend_new` bit(1) NOT NULL COMMENT '是否新品推荐: 0 默认 1 新品',
                                `recommend_good` bit(1) NOT NULL COMMENT '是否优品推荐',
                                `give_integral` int(11) NOT NULL COMMENT '赠送积分',
                                `give_coupon_template_ids` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '赠送的优惠劵编号的数组',
                                `sub_commission_type` bit(1) NOT NULL COMMENT '分销类型',
                                `activity_orders` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动显示排序0=默认, 1=秒杀，2=砍价，3=拼团',
                                `sales_count` int(11) NULL DEFAULT 0 COMMENT '商品销量',
                                `virtual_sales_count` int(11) NULL DEFAULT 0 COMMENT '虚拟销量',
                                `browse_count` int(11) NULL DEFAULT 0 COMMENT '商品点击量',
                                `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                `deleted` bit(1) NOT NULL DEFAULT b'0',
                                `tenant_id` bigint(20) NOT NULL DEFAULT 0,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品spu' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_combination_activity
-- ----------------------------
DROP TABLE IF EXISTS `promotion_combination_activity`;
CREATE TABLE `promotion_combination_activity`  (
                                                   `id` bigint(20) NOT NULL,
                                                   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                                   `spu_id` bigint(20) NULL DEFAULT NULL,
                                                   `total_limit_count` int(11) NOT NULL,
                                                   `single_limit_count` int(11) NOT NULL,
                                                   `start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                                   `end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                                   `user_size` int(11) NOT NULL,
                                                   `total_num` int(11) NOT NULL,
                                                   `success_num` int(11) NOT NULL,
                                                   `order_user_count` int(11) NOT NULL,
                                                   `virtual_group` int(11) NOT NULL,
                                                   `status` int(11) NOT NULL,
                                                   `limit_duration` int(11) NOT NULL,
                                                   `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                                   `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                   `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                                   `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                   `deleted` bit(1) NOT NULL DEFAULT b'0',
                                                   `tenant_id` bigint(20) NOT NULL,
                                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '拼团活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_coupon
-- ----------------------------
DROP TABLE IF EXISTS `promotion_coupon`;
CREATE TABLE `promotion_coupon`  (
                                     `id` bigint(20) NOT NULL,
                                     `template_id` bigint(20) NOT NULL,
                                     `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `status` int(11) NOT NULL,
                                     `user_id` bigint(20) NOT NULL,
                                     `take_type` int(11) NOT NULL,
                                     `useprice` int(11) NOT NULL,
                                     `valid_start_time` datetime(0) NOT NULL,
                                     `valid_end_time` datetime(0) NOT NULL,
                                     `product_scope` int(11) NOT NULL,
                                     `product_spu_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `discount_type` int(11) NOT NULL,
                                     `discount_percent` int(11) NULL DEFAULT NULL,
                                     `discount_price` int(11) NULL DEFAULT NULL,
                                     `discount_limit_price` int(11) NULL DEFAULT NULL,
                                     `use_order_id` bigint(20) NULL DEFAULT NULL,
                                     `use_time` datetime(0) NULL DEFAULT NULL,
                                     `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                     `deleted` bit(1) NOT NULL DEFAULT b'0',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠劵' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_coupon_template
-- ----------------------------
DROP TABLE IF EXISTS `promotion_coupon_template`;
CREATE TABLE `promotion_coupon_template`  (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                              `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                              `status` int(11) NOT NULL,
                                              `total_count` int(11) NOT NULL,
                                              `take_limit_count` int(11) NOT NULL,
                                              `take_type` int(11) NOT NULL,
                                              `use_price` int(11) NOT NULL,
                                              `product_scope` int(11) NOT NULL,
                                              `product_spu_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              `validity_type` int(11) NOT NULL,
                                              `valid_start_time` datetime(0) NULL DEFAULT NULL,
                                              `valid_end_time` datetime(0) NULL DEFAULT NULL,
                                              `fixed_start_term` int(11) NULL DEFAULT NULL,
                                              `fixed_end_term` int(11) NULL DEFAULT NULL,
                                              `discount_type` int(11) NOT NULL,
                                              `discount_percent` int(11) NULL DEFAULT NULL,
                                              `discount_price` int(11) NULL DEFAULT NULL,
                                              `discount_limit_price` int(11) NULL DEFAULT NULL,
                                              `take_count` int(11) NOT NULL DEFAULT 0,
                                              `use_count` int(11) NOT NULL DEFAULT 0,
                                              `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                              `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                              `deleted` bit(1) NOT NULL DEFAULT b'0',
                                              `tenant_id` bigint(20) NULL DEFAULT NULL,
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠劵模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_discount_activity
-- ----------------------------
DROP TABLE IF EXISTS `promotion_discount_activity`;
CREATE TABLE `promotion_discount_activity`  (
                                                `id` bigint(20) NOT NULL,
                                                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                                `status` int(11) NOT NULL,
                                                `start_time` datetime(0) NOT NULL,
                                                `end_time` datetime(0) NOT NULL,
                                                `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                                `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                `deleted` bit(1) NOT NULL DEFAULT b'0',
                                                `tenant_id` int(20) NULL DEFAULT NULL,
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时折扣活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_reward_activity
-- ----------------------------
DROP TABLE IF EXISTS `promotion_reward_activity`;
CREATE TABLE `promotion_reward_activity`  (
                                              `id` bigint(20) NOT NULL,
                                              `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                              `status` int(11) NOT NULL,
                                              `start_time` datetime(0) NOT NULL,
                                              `end_time` datetime(0) NOT NULL,
                                              `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              `condition_type` int(11) NOT NULL,
                                              `product_scope` int(11) NOT NULL,
                                              `product_spu_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              `rules` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                              `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                              `deleted` bit(1) NOT NULL DEFAULT b'0',
                                              `tenant_id` int(20) NULL DEFAULT NULL,
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '满减送活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_seckill_activity
-- ----------------------------
DROP TABLE IF EXISTS `promotion_seckill_activity`;
CREATE TABLE `promotion_seckill_activity`  (
                                               `id` bigint(20) NOT NULL,
                                               `spu_id` bigint(20) NOT NULL,
                                               `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                               `status` int(11) NOT NULL,
                                               `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                               `start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                               `end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                               `sort` int(11) NOT NULL,
                                               `config_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                               `order_count` int(11) NOT NULL,
                                               `user_count` int(11) NOT NULL,
                                               `total_price` int(11) NOT NULL,
                                               `total_limit_count` int(11) NULL DEFAULT NULL,
                                               `single_limit_count` int(11) NULL DEFAULT NULL,
                                               `stock` int(11) NULL DEFAULT NULL,
                                               `total_stock` int(11) NULL DEFAULT NULL,
                                               `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                               `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                               `deleted` bit(1) NOT NULL DEFAULT b'0',
                                               `tenant_id` bigint(20) NOT NULL,
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '秒杀活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_seckill_config
-- ----------------------------
DROP TABLE IF EXISTS `promotion_seckill_config`;
CREATE TABLE `promotion_seckill_config`  (
                                             `id` bigint(20) NOT NULL,
                                             `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                             `start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                             `end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                             `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                             `status` int(11) NOT NULL,
                                             `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                             `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                             `deleted` bit(1) NOT NULL DEFAULT b'0',
                                             `tenant_id` bigint(20) NOT NULL,
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '秒杀时段配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_after_sale
-- ----------------------------
DROP TABLE IF EXISTS `trade_after_sale`;
CREATE TABLE `trade_after_sale`  (
                                     `id` bigint(20) NOT NULL,
                                     `no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `status` int(11) NOT NULL,
                                     `type` int(11) NOT NULL,
                                     `way` int(11) NOT NULL,
                                     `user_id` bigint(20) NOT NULL,
                                     `apply_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `apply_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `apply_pic_urls` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `order_id` bigint(20) NOT NULL,
                                     `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `order_item_id` bigint(20) NOT NULL,
                                     `spu_id` bigint(20) NOT NULL,
                                     `spu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `sku_id` bigint(20) NOT NULL,
                                     `properties` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `count` int(11) NOT NULL,
                                     `audit_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `audit_user_id` bigint(20) NULL DEFAULT NULL,
                                     `audit_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `refund_price` int(11) NOT NULL,
                                     `pay_refund_id` bigint(20) NULL DEFAULT NULL,
                                     `refund_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `logistics_id` bigint(20) NULL DEFAULT NULL,
                                     `logistics_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `delivery_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `receive_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `receive_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                     `deleted` bit(1) NOT NULL DEFAULT b'0',
                                     `tenant_id` bigint(20) NULL DEFAULT NULL,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易售后表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_after_sale_log
-- ----------------------------
DROP TABLE IF EXISTS `trade_after_sale_log`;
CREATE TABLE `trade_after_sale_log`  (
                                         `id` bigint(20) NOT NULL,
                                         `user_id` bigint(20) NOT NULL,
                                         `user_type` int(11) NOT NULL,
                                         `after_sale_id` bigint(20) NOT NULL,
                                         `order_id` bigint(20) NOT NULL,
                                         `order_item_id` bigint(20) NOT NULL,
                                         `before_status` int(11) NULL DEFAULT NULL,
                                         `after_status` int(11) NOT NULL,
                                         `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                         `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                         `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                         `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                         `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                         `deleted` bit(1) NOT NULL DEFAULT b'0',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易售后日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_order
-- ----------------------------
DROP TABLE IF EXISTS `trade_order`;
CREATE TABLE `trade_order`  (
                                `id` bigint(20) NOT NULL,
                                `no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `type` int(11) NOT NULL,
                                `terminal` int(11) NOT NULL,
                                `user_id` bigint(20) NOT NULL,
                                `user_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `user_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `status` int(11) NOT NULL,
                                `product_count` int(11) NOT NULL,
                                `cancel_type` int(11) NULL DEFAULT NULL,
                                `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `pay_status` bit(1) NOT NULL,
                                `pay_time` datetime(0) NULL DEFAULT NULL,
                                `finish_time` datetime(0) NULL DEFAULT NULL,
                                `cancel_time` datetime(0) NULL DEFAULT NULL,
                                `original_price` int(11) NOT NULL,
                                `order_price` int(11) NOT NULL,
                                `discount_price` int(11) NOT NULL,
                                `delivery_price` int(11) NOT NULL,
                                `adjust_price` int(11) NOT NULL,
                                `pay_price` int(11) NOT NULL,
                                `pay_order_id` bigint(20) NULL DEFAULT NULL,
                                `pay_channel_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `delivery_template_id` bigint(20) NULL DEFAULT NULL,
                                `logistics_id` bigint(20) NULL DEFAULT NULL,
                                `logistics_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `delivery_status` smallint(6) NOT NULL,
                                `delivery_time` datetime(0) NULL DEFAULT NULL,
                                `receive_time` datetime(0) NULL DEFAULT NULL,
                                `receiver_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `receiver_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `receiver_area_id` int(11) NOT NULL,
                                `receiver_post_code` int(11) NULL DEFAULT NULL,
                                `receiver_detail_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                `after_sale_status` int(11) NOT NULL,
                                `refund_price` int(11) NOT NULL,
                                `coupon_id` bigint(20) NOT NULL,
                                `coupon_price` int(11) NOT NULL,
                                `point_price` int(11) NOT NULL,
                                `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                `deleted` bit(1) NOT NULL DEFAULT b'0',
                                `tenant_id` bigint(20) NULL DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_order_item
-- ----------------------------
DROP TABLE IF EXISTS `trade_order_item`;
CREATE TABLE `trade_order_item`  (
                                     `id` bigint(20) NOT NULL,
                                     `user_id` bigint(20) NOT NULL,
                                     `order_id` bigint(20) NOT NULL,
                                     `spu_id` bigint(20) NOT NULL,
                                     `spu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `sku_id` bigint(20) NOT NULL,
                                     `properties` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `count` int(11) NOT NULL,
                                     `original_price` int(11) NOT NULL,
                                     `original_unit_price` int(11) NOT NULL,
                                     `discount_price` int(11) NOT NULL,
                                     `pay_price` int(11) NOT NULL,
                                     `order_part_price` int(11) NOT NULL,
                                     `order_divide_price` int(11) NOT NULL,
                                     `after_sale_status` int(11) NOT NULL,
                                     `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `updater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                     `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                     `deleted` bit(1) NOT NULL DEFAULT b'0',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易订单明细表' ROW_FORMAT = Dynamic;
-- auto-generated definition 空间表
create table space_template
(
    template_id        bigint       not null comment '模板id'
        primary key,
    template_en_name   varchar(64)  null comment '模板英文名称',
    template_cn_name   varchar(64)  null comment '模板中文名称',
    template_code      varchar(32)  null,
    template_type_id   bigint       null comment '模板类型id',
    template_type_code varchar(64)  null comment '模板类型码值',
    point_num_2        int          null comment '2d挂载点数',
    point_num_3        int          null comment '3d挂载点数',
    cn_description     text         null comment '中文说明',
    en_description     text         null comment '英文说明',
    template_status    int          null comment '状态 0 初始化 1启用 2 停用 3 删除',
    template_cover     varchar(256) null comment '空间封面',
    template_res       varchar(512) null comment '模板地址',
    template_config    longtext     null comment '文本内容',
    rights_id          bigint       null comment '权益id',
    create_default     int          null comment '0系统初始化 1 用户自定义 2 其他',
    template_tags      varchar(128) null comment 'template_tags',
    create_user_id     bigint       null comment '创建人id',
    create_user_name   varchar(64)  null comment '创建用户名称',
    create_data        date         null comment '创建日期',
    create_time        datetime     null comment '创建时间',
    update_user_id     bigint       null comment '更新人',
    update_user_name   varchar(32)  null comment '更新人',
    update_data        date         null comment '更新日期',
    update_time        datetime     null comment '更新时间',
    tenant_id          bigint       null comment '租户id',
    tenant_code        varchar(32)  null comment '租户Code'
)
    comment '空间模板';

create index space_template_template_tags_index
    on space_template (template_tags);

create index space_template_template_type_code_index
    on space_template (template_type_code);

create index space_template_template_type_id_index
    on space_template (template_type_id);

create index space_template_tenant_id_index
    on space_template (tenant_id);



-- auto-generated definition
create table space_template_tag
(
    id               bigint      null comment 'id',
    name             varchar(64) null comment '名称',
    create_user_id   bigint      null comment '创建人id',
    create_user_name varchar(64) null comment '创建用户名称',
    create_data      date        null comment '创建日期',
    create_time      datetime    null comment '创建时间',
    update_user_id   bigint      null comment '更新人',
    update_user_name varchar(32) null comment '更新人',
    update_data      date        null comment '更新日期',
    update_time      datetime    null comment '更新时间',
    tenant_id        bigint      null comment '租户id',
    tenant_code      varchar(32) null comment '租户Code'
)
    comment '空间模板标签表';


-- auto-generated definition
create table space_template_type
(
    id               bigint      not null comment '主键'
        primary key,
    name             varchar(64) null comment '空间模板名称',
    create_user_id   bigint      null comment '创建人id',
    create_user_name varchar(64) null comment '创建用户名称',
    create_data      date        null comment '创建日期',
    create_time      datetime    null comment '创建时间',
    update_user_id   bigint      null comment '更新人',
    update_user_name varchar(32) null comment '更新人',
    update_data      date        null comment '更新日期',
    update_time      datetime    null comment '更新时间',
    tenant_id        bigint      null comment '租户id',
    tenant_code      varchar(32) null comment '租户Code'
)
    comment '空间模板类型表';


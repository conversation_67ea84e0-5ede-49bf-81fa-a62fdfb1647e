<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>ym</artifactId>
        <groupId>com.fozmo</groupId>
        <version>1.0</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>ym-common</module>
        <module>ym-spring-boot-starter-mybatis</module>
        <module>ym-spring-boot-starter-redis</module>
        <module>ym-spring-boot-starter-web</module>
        <module>ym-spring-boot-starter-security</module>

        <module>ym-spring-boot-starter-monitor</module>
        <module>ym-spring-boot-starter-protection</module>
        <module>ym-spring-boot-starter-job</module>
        <module>ym-spring-boot-starter-mq</module>

        <module>ym-spring-boot-starter-excel</module>
        <module>ym-spring-boot-starter-test</module>

        <module>ym-spring-boot-starter-biz-tenant</module>
        <module>ym-spring-boot-starter-biz-data-permission</module>
        <module>ym-spring-boot-starter-biz-ip</module>
        <module>ym-common-sensitive</module>
    </modules>

    <artifactId>ym-framework</artifactId>
    <description>
        该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz
    </description>

</project>

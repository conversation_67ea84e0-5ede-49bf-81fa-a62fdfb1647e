//package com.fozmo.ym.framework.sensitive.config;
//
/// /import com.fozmo.ym.framework.sensitive.filter.DatabaseSensitiveFilter;
//import com.fozmo.ym.framework.sensitive.loader.DatabaseWordLoader;
//import com.fozmo.ym.framework.sensitive.mapper.SensitiveMapper;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 敏感词自动配置类
// */
//@Configuration
//@EnableConfigurationProperties(SensitiveProperties.class)
//public class SensitiveAutoConfig {
//
//    @Bean
//    @ConditionalOnMissingBean
//    public DatabaseSensitiveFilter sensitiveFilter(SensitiveProperties properties) {
//        return new DatabaseSensitiveFilter(properties);
//    }
//
//    @Bean
//    @ConditionalOnMissingBean
//    public SensitiveMapper sensitiveMapper() {
//        return new SensitiveMapper();
//    }
//
//    @Bean
//    @ConditionalOnMissingBean
//    public DatabaseWordLoader databaseWordLoader() {
//        return new DatabaseWordLoader();
//    }
//}
package com.fozmo.ym.framework.sensitive.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

// 配置类
@Configuration
@EnableConfigurationProperties(SensitiveProperties.class)
public class SensitiveAutoConfig {
//
//    @Bean
//    @ConditionalOnMissingBean
//    public SensitiveWordFilter sensitiveFilter(SensitiveProperties properties) {
//        return new DatabaseSensitiveFilter(properties);
//    }
}

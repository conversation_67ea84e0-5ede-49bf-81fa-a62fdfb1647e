package com.fozmo.ym.framework.sensitive.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 敏感词配置参数
 */
@ConfigurationProperties(prefix = "ym.sensitive")
public class SensitiveProperties {
    /**
     * 敏感词替换字符
     */
    private String replacement = "***";

    /**
     * 是否启用缓存
     */
    private boolean enableCache = true;

    /**
     * 敏感词刷新间隔（毫秒）
     */
    private long refreshInterval = 3600000L;

    public String getReplacement() {
        return replacement;
    }

    public void setReplacement(String replacement) {
        this.replacement = replacement;
    }

    public boolean isEnableCache() {
        return enableCache;
    }

    public void setEnableCache(boolean enableCache) {
        this.enableCache = enableCache;
    }

    public long getRefreshInterval() {
        return refreshInterval;
    }

    public void setRefreshInterval(long refreshInterval) {
        this.refreshInterval = refreshInterval;
    }
}
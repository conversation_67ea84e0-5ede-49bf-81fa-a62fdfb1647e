package com.fozmo.ym.framework.sensitive.filter;

import com.fozmo.ym.framework.sensitive.config.SensitiveProperties;
import com.fozmo.ym.framework.sensitive.exception.SensitiveException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于数据库的敏感词过滤器
 * 使用DFA算法实现敏感词过滤
 */
public class DatabaseSensitiveFilter {
    private static final Logger log = LoggerFactory.getLogger(DatabaseSensitiveFilter.class);

    /**
     * 敏感词树根节点
     */
    private final Map<Character, Map> sensitiveWordMap = new ConcurrentHashMap<>();
    
    private final SensitiveProperties properties;

    public DatabaseSensitiveFilter(SensitiveProperties properties) {
        this.properties = properties;
    }

    /**
     * 过滤文本中的敏感词
     *
     * @param text 待过滤文本
     * @return 过滤后的文本
     * @throws SensitiveException 过滤异常
     */
    public String filter(String text) throws SensitiveException {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }
        
        try {
            // DFA算法实现
            return doFilter(text);
        } catch (Exception e) {
            log.error("敏感词过滤异常: {}", e.getMessage(), e);
            throw new SensitiveException("内容安全检查失败", e);
        }
    }

    /**
     * 执行敏感词过滤
     *
     * @param text 待过滤文本
     * @return 过滤后的文本
     */
    private String doFilter(String text) {
        char[] chars = text.toCharArray();
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < chars.length; i++) {
            // 从当前字符开始匹配敏感词
            int matchLength = checkSensitiveWord(chars, i);
            if (matchLength > 0) {
                // 发现敏感词，替换为指定字符
                for (int j = 0; j < matchLength; j++) {
                    result.append(properties.getReplacement());
                }
                // 跳过已匹配的敏感词
                i += matchLength - 1;
            } else {
                // 未发现敏感词，保留原字符
                result.append(chars[i]);
            }
        }
        
        return result.toString();
    }

    /**
     * 检查从指定位置开始是否包含敏感词
     *
     * @param chars 字符数组
     * @param begin 开始位置
     * @return 敏感词长度，0表示未发现敏感词
     */
    private int checkSensitiveWord(char[] chars, int begin) {
        Map<Character, Map> nowMap = sensitiveWordMap;
        int matchCount = 0;
        
        for (int i = begin; i < chars.length; i++) {
            char word = chars[i];
            // 转换为小写进行匹配
            word = Character.toLowerCase(word);
            
            // 获取当前字符对应的节点
            Map<Character, Map> nextMap = nowMap.get(word);
            if (nextMap != null) {
                // 找到匹配的字符
                matchCount++;
                nowMap = nextMap;
                
                // 检查是否是敏感词结尾
                if (Boolean.TRUE.equals(nowMap.get("isEnd"))) {
                    // 发现敏感词，返回匹配长度
                    return matchCount;
                }
            } else {
                // 未找到匹配字符，中断匹配
                break;
            }
        }
        
        // 未发现完整敏感词
        return 0;
    }

    /**
     * 初始化敏感词库
     *
     * @param words 敏感词列表
     */
    public void initWordMap(List<String> words) {
        // 清空原有敏感词
        sensitiveWordMap.clear();
        
        if (CollectionUtils.isEmpty(words)) {
            log.warn("敏感词库为空");
            return;
        }
        
        // 构建敏感词树
        for (String word : words) {
            if (word == null || word.trim().isEmpty()) {
                continue;
            }
            
            // 转换为小写统一处理
            word = word.toLowerCase().trim();
            Map<Character, Map> nowMap = sensitiveWordMap;
            
            for (int i = 0; i < word.length(); i++) {
                char keyChar = word.charAt(i);
                Map<Character, Map> nextMap = nowMap.get(keyChar);
                
                if (nextMap == null) {
                    // 创建新节点
                    nextMap = new HashMap<>();
                    nowMap.put(keyChar, nextMap);
                    nowMap = nextMap;
                } else {
                    // 使用已有节点
                    nowMap = nextMap;
                }
                
                // 最后一个字符标记为结尾
                if (i == word.length() - 1) {
                    nowMap.put("isEnd", true);
                }
            }
        }
        
        log.info("敏感词库初始化完成，共加载 {} 个敏感词", words.size());
    }

    /**
     * 检查文本是否包含敏感词
     *
     * @param text 待检查文本
     * @return true包含敏感词，false不包含
     */
    public boolean containsSensitiveWord(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        char[] chars = text.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (checkSensitiveWord(chars, i) > 0) {
                return true;
            }
        }
        
        return false;
    }
}
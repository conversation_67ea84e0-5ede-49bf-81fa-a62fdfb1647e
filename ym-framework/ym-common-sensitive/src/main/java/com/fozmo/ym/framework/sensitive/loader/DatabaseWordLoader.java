package com.fozmo.ym.framework.sensitive.loader;

import com.fozmo.ym.framework.sensitive.mapper.SensitiveMapper;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DatabaseWordLoader {
    @Autowired
    private SensitiveMapper sensitiveMapper;

    @PostConstruct
    public void initTrie() {
//        List<String> words = sensitiveMapper.findAllWords();
        List<String> words = List.of("敏感词1", "敏感词2", "敏感词3");
        // 构建Trie树‌:ml-citation{ref="8" data="citationList"}
    }

    @Scheduled(fixedRate = 3600000)
    public void refreshWords() {
        // 每小时更新词库‌:ml-citation{ref="2" data="citationList"}
    }
}

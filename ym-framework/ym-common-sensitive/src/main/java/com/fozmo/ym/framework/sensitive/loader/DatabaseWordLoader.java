package com.fozmo.ym.framework.sensitive.loader;

import com.fozmo.ym.framework.sensitive.config.SensitiveProperties;
import com.fozmo.ym.framework.sensitive.filter.DatabaseSensitiveFilter;
import com.fozmo.ym.framework.sensitive.mapper.SensitiveMapper;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据库敏感词加载器
 * 负责从数据库加载敏感词并构建敏感词树
 */
@Component
public class DatabaseWordLoader {
    private static final Logger log = LoggerFactory.getLogger(DatabaseWordLoader.class);
    
    @Autowired
    private SensitiveMapper sensitiveMapper;

    @Autowired
    private DatabaseSensitiveFilter sensitiveFilter;

    @Autowired
    private SensitiveProperties sensitiveProperties;

    /**
     * 初始化敏感词树
     */
    @PostConstruct
    public void initTrie() {
        log.info("开始初始化敏感词库");
        try {
            List<String> words = sensitiveMapper.findAllWords();
            sensitiveFilter.initWordMap(words);
            log.info("敏感词库初始化完成，共加载 {} 个敏感词", words.size());
        } catch (Exception e) {
            log.error("敏感词库初始化失败", e);
        }
    }

    /**
     * 定时刷新敏感词库
     */
    @Scheduled(fixedRateString = "${ym.sensitive.refresh-interval:3600000}")
    public void refreshWords() {
        log.info("开始刷新敏感词库");
        try {
            List<String> words = sensitiveMapper.findAllWords();
            sensitiveFilter.initWordMap(words);
            log.info("敏感词库刷新完成，共加载 {} 个敏感词", words.size());
        } catch (Exception e) {
            log.error("敏感词库刷新失败", e);
        }
    }
}
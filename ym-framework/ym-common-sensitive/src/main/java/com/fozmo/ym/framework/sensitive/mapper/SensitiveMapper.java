package com.fozmo.ym.framework.sensitive.mapper;

import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * 敏感词Mapper
 * 在实际项目中应该连接数据库查询敏感词列表
 */
@Repository
public class SensitiveMapper {
    
    /**
     * 从数据库查询所有敏感词
     * 
     * @return 敏感词列表
     */
    public List<String> findAllWords() {
        // 在实际项目中应该查询数据库
        // 示例数据，实际应从数据库获取
        return Arrays.asList(
            "敏感词1",
            "敏感词2", 
            "敏感词3"
        );
    }
}
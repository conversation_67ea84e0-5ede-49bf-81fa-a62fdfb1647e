package com.fozmo.ym.framework.common.constants;

/**
 * Redis 缓存 Key 常量定义
 * 
 * 命名规范：
 * 1. 所有键名以 "ym:" 开头
 * 2. 按模块分类，格式：ym:{模块}:{业务}:{具体含义}
 * 3. 使用单冒号分隔，兼容Redis管理工具
 */
public interface RedisKeyConstants {

    /**
     * 系统模块相关缓存键
     */
    
    /**
     * 指定部门的所有子部门编号数组的缓存
     * <p>
     * KEY 格式：ym:system:dept:children:ids:{id}
     * VALUE 数据类型：String 子部门编号集合
     */
    String DEPT_CHILDREN_ID_LIST = "ym:system:dept:children:ids";

    /**
     * 角色的缓存
     * <p>
     * KEY 格式：ym:system:role:{id}
     * VALUE 数据类型：String 角色信息
     */
    String ROLE = "ym:system:role";

    /**
     * 用户拥有的角色编号的缓存
     * <p>
     * KEY 格式：ym:system:user:role:ids:{userId}
     * VALUE 数据类型：String 角色编号集合
     */
    String USER_ROLE_ID_LIST = "ym:system:user:role:ids";

    /**
     * 拥有指定菜单的角色编号的缓存
     * <p>
     * KEY 格式：ym:system:menu:role:ids:{menuId}
     * VALUE 数据类型：String 角色编号集合
     */
    String MENU_ROLE_ID_LIST = "ym:system:menu:role:ids";

    /**
     * 拥有权限对应的菜单编号数组的缓存
     * <p>
     * KEY 格式：ym:system:permission:menu:ids:{permission}
     * VALUE 数据类型：String 菜单编号数组
     */
    String PERMISSION_MENU_ID_LIST = "ym:system:permission:menu:ids";

    /**
     * 认证模块相关缓存键
     */

    /**
     * OAuth2 客户端的缓存
     * <p>
     * KEY 格式：ym:auth:oauth:client:{id}
     * VALUE 数据类型：String 客户端信息
     */
    String OAUTH_CLIENT = "ym:auth:oauth:client";

    /**
     * 访问令牌的缓存
     * <p>
     * KEY 格式：ym:auth:oauth:access_token:{token}
     * VALUE 数据类型：String 访问令牌信息
     * <p>
     * 由于动态过期时间，使用 RedisTemplate 操作
     */
    String OAUTH2_ACCESS_TOKEN = "ym:auth:oauth:access_token:%s";

    /**
     * 消息模块相关缓存键
     */

    /**
     * 站内信模版的缓存
     * <p>
     * KEY 格式：ym:message:notify:template:{code}
     * VALUE 数据格式：String 模版信息
     */
    String NOTIFY_TEMPLATE = "ym:message:notify:template";

    /**
     * 邮件账号的缓存
     * <p>
     * KEY 格式：ym:message:mail:account:{id}
     * VALUE 数据格式：String 账号信息
     */
    String MAIL_ACCOUNT = "ym:message:mail:account";

    /**
     * 邮件模版的缓存
     * <p>
     * KEY 格式：ym:message:mail:template:{code}
     * VALUE 数据格式：String 模版信息
     */
    String MAIL_TEMPLATE = "ym:message:mail:template";

    /**
     * 短信模版的缓存
     * <p>
     * KEY 格式：ym:message:sms:template:{id}
     * VALUE 数据格式：String 模版信息
     */
    String SMS_TEMPLATE = "ym:message:sms:template";

    /**
     * 小程序订阅模版的缓存
     * <p>
     * KEY 格式：ym:message:wxa:subscribe_template:{userType}
     * VALUE 数据格式 String, 模版信息
     */
    String WXA_SUBSCRIBE_TEMPLATE = "ym:message:wxa:subscribe_template";
}
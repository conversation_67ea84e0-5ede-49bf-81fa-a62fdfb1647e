package com.fozmo.ym.framework.tenant.core.db;

import com.fozmo.ym.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.EqualsAndHashCode;

/**
 * 拓展多租户的 BaseDO 基类
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class TenantBaseDO extends BaseDO {

    /**
     * 多租户编号
     */
    private Long tenantId;

}

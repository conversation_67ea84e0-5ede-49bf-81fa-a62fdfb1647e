<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-framework</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ym-spring-boot-starter-job</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>任务拓展
        1. 定时任务，基于 Quartz 拓展
        2. 异步任务，基于 Spring Async 拓展
    </description>
    <url></url>

    <dependencies>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-common</artifactId>
        </dependency>
        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Job 相关 -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>3.0.0</version>
        </dependency>


        <!-- 工具类相关 -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

    </dependencies>

</project>

package com.fozmo.ym.framework.mybatis.core.util;

import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

@Service
public class IdServiceImpl implements IdService {
	// 业务逻辑
	private static final String DAILY_KEY_PREFIX = "ym:daily:"; // 按日流水号
	private static final String DEFAULT_KEY_PREFIX = "ym:seq:"; // 通用自增ID
	private static final long SNOWFLAKE_EPOCH = 1672531200000L; // 2023-01-01
	
	@Resource
	private RedisTemplate<String, Long> redisTemplate;
	
	// ========== 基础自增ID ==========
	@Override
	public long nextId(String bizKey) {
		try {
			String key = DEFAULT_KEY_PREFIX + bizKey;
			String  id =  redisTemplate.opsForValue().increment(key)+""; // 原子自增
			long random = RandomUtil.randomLong(1000, 9999);
			return Long.parseLong(id+random);
		} catch (Exception e) {
			return generateSnowflakeId(); // Redis故障时降级
		}
	}
	
	// ========== 按日流水号（格式：yyyyMMdd0001） ==========
	public String nextDailyId(String bizKey) {
		String date = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE); // yyyyMMdd
		String key = DAILY_KEY_PREFIX + bizKey + ":" + date;
		
		try {
			Long seq = redisTemplate.opsForValue().increment(key);
			// 首次设置过期时间（24小时）
			if (seq == 1L) {
				redisTemplate.expire(key, 1, TimeUnit.DAYS);
			}
			return date + String.format("%04d", seq); // 补零至4位
		} catch (Exception e) {
			return date + "-" + generateSnowflakeId(); // 降级方案
		}
	}
	
	// ========== 批量获取ID（减少Redis压力） ==========
	public long nextBatch(String bizKey, int batchSize) {
		String key = DEFAULT_KEY_PREFIX + bizKey;
		// Lua脚本保证原子性
		String luaScript = "local current = redis.call('INCRBY', KEYS[1], ARGV[1]); " +
				                   "return current - tonumber(ARGV[1])";
		DefaultRedisScript<Long> script = new DefaultRedisScript<>(luaScript, Long.class);
		return redisTemplate.execute(script, Collections.singletonList(key), batchSize);
	}
	
	// ========== Snowflake降级算法 ==========
	private long generateSnowflakeId() {
		long timestamp = System.currentTimeMillis() - SNOWFLAKE_EPOCH;
		long workerId = Thread.currentThread().getId() % 32; // 简易机器ID
		long sequence = (long) (Math.random() * 4096); // 随机序列
		return (timestamp << 22) | (workerId << 12) | sequence;
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>ym-module</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <modules>
        <!-- 后管系统模块-->
        <module>ym-module-system</module>
        <!-- 内部模块：通用能力、监控等-->
        <module>ym-module-infra</module>
        <!-- 空间模块 包括空间、模板、作品、挂载、编辑器等信息-->
        <module>ym-module-space</module>
        <!-- 账户模块 包括会员管理、会员等级、会员群组、会员中心 -->
        <module>ym-module-account</module>
        <!-- 营销模块 营销活动 营销规则、营销公告、营销信息发布、优惠券 -->
        <module>ym-module-market</module>
        <!-- 商家管理（系统和企业管理员两种商户，系统默认初始化、企业会员管理员需要认证）商品(会员、各种虚拟资源（付费观看的画展、数字形象、背景音乐等等)、等-->
        <module>ym-module-shop</module>
        <!-- 权益模块：会员权益、规则、权益限制 权益计算、权益任务（生效失效等）-->
        <module>ym-module-rights</module>
        <!-- 短信模块：短信模板定制、短信发送、发送日志、短信分类场景等-->
        <module>ym-module-sms</module>
        <!-- 消息模块-空间游览专用-->
        <module>ym-module-information</module>
        <!-- 鉴权模块：权限控制、权限信息、登陆、退出等-->
        <module>ym-module-auth</module>
        <!-- 日志 模块：包括行为日志、用户操作流水、异常记录-->
        <module>ym-module-log</module>
        <!--租户 模块-->
        <module>ym-module-tenant</module>
        <!--文件 模块：文件上传下载等管理-->
        <module>ym-module-file</module>
        <!-- 社交模块：包括评论、粉丝、点赞、分享、三方社交用户-->
        <module>ym-module-social</module>
        <!-- 公告模块 站内信、系统通知等-->
        <module>ym-module-notice</module>
        <!-- AI 模块：AI 模型、AI 模型训练、AI 模型预测、AI 模型发布、AI 模型使用等-->
        <module>ym-module-ai</module>
        <!-- 支付 模块：支付订单、支付渠道、支付流水、支付日志、支付规则、支付优惠券、支付优惠券使用等-->
        <module>ym-module-pay</module>


    </modules>

</project>
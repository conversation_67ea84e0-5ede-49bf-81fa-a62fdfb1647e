package com.fozmo.ym.module.account.api.account;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.api.account.dto.*;

public interface AccountApi {
    

//    AccountInfoDTO queryAccountInfoById(Long id);

    void updateAccountInfo(UpdateAccountDTO accountInfoDTO);

    PageResult<AccountInfoDTO> queryAccountInfoList(Integer pageNo, Integer pageSize, String nickname);
    
    
    
    // 创建账户 创建时 既要 创建 accountBase 又要 创建 accountinfo

    // 查询基础账户信息
    AccountBaseInfoDTO queryAccountBaseInfoById(Long id);

    // 查询用户权益信息
    AccountRightsInfoDTO queryAccountRightsInfoById(Long id);

    Long createAccount(CreateAccountDTO createAccountDTO);
    // 查询我的账户信息


    // 缓存操作
    // 检查缓存是否存在
     Boolean checkAccountMobile(String mobile);

     Boolean checkAccountOpenId(String openId);

     void saveAccountOpenId(Long accountId,String openId);

     Long getAccountByOpenId(String openId);

     void saveAccountMobile(Long accountId,String mobile);

    Long getAccountByMobile(String mobile);
}

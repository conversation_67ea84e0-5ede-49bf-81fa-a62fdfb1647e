package com.fozmo.ym.module.account.api.account.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class UpdateAccountDTO {
	private Long id;
	/**
	 * 账户名称
	 */
	private String name;
	/**
	 * 唯一标识
	 */
	private String code;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 0 自然流量   1推广用户    2投放用户
	 */
	private Integer accountType;
	/**
	 * 会员等级
	 */
	private Integer rightsLevel;
	/**
	 * 权益id
	 */
	private Long rightsId;
	/**
	 * 注册时间
	 */
	private LocalDateTime registTime;
	
	private LocalDate registDate;
	/**
	 * 0 男 1 女  3未知
	 */
	private Integer sex;
	/**
	 * 注册ip
	 */
	private String registIp;
	/**
	 * 注册设备
	 */
	private String registDevice;
	/**
	 * 注册渠道
	 */
	private String registerChannel;
	/**
	 * 账户状态 0 正常 1 停权 2封禁
	 */
	private Integer accountStatus;
	/**
	 * 最后登陆时间
	 */
	private LocalDateTime lastLoginTime;
	/**
	 * 最后登陆设备
	 */
	private String lastLoginDevice;
	/**
	 * 昵称
	 */
	private String nickname;
	/**
	 * 用户头像
	 */
	private String avatar;
	/**
	 * 积分
	 */
	private Long point;
	/**
	 * 会员分组
	 */
	private Long groupId;
	
	private String password;
	
	private String description;
	
	private String guidStatus;
}

package com.fozmo.ym.module.account.enums;


import com.fozmo.ym.framework.common.exception.ErrorCode;

/**
 * Member 错误码枚举类
 * <p>
 * account 系统，使用 80000 段
 */
public interface ErrorCodeConstants {
    ErrorCode ACCOUNT_NOT_EXISTS = new ErrorCode(800001, "账户不存在");
    ErrorCode USER_MOBILE_NOT_EXISTS = new ErrorCode(800002, "手机号未注册用户");
    ErrorCode USER_MOBILE_USED = new ErrorCode(800003, "修改手机失败，该手机号({})已经被使用");
    ErrorCode USER_POINT_NOT_ENOUGH = new ErrorCode(800004, "用户积分余额不足");
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(800005, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(800006, "登录失败，账号被禁用");
    ErrorCode AUTH_SOCIAL_USER_NOT_FOUND = new ErrorCode(800007, "登录失败，解析不到三方登录信息");
    ErrorCode AUTH_MOBILE_USED = new ErrorCode(800008, "手机号已经被使用");
    ErrorCode ADDRESS_NOT_EXISTS = new ErrorCode(800009, "用户收件地址不存在");
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(800010, "用户标签不存在");
    ErrorCode TAG_NAME_EXISTS = new ErrorCode(800011, "用户标签已经存在");
    ErrorCode TAG_HAS_USER = new ErrorCode(800012, "用户标签下存在用户，无法删除");
    ErrorCode POINT_RECORD_BIZ_NOT_SUPPORT = new ErrorCode(800013, "用户积分记录业务类型不支持");
    ErrorCode SIGN_IN_CONFIG_NOT_EXISTS = new ErrorCode(800014, "签到天数规则不存在");
    ErrorCode SIGN_IN_CONFIG_EXISTS = new ErrorCode(800015, "签到天数规则已存在");
    ErrorCode SIGN_IN_RECORD_TODAY_EXISTS = new ErrorCode(800016, "今日已签到，请勿重复签到");
    ErrorCode LEVEL_NOT_EXISTS = new ErrorCode(800017, "用户等级不存在");
    ErrorCode LEVEL_NAME_EXISTS = new ErrorCode(800018, "用户等级名称[{}]已被使用");
    ErrorCode LEVEL_VALUE_EXISTS = new ErrorCode(800019, "用户等级值[{}]已被[{}]使用");
    ErrorCode LEVEL_EXPERIENCE_MIN = new ErrorCode(800020, "升级经验必须大于上一个等级[{}]设置的升级经验[{}]");
    ErrorCode LEVEL_EXPERIENCE_MAX = new ErrorCode(800021, "升级经验必须小于下一个等级[{}]设置的升级经验[{}]");
    ErrorCode LEVEL_HAS_USER = new ErrorCode(800022, "用户等级下存在用户，无法删除");
    ErrorCode EXPERIENCE_BIZ_NOT_SUPPORT = new ErrorCode(800023, "用户经验业务类型不支持");
    ErrorCode GROUP_NOT_EXISTS = new ErrorCode(800024, "用户分组不存在");
    ErrorCode GROUP_HAS_USER = new ErrorCode(800025, "用户分组下存在用户，无法删除");

    ErrorCode HUMAN_IS_NOT_NULL = new ErrorCode(800026, "用户已存在数字人，无法新增");

    ErrorCode HUMAN_IS_NULL = new ErrorCode(800027, "数字人形象不存在，请重新选择");

    ErrorCode HUMAN_NOT = new ErrorCode(800027, "当前用户权益无法开通数字人，请重新选择");

}

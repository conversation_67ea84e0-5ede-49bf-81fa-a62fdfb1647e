package com.fozmo.ym.module.account.api.account;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.account.api.account.dto.*;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoPageReqVO;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoSaveReqVO;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import com.fozmo.ym.module.account.service.info.AccountInfoService;
import com.fozmo.ym.module.account.service.my.MyAccountService;
import com.fozmo.ym.module.rights.api.RightsApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AccountApiImpl implements AccountApi {

    @Resource
    private MyAccountService myAccountService;

    @Resource
    private AccountInfoService accountInfoService;

    @Resource
    private RightsApi rightsApi;

    @Override
    public List<Long> getAccountId() {
        return accountInfoService.getAccountId();
    }

/**
     * @param accountInfoDTO
     */
    @Override
    public void updateAccountInfo(UpdateAccountDTO accountInfoDTO) {

        AccountInfoSaveReqVO reqVO = BeanUtil.toBean(accountInfoDTO, AccountInfoSaveReqVO.class);
        accountInfoService.updateInfo(reqVO);
    }

/**
 * @param pageNo
 * @param pageSize
 *
 * @return
 */
    @Override
    public PageResult<AccountInfoDTO> queryAccountInfoList(Integer pageNo, Integer pageSize, String nickname) {
        
        AccountInfoPageReqVO reqVO = new AccountInfoPageReqVO();
        reqVO.setPageNo(pageNo);
        reqVO.setPageSize(pageSize);
        reqVO.setNickname(nickname);
        reqVO.setAccountStatus(0);
        // 首页账户查询
        
        PageResult<AccountInfoDO> pageResult = accountInfoService.getInfoPage(reqVO);
        if (ObjectUtil.isEmpty(pageResult)) {
            return PageResult.empty();
        }
        if (CollUtil.isNotEmpty(pageResult.getList())) {
            PageResult<AccountInfoDTO> dtoResult = new PageResult<>();
            // 复制总记录数
            dtoResult.setList(pageResult.getList().stream().map(accountInfoDO -> BeanUtil.toBean(accountInfoDO, AccountInfoDTO.class)).collect(Collectors.toList()));
            dtoResult.setTotal(pageResult.getTotal());
            return dtoResult;
        } else {
            return PageResult.empty();
        }

        }

    /**
     * @param id
     *
     * @return
     */
    @Override
    public AccountBaseInfoDTO queryAccountBaseInfoById(Long id) {
        AccountInfoDO accountInfoDO = accountInfoService.getInfo(id);
        return BeanUtil.toBean(accountInfoDO, AccountBaseInfoDTO.class);
    }

    /**
     * @param id 
     * @return
     */
    @Override
    public AccountRightsInfoDTO queryAccountRightsInfoById(Long id) {
        AccountRightsInfoDTO accountRightsInfoDTO = new AccountRightsInfoDTO();
        AccountInfoDO accountInfoDO = accountInfoService.getInfo(id);
        accountRightsInfoDTO = BeanUtil.toBean(accountInfoDO, AccountRightsInfoDTO.class);

        // 处理权益信息
        Long rightsId = accountInfoDO.getRightsId();
        if (rightsId != null) {
            accountRightsInfoDTO.setRightsInfo(rightsApi.getRightsInfo(rightsId));
        }
        return accountRightsInfoDTO;
    }

    /**
     * @param createAccountDTO
     */
    @Override
    public Long createAccount(CreateAccountDTO createAccountDTO) {
        
        AccountInfoSaveReqVO reqVO = BeanUtil.toBean(createAccountDTO, AccountInfoSaveReqVO.class);
        return accountInfoService.createInfo(reqVO);
    }

    /**
     * @param mobile
     *
     * @return
     */
    @Override
    public Boolean checkAccountMobile(String mobile) {
        return accountInfoService.checkAccountMobile(mobile);
    }
    
    /**
     * @param openId
     *
     * @return
     */
    @Override
    public Boolean checkAccountOpenId(String openId) {
        return accountInfoService.checkAccountOpenId(openId);
    }
    
    /**
     * @param accountId
     * @param openId
     */
    @Override
    public void saveAccountOpenId(Long accountId, String openId) {
        accountInfoService.saveAccountOpenId(accountId,openId);
    }
    
    /**
     * @param openId
     *
     * @return
     */
    @Override
    public Long getAccountByOpenId(String openId) {
        AccountInfoDO accountInfoDO = accountInfoService.getAccountByOpenId(openId);
        if (ObjectUtil.isNotEmpty(accountInfoDO)){
            return accountInfoDO.getId();
        }
        return 0L ;
    }
    
    /**
     * @param accountId
     * @param mobile
     */
    @Override
    public void saveAccountMobile(Long accountId, String mobile) {
        accountInfoService.saveAccountMobile(accountId,mobile);
    }
    
    /**
     * @param mobile
     *
     * @return
     */
    @Override
    public Long getAccountByMobile(String mobile) {
        AccountInfoDO accountInfoDO = accountInfoService.getAccountByMobile(mobile);
        if (ObjectUtil.isNotEmpty(accountInfoDO)){
            return accountInfoDO.getId();
        }
       return 0L ;
    }

    private AccountInfoDTO convertToDTO(AccountInfoDO doObj) {
        return BeanUtils.toBean(doObj, AccountInfoDTO.class);
    }

}

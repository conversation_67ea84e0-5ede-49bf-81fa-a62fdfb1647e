package com.fozmo.ym.module.account.api.human;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.account.api.account.HumanApi;
import com.fozmo.ym.module.account.api.account.dto.HumanDTO;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanSaveReqVO;
import com.fozmo.ym.module.account.service.human.AccountHumanService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class HumanApiImpl implements HumanApi {

	@Resource
	private AccountHumanService accountHumanService;
	/**
	 * 创建数字人
	 *
	 * @param humanDTO
	 */
	@Override
	public Long createHuman(HumanDTO humanDTO) {
		AccountHumanSaveReqVO reqVO = BeanUtils.toBean(humanDTO, AccountHumanSaveReqVO.class);
		return accountHumanService.createHuman(reqVO);
	}
}

//package com.fozmo.ym.module.account.controller.admin.group;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.account.controller.admin.group.vo.*;
//import com.fozmo.ym.module.account.dal.dataobject.group.AccountGroupDO;
//import com.fozmo.ym.module.account.service.group.AccountGroupService;
//
//@Tag(name = "管理后台 - 账户群组")
//@RestController
//@RequestMapping("/account/group")
//@Validated
//public class AccountGroupController {
//
//    @Resource
//    private AccountGroupService groupService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建账户群组")
//    @PreAuthorize("@ss.hasPermission('account:group:create')")
//    public CommonResult<Long> createGroup(@Valid @RequestBody AccountGroupSaveReqVO createReqVO) {
//        return success(groupService.createGroup(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新账户群组")
//    @PreAuthorize("@ss.hasPermission('account:group:update')")
//    public CommonResult<Boolean> updateGroup(@Valid @RequestBody AccountGroupSaveReqVO updateReqVO) {
//        groupService.updateGroup(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除账户群组")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('account:group:delete')")
//    public CommonResult<Boolean> deleteGroup(@RequestParam("id") Long id) {
//        groupService.deleteGroup(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得账户群组")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('account:group:query')")
//    public CommonResult<AccountGroupRespVO> getGroup(@RequestParam("id") Long id) {
//        AccountGroupDO group = groupService.getGroup(id);
//        return success(BeanUtils.toBean(group, AccountGroupRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得账户群组分页")
//    @PreAuthorize("@ss.hasPermission('account:group:query')")
//    public CommonResult<PageResult<AccountGroupRespVO>> getGroupPage(@Valid AccountGroupPageReqVO pageReqVO) {
//        PageResult<AccountGroupDO> pageResult = groupService.getGroupPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, AccountGroupRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出账户群组 Excel")
//    @PreAuthorize("@ss.hasPermission('account:group:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportGroupExcel(@Valid AccountGroupPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<AccountGroupDO> list = groupService.getGroupPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "账户群组.xls", "数据", AccountGroupRespVO.class,
//                        BeanUtils.toBean(list, AccountGroupRespVO.class));
//    }
//
//}
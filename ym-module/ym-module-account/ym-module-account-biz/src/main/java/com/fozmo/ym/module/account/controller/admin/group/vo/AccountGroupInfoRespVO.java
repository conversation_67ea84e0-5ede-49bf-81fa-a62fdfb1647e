package com.fozmo.ym.module.account.controller.admin.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "账户分组返回详情")
public class AccountGroupInfoRespVO {
    @Schema(description = "分组id")
    private Long id;
    @Schema(description = "分组名称")
    private String groupName;
}

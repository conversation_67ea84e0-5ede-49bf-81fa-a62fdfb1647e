package com.fozmo.ym.module.account.controller.admin.group.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 账户群组 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AccountGroupRespVO {

    @Schema(description = "群组", requiredMode = Schema.RequiredMode.REQUIRED, example = "6973")
    @ExcelProperty("群组")
    private Long id;

    @Schema(description = "群组名称", example = "王五")
    @ExcelProperty("群组名称")
    private String groupName;

    @Schema(description = "状态 0 正常", example = "1")
    @ExcelProperty("状态 0 正常")
    private Integer status;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String description;

    @Schema(description = "创建人id", example = "26328")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "23950")
    @ExcelProperty("更新人")
    private Long updateId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

    @Schema(description = "0 系统预置 1 管理新增 3 营销新增")
    @ExcelProperty("0 系统预置 1 管理新增 3 营销新增")
    private Integer createDefault;

}
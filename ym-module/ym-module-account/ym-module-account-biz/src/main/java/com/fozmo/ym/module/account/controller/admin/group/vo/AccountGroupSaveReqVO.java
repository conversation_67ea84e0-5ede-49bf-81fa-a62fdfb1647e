package com.fozmo.ym.module.account.controller.admin.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 账户群组新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AccountGroupSaveReqVO {

    @Schema(description = "群组", requiredMode = Schema.RequiredMode.REQUIRED, example = "6973")
    private Long id;

    @Schema(description = "群组名称", example = "王五")
    private String groupName;

    @Schema(description = "状态 0 正常", example = "1")
    private Integer status;

    @Schema(description = "备注", example = "随便")
    private String description;

    @Schema(description = "创建人id", example = "26328")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "23950")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

    @Schema(description = "0 系统预置 1 管理新增 3 营销新增")
    private Integer createDefault;

}
//package com.fozmo.ym.module.account.controller.admin.human;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanPageReqVO;
//import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanRespVO;
//import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanSaveReqVO;
//import com.fozmo.ym.module.account.dal.dataobject.human.AccountHumanDO;
//import com.fozmo.ym.module.account.service.human.AccountHumanService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.Valid;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.io.IOException;
//import java.util.List;
//
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "后管-账户模块-数字人")
//@RestController
//@RequestMapping("/account/human")
//public class HumanController {
//
//	@Resource
//	private AccountHumanService humanService;
//
//	@PostMapping("/create")
//	@Operation(summary = "创建数字人")
//	@PreAuthorize("@ss.hasPermission('account:human:create')")
//	public CommonResult<Long> createHuman(@Valid @RequestBody AccountHumanSaveReqVO createReqVO) {
//		return success(humanService.createHuman(createReqVO));
//	}
//
//	@PutMapping("/update")
//	@Operation(summary = "更新数字人")
//	@PreAuthorize("@ss.hasPermission('account:human:update')")
//	public CommonResult<Boolean> updateHuman(@Valid @RequestBody AccountHumanSaveReqVO updateReqVO) {
//		humanService.updateHuman(updateReqVO);
//		return success(true);
//	}
//
//	@DeleteMapping("/delete")
//	@Operation(summary = "删除数字人")
//	@Parameter(name = "id", description = "编号", required = true)
//	@PreAuthorize("@ss.hasPermission('account:human:delete')")
//	public CommonResult<Boolean> deleteHuman(@RequestParam("id") Long id) {
//		humanService.deleteHuman(id);
//		return success(true);
//	}
//
//	@GetMapping("/get")
//	@Operation(summary = "获得数字人")
//	@Parameter(name = "id", description = "编号", required = true, example = "1024")
//	@PreAuthorize("@ss.hasPermission('account:human:query')")
//	public CommonResult<AccountHumanRespVO> getHuman(@RequestParam("id") Long id) {
//		AccountHumanDO human = humanService.getHuman(id);
//		return success(BeanUtils.toBean(human, AccountHumanRespVO.class));
//	}
//
//	@GetMapping("/page")
//	@Operation(summary = "获得数字人分页")
//	@PreAuthorize("@ss.hasPermission('account:human:query')")
//	public CommonResult<PageResult<AccountHumanRespVO>> getHumanPage(@Valid AccountHumanPageReqVO pageReqVO) {
//		PageResult<AccountHumanDO> pageResult = humanService.getHumanPage(pageReqVO);
//		return success(BeanUtils.toBean(pageResult, AccountHumanRespVO.class));
//	}
//
//	@GetMapping("/export-excel")
//	@Operation(summary = "导出数字人 Excel")
//	@PreAuthorize("@ss.hasPermission('account:human:export')")
//	@ApiAccessLog(operateType = EXPORT)
//	public void exportHumanExcel(@Valid AccountHumanPageReqVO pageReqVO,
//	                             HttpServletResponse response) throws IOException {
//		pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//		List<AccountHumanDO> list = humanService.getHumanPage(pageReqVO).getList();
//		// 导出 Excel
//		ExcelUtils.write(response, "数字人.xls", "数据", AccountHumanRespVO.class,
//				BeanUtils.toBean(list, AccountHumanRespVO.class));
//	}
//}

package com.fozmo.ym.module.account.controller.admin.human.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数字人 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AccountHumanRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32436")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "数字人id", example = "22929")
    @ExcelProperty("数字人id")
    private Long humanId;

    @Schema(description = "账户id", example = "31984")
    @ExcelProperty("账户id")
    private Long accountId;

}
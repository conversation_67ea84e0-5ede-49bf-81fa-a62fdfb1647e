package com.fozmo.ym.module.account.controller.admin.human.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数字人新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AccountHumanSaveReqVO {

    @Schema(description = "id,新增不传递，更新必传", requiredMode = Schema.RequiredMode.REQUIRED, example = "32436")
    private Long id;

    @Schema(description = "数字人id", example = "22929")
    @NotNull(message = "数字人Id不能为空")
    private Long humanId;

    @Schema(description = "账户id", example = "31984")
    private Long accountId;
}
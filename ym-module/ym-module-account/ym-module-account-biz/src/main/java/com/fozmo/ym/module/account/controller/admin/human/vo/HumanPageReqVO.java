package com.fozmo.ym.module.account.controller.admin.human.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数字人配置分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HumanPageReqVO extends PageParam {

@Schema(description = "数字人名称", example = "张三")
private String name;

@Schema(description = "权限", example = "15188")
private Long rightsId;
}

package com.fozmo.ym.module.account.controller.admin.human.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数字人配置新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class HumanSaveReqVO {

	@Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6033")
	private Long id;
	
	@Schema(description = "数字人名称", example = "张三")
	private String name;
	
	@Schema(description = "说明", example = "你猜")
	private String description;
	
	@Schema(description = "数字人文件路径")
	private String filePath;
	
	@Schema(description = "数字人文件后缀")
	private String fileSuffix;
	
	@Schema(description = "数字人文件状态", example = "1")
	private Integer fileStatus;
	
	@Schema(description = "数字人属性")
	private String fileAttribute;
	
	@Schema(description = "文件桶")
	private String fileBucket;
	
	@Schema(description = "权限", example = "15188")
	private Long rightsId;
	
	@Schema(description = "数字人文件url", example = "https://www.iocoder.cn")
	private String fileUrl;
	
	@Schema(description = "租户Code")
	private String tenantCode;
	
	@Schema(description = "封面")
	private String cover;
}

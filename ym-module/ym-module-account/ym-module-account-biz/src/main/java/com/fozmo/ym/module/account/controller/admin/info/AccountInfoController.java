//package com.fozmo.ym.module.account.controller.admin.info;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.account.controller.admin.info.vo.*;
//import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
//import com.fozmo.ym.module.account.service.info.AccountInfoService;
//
//@Tag(name = "后管-账户管理-账户信息")
//@RestController
//@RequestMapping("/account/info")
//@Validated
//public class AccountInfoController {
//
//    @Resource
//    private AccountInfoService infoService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建账户信息")
//    @PreAuthorize("@ss.hasPermission('account:info:create')")
//    public CommonResult<Long> createInfo(@Valid @RequestBody AccountInfoSaveReqVO createReqVO) {
//        return success(infoService.createInfo(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新账户信息")
//    @PreAuthorize("@ss.hasPermission('account:info:update')")
//    public CommonResult<Boolean> updateInfo(@Valid @RequestBody AccountInfoSaveReqVO updateReqVO) {
//        infoService.updateInfo(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除账户信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('account:info:delete')")
//    public CommonResult<Boolean> deleteInfo(@RequestParam("id") Long id) {
//        infoService.deleteInfo(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得账户信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('account:info:query')")
//    public CommonResult<AccountInfoRespVO> getInfo(@RequestParam("id") Long id) {
//        AccountInfoDO info = infoService.getInfo(id);
//        return success(BeanUtils.toBean(info, AccountInfoRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得账户信息分页")
//    @PreAuthorize("@ss.hasPermission('account:info:query')")
//    public CommonResult<PageResult<AccountInfoRespVO>> getInfoPage(@Valid AccountInfoPageReqVO pageReqVO) {
//        PageResult<AccountInfoDO> pageResult = infoService.getInfoPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, AccountInfoRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出账户信息 Excel")
//    @PreAuthorize("@ss.hasPermission('account:info:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportInfoExcel(@Valid AccountInfoPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<AccountInfoDO> list = infoService.getInfoPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "账户信息.xls", "数据", AccountInfoRespVO.class,
//                        BeanUtils.toBean(list, AccountInfoRespVO.class));
//    }
//
//}
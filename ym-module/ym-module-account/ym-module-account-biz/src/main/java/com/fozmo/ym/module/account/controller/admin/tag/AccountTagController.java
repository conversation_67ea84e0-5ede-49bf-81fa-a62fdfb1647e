//package com.fozmo.ym.module.account.controller.admin.tag;
//
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagCreateReqVO;
//import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagPageReqVO;
//import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagRespVO;
//import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagUpdateReqVO;
//import com.fozmo.ym.module.account.convert.tag.AccountTagConvert;
//import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
//import com.fozmo.ym.module.account.service.tag.AccountTagService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.validation.Valid;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Collection;
//import java.util.List;
//
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "管理后台 - 会员标签")
//@RestController
//@RequestMapping("/member/tag")
//@Validated
//public class AccountTagController {
//
//    @Resource
//    private AccountTagService tagService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建会员标签")
//    @PreAuthorize("@ss.hasPermission('member:tag:create')")
//    public CommonResult<Long> createTag(@Valid @RequestBody AccountTagCreateReqVO createReqVO) {
//        return success(tagService.createTag(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新会员标签")
//    @PreAuthorize("@ss.hasPermission('member:tag:update')")
//    public CommonResult<Boolean> updateTag(@Valid @RequestBody AccountTagUpdateReqVO updateReqVO) {
//        tagService.updateTag(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除会员标签")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('member:tag:delete')")
//    public CommonResult<Boolean> deleteTag(@RequestParam("id") Long id) {
//        tagService.deleteTag(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得会员标签")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('member:tag:query')")
//    public CommonResult<AccountTagRespVO> getAccountTag(@RequestParam("id") Long id) {
//        AccountTagDO tag = tagService.getTag(id);
//        return success(AccountTagConvert.INSTANCE.convert(tag));
//    }
//
//    @GetMapping("/list-all-simple")
//    @Operation(summary = "获取会员标签精简信息列表", description = "只包含被开启的会员标签，主要用于前端的下拉选项")
//    public CommonResult<List<AccountTagRespVO>> getSimpleTagList() {
//        // 获用户列表，只要开启状态的
//        List<AccountTagDO> list = tagService.getTagList();
//        // 排序后，返回给前端
//        return success(AccountTagConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得会员标签列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('member:tag:query')")
//    public CommonResult<List<AccountTagRespVO>> getAccountTagList(@RequestParam("ids") Collection<Long> ids) {
//        List<AccountTagDO> list = tagService.getTagList(ids);
//        return success(AccountTagConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得会员标签分页")
//    @PreAuthorize("@ss.hasPermission('member:tag:query')")
//    public CommonResult<PageResult<AccountTagRespVO>> getTagPage(@Valid AccountTagPageReqVO pageVO) {
//        PageResult<AccountTagDO> pageResult = tagService.getTagPage(pageVO);
//        return success(AccountTagConvert.INSTANCE.convertPage(pageResult));
//    }
//
//}

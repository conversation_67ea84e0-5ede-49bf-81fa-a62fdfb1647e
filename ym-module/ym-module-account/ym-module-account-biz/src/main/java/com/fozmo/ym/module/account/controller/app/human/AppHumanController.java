package com.fozmo.ym.module.account.controller.app.human;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanSaveReqVO;
import com.fozmo.ym.module.account.controller.admin.human.vo.HumanPageReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppAccountHumanInfoResp;
import com.fozmo.ym.module.account.controller.app.human.vo.AppAccountHumanSaveReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppHumanPageReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppHumanRespVO;
import com.fozmo.ym.module.account.dal.dataobject.human.HumanDO;
import com.fozmo.ym.module.account.service.human.AccountHumanService;
import com.fozmo.ym.module.account.service.human.HumanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-账户模块-数字人")
@RestController
@RequestMapping("/account/human")
public class AppHumanController {

	@Resource
	private HumanService humanService;
	
	@Resource
	private AccountHumanService accountHumanService;

	/**
	 * 我的数字人、他人数字人
	 */
	@GetMapping("/humanInfo")
	@Operation(summary = "用户数字人详情",description = "id 不传入时 查询自己数字人信息")
	@Parameter(name = "id", description = "编号", required = true)
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAccountHumanInfoResp> getAppHumanInfo(@RequestParam(required = false) Long accountId) {
		if (accountId == null) {
			accountId = SecurityFrameworkUtils.getLoginUserId();
		}
		AppAccountHumanInfoResp humanInfoResp = accountHumanService.getHumanByAccountId(accountId);
		return CommonResult.success(humanInfoResp);
		
	}
	
	@GetMapping("/humanPage")
	@Operation(summary = "数字人列表",description = "选择数字人列表")
	@PermitAll
	@ApiAccessLog
	public CommonResult<PageResult<AppHumanRespVO>> getHumanPage(@Valid AppHumanPageReqVO pageReqVO) {
		HumanPageReqVO reqVO = BeanUtils.toBean(pageReqVO, HumanPageReqVO.class);
		PageResult<HumanDO> pageResult = humanService.getHumanPage(reqVO);
		return success(BeanUtils.toBean(pageResult, AppHumanRespVO.class));
	}

	@GetMapping("/humanList")
	@Operation(summary = "默认数字人列表",description = "选择数字人列表")
	@PermitAll
	@ApiAccessLog
	public CommonResult<List<AppHumanRespVO>> humanList() {
		List<HumanDO> humanList = humanService.getHumanList();
		return success(BeanUtils.toBean(humanList, AppHumanRespVO.class));
	}
	
	@PostMapping("/createHuman")
	@Operation(summary = "新增用户数字人")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Long> createHuman(@RequestBody AppAccountHumanSaveReqVO createReqVO) {
		AccountHumanSaveReqVO reqVO= BeanUtils.toBean(createReqVO, AccountHumanSaveReqVO.class);
		return success(accountHumanService.createHuman(reqVO));
	}

	@PostMapping("/updateHuman")
	@Operation(summary = "修改用户数字人")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Boolean> updateHuman(@RequestBody AppAccountHumanSaveReqVO createReqVO) {
		AccountHumanSaveReqVO reqVO= BeanUtils.toBean(createReqVO, AccountHumanSaveReqVO.class);
		accountHumanService.updateHuman(reqVO);
		return success(Boolean.TRUE);
	}
	
	
	
	
}

package com.fozmo.ym.module.account.controller.app.human.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "App - 数字人返回")
@Accessors(chain = true)
@Data
public class AppAccountHumanInfoResp {
@Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6033")
private Long id;

@Schema(description = "主键", example = "6035")
private Long accountId;

@Schema(description = "账户名称", example = "王五")
private String accountName;

@Schema(description = "humanId")
private Long humanId;

@Schema(description = "humanName")
private String humanName;

@Schema(description = "说明", example = "你猜")
private String description;

@Schema(description = "权限", example = "15188")
private Long rightsId;

@Schema(description = "数字人文件url", example = "https://www.iocoder.cn")
private String fileUrl;

@Schema(description = "封面")
private String cover;
}

package com.fozmo.ym.module.account.controller.app.human.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "App - 创建用户数字人请求")
@Accessors(chain = true)
@Data
public class AppAccountHumanSaveReqVO {

	@Schema(description = "id", example = "6035")
	private Long id;

	@Schema(description = "账户id", example = "6035")
	private Long accountId;
	
	@Schema(description = "humanId")
	private Long humanId;
	
}

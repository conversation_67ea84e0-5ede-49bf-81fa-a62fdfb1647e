package com.fozmo.ym.module.account.controller.app.human.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数字人配置分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppHumanPageReqVO extends PageParam {

    @Schema(description = "数字人名称", example = "张三")
    private String name;

    @Schema(description = "说明", example = "你猜")
    private String description;

    @Schema(description = "数字人文件路径")
    private String filePath;

    @Schema(description = "数字人文件后缀")
    private String fileSuffix;

    @Schema(description = "数字人文件状态", example = "1")
    private Integer fileStatus;

    @Schema(description = "数字人属性")
    private String fileAttribute;

    @Schema(description = "文件桶")
    private String fileBucket;

    @Schema(description = "权限", example = "15188")
    private Long rightsId;

    @Schema(description = "数字人文件url", example = "https://www.iocoder.cn")
    private String fileUrl;

}
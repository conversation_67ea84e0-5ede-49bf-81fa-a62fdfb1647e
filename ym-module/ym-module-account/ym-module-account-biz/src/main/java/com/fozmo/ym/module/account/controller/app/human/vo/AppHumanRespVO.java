package com.fozmo.ym.module.account.controller.app.human.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数字人配置 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppHumanRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6033")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "数字人名称", example = "张三")
    @ExcelProperty("数字人名称")
    private String name;

    @Schema(description = "说明", example = "你猜")
    @ExcelProperty("说明")
    private String description;

    @Schema(description = "数字人文件路径")
    @ExcelProperty("数字人文件路径")
    private String filePath;

    @Schema(description = "数字人文件后缀")
    @ExcelProperty("数字人文件后缀")
    private String fileSuffix;

    @Schema(description = "数字人文件状态", example = "1")
    @ExcelProperty("数字人文件状态")
    private Integer fileStatus;

    @Schema(description = "权限", example = "15188")
    @ExcelProperty("权限")
    private Long rightsId;

    @Schema(description = "数字人文件url", example = "https://www.iocoder.cn")
    private String fileUrl;
    
    private String cover;

    private Integer humanSort;
    

}
package com.fozmo.ym.module.account.controller.app.info;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoSaveReqVO;
import com.fozmo.ym.module.account.controller.app.info.vo.AccountOtherInfoRespVO;
import com.fozmo.ym.module.account.controller.app.info.vo.AppAccountInfoSaveReqVO;
import com.fozmo.ym.module.account.service.info.AccountInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-账户模块-账户")
@RestController
@RequestMapping("/account")
public class AppAccountController {

    @Resource
    private AccountInfoService infoService;

    @PostMapping("/update")
    @Operation(description = "更新账户信息")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> updateInfo(@Valid @RequestBody AppAccountInfoSaveReqVO updateReqVO) {

        AccountInfoSaveReqVO reqVO = BeanUtils.toBean(updateReqVO, AccountInfoSaveReqVO.class);
        reqVO.setId(SecurityFrameworkUtils.getLoginUserId());
        infoService.updateInfo(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除账户信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> deleteInfo(@RequestParam("id") Long id) {
        infoService.deleteInfo(id);
        return success(true);
    }
    
    @GetMapping("/other")
    @Operation(summary = "获取他人账户信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<AccountOtherInfoRespVO> getOtherInfo(@RequestParam("id") Long id) {
        return success(infoService.getOtherInfo(id));
    }

}

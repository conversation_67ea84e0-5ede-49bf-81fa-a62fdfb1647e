package com.fozmo.ym.module.account.controller.app.info.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 账户信息分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountInfoPageReqVO extends PageParam {

    @Schema(description = "账户名称", example = "王五")
    private String name;

    @Schema(description = "唯一标识")
    private String code;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "0 自然流量   1推广用户    2投放用户", example = "1")
    private Integer accountType;

    @Schema(description = "会员等级")
    private Integer rightsLevel;

    @Schema(description = "权益id", example = "7736")
    private Long rightsId;

    @Schema(description = "注册时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registTime;

    @Schema(description = "0 男 1 女  3未知")
    private Integer sex;

    @Schema(description = "注册ip")
    private Integer registIp;

    @Schema(description = "注册设备")
    private String registDevice;

    @Schema(description = "注册渠道")
    private String registerChannel;

    @Schema(description = "账户状态 0 正常 1 停权 2封禁", example = "2")
    private Integer accountStatus;

    @Schema(description = "最后登陆时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastLoginTime;

    @Schema(description = "最后登陆设备")
    private String lastLoginDevice;

    @Schema(description = "昵称", example = "张三")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "积分")
    private Long point;

    @Schema(description = "会员分组", example = "4987")
    private Long groupId;

    @Schema(description = "创建人id", example = "16282")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "16727")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
package com.fozmo.ym.module.account.controller.app.info.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 账户信息 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AccountInfoRespVO {

    @Schema(description = "主键", example = "6035")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "账户名称", example = "王五")
    @ExcelProperty("账户名称")
    private String name;

    @Schema(description = "唯一标识")
    @ExcelProperty("唯一标识")
    private String code;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @Schema(description = "0 自然流量   1推广用户    2投放用户", example = "1")
    @ExcelProperty("0 自然流量   1推广用户    2投放用户")
    private Integer accountType;

    @Schema(description = "会员等级")
    @ExcelProperty("会员等级")
    private Integer rightsLevel;

    @Schema(description = "权益id", example = "7736")
    @ExcelProperty("权益id")
    private Long rightsId;

    @Schema(description = "注册时间")
    @ExcelProperty("注册时间")
    private LocalDateTime registTime;

    @Schema(description = "0 男 1 女  3未知")
    @ExcelProperty("0 男 1 女  3未知")
    private Integer sex;

    @Schema(description = "注册ip")
    @ExcelProperty("注册ip")
    private Integer registIp;

    @Schema(description = "注册设备")
    @ExcelProperty("注册设备")
    private String registDevice;

    @Schema(description = "注册渠道")
    @ExcelProperty("注册渠道")
    private String registerChannel;

    @Schema(description = "账户状态 0 正常 1 停权 2封禁", example = "2")
    @ExcelProperty("账户状态 0 正常 1 停权 2封禁")
    private Integer accountStatus;

    @Schema(description = "最后登陆时间")
    @ExcelProperty("最后登陆时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登陆设备")
    @ExcelProperty("最后登陆设备")
    private String lastLoginDevice;

    @Schema(description = "昵称", example = "张三")
    @ExcelProperty("昵称")
    private String nickname;

    @Schema(description = "用户头像")
    @ExcelProperty("用户头像")
    private String avatar;

    @Schema(description = "积分")
    @ExcelProperty("积分")
    private Long point;

    @Schema(description = "会员分组", example = "4987")
    @ExcelProperty("会员分组")
    private Long groupId;

    @Schema(description = "创建人id", example = "16282")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "16727")
    @ExcelProperty("更新人")
    private Long updaterId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
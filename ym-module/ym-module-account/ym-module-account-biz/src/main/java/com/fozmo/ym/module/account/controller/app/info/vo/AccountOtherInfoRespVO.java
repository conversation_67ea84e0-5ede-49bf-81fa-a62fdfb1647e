package com.fozmo.ym.module.account.controller.app.info.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AccountOtherInfoRespVO {

	@Schema(description = "主键", example = "6035")
	@ExcelProperty("主键")
	private Long id;
	
	@Schema(description = "账户名称", example = "王五")
	@ExcelProperty("账户名称")
	private String name;

	@Schema(description = "权益id", example = "7736")
	private Long rightsId;

	@Schema(description = "0 男 1 女  3未知")
	@ExcelProperty("0 男 1 女  3未知")
	private Integer sex;

	@Schema(description = "注册渠道")
	@ExcelProperty("注册渠道")
	private String registerChannel;
	
	@Schema(description = "账户状态 0 正常 1 停权 2封禁", example = "2")
	@ExcelProperty("账户状态 0 正常 1 停权 2封禁")
	private Integer accountStatus;

	@Schema(description = "昵称", example = "张三")
	private String nickname;
	
	@Schema(description = "用户头像")
	private String avatar;
	
	private String description;
	
	private Boolean fansStatus;
	
	private Long fansNum;

	private Long followNum;
}

package com.fozmo.ym.module.account.controller.app.my;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.controller.app.my.vo.MyAccountInfoVO;
import com.fozmo.ym.module.account.controller.app.my.vo.MyWalletPageReqVO;
import com.fozmo.ym.module.account.controller.app.my.vo.MyWalletPageResVO;
import com.fozmo.ym.module.account.controller.app.my.vo.MyWalletVO;
import com.fozmo.ym.module.account.service.my.MyAccountService;
import com.fozmo.ym.module.space.api.dto.SpaceDTO;
import com.fozmo.ym.module.space.api.dto.WorksDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-账户模块-个人中心")
@RestController
@RequestMapping("/account")
@Validated
public class MyAccountController {

    @Resource
    private MyAccountService myAccountService;

    @GetMapping("/myAccount")
    @Operation(description = "我的账户信息")
    @PermitAll
    @ApiAccessLog

    public CommonResult<MyAccountInfoVO> getMyAccount(@RequestParam(value = "id",required = false) Long id) {
        if (id == null) {
            id = WebFrameworkUtils.getLoginUserId();
        }
        if (ObjectUtil.isNotEmpty(id)){
            return success(myAccountService.getMyAccountInfoById(id));
        }else {
            throw exception(UNAUTHORIZED);
        }
      
    }
    @GetMapping("/myWallet")
    @Operation(description = "我的钱包信息")
    @PermitAll
    @ApiAccessLog
    public CommonResult<MyWalletVO> getMyWallet() {

        return success(myAccountService.getMyWallet(WebFrameworkUtils.getLoginUserId()));
    }

    @GetMapping("/myWalletPage")
    @Operation(description = "我的钱包消费信息")
    @PermitAll
    @ApiAccessLog
    public CommonResult<MyWalletPageResVO> myWalletPage(@Validated MyWalletPageReqVO reqVO) {

        return success(myAccountService.myWalletPage());
    }

    
    @GetMapping("/myOrder")
    @Operation(description = "我的订单信息")
    @PermitAll
    @ApiAccessLog
    public String getMyOrder() {
        return "我的订单信息";
    }

    @GetMapping("/myFavorite")
    @Operation(description = "我的收藏信息")
    @PermitAll
    @ApiAccessLog
    public String getMyFavorite() {
        return "我的收藏信息";
    }

    @GetMapping("/myMessage")
    @Operation(description = "我的消息信息")
    @PermitAll
    @ApiAccessLog
    public String getMyMessage() {
        return "我的消息信息";
    }

    @GetMapping("/myPoint")
    @Operation(description = "我的积分信息")
    @PermitAll
    @ApiAccessLog
    public String getMyPoint() {
        return "我的积分信息";
    }

    @GetMapping("/myCoupon")
    @Operation(description = "我的优惠券信息")
    @PermitAll
    @ApiAccessLog
    public String getMyCoupon() {
        return "我的优惠券信息";
    }

    @GetMapping("/myWorks")
    @Operation(description = "我的作品信息")
    @PermitAll
    @ApiAccessLog
    public CommonResult<WorksDTO> getMyWorks(@Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize,@RequestParam(value = "id",required = false) Long id,@RequestParam(value = "pointType",required = false) Integer pointType) {
        return success(myAccountService.getMyWorks(pageNo,pageSize,id,pointType));
    }

    @GetMapping("/mySpace")
    @Operation(description = "我的空间信息")
    @PermitAll
    @ApiAccessLog
    public  CommonResult<SpaceDTO> getMySpace(@Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize,@RequestParam(value = "id",required = false) Long id) {
        return success(myAccountService.getMySpace(pageNo,pageSize,id));
    }

    @GetMapping("/myFile")
    @Operation(description = "我的资源信息")
    @PermitAll
    @ApiAccessLog
    public String getMyFile() {
        return "我的资源信息";
    }


}

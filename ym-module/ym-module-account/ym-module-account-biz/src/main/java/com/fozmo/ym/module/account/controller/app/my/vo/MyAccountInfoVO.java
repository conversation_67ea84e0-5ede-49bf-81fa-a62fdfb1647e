package com.fozmo.ym.module.account.controller.app.my.vo;

import com.fozmo.ym.module.account.api.account.dto.HumanDTO;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupInfoRespVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagInfoRespVO;
import com.fozmo.ym.module.rights.api.dto.RightsInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "个人中心-账户信息")
public class MyAccountInfoVO {

    @Schema(description = "账户Id")
    private Long id;
    /**
     * 账户名称
     */
    @Schema(description = "账户名称")
    private String name;
    /**
     * 唯一标识
     */
    @Schema(description = "唯一标识")
    private String code;
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;
    /**
     * 0 自然流量   1推广用户    2投放用户
     */
    @Schema(description = "用户类型")
    private Integer accountType;
    /**
     * 会员等级
     */
    @Schema(description = "会员等级")
    private Integer rightsLevel;
    /**
     * 权益id
     */
    @Schema(description = "权益id")
    private Long rightsId;
    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    private LocalDateTime registTime;
    /**
     * 0 男 1 女  3未知
     */
    @Schema(description = "性别")
    private Integer sex;
    /**
     * 注册ip
     */
    @Schema(description = "注册ip")
    private String registIp;
    /**
     * 注册设备
     */
    @Schema(description = "注册设备")
    private String registDevice;
    /**
     * 注册渠道
     */
    @Schema(description = "注册渠道")
    private String registerChannel;
    /**
     * 账户状态 0 正常 1 停权 2封禁
     */
    @Schema(description = "账户状态")
    private Integer accountStatus;
    /**
     * 最后登陆时间
     */
    @Schema(description = "最后登陆时间")
    private LocalDateTime lastLoginTime;
    /**
     * 最后登陆设备
     */
    @Schema(description = "最后登陆设备")
    private String lastLoginDevice;
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String avatar;
    /**
     * 积分
     */
    @Schema(description = "积分")
    private Long point;
    /**
     * 会员分组
     */
    @Schema(description = "会员分组")
    private Long groupId;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "引导状态")
    private String guidStatus;

    @Schema(description = "权益信息")
    private RightsInfoDTO rightsInfo;
    
    @Schema(description = "数字人信息")
    private HumanDTO humanInfo;

    @Schema(description = "标签详情")
    private List<AccountTagInfoRespVO> accountTagInfo;

    @Schema(description = "账户分组详情")
    private List<AccountGroupInfoRespVO> accountGroupInfo;
    
    @Schema(description = "作品数量")
    private Integer worksNum;
    @Schema(description = "空间数量")
    private Integer spaceNum;

    @Schema(description = "是否关注", defaultValue = "false")
    private Boolean fans;
    @Schema(description = "粉丝数", defaultValue = "关注此用户的数量")
    private Integer fansNum;
    @Schema(description = "关注数", defaultValue = "指用户关注的数量")
    private Integer followNum;

    
}

package com.fozmo.ym.module.account.controller.app.my.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "APP-账户模块-我的钱包")
public class MyWalletVO {

    @Schema(description = "钱包编号")
    private Long walletId;

    @Schema(description = "钱包余额")
    private Long balance;

    @Schema(description = "钱包冻结金额")
    private Long freezeBalance;

    @Schema(description = "单位")
    private String unit;


}

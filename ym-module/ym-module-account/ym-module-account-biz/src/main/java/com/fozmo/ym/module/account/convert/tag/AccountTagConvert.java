package com.fozmo.ym.module.account.convert.tag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagCreateReqVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagRespVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagUpdateReqVO;
import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 会员标签 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountTagConvert {

    AccountTagConvert INSTANCE = Mappers.getMapper(AccountTagConvert.class);

    AccountTagDO convert(AccountTagCreateReqVO bean);

    AccountTagDO convert(AccountTagUpdateReqVO bean);

    AccountTagRespVO convert(AccountTagDO bean);

    List<AccountTagRespVO> convertList(List<AccountTagDO> list);

    PageResult<AccountTagRespVO> convertPage(PageResult<AccountTagDO> page);

}

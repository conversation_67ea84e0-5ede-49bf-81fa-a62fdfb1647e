package com.fozmo.ym.module.account.dal.dataobject.group;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 账户群组 DO
 *
 * <AUTHOR>
 */
@TableName("account_group")
@KeySequence("account_group_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountGroupDO extends TenantBaseDO {

    /**
     * 群组
     */
    @TableId
    private Long id;
    /**
     * 群组名称
     */
    private String groupName;
    /**
     * 状态 0 正常
     */
    private Integer status;
    /**
     * 备注
     */
    private String description;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;
    /**
     * 0 系统预置 1 管理新增 3 营销新增
     */
    private Integer createDefault;

}
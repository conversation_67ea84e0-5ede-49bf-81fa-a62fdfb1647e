package com.fozmo.ym.module.account.dal.dataobject.human;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 数字人配置 DO
 *
 * <AUTHOR>
 */
@TableName("human")
@KeySequence("human_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HumanDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 数字人名称
     */
    private String name;
    /**
     * 说明
     */
    private String description;
    /**
     * 数字人文件路径
     */
    private String filePath;
    /**
     * 数字人文件后缀
     */
    private String fileSuffix;
    /**
     * 数字人文件状态
     */
    private Integer fileStatus;
    /**
     * 数字人属性
     */
    private String fileAttribute;
    /**
     * 文件桶
     */
    private String fileBucket;
    /**
     * 权限
     */
    private Long rightsId;
    /**
     * 数字人文件url
     */
    private String fileUrl;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;
    
    private String cover;

    private Integer humanSort;

}
package com.fozmo.ym.module.account.dal.dataobject.info;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("account_base")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountBaseDO extends TenantBaseDO{
	/**
	 * 主键
	 */
	@TableId
	private Long id;
	/**
	 * 账户名称
	 */
	private String name;
	/**
	 * 唯一标识
	 */
	private String code;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 0 自然流量   1推广用户    2投放用户
	 */
	private Integer accountType;

	/**
	 * 账户状态 0 正常 1 停权 2封禁
	 */
	private Integer accountStatus;

	private LocalDateTime registTime;

	private LocalDate registDate;
	
	/**
	 * 注册ip
	 */
	private String registIp;
	/**
	 * 注册设备
	 */
	private String registDevice;
	/**
	 * 注册渠道
	 */
	private String registerChannel;


	/**
	 * 创建人id
	 */
	private Long createId;
	/**
	 * 创建日期
	 */
	private LocalDate createData;
	/**
	 * 更新人
	 */
	private Long updaterId;
	/**
	 * 更新日期
	 */
	private LocalDate updateData;
	/**
	 * 租户Code
	 */
	private String tenantCode;
}

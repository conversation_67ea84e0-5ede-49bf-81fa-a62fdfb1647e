package com.fozmo.ym.module.account.dal.dataobject.info;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 账户信息 DO
 *
 * <AUTHOR>
 */
@TableName("account_info")
@KeySequence("account_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfoDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 账户名称
     */
    private String name;
    /**
     * 唯一标识
     */
    private String code;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 0 自然流量   1推广用户    2投放用户
     */
    private Integer accountType;
    /**
     * 会员等级
     */
    private Integer rightsLevel;
    /**
     * 权益id
     */
    private Long rightsId;
    /**
     * 注册时间
     */
    private LocalDateTime registTime;
    /**
     * 0 男 1 女  3未知
     */
    private Integer sex;
    /**
     * 注册ip
     */
    private String registIp;
    /**
     * 注册设备
     */
    private String registDevice;
    /**
     * 注册渠道
     */
    private String registerChannel;
    /**
     * 账户状态 0 正常 1 停权 2封禁
     */
    private Integer accountStatus;
    /**
     * 最后登陆时间
     */
    private LocalDateTime lastLoginTime;
    /**
     * 最后登陆设备
     */
    private String lastLoginDevice;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 积分
     */
    private Long point;
    /**
     * 会员分组
     */
    private Long groupId;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    private String password;

    private String description;

    private String guidStatus;


}
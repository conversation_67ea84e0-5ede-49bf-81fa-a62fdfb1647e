package com.fozmo.ym.module.account.dal.mysql.group;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupPageReqVO;
import com.fozmo.ym.module.account.dal.dataobject.group.AccountGroupDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 账户群组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountGroupMapper extends BaseMapperX<AccountGroupDO> {

    default PageResult<AccountGroupDO> selectPage(AccountGroupPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountGroupDO>()
                .likeIfPresent(AccountGroupDO::getGroupName, reqVO.getGroupName())
                .eqIfPresent(AccountGroupDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AccountGroupDO::getDescription, reqVO.getDescription())
                .eqIfPresent(AccountGroupDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(AccountGroupDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(AccountGroupDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AccountGroupDO::getUpdateId, reqVO.getUpdateId())
                .eqIfPresent(AccountGroupDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(AccountGroupDO::getTenantCode, reqVO.getTenantCode())
                .eqIfPresent(AccountGroupDO::getCreateDefault, reqVO.getCreateDefault())
                .orderByDesc(AccountGroupDO::getId));
    }

}
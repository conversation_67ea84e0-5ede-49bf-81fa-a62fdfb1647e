package com.fozmo.ym.module.account.dal.mysql.human;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanPageReqVO;
import com.fozmo.ym.module.account.dal.dataobject.human.AccountHumanDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数字人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountHumanMapper extends BaseMapperX<AccountHumanDO> {

    default PageResult<AccountHumanDO> selectPage(AccountHumanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountHumanDO>()
                .eqIfPresent(AccountHumanDO::getHumanId, reqVO.getHumanId())
                .eqIfPresent(AccountHumanDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(AccountHumanDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(AccountHumanDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(AccountHumanDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AccountHumanDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(AccountHumanDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(AccountHumanDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(AccountHumanDO::getId));
    }
    
    default AccountHumanDO selectByAccountId(Long accountId){
        return selectOne(AccountHumanDO::getAccountId, accountId,AccountHumanDO::getDeleted,false);
    }
}
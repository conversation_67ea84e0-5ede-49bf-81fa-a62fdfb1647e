package com.fozmo.ym.module.account.dal.mysql.human;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.account.controller.admin.human.vo.HumanPageReqVO;
import com.fozmo.ym.module.account.dal.dataobject.human.HumanDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数字人配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HumanMapper extends BaseMapperX<HumanDO> {

    default PageResult<HumanDO> selectPage(HumanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HumanDO>()
                .likeIfPresent(HumanDO::getName, reqVO.getName())
                .eqIfPresent(HumanDO::getRightsId, reqVO.getRightsId())
                .orderByDesc(HumanDO::getId));
    }
	
}
package com.fozmo.ym.module.account.dal.mysql.info;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.fozmo.ym.module.account.controller.admin.info.vo.*;

/**
 * 账户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountInfoMapper extends BaseMapperX<AccountInfoDO> {

    default PageResult<AccountInfoDO> selectPage(AccountInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountInfoDO>()
                .likeIfPresent(AccountInfoDO::getName, reqVO.getName())
                .eqIfPresent(AccountInfoDO::getCode, reqVO.getCode())
                .eqIfPresent(AccountInfoDO::getMobile, reqVO.getMobile())
                .eqIfPresent(AccountInfoDO::getAccountType, reqVO.getAccountType())
                .eqIfPresent(AccountInfoDO::getRightsLevel, reqVO.getRightsLevel())
                .eqIfPresent(AccountInfoDO::getRightsId, reqVO.getRightsId())
                .betweenIfPresent(AccountInfoDO::getRegistTime, reqVO.getRegistTime())
                .eqIfPresent(AccountInfoDO::getSex, reqVO.getSex())
                .eqIfPresent(AccountInfoDO::getRegistIp, reqVO.getRegistIp())
                .eqIfPresent(AccountInfoDO::getRegistDevice, reqVO.getRegistDevice())
                .eqIfPresent(AccountInfoDO::getRegisterChannel, reqVO.getRegisterChannel())
                .eqIfPresent(AccountInfoDO::getAccountStatus, reqVO.getAccountStatus())
                .betweenIfPresent(AccountInfoDO::getLastLoginTime, reqVO.getLastLoginTime())
                .eqIfPresent(AccountInfoDO::getLastLoginDevice, reqVO.getLastLoginDevice())
                .likeIfPresent(AccountInfoDO::getNickname, reqVO.getNickname())
                .eqIfPresent(AccountInfoDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(AccountInfoDO::getPoint, reqVO.getPoint())
                .eqIfPresent(AccountInfoDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(AccountInfoDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(AccountInfoDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(AccountInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AccountInfoDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(AccountInfoDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(AccountInfoDO::getTenantCode, reqVO.getTenantCode())
                .gt(AccountInfoDO::getRightsId,-1)
                .orderByDesc(AccountInfoDO::getId));
    }


    default Long selectCountByTagId(Long tagId) {
        return selectCount(new LambdaQueryWrapperX<AccountInfoDO>()
                .apply("FIND_IN_SET({0}, tag_ids)", tagId));
    }

    default AccountInfoDO selectByMobile(String mobile){
        return selectOne(new LambdaQueryWrapperX<AccountInfoDO>()
                .eq(AccountInfoDO::getDeleted,false)
                .eq(AccountInfoDO::getAccountStatus,0)
                .eq(AccountInfoDO::getMobile,mobile));
    }

    default AccountInfoDO selectByCode(String code){
        return selectOne(new LambdaQueryWrapperX<AccountInfoDO>()
                .eq(AccountInfoDO::getDeleted,false)
                .eq(AccountInfoDO::getAccountStatus,0)
                .eq(AccountInfoDO::getCode,code));
    }
}
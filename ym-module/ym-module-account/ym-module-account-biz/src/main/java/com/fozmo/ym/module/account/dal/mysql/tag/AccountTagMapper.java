package com.fozmo.ym.module.account.dal.mysql.tag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagPageReqVO;
import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 会员标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountTagMapper extends BaseMapperX<AccountTagDO> {

    default PageResult<AccountTagDO> selectPage(AccountTagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountTagDO>()
                .likeIfPresent(AccountTagDO::getName, reqVO.getName())
                .betweenIfPresent(AccountTagDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AccountTagDO::getId));
    }

    default AccountTagDO selectByName(String name) {
        return selectOne(AccountTagDO::getName, name);
    }
}

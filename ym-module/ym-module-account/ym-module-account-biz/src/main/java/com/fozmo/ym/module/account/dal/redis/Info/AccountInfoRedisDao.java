package com.fozmo.ym.module.account.dal.redis.Info;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.account.constants.AccountRedisKeyConstants.*;

@Repository
public class AccountInfoRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	public boolean hasIdKey(Long accountId) {
		if (ObjectUtil.isNotEmpty(accountId)) {
			String hkey = accountId.toString();
			return redisTemplate.opsForHash().hasKey(ACCOUNT_INFO, hkey);
		}
		return false;
	}
	
	public AccountInfoDO getAccountById(Long accountId){
		if (ObjectUtil.isNotEmpty(accountId)) {
			String hkey = accountId.toString();
			return (AccountInfoDO) redisTemplate.opsForHash().get(ACCOUNT_INFO, hkey);
		}
		return null;
	}
	
	public void saveAccountInfo(AccountInfoDO accountInfoDO){
		if (ObjectUtil.isNotEmpty(accountInfoDO.getId())) {
			String hkey = accountInfoDO.getId().toString();
			redisTemplate.opsForHash().put(ACCOUNT_INFO, hkey, accountInfoDO);
			
		}
	}
	
	public void saveAccountMobile(Long accountId,String mobile){
		if (ObjectUtil.isNotEmpty(accountId) && StringUtils.isNotEmpty(mobile)) {
			AccountInfoDO accountInfoDO = getAccountById(accountId);
			if (ObjectUtil.isNotEmpty(accountInfoDO)) {
			
			}else {
				accountInfoDO = new AccountInfoDO();
				accountInfoDO.setId(accountId);
			}
			String mobileKey = mobile;
			redisTemplate.opsForHash().put(Account_mobile, mobileKey, accountInfoDO);
		}
		
		
	}
	 public boolean checkAccountMobile(String mobile){
		if (ObjectUtil.isNotEmpty(mobile)) {
			return redisTemplate.opsForHash().hasKey(Account_mobile, mobile);
		}
		return false;
	 }

	public boolean checkAccountOpenId(String openId){
		if (ObjectUtil.isNotEmpty(openId)) {
			return redisTemplate.opsForHash().hasKey(ACCOUNT_CODE, openId);
		}
		return false;
	}
	
	public void saveAccountOpenId(Long accountId,String openId){
		if (openId != null && ObjectUtil.isNotEmpty(openId)) {
			AccountInfoDO accountInfoDO = getAccountById(accountId);
			if (ObjectUtil.isNotEmpty(accountInfoDO)) {
			
			}else {
				accountInfoDO = new AccountInfoDO();
				accountInfoDO.setId(accountId);
			}
			String openIdKey = openId;
			redisTemplate.opsForHash().put(ACCOUNT_CODE, openIdKey, accountInfoDO);
		}
		
	}
	
	public AccountInfoDO getAccountByOpenId(String openId){
		if (ObjectUtil.isNotEmpty(openId)) {
			if (checkAccountOpenId(openId)){
				return (AccountInfoDO) redisTemplate.opsForHash().get(ACCOUNT_CODE, openId);
			}
			
		}
		return null;
	}

	public AccountInfoDO getAccountByMobile(String mobile){
		if (ObjectUtil.isNotEmpty(mobile)) {
			if (checkAccountMobile(mobile)){
				return (AccountInfoDO) redisTemplate.opsForHash().get(Account_mobile, mobile);
			}
			
		}
		return null;
	}
	
	public void deleteAccountInfo(Long accountId){
		if (ObjectUtil.isNotEmpty(accountId)){
			String hkey = accountId.toString();
			redisTemplate.opsForHash().delete(ACCOUNT_INFO, hkey);
		}

	}

	public PageResult<AccountInfoDO> getAccountInfoPage(String name, Integer pageNo, Integer pageSize) {
		PageResult<AccountInfoDO> pageResult = PageResult.empty();
		// 获取空间总数

		// 获取Hash操作对象
		HashOperations<String, String, AccountInfoDO> hashOps = redisTemplate.opsForHash();

		ScanOptions scanOptions = scanOptions = ScanOptions.scanOptions()
				.count(10000)      // 每次扫描数量（优化性能）
				.build();
		// 创建结果列表
		List<Map.Entry<String, AccountInfoDO>> allResult = new ArrayList<>();
		// 扫描Hash
		try (Cursor<Map.Entry<String, AccountInfoDO>> cursor = hashOps.scan(ACCOUNT_INFO, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		List<AccountInfoDO> accountList = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			accountList = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		if (StringUtils.isNotEmpty(name) && CollUtil.isNotEmpty(accountList)) {
			accountList = accountList.stream().filter(accountInfoDO -> ObjectUtil.isNotEmpty(accountInfoDO.getNickname()) && accountInfoDO.getNickname().contains(name)).collect(Collectors.toList());

		}

		// 创建列表
		List<AccountInfoDO> spaceDOList = new ArrayList<>();
		// 判断结果是否为空
		if (CollUtil.isNotEmpty(accountList)) {
			// 计算起始位置和结束位置
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, accountList.size());

			// 获取子结果
			spaceDOList = accountList.subList(start, end);

		}
		// 设置分页结果
		pageResult.setTotal((long) accountList.size());
		pageResult.setList(spaceDOList);
		return pageResult;
	}

}

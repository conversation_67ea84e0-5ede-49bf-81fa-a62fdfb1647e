package com.fozmo.ym.module.account.dal.redis.human;

import com.fozmo.ym.module.account.dal.dataobject.human.AccountHumanDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import static com.fozmo.ym.module.account.constants.AccountRedisKeyConstants.HUMAN_ACCOUNT_INFO;

@Repository
public class AccountHumanRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	public boolean hasIdKey(Long accountId) {
		String humanKey = HUMAN_ACCOUNT_INFO;
		return redisTemplate.opsForHash().hasKey(humanKey, accountId.toString());
	}
	
	public void addHumanInfo(AccountHumanDO accountHumanDO) {
		 String humanKey = HUMAN_ACCOUNT_INFO;
		 redisTemplate.opsForHash().put(humanKey, accountHumanDO.getAccountId().toString(), accountHumanDO);
	}
	
	public AccountHumanDO getHumanInfo(Long accountId) {
		String humanKey = HUMAN_ACCOUNT_INFO;
		return (AccountHumanDO) redisTemplate.opsForHash().get(humanKey, accountId.toString());
	}
	
	public void removeHumanInfo(Long accountId) {
		String humanKey = HUMAN_ACCOUNT_INFO;
		redisTemplate.opsForHash().delete(humanKey, accountId.toString());
	}
}

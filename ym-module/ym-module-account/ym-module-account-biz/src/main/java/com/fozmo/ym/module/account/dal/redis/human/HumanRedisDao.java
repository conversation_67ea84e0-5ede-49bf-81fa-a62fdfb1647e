package com.fozmo.ym.module.account.dal.redis.human;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.dal.dataobject.human.HumanDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.account.constants.AccountRedisKeyConstants.HUMAN_INFO;

@Repository
public class HumanRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	/**
	 *
	 */
	public void addHuman(HumanDO human) {
		String key = HUMAN_INFO;
		redisTemplate.opsForHash().put(key, human.getId().toString(), human);
	}
	
	public HumanDO getHuman(Long id){
		String key = HUMAN_INFO;
		return (HumanDO)redisTemplate.opsForHash().get(key, id.toString());
	}
	
	public boolean checkHuman(Long id){
		String key = HUMAN_INFO;
		return redisTemplate.opsForHash().hasKey(key, id.toString());
	}
	
	public void deleteHuman(Long id){
		String key = HUMAN_INFO;
		redisTemplate.opsForHash().delete(key, id.toString());
	}
	
	public PageResult<HumanDO> getHumanPage(Integer pageNo,Integer pageSize){
		PageResult<HumanDO> pageResult = new PageResult<>();
		String key = HUMAN_INFO;
		Long total = redisTemplate.opsForHash().size(key);
		// 构建Redis 查询 条件
		HashOperations<String, String, HumanDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				                          .count(1000)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, HumanDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, HumanDO>> cursor = hashOps.scan(key, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<HumanDO> humanDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, HumanDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			humanDOS= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal(total);
		pageResult.setList(humanDOS);
		return  pageResult;
	}

	public List<HumanDO> getHumanList() {
		String key = HUMAN_INFO;
		// 构建Redis 查询 条件
		HashOperations<String, String, HumanDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				                          .count(1000)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, HumanDO>> allResult = new ArrayList<>();
		List<HumanDO> humanDOS = new ArrayList<>();
		try (Cursor<Map.Entry<String, HumanDO>> cursor = hashOps.scan(key, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		humanDOS= allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		return humanDOS;
	}
}

package com.fozmo.ym.module.account.framework.web.config;

import com.fozmo.ym.framework.swagger.config.YmSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class AccountWebConfiguration {

    /**
     * 账户模块的 API 分组
     */
    @Bean
    public GroupedOpenApi accountGroupedOpenApi() {
        return YmSwaggerAutoConfiguration.buildGroupedOpenApi("account");
    }
}

package com.fozmo.ym.module.account.service.group;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupPageReqVO;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupSaveReqVO;
import com.fozmo.ym.module.account.dal.dataobject.group.AccountGroupDO;
import jakarta.validation.Valid;

/**
 * 账户群组 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountGroupService {

    /**
     * 创建账户群组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGroup(@Valid AccountGroupSaveReqVO createReqVO);

    /**
     * 更新账户群组
     *
     * @param updateReqVO 更新信息
     */
    void updateGroup(@Valid AccountGroupSaveReqVO updateReqVO);

    /**
     * 删除账户群组
     *
     * @param id 编号
     */
    void deleteGroup(Long id);

    /**
     * 获得账户群组
     *
     * @param id 编号
     * @return 账户群组
     */
    AccountGroupDO getGroup(Long id);

    /**
     * 获得账户群组分页
     *
     * @param pageReqVO 分页查询
     * @return 账户群组分页
     */
    PageResult<AccountGroupDO> getGroupPage(AccountGroupPageReqVO pageReqVO);

}
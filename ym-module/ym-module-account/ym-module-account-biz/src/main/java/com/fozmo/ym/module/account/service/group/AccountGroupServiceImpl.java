package com.fozmo.ym.module.account.service.group;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupPageReqVO;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupSaveReqVO;
import com.fozmo.ym.module.account.dal.dataobject.group.AccountGroupDO;
import com.fozmo.ym.module.account.dal.mysql.group.AccountGroupMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.account.enums.ErrorCodeConstants.GROUP_NOT_EXISTS;

/**
 * 账户群组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AccountGroupServiceImpl implements AccountGroupService {

    @Resource
    private AccountGroupMapper groupMapper;

    @Override
    public Long createGroup(AccountGroupSaveReqVO createReqVO) {
        // 插入
        AccountGroupDO group = BeanUtils.toBean(createReqVO, AccountGroupDO.class);
        groupMapper.insert(group);
        // 返回
        return group.getId();
    }

    @Override
    public void updateGroup(AccountGroupSaveReqVO updateReqVO) {
        // 校验存在
        validateGroupExists(updateReqVO.getId());
        // 更新
        AccountGroupDO updateObj = BeanUtils.toBean(updateReqVO, AccountGroupDO.class);
        groupMapper.updateById(updateObj);
    }

    @Override
    public void deleteGroup(Long id) {
        // 校验存在
        validateGroupExists(id);
        // 删除
        groupMapper.deleteById(id);
    }

    private void validateGroupExists(Long id) {
        if (groupMapper.selectById(id) == null) {
            throw exception(GROUP_NOT_EXISTS);
        }
    }

    @Override
    public AccountGroupDO getGroup(Long id) {
        return groupMapper.selectById(id);
    }

    @Override
    public PageResult<AccountGroupDO> getGroupPage(AccountGroupPageReqVO pageReqVO) {
        return groupMapper.selectPage(pageReqVO);
    }

}
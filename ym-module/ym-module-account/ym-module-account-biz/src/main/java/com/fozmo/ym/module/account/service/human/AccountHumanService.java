package com.fozmo.ym.module.account.service.human;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanPageReqVO;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanSaveReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppAccountHumanInfoResp;
import com.fozmo.ym.module.account.dal.dataobject.human.AccountHumanDO;
import jakarta.validation.Valid;

/**
 * 数字人 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountHumanService {

    /**
     * 创建数字人
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHuman(@Valid AccountHumanSaveReqVO createReqVO);

    /**
     * 更新数字人
     *
     * @param updateReqVO 更新信息
     */
    void updateHuman(@Valid AccountHumanSaveReqVO updateReqVO);

    /**
     * 删除数字人
     *
     * @param id 编号
     */
    void deleteHuman(Long id);

    /**
     * 获得数字人
     *
     * @param id 编号
     * @return 数字人
     */
    AccountHumanDO getHuman(Long id);

    /**
     * 获得数字人分页
     *
     * @param pageReqVO 分页查询
     * @return 数字人分页
     */
    PageResult<AccountHumanDO> getHumanPage(AccountHumanPageReqVO pageReqVO);

    AppAccountHumanInfoResp getHumanByAccountId(Long accountId);
}
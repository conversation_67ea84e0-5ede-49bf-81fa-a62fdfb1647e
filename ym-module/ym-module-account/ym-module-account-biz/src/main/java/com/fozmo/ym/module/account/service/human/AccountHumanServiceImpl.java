package com.fozmo.ym.module.account.service.human;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanPageReqVO;
import com.fozmo.ym.module.account.controller.admin.human.vo.AccountHumanSaveReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppAccountHumanInfoResp;
import com.fozmo.ym.module.account.dal.dataobject.human.AccountHumanDO;
import com.fozmo.ym.module.account.dal.dataobject.human.HumanDO;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import com.fozmo.ym.module.account.dal.mysql.human.AccountHumanMapper;
import com.fozmo.ym.module.account.dal.redis.human.AccountHumanRedisDao;
import com.fozmo.ym.module.account.service.info.AccountInfoService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.account.enums.ErrorCodeConstants.HUMAN_IS_NOT_NULL;
import static com.fozmo.ym.module.account.enums.ErrorCodeConstants.HUMAN_IS_NULL;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.rights.enums.ErrorCodeConstants.RIGHTS_NOT;
import static com.fozmo.ym.module.rights.enums.ErrorCodeConstants.RIGHTS_NOT_EXISTS;

/**
 * 数字人 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AccountHumanServiceImpl implements AccountHumanService {

    @Resource
     @Lazy
    IdService idService;

    @Resource
    private AccountHumanMapper accountHumanMapper;
    
    @Resource
    private AccountInfoService accountInfoService;
    
    @Resource
    private HumanService humanService;
    
    @Resource
    private AccountHumanRedisDao accountHumanRedisDao;

    @Override
    public Long createHuman(AccountHumanSaveReqVO createReqVO) {
        
        Long accountId = createReqVO.getAccountId();
        if (ObjectUtil.isEmpty(accountId)) {
           accountId=WebFrameworkUtils.getLoginUserId();
        }
        
        // 检查 数字人是否存在
        if (accountHumanRedisDao.hasIdKey(accountId)){
            throw exception(HUMAN_IS_NOT_NULL);
        }
        // 插入
        AccountHumanDO createObj = BeanUtils.toBean(createReqVO, AccountHumanDO.class);
        AccountInfoDO accountInfoDO = accountInfoService.getInfo(createObj.getAccountId());
        createObj.setCreateId(accountInfoDO.getId());
        createObj.setCreator(accountInfoDO.getName());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("account_human"));
        createObj.setTenantId(accountInfoDO.getTenantId());
        createObj.setTenantCode(accountInfoDO.getTenantId()+"");
        createObj.setAccountId(accountInfoDO.getId());
      
        accountHumanRedisDao.addHumanInfo(createObj);
        accountHumanMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateHuman(AccountHumanSaveReqVO updateReqVO) {

        // 校验存在
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(UNAUTHORIZED);
        }

        AccountInfoDO loginUser = accountInfoService.getInfo(accountId);
        if (ObjectUtil.isEmpty(loginUser)) {
            throw exception(ACCOUNT_NOT);
        }

        Long userRights = loginUser.getRightsId();
        if (ObjectUtil.isEmpty(userRights)) {
            throw exception(RIGHTS_NOT_EXISTS);
        }

        Long humanId = updateReqVO.getHumanId();
        if (ObjectUtil.isEmpty(humanId)) {
            throw exception(HUMAN_IS_NULL);
        }
        HumanDO human = humanService.getHuman(humanId);
        if (ObjectUtil.isEmpty(human)) {
            throw exception(HUMAN_IS_NULL);
        }
        Long humanRights = human.getRightsId();
        if (ObjectUtil.isEmpty(humanRights)) {
            throw exception(HUMAN_IS_NULL);
        }
        if (userRights < humanRights) {
            throw exception(RIGHTS_NOT);
        }

      if (accountHumanRedisDao.hasIdKey(accountId)){
          AccountHumanDO humanDO = accountHumanRedisDao.getHumanInfo(accountId);
          accountHumanRedisDao.removeHumanInfo(accountId);
          humanDO.setUpdaterId(loginUser.getId());
          humanDO.setUpdater(loginUser.getName());
          humanDO.setUpdateData(LocalDate.now());
          humanDO.setHumanId(updateReqVO.getHumanId());
          accountHumanMapper.updateById(humanDO);
          accountHumanRedisDao.addHumanInfo(humanDO);
      } else {
          throw exception(HUMAN_IS_NULL);
      }
    }

    @Override
    public void deleteHuman(Long id) {
        // 校验存在
        validateHumanExists(id);
        // 删除
        accountHumanMapper.deleteById(id);
    }

    private void validateHumanExists(Long id) {
        if (accountHumanMapper.selectById(id) == null) {
//            throw exception(HUMAN_NOT_EXISTS);
        }
    }

    @Override
    public AccountHumanDO getHuman(Long accountId) {
        AccountHumanDO accountHuman = new AccountHumanDO();
        if (accountHumanRedisDao.hasIdKey(accountId)) {
            accountHuman=  accountHumanRedisDao.getHumanInfo(accountId);
        }else {
            accountHuman = accountHumanMapper.selectByAccountId(accountId);
            if (ObjectUtil.isNotEmpty(accountHuman)) {
                accountHumanRedisDao.addHumanInfo(accountHuman);
            }
        }
        return accountHuman;
    }

    @Override
    public PageResult<AccountHumanDO> getHumanPage(AccountHumanPageReqVO pageReqVO) {
        return accountHumanMapper.selectPage(pageReqVO);
    }

    /**
     * @param accountId
     *
     * @return
     */
    @Override
    public AppAccountHumanInfoResp getHumanByAccountId(Long accountId) {
        AppAccountHumanInfoResp resp = new AppAccountHumanInfoResp();
        
        
        AccountHumanDO accountHumanDO = this.getHuman(accountId);
        if (ObjectUtil.isNull(accountHumanDO)) {
            return resp;
        }
        // 根据 humanId 查询
        AccountInfoDO accountInfoDO = accountInfoService.getInfo(accountId);
        HumanDO humanDO = humanService.getHuman(accountHumanDO.getHumanId());
        
        resp= BeanUtils.toBean(humanDO, AppAccountHumanInfoResp.class);
        resp.setAccountId(accountHumanDO.getId());
        resp.setAccountName(accountInfoDO.getName());
        resp.setHumanName(humanDO.getName());
        resp.setId(accountHumanDO.getId());
        resp.setHumanId(accountHumanDO.getHumanId());
        
        return resp;
    }
    
}
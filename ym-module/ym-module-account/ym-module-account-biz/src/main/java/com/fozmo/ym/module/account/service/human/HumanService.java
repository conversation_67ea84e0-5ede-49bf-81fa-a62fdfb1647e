package com.fozmo.ym.module.account.service.human;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.human.vo.HumanPageReqVO;
import com.fozmo.ym.module.account.controller.admin.human.vo.HumanSaveReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppHumanInfoResp;
import com.fozmo.ym.module.account.dal.dataobject.human.HumanDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 数字人配置 Service 接口
 *
 * <AUTHOR>
 */
public interface HumanService {

    /**
     * 创建数字人配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHuman(@Valid HumanSaveReqVO createReqVO);

    /**
     * 更新数字人配置
     *
     * @param updateReqVO 更新信息
     */
    void updateHuman(@Valid HumanSaveReqVO updateReqVO);

    /**
     * 删除数字人配置
     *
     * @param id 编号
     */
    void deleteHuman(Long id);

    /**
     * 获得数字人配置
     *
     * @param id 编号
     * @return 数字人配置
     */
    HumanDO getHuman(Long id);

    /**
     * 获得数字人配置分页
     *
     * @param pageReqVO 分页查询
     * @return 数字人配置分页
     */
    PageResult<HumanDO> getHumanPage(HumanPageReqVO pageReqVO);
	
	/***
	 * 通过账户查询账户信息
	 */
	AppHumanInfoResp getHumanByAccountId(Long accountId);

	List<HumanDO> getHumanList();
}
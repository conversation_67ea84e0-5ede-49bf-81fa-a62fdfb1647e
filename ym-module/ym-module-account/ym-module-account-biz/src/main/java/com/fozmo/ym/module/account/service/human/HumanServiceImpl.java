package com.fozmo.ym.module.account.service.human;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.controller.admin.human.vo.HumanPageReqVO;
import com.fozmo.ym.module.account.controller.admin.human.vo.HumanSaveReqVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppHumanInfoResp;
import com.fozmo.ym.module.account.dal.dataobject.human.HumanDO;
import com.fozmo.ym.module.account.dal.mysql.human.HumanMapper;
import com.fozmo.ym.module.account.dal.redis.human.HumanRedisDao;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.account.enums.ErrorCodeConstants.HUMAN_IS_NULL;

/**
 * 数字人配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HumanServiceImpl implements HumanService {

    @Resource
    private HumanMapper humanMapper;
    
    @Resource
    private IdService idService;
    
    @Resource
    private HumanRedisDao humanRedisDao;
    
    @Override
    public Long createHuman(HumanSaveReqVO createReqVO) {
        // 插入
        HumanDO human = BeanUtils.toBean(createReqVO, HumanDO.class);
        
        // 获取当前用户
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        human.setCreateId(loginUser.getId());
        human.setCreateData(LocalDate.now());
        human.setCreator(loginUser.getUsername());
        human.setTenantId(loginUser.getTenantId());
        human.setTenantCode(loginUser.getTenantId().toString());
        human.setId(idService.nextId("human"));
        humanRedisDao.addHuman(human);
        humanMapper.insert(human);
        // 返回
        return human.getId();
    }

    @Override
    public void updateHuman(HumanSaveReqVO updateReqVO) {
        // 校验存在
        if (humanRedisDao.checkHuman(updateReqVO.getId())) {
            // 更新
            HumanDO updateObj = BeanUtils.toBean(updateReqVO, HumanDO.class);
            humanMapper.updateById(updateObj);
        }else {
            throw exception(HUMAN_IS_NULL);
        }
        
    }

    @Override
    public void deleteHuman(Long id) {
        // 校验存在
        if (humanRedisDao.checkHuman(id)){
            humanRedisDao.deleteHuman(id);
            humanMapper.deleteById(id);
        }else {
            throw exception(HUMAN_IS_NULL);
        }
     
    }

    private void validateHumanExists(Long id) {
        if (humanMapper.selectById(id) == null) {
//            throw exception(HUMAN_NOT_EXISTS);
        }
    }

    @Override
    public HumanDO getHuman(Long id) {
        HumanDO humanDO =new HumanDO();
        if (humanRedisDao.checkHuman(id)) {
            humanDO=  humanRedisDao.getHuman(id);
        }else {
            humanDO= humanMapper.selectById(id);
            if (ObjectUtil.isNotEmpty(humanDO)) {
                humanRedisDao.addHuman(humanDO);
            }
        }
        
        return humanDO;
    }

    @Override
    public PageResult<HumanDO> getHumanPage(HumanPageReqVO pageReqVO) {
        PageResult<HumanDO> pageResult =  humanRedisDao.getHumanPage(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0 || CollUtil.isEmpty(pageResult.getList())) {
            pageResult = humanMapper.selectPage(pageReqVO);
            if (ObjectUtil.isNotEmpty(pageResult) && CollUtil.isNotEmpty(pageResult.getList())) {
                pageResult.getList().forEach(human -> {
                    humanRedisDao.addHuman(human);
                });
            }
        }
        return pageResult;
    }

    /***
     * 通过账户查询账户信息
     * @param accountId
     */
    @Override
    public AppHumanInfoResp getHumanByAccountId(Long accountId) {
        return null;
    }

/**
 * @return
 */
@Override
public List<HumanDO> getHumanList() {
    List<HumanDO> humanList = humanRedisDao.getHumanList();
    if (CollUtil.isEmpty(humanList)) {
        humanList= humanMapper.selectList();
        if (CollUtil.isNotEmpty(humanList)) {
            for (HumanDO humanDO : humanList) {
                humanRedisDao.addHuman(humanDO);
            }
        }
    }
    if (ObjectUtil.isNotEmpty(humanList)) {
        humanList = humanList.stream().filter(humanDO -> StringUtils.isNotEmpty(humanDO.getFileAttribute()) && "default".equals(humanDO.getFileAttribute())).collect(Collectors.toList());
        humanList.sort(Comparator.comparingInt(HumanDO::getHumanSort));
        if (CollUtil.isNotEmpty(humanList) && humanList.size() > 4) {
            humanList.subList(0, 4);
        } else {
            return humanList;
        }

    }
    return List.of();
}
    
}
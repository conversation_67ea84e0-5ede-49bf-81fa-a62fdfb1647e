package com.fozmo.ym.module.account.service.info;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.validation.Mobile;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoPageReqVO;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoSaveReqVO;
import com.fozmo.ym.module.account.controller.app.info.vo.AccountOtherInfoRespVO;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import jakarta.validation.Valid;

/**
 * 账户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountInfoService {

    /**
     * 创建账户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInfo(@Valid AccountInfoSaveReqVO createReqVO);

    /**
     * 更新账户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInfo(@Valid AccountInfoSaveReqVO updateReqVO);

    /**
     * 删除账户信息
     *
     * @param id 编号
     */
    
    void deleteInfo(Long id);

    /**
     * 获得账户信息
     *
     * @param id 编号
     * @return 账户信息
     */
    AccountInfoDO getInfo(Long id);

    /**
     * 获得账户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 账户信息分页
     */
    PageResult<AccountInfoDO> getInfoPage(AccountInfoPageReqVO pageReqVO);
    /**
     * 获得指定会员标签下的用户数量
     *
     * @param tagId 用户标签编号
     * @return 用户数量
     */
    Long getUserCountByTagId(Long tagId);

    AccountInfoDO getUserByMobile(@Mobile String mobile);

    AccountOtherInfoRespVO getOtherInfo(Long id);

    Boolean checkAccountMobile(String mobile);
    
    Boolean checkAccountOpenId(String openId);
    
    void saveAccountOpenId(Long accountId, String openId);

    AccountInfoDO getAccountByOpenId(String openId);
    
    void saveAccountMobile(Long accountId, String mobile);

    AccountInfoDO getAccountByMobile(String mobile);
}
package com.fozmo.ym.module.account.service.info;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoPageReqVO;
import com.fozmo.ym.module.account.controller.admin.info.vo.AccountInfoSaveReqVO;
import com.fozmo.ym.module.account.controller.app.info.vo.AccountOtherInfoRespVO;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import com.fozmo.ym.module.account.dal.mysql.info.AccountInfoMapper;
import com.fozmo.ym.module.account.dal.redis.Info.AccountInfoRedisDao;
import com.fozmo.ym.module.social.api.dto.SocialFansDTO;
import com.fozmo.ym.module.social.api.fans.SocialFansApi;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.account.enums.ErrorCodeConstants.ACCOUNT_NOT_EXISTS;

/**
 * 账户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AccountInfoServiceImpl implements AccountInfoService {

    @Resource
    @Lazy
    private IdService idService;


    @Resource
    private AccountInfoMapper infoMapper;
    
    @Resource
    private AccountInfoRedisDao accountInfoRedisDao;
    
    @Resource
    private SocialFansApi socialFansApi;

    @Override
    public Long createInfo(AccountInfoSaveReqVO createReqVO) {
        // 插入
        AccountInfoDO createObj = BeanUtils.toBean(createReqVO, AccountInfoDO.class);
        Long id = idService.nextId("accountInfo");
        createObj.setId(id);
        createObj.setTenantId(1L);
        createObj.setTenantCode("ym");
        createObj.setCreateData(LocalDate.now());
        createObj.setCreateTime(LocalDateTime.now());
        createObj.setCreator(createObj.getName());
        createObj.setCreateId(id);
//        accountInfoRedisDao.saveAccountInfo(createObj);
        infoMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateInfo(AccountInfoSaveReqVO updateReqVO) {
        // 校验存在
        AccountInfoDO infoObj = this.getInfo(updateReqVO.getId());
        if (ObjectUtil.isEmpty(infoObj)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        
        if (accountInfoRedisDao.hasIdKey(infoObj.getId())) {
            accountInfoRedisDao.deleteAccountInfo(infoObj.getId());
        }
        // 更新
        AccountInfoDO updateObj = BeanUtils.toBean(updateReqVO, AccountInfoDO.class);
        updateObj.setUpdateData(LocalDate.now());
        updateObj.setUpdateTime(LocalDateTime.now());
        infoMapper.updateById(updateObj);

    }

    @Override
    public void deleteInfo(Long id) {
        // 校验存在
        AccountInfoDO info = this.getInfo(id);
        if (ObjectUtil.isEmpty(info)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        
        // 删除
        if (accountInfoRedisDao.hasIdKey(id)){
            accountInfoRedisDao.deleteAccountInfo(id);
        }
       
        infoMapper.deleteById(id);
    }

    private void validateInfoExists(Long id) {
        if (infoMapper.selectById(id) == null) {
//            throw exception(INFO_NOT_EXISTS);
        }
    }

    @Override
    public AccountInfoDO getInfo(Long id) {
        AccountInfoDO info = new AccountInfoDO();
        if (accountInfoRedisDao.hasIdKey(id)){
           info = accountInfoRedisDao.getAccountById(id);
           return info;
        }else {
            info = infoMapper.selectById(id);
            if (ObjectUtil.isNotEmpty(info)) {
                accountInfoRedisDao.saveAccountInfo(info);
            }
            return info;
        }
       
    }

    @Override
    public PageResult<AccountInfoDO> getInfoPage(AccountInfoPageReqVO pageReqVO) {
//        return infoMapper.selectPage(pageReqVO);
        PageResult<AccountInfoDO> pageResult = accountInfoRedisDao.getAccountInfoPage(pageReqVO.getNickname(), pageReqVO.getPageNo(), pageReqVO.getPageSize());

        if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0 && pageResult.getList().size() == 0) {
            pageResult = infoMapper.selectPage(pageReqVO);

            if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0 && CollUtil.isNotEmpty(pageResult.getList())) {
                pageResult.getList().forEach(info -> {
                    accountInfoRedisDao.saveAccountInfo(info);
                });
            }
        }
        return pageResult;
    }

    @Override
    public Long getUserCountByTagId(Long tagId) {
        return infoMapper.selectCountByTagId(tagId);
    }

    /**
     * @param mobile
     * @return
     */
    @Override
    public AccountInfoDO getUserByMobile(String mobile) {
        AccountInfoDO accountInfoDO = infoMapper.selectByMobile(mobile);
        return accountInfoDO;
    }

/**
 * @param id
 *
 * @return
 */
@Override
public AccountOtherInfoRespVO getOtherInfo(Long id) {
    Boolean fansStatus = Boolean.FALSE;
    AccountOtherInfoRespVO info = BeanUtils.toBean(getInfo(id), AccountOtherInfoRespVO.class);
    
    if (ObjectUtil.isEmpty(info)) {
        return info;
    }
    List<SocialFansDTO> fansList = socialFansApi.queryFans(id);
    if (CollUtil.isNotEmpty(fansList)) {
        info.setFansNum((long) fansList.size());
        Long loginAccountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isNotEmpty(loginAccountId)) {
            fansStatus = socialFansApi.isFans(id, loginAccountId);
        }
    }else {
        info.setFansNum(0L);
    }
    info.setFollowNum(socialFansApi.queryFollowNum(id));
    info.setFansStatus(fansStatus);
    return info;
}

    /**
     * @param mobile
     *
     * @return
     */
    @Override
    public Boolean checkAccountMobile(String mobile) {
        return accountInfoRedisDao.checkAccountMobile(mobile);
    }
    
    /**
     * @param openId
     *
     * @return
     */
    @Override
    public Boolean checkAccountOpenId(String openId) {
        return accountInfoRedisDao.checkAccountOpenId(openId);
    }
    
    /**
     * @param accountId
     * @param openId
     */
    @Override
    public void saveAccountOpenId(Long accountId, String openId) {
        accountInfoRedisDao.saveAccountOpenId(accountId, openId);
    }
    
    /**
     * @param openId
     *
     * @return
     */
    @Override
    public AccountInfoDO getAccountByOpenId(String openId) {
        return accountInfoRedisDao.getAccountByOpenId(openId);
    }
    
    /**
     * @param accountId
     * @param mobile
     */
    @Override
    public void saveAccountMobile(Long accountId, String mobile) {
        accountInfoRedisDao.saveAccountMobile(accountId, mobile);
    }
    
    /**
     * @param mobile
     *
     * @return
     */
    @Override
    public AccountInfoDO getAccountByMobile(String mobile) {
        return accountInfoRedisDao.getAccountByMobile(mobile);
    }

    /**
     * @return
     */
    @Override
    public List<Long> getAccountId() {
        List<Long> accountIdList = new ArrayList<>();
        List<AccountInfoDO> accountInfoDOS = accountInfoRedisDao.getAccountInfoList();
        if (CollUtil.isNotEmpty(accountInfoDOS)) {
            accountIdList = accountInfoDOS.stream()
                    .filter(account -> account.getRightsId() > -1L)
                    .map(AccountInfoDO::getId)
                    .collect(Collectors.toList());
        }
        return accountIdList;
    }


}
package com.fozmo.ym.module.account.service.my;

import com.fozmo.ym.module.account.controller.app.my.vo.MyAccountInfoVO;
import com.fozmo.ym.module.space.api.dto.SpaceDTO;
import com.fozmo.ym.module.space.api.dto.WorksDTO;


public interface MyAccountService {
    MyAccountInfoVO getMyAccountInfoById(Long accountId);

    /**
     *  获取我的作品详情
     */
    WorksDTO getMyWorks(Integer pageNo, Integer pageSize,Long accountId,Integer pointType);
    /**
     *  获取我的空间
     */
    SpaceDTO getMySpace(Integer pageNo, Integer pageSize,Long accountId);
}

package com.fozmo.ym.module.account.service.my;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.dto.HumanDTO;
import com.fozmo.ym.module.account.controller.admin.group.vo.AccountGroupInfoRespVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagInfoRespVO;
import com.fozmo.ym.module.account.controller.app.human.vo.AppAccountHumanInfoResp;
import com.fozmo.ym.module.account.controller.app.my.vo.MyAccountInfoVO;
import com.fozmo.ym.module.account.dal.dataobject.group.AccountGroupDO;
import com.fozmo.ym.module.account.dal.dataobject.info.AccountInfoDO;
import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
import com.fozmo.ym.module.account.service.group.AccountGroupService;
import com.fozmo.ym.module.account.service.human.AccountHumanService;
import com.fozmo.ym.module.account.service.info.AccountInfoService;
import com.fozmo.ym.module.account.service.tag.AccountTagService;
import com.fozmo.ym.module.rights.api.RightsApi;
import com.fozmo.ym.module.rights.api.dto.RightsInfoDTO;
import com.fozmo.ym.module.social.api.fans.SocialFansApi;
import com.fozmo.ym.module.social.api.social.SocialApi;
import com.fozmo.ym.module.space.api.SpaceApi;
import com.fozmo.ym.module.space.api.WorksApi;
import com.fozmo.ym.module.space.api.dto.SpaceDTO;
import com.fozmo.ym.module.space.api.dto.WorksDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class MyAccountServiceImpl implements MyAccountService {

    @Resource
    private AccountInfoService accountInfoService;

    @Resource
    private RightsApi rightsApi;

    @Resource
    @Lazy
    private WorksApi worksApi;

    @Resource
    @Lazy
    private SocialApi socialApi;

    @Resource
    @Lazy
    private SpaceApi spaceApi;

    @Resource
    private AccountGroupService accountGroupService;

    @Resource
    private AccountTagService accountTagService;
    
    @Resource
    @Lazy
    private AccountHumanService accountHumanService;

    @Resource
    @Lazy
    private SocialFansApi socialFansApi;


    /**
 * @param accountId
 *
 * @return
 */
@Override
public MyAccountInfoVO getMyAccountInfoById(Long accountId) {
    MyAccountInfoVO myAccountInfoVO = new MyAccountInfoVO();
    // 处理用户基础信息
    myAccountInfoVO= BeanUtils.toBean(queryAccountInfoById(accountId), MyAccountInfoVO.class);
    if (ObjectUtil.isEmpty(myAccountInfoVO)) {
        return null;
    }
    // 处理用户权益信息
    myAccountInfoVO.setRightsInfo(queryRightsInfo(myAccountInfoVO.getRightsId()));
    // 处理用户积分信息
    
//    // 处理社交用户信息
//    myAccountInfoVO.setSocialUserInfo(querySocialUserInfo(myAccountInfoVO.getId()));
    // 处理用户标签
    List<AccountTagDO> tagList = accountTagService.getTagListByAccountId(myAccountInfoVO.getId())   ;
    if (CollectionUtil.isNotEmpty(tagList)) {
        myAccountInfoVO.setAccountTagInfo(BeanUtils.toBean(tagList, AccountTagInfoRespVO.class));
    }
    
    // 处理用户分组
    AccountGroupDO groupInfo = accountGroupService.getGroup(myAccountInfoVO.getGroupId());
    if (groupInfo != null) {
        List<AccountGroupInfoRespVO> groupList = new ArrayList<>();
        AccountGroupInfoRespVO groupInfoRespVO = new AccountGroupInfoRespVO();
        groupInfoRespVO.setId(groupInfo.getId());
        groupInfoRespVO.setGroupName(groupInfo.getGroupName());
        groupList.add(groupInfoRespVO);
        myAccountInfoVO.setAccountGroupInfo(groupList);
    }
    // 处理空间数量
//    SpaceDTO spaceDTO = querySpaceInfo(null,null,accountId);
//    if (spaceDTO != null) {
//        myAccountInfoVO.setSpaceNum(spaceDTO.getCurrentNum());
//    }
    if (accountId != null) {
        myAccountInfoVO.setFans(socialFansApi.isFans(accountId, accountId));
        myAccountInfoVO.setFansNum(Math.toIntExact(socialFansApi.fansNum(accountId)));
    } else {
        myAccountInfoVO.setFans(false);
        myAccountInfoVO.setFansNum(0);
    }
    myAccountInfoVO.setFollowNum(Math.toIntExact(socialFansApi.queryFollowNum(accountId)));
    
//    // 处理作品数量
//    WorksDTO worksDTO = queryWorksInfo(null,null,accountId,null);
//    if (ObjectUtil.isNotEmpty(worksDTO)) {
//        myAccountInfoVO.setWorksNum(worksDTO.getCurrentNum());
//    }
    
    
//     处理数字人信息
   
    AppAccountHumanInfoResp appAccountHumanInfoResp = accountHumanService.getHumanByAccountId(accountId);
    if (appAccountHumanInfoResp != null && appAccountHumanInfoResp.getHumanId()!= null) {
        HumanDTO humanDTO=BeanUtils.toBean(appAccountHumanInfoResp, HumanDTO.class);
        myAccountInfoVO.setHumanInfo(humanDTO);
    }
    return myAccountInfoVO;
}

/**
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public WorksDTO getMyWorks(Integer pageNo, Integer pageSize,Long accountId,Integer pointType) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if(loginUser == null){
            throw new RuntimeException("用户未登录");
        }
        return queryWorksInfo(pageNo, pageSize,loginUser.getId(),pointType);
    }

    /**
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public SpaceDTO getMySpace(Integer pageNo, Integer pageSize,Long accountId) {
        
        if (accountId == null) {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            if(loginUser == null){
                throw new RuntimeException("用户未登录");
            }
            return querySpaceInfo(pageNo, pageSize,loginUser.getId());
        }else {
            return querySpaceInfo(pageNo, pageSize,accountId);
        }
      
    }

    private AccountInfoDO queryAccountInfoById(Long accountId) {
    return accountInfoService.getInfo(accountId);
}


    /**
     * 获取权益规则详情
     */
    private RightsInfoDTO queryRightsInfo(Long rights){
        return rightsApi.getRightsInfo(rights);
    }

    /***
     * 获取社交用户详情
     */

//    private List<SocialUserDTO> querySocialUserInfo(Long accountId){
//       return socialApi.querySocialUsersByAccountId(accountId);
//    }

    private WorksDTO queryWorksInfo(Integer pageNo,Integer pageSize,Long accountId,Integer pointType){
       if (pageNo == null || pageSize == null) {
           pageNo = 1;
           pageSize = 10;
       }
        return worksApi.getWorks(pageNo,pageSize,accountId,pointType);
    }

    private SpaceDTO querySpaceInfo(Integer pageNo,Integer pageSize,Long accountId){
        if (pageNo == null || pageSize == null) {
            pageNo = 1;
            pageSize = 10;
        }
        return spaceApi.getSpaceInfo(pageNo,pageSize,accountId);
    }
}

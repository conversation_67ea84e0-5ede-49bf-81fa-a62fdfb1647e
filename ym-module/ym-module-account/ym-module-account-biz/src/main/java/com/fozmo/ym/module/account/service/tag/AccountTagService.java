package com.fozmo.ym.module.account.service.tag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagCreateReqVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagPageReqVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagUpdateReqVO;
import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 会员标签 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountTagService {

    /**
     * 创建会员标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTag(@Valid AccountTagCreateReqVO createReqVO);

    /**
     * 更新会员标签
     *
     * @param updateReqVO 更新信息
     */
    void updateTag(@Valid AccountTagUpdateReqVO updateReqVO);

    /**
     * 删除会员标签
     *
     * @param id 编号
     */
    void deleteTag(Long id);

    /**
     * 获得会员标签
     *
     * @param id 编号
     * @return 会员标签
     */
    AccountTagDO getTag(Long id);

    /**
     * 获得会员标签列表
     *
     * @param ids 编号
     * @return 会员标签列表
     */
    List<AccountTagDO> getTagList(Collection<Long> ids);

    /**
     * 获得会员标签分页
     *
     * @param pageReqVO 分页查询
     * @return 会员标签分页
     */
    PageResult<AccountTagDO> getTagPage(AccountTagPageReqVO pageReqVO);

    /**
     * 获取标签列表
     *
     * @return 标签列表
     */
    List<AccountTagDO> getTagList();


    List<AccountTagDO> getTagListByAccountId(Long id);
}

package com.fozmo.ym.module.account.service.tag;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagCreateReqVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagPageReqVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagUpdateReqVO;
import com.fozmo.ym.module.account.convert.tag.AccountTagConvert;
import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
import com.fozmo.ym.module.account.dal.mysql.tag.AccountTagMapper;
import com.fozmo.ym.module.account.service.info.AccountInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.account.enums.ErrorCodeConstants.*;

/**
 * 会员标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AccountTagServiceImpl implements AccountTagService {

    @Resource
    private AccountTagMapper accountTagMapper;

    @Resource
    private AccountInfoService accountInfoService;

    @Override
    public Long createTag(AccountTagCreateReqVO createReqVO) {
        // 校验名称唯一
        validateTagNameUnique(null, createReqVO.getName());
        // 插入
        AccountTagDO tag = AccountTagConvert.INSTANCE.convert(createReqVO);
        accountTagMapper.insert(tag);
        // 返回
        return tag.getId();
    }

    @Override
    public void updateTag(AccountTagUpdateReqVO updateReqVO) {
        // 校验存在
        validateTagExists(updateReqVO.getId());
        // 校验名称唯一
        validateTagNameUnique(updateReqVO.getId(), updateReqVO.getName());
        // 更新
        AccountTagDO updateObj = AccountTagConvert.INSTANCE.convert(updateReqVO);
        accountTagMapper.updateById(updateObj);
    }

    @Override
    public void deleteTag(Long id) {
        // 校验存在
        validateTagExists(id);
        // 校验标签下是否有用户
        validateTagHasUser(id);
        // 删除
        accountTagMapper.deleteById(id);
    }

    private void validateTagExists(Long id) {
        if (accountTagMapper.selectById(id) == null) {
            throw exception(TAG_NOT_EXISTS);
        }
    }

    private void validateTagNameUnique(Long id, String name) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        AccountTagDO tag = accountTagMapper.selectByName(name);
        if (tag == null) {
            return;
        }

        // 如果 id 为空，说明不用比较是否为相同 id 的标签
        if (id == null) {
            throw exception(TAG_NAME_EXISTS);
        }
        if (!tag.getId().equals(id)) {
            throw exception(TAG_NAME_EXISTS);
        }
    }

    void validateTagHasUser(Long id) {
        Long count = accountInfoService.getUserCountByTagId(id);
        if (count > 0) {
            throw exception(TAG_HAS_USER);
        }
    }

    @Override
    public AccountTagDO getTag(Long id) {
        return accountTagMapper.selectById(id);
    }

    @Override
    public List<AccountTagDO> getTagList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ListUtil.empty();
        }
        return accountTagMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<AccountTagDO> getTagPage(AccountTagPageReqVO pageReqVO) {
        return accountTagMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AccountTagDO> getTagList() {
        return accountTagMapper.selectList();
    }

    /**
     * @param id
     * @return
     */
    @Override
    public List<AccountTagDO> getTagListByAccountId(Long id) {

        LambdaQueryWrapper<AccountTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountTagDO::getAccountId, id);
        queryWrapper.eq(AccountTagDO::getDeleted,false);
        queryWrapper.orderByAsc(AccountTagDO::getId);
        return accountTagMapper.selectList(queryWrapper);
    }

}

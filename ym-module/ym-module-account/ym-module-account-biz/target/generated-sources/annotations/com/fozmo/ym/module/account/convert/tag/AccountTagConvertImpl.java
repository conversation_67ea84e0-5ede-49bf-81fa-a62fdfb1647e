package com.fozmo.ym.module.account.convert.tag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagCreateReqVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagRespVO;
import com.fozmo.ym.module.account.controller.admin.tag.vo.AccountTagUpdateReqVO;
import com.fozmo.ym.module.account.dal.dataobject.tag.AccountTagDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T14:06:56+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.15 (Ubuntu)"
)
public class AccountTagConvertImpl implements AccountTagConvert {

    @Override
    public AccountTagDO convert(AccountTagCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        AccountTagDO.AccountTagDOBuilder accountTagDO = AccountTagDO.builder();

        accountTagDO.name( bean.getName() );

        return accountTagDO.build();
    }

    @Override
    public AccountTagDO convert(AccountTagUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        AccountTagDO.AccountTagDOBuilder accountTagDO = AccountTagDO.builder();

        accountTagDO.id( bean.getId() );
        accountTagDO.name( bean.getName() );

        return accountTagDO.build();
    }

    @Override
    public AccountTagRespVO convert(AccountTagDO bean) {
        if ( bean == null ) {
            return null;
        }

        AccountTagRespVO accountTagRespVO = new AccountTagRespVO();

        accountTagRespVO.setName( bean.getName() );
        accountTagRespVO.setId( bean.getId() );
        accountTagRespVO.setCreateTime( bean.getCreateTime() );

        return accountTagRespVO;
    }

    @Override
    public List<AccountTagRespVO> convertList(List<AccountTagDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AccountTagRespVO> list1 = new ArrayList<AccountTagRespVO>( list.size() );
        for ( AccountTagDO accountTagDO : list ) {
            list1.add( convert( accountTagDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AccountTagRespVO> convertPage(PageResult<AccountTagDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AccountTagRespVO> pageResult = new PageResult<AccountTagRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );
        pageResult.setOtherTotal( page.getOtherTotal() );

        return pageResult;
    }
}

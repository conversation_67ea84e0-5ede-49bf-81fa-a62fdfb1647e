package com.fozmo.ym.module.auth.api.dept;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.auth.api.dept.dto.DeptRespDTO;
import com.fozmo.ym.module.auth.dal.dataobject.dept.DeptDO;
import com.fozmo.ym.module.auth.service.dept.DeptService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 部门 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class DeptApiImpl implements DeptApi {

    @Resource
    private DeptService deptService;

    @Override
    public DeptRespDTO getDept(Long id) {
        DeptDO dept = deptService.getDept(id);
        return BeanUtils.toBean(dept, DeptRespDTO.class);
    }

    @Override
    public List<DeptRespDTO> getDeptList(Collection<Long> ids) {
        List<DeptDO> depts = deptService.getDeptList(ids);
        return BeanUtils.toBean(depts, DeptRespDTO.class);
    }

    @Override
    public void validateDeptList(Collection<Long> ids) {
        deptService.validateDeptList(ids);
    }

    @Override
    public List<DeptRespDTO> getChildDeptList(Long id) {
        List<DeptDO> childDeptList = deptService.getChildDeptList(id);
        return BeanUtils.toBean(childDeptList, DeptRespDTO.class);
    }

    /**
     */
    @Override
    public List<DeptRespDTO> getDeptListByLeaderUserId(Long id) {

        if (ObjectUtil.isNull(id)) {
            return List.of();
        }
        List<DeptDO> deptDOS = deptService.getDeptListByLeaderUserId(id);
        if (CollectionUtil.isEmpty(deptDOS)) {
            return List.of();
        }
        return BeanUtil.copyToList(deptDOS, DeptRespDTO.class);
    }

    /**
     */
    @Override
    public List<DeptRespDTO> getChildDeptList(Set<Long> deptIds) {
        if (CollectionUtil.isNotEmpty(deptIds)) {
            List<DeptDO> deptList = deptService.getChildDeptList(deptIds);
            return BeanUtil.copyToList(deptList, DeptRespDTO.class);
        }
        return List.of();
    }

}

package com.fozmo.ym.module.auth.api.dept;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.enums.CommonStatusEnum;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.auth.api.dept.dto.PostRespDTO;
import com.fozmo.ym.module.auth.api.dept.dto.UserPostDTO;
import com.fozmo.ym.module.auth.dal.dataobject.dept.PostDO;
import com.fozmo.ym.module.auth.dal.dataobject.dept.UserPostDO;
import com.fozmo.ym.module.auth.dal.mysql.dept.PostMapper;
import com.fozmo.ym.module.auth.dal.mysql.dept.UserPostMapper;
import com.fozmo.ym.module.auth.service.dept.PostService;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.framework.common.util.collection.CollectionUtils.convertMap;
import static com.fozmo.ym.module.system.enums.ErrorCodeConstants.POST_NOT_ENABLE;
import static com.fozmo.ym.module.system.enums.ErrorCodeConstants.POST_NOT_FOUND;

/**
 * 岗位 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class PostApiImpl implements PostApi {

    @Resource
    private PostService postService;
    @Resource
    private UserPostMapper userPostMapper;
    @Resource
    private PostMapper postMapper;

    @Override
    public void validPostList(Collection<Long> ids) {
        postService.validatePostList(ids);
    }

    /**
     * @param id
     * @return
     */
    @Override
    public PostRespDTO getPostById(Long id) {
        PostDO postDO = postService.getPost(id);
        return BeanUtils.toBean(postDO, PostRespDTO.class);
    }

    @Override
    public List<PostRespDTO> getPostList(Collection<Long> ids) {
        List<PostDO> list = postService.getPostList(ids);
        return BeanUtils.toBean(list, PostRespDTO.class);
    }

    /**
     * @param ids
     * @return
     */
    @Override
    public Map<Long, PostRespDTO> getPostMap(Collection<Long> ids) {
        return PostApi.super.getPostMap(ids);
    }

    /**
     * @param list
     */
    @Override
    public void insertBatchUserPost(List<UserPostDTO> list) {
        if (list.isEmpty()) {
            return;
        }
        List<UserPostDO> postDOList = BeanUtils.toBean(list, UserPostDO.class);
        postService.insertBatchUserPost(postDOList);
    }

    /**
     * @param userId
     * @return
     */
    @Override
    public Collection<UserPostDTO> selectListByUserId(Long userId) {
        return List.of();
    }

    /**
     * @param userId
     * @param deletePostIds
     */
    @Override
    public void deleteByUserIdAndPostId(Long userId, Collection<Long> deletePostIds) {

        postService.deleteByUserIdAndPostId(userId,deletePostIds);

    }

    /**
     * @param ids
     */
    @Override
    public void validatePostList(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得岗位信息
        List<PostDO> posts = postMapper.selectBatchIds(ids);
        Map<Long, PostDO> postMap = convertMap(posts, PostDO::getId);
        // 校验
        ids.forEach(id -> {
            PostDO post = postMap.get(id);
            if (post == null) {
                throw exception(POST_NOT_FOUND);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(post.getStatus())) {
                throw exception(POST_NOT_ENABLE, post.getName());
            }
        });
    }

    /**
     * @param id
     */
    @Override
    public void deleteByUserId(Long id) {
        postService.deleteByUserId(id);
    }

    /**
     * @param postIds
     * @return
     */
    @Override
    public List<UserPostDTO> selectListByPostIds(Collection<Long> postIds) {
        List<UserPostDO> userPostDOS = postService.selectListByPostIds(postIds);
        return BeanUtil.copyToList(userPostDOS, UserPostDTO.class);
    }
}


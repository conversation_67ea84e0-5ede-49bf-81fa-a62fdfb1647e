package com.fozmo.ym.module.auth.api.permission;

import com.fozmo.ym.module.auth.api.menu.MenuApi;
import com.fozmo.ym.module.auth.api.menu.dto.MenuDataDTO;
import com.fozmo.ym.module.auth.dal.dataobject.permission.MenuDO;
import com.fozmo.ym.module.auth.service.permission.MenuService;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Service
@Validated
public class MenuApiImpl implements MenuApi {


    @Resource
    private MenuService menuService;
    /**
     * @return
     */
    @Override
    public List<MenuDataDTO> getMenuList() {
      List<MenuDO> menuList = menuService.getMenuList();
      return BeanUtil.copyToList(menuList, MenuDataDTO.class);
    }
}

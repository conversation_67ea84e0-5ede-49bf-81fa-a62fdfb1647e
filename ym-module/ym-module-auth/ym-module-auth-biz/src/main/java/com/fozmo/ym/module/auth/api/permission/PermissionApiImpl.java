package com.fozmo.ym.module.auth.api.permission;

import com.fozmo.ym.module.auth.api.permission.dto.DeptDataPermissionRespDTO;
import com.fozmo.ym.module.auth.service.permission.PermissionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Set;
@Service
@Validated
public class PermissionApiImpl implements PermissionApi {

    @Resource
    private PermissionService permissionService;

    @Override
    public Set<Long> getUserRoleIdListByRoleIds(Collection<Long> roleIds) {
        return permissionService.getUserRoleIdListByRoleId(roleIds);
    }

    @Override
    public boolean hasAnyPermissions(Long userId, String... permissions) {
        return permissionService.hasAnyPermissions(userId, permissions);
    }

    @Override
    public boolean hasAnyRoles(Long userId, String... roles) {
        return permissionService.hasAnyRoles(userId, roles);
    }

    @Override
    public DeptDataPermissionRespDTO getDeptDataPermission(Long userId) {
        return permissionService.getDeptDataPermission(userId);
    }

    /**
     * @param userId
     * @param singleton
     */
    @Override
    public void assignUserRole(Long userId, Set<Long> singleton) {
        permissionService.assignUserRole(userId, singleton);
    }

    /**
     * @param id
     * @param menuIds
     */
    @Override
    public void assignRoleMenu(Long id, Set<Long> menuIds) {
        permissionService.assignRoleMenu(id, menuIds);
    }

    /**
     * @param id
     * @return
     */
    @Override
    public Set<Long> getRoleMenuListByRoleId(Long id) {
        return permissionService.getRoleMenuListByRoleId(id);
    }

}

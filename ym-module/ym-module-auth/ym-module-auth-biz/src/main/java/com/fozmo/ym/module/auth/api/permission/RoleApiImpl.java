package com.fozmo.ym.module.auth.api.permission;

import com.fozmo.ym.module.auth.api.permission.dto.RoleDataDTO;
import com.fozmo.ym.module.auth.controller.admin.permission.vo.role.RoleSaveReqVO;
import com.fozmo.ym.module.auth.dal.dataobject.permission.RoleDO;
import com.fozmo.ym.module.auth.service.permission.RoleService;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;

@Service
@Validated
public class RoleApiImpl implements RoleApi {

    @Resource
    private RoleService roleService;

    @Override
    public void validRoleList(Collection<Long> ids) {
        roleService.validateRoleList(ids);
    }

    /**
     * @return
     */
    @Override
    public List<RoleDataDTO> getRoleList() {
        List<RoleDO> roleDOList =  roleService.getRoleList();
        return BeanUtil.copyToList(roleDOList, RoleDataDTO.class);
    }

    /**
     * @param reqVO
     * @param type
     * @return
     */
    @Override
    public Long createRole(RoleDataDTO reqVO, Integer type) {

        RoleSaveReqVO roleSaveReqVO = BeanUtil.copyProperties(reqVO, RoleSaveReqVO.class);

        return roleService.createRole(roleSaveReqVO, type);
    }
}

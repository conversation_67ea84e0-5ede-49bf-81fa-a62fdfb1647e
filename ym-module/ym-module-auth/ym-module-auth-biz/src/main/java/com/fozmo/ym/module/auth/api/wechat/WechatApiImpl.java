package com.fozmo.ym.module.auth.api.wechat;

import com.fozmo.ym.module.auth.api.WechatApi;
import com.fozmo.ym.module.auth.util.oauth2.WechatAppletUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WechatApiImpl implements WechatApi {

@Resource
private WechatAppletUtil wechatAppletUtil;

	/**
	 * @param code
	 *
	 * @return
	 */
	@Override
	public String createQRCode(String page,String code,String env) {
		
		return wechatAppletUtil.getAppletQrcodeStr(page,code,env);
	}
}

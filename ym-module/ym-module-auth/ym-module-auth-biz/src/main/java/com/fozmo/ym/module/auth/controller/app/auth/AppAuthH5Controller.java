package com.fozmo.ym.module.auth.controller.app.auth;

import cn.hutool.core.util.StrUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.security.config.SecurityProperties;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthH5LoginReqVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.service.auth.app.h5.AppAuthH5Service;
import com.fozmo.ym.module.log.enums.logger.LoginLogTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-鉴权模块-游客登录")
@RestController
@RequestMapping("/auth/h5")
@Validated
public class AppAuthH5Controller {
	
	@Resource
    private AppAuthH5Service appAuthH5Service;
	
	@Resource
	private SecurityProperties securityProperties;

	@PostMapping("/login")
	@Operation(summary = "h5登录（游客）")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> h5Login(@RequestBody @Valid AppAuthH5LoginReqVO reqVO) {
		AppAuthLoginRespVO authLogin= appAuthH5Service.h5Login(reqVO);
		return success(authLogin);
	}

	@PostMapping("/logout")
	@Operation(summary = "正式用户退出登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> logout(HttpServletRequest request, String code) {
		String token = SecurityFrameworkUtils.obtainAuthorization(request,
				securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
		if (StrUtil.isNotBlank(token)) {
			appAuthH5Service.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
			AppAuthH5LoginReqVO reqVO = new AppAuthH5LoginReqVO();
			reqVO.setCode(code);
			AppAuthLoginRespVO authLogin= appAuthH5Service.h5Login(reqVO);
			return success(authLogin);
		}
		return null;
	}
	
}

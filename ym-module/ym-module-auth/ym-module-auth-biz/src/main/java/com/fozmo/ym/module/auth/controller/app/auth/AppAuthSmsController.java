package com.fozmo.ym.module.auth.controller.app.auth;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthSmsLoginReqVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthSmsSendReqVO;
import com.fozmo.ym.module.auth.service.auth.app.sms.AppAuthSmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-鉴权模块-短信登录")
@RestController
@RequestMapping("/auth/sms")
@Validated
public class AppAuthSmsController {
	@Resource
	private AppAuthSmsService appAuthSmsService;

	@PostMapping("/login")
	@Operation(summary = "验证码登录-短信登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> smsLogin(@RequestBody @Valid AppAuthSmsLoginReqVO reqVO) {
		AppAuthLoginRespVO authLogin= appAuthSmsService.smsLogin(reqVO);
		return success(authLogin);
	}

	@PostMapping("/code")
	@Operation(summary = "发送手机验证码-短信登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Boolean> sendSmsCode(@RequestBody @Valid AppAuthSmsSendReqVO reqVO) {
		appAuthSmsService.sendSmsCode(reqVO);
		return success(true);
	}
}

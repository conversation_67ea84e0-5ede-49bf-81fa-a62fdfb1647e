package com.fozmo.ym.module.auth.controller.app.auth;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.auth.controller.app.auth.vo.*;
import com.fozmo.ym.module.auth.service.auth.app.wx.AppAuthWxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-鉴权模块-微信")
@RestController
@RequestMapping("/auth/wx")
@Validated
public class AppAuthWxController {

	@Resource
	private AppAuthWxService appAuthWxService;

	@PostMapping("/appMobile")
	@Operation(summary = "微信手机号登录",description = "仅限于新用户拿openId使用")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> appMobile(@RequestBody @Valid AppMobileLoginReqVO reqVO) {
		AppAuthLoginRespVO authLogin= appAuthWxService.appMobile(reqVO);
		return success(authLogin);
	}
	
	@PostMapping("/appLogin")
	@Operation(summary = "微信openId登录）",description = "新用户需要调用调用微信手机号登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> weixinMiniAppLogin(@RequestBody @Valid AppAuthWxMiniAppLoginReqVO reqVO) {
		AppAuthLoginRespVO authLogin= appAuthWxService.weixinMiniAppLogin(reqVO);
		return success(authLogin);
	}


	@PostMapping("/qrcodeLogin")
	@Operation(summary = "微信扫码登录）",description = "新用户需要调用调用微信手机号登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> qrcodeLogin(@RequestBody @Valid AppAuthWxMiniAppLoginReqVO reqVO) {
		AppAuthLoginRespVO authLogin= appAuthWxService.weixinMiniAppLogin(reqVO);
		return success(authLogin);
	}
	
	@PostMapping("/brand")
	@Operation(summary = "微信小程序印记")
	@PermitAll
	@ApiAccessLog
	public CommonResult<BrandRespVO> miniappBrand(@RequestBody @Valid AppAuthWxMiniAppLoginReqVO reqVO) {
		
		BrandRespVO brandInfo= appAuthWxService.miniappBrand(reqVO);
		return success(brandInfo);
	}
	
	
	@PostMapping("/qrcode")
	@Operation(summary = "获取二维码-扫码登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppQRCodeRespVO> getQRCode(){
		AppQRCodeRespVO qrInfo = appAuthWxService.getQRCode();
		return success(qrInfo);
	}
	
	
	@GetMapping("/getScene")
	@Operation(summary = "获取登录结果-扫码登录")
	@PermitAll
	@ApiAccessLog
	public CommonResult<AppAuthLoginRespVO> getScene(@RequestParam String scene) {
		AppAuthLoginRespVO authLogin= appAuthWxService.getScene(scene);
		return success(authLogin);
	}
	
}

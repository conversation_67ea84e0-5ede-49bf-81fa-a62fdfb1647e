package com.fozmo.ym.module.auth.controller.app.auth.vo;

import com.fozmo.ym.framework.common.validation.InEnum;
import com.fozmo.ym.framework.common.validation.Mobile;
import com.fozmo.ym.module.sms.enums.SmsSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "用户 APP - 发送手机验证码 Request VO")
@Data
@Accessors(chain = true)
public class AppAuthSmsSendReqVO {

    @Schema(description = "手机号", example = "***********")
    @Mobile
    private String mobile;

    @Schema(description = "发送场景,对应 SmsSceneEnum 枚举", example = "1")
    @NotNull(message = "发送场景不能为空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;
    
    @Schema(description = "修改密码时 传入", example = "1")
    private Long accountId;

}

package com.fozmo.ym.module.auth.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppMobileLoginReqVO {

    @NotNull(message = "code 不可为空")
    @Schema(description = "微信获取手机号的code")
    private String code;
    @NotNull(message = "openId 不可为空")
    @Schema(description = "微信OpenId")
    private String openid;
    @Schema(description = "扫码登录时使用")
    private String scene;
}

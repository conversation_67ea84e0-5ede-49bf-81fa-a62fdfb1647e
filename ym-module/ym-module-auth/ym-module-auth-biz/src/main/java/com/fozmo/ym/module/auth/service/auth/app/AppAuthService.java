package com.fozmo.ym.module.auth.service.auth.app;

import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthRegisterVO;
import com.fozmo.ym.module.social.api.dto.SocialPlatformDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;

public interface AppAuthService {

    void logout(String token, Integer type);
    
    Long register(AppAuthRegisterVO vo);
    
    AppAuthLoginRespVO login(Long accountId,Integer type);

   SocialPlatformDTO getPlatformByCode(String code);


    void userBind(SocialUserMessageDTO socialUserMessageDTO);
    
    void updateAccount(AppAuthRegisterVO appAuthRegisterVO);

    SocialUserDTO getSocialUserByCode(String code);
}

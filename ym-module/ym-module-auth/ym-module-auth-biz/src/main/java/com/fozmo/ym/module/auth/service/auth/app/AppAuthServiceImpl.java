package com.fozmo.ym.module.auth.service.auth.app;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.enums.UserTypeEnum;
import com.fozmo.ym.framework.common.util.monitor.TracerUtils;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.common.util.servlet.ServletUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.HumanApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.account.api.account.dto.CreateAccountDTO;
import com.fozmo.ym.module.account.api.account.dto.HumanDTO;
import com.fozmo.ym.module.account.api.account.dto.UpdateAccountDTO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthRegisterVO;
import com.fozmo.ym.module.auth.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.fozmo.ym.module.auth.service.oauth2.OAuth2TokenService;
import com.fozmo.ym.module.log.api.logger.LoginLogApi;
import com.fozmo.ym.module.log.api.logger.dto.LoginLogCreateReqDTO;
import com.fozmo.ym.module.social.api.dto.SocialPlatformDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;
import com.fozmo.ym.module.social.api.social.SocialApi;
import com.fozmo.ym.module.social.enums.social.SocialPlatformEnum;
import com.fozmo.ym.module.system.enums.logger.LoginResultEnum;
import com.fozmo.ym.module.system.enums.oauth2.OAuth2ClientConstants;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.framework.common.util.servlet.ServletUtils.getClientIP;
import static com.fozmo.ym.framework.common.util.servlet.ServletUtils.getUserAgent;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT_EXISTS;
import static com.fozmo.ym.module.social.enums.social.SocialPlatformEnum.H5;

@Service
@Slf4j
public class AppAuthServiceImpl implements AppAuthService {


    public String userAvatar = "https://fozmo-yuanmei.oss-cn-hangzhou.aliyuncs.com/system/uavatar/bg4.png";
    @Resource
    private OAuth2TokenService oAuth2TokenService;
    @Resource
    private LoginLogApi loginLogApi;
    @Resource
    private AccountApi accountApi;
    @Resource
    private SocialApi socialApi;
    @Resource
    private HumanApi humanApi;

//    /**
//     * 短信验证码登录
//     *
//     * @param reqVO
//     * @return
//     */
//    @Override
//    public AppAuthLoginRespVO smsLogin(AppAuthSmsLoginReqVO reqVO) {
//        AppAuthSmsLoginDTO loginDTO = BeanUtils.toBean(reqVO, AppAuthSmsLoginDTO.class);
//        String mobiel = loginDTO.getMobile();
//        if (StringUtils.isEmpty(mobiel)) {
////            throw exception()
//        }
//        // 查询 社交渠道
//        SocialPlatformDTO socialPlatform = socialApi.getPlatformByCode(SocialPlatformEnum.YM_APP.getCode());
//
//        if (StringUtils.isBlank(mobiel)) {
//            throw new RuntimeException("手机号不可为空");
//        }else {
//            // 通过 手机号 查询社交用户是否存在
//            SocialUserDTO socialUser = socialApi.getUserByMobile(socialPlatform.getId(),mobiel);
//            Long socialUserId = 0L;
//            if (ObjectUtil.isNull(socialUser)) {
//                // 用户不存在时 新增社交用户
//                SocialUserSaveReqVO saveReqVO = new SocialUserSaveReqVO();
//                saveReqVO.setMobile(mobiel);
//                saveReqVO.setCode(socialPlatform.getCode());
//                saveReqVO.setUserCode(mobiel);
//                saveReqVO.setType(socialPlatform.getId());
//                saveReqVO.setOpenId(mobiel);
//                saveReqVO.setToken(mobiel);
//                saveReqVO.setTenantCode("1");
//                saveReqVO.setAvatar(userAvatar);
//                socialUserId=socialUserService.createUser(saveReqVO);
//            }else {
//                socialUserId=socialUser.getId();
//            }
//
//            // 通过手机号 查询 账户
//            AppAccountInfoRespDTO accountInfo = accountAuthApi.getAccountInfoByMobile(mobiel);
//            Long accountId = 0L;
//            if (ObjectUtil.isNull(accountInfo)) {
//                accountInfo.setMobile(mobiel);
//
//            }else {
//                accountId = accountInfo.getId();
//            }
//
//        }
//
//        AppAuthLoginRespDTO appAuthLoginRespDTO = accountAuthApi.smsLogin(loginDTO);
//
//
//        return BeanUtils.toBean(appAuthLoginRespDTO, AppAuthLoginRespVO.class);
//    }

//    /**
//     * 微信小程序登录
//     *
//     * @param reqVO
//     */
//    @Override
//    public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWxMiniAppLoginReqVO reqVO) {
//        Boolean updateMobile = Boolean.TRUE;
//        AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
//        String scene = reqVO.getScene();
//
//        String name ="";
//        // 1、执行小程序登录
//        LoginRespDto loginRespDto = wechatAppletUtil.appletLogin(reqVO.getLoginCode());
//        if (ObjectUtil.isNull(loginRespDto)) {
//            throw exception(SOCIAL_USER_AUTH_FAILURE,"小程授权失败");
//        }
//
//        String unionid = loginRespDto.getUnionid();
//        String openid = loginRespDto.getOpenid();
//
//        // 2、查询社交渠道
//        SocialPlatformDO socialPlatform = socialPlatformService.getPlatform(Long.valueOf(SocialPlatformEnum.YM_APP.getType()));
//        AppAccountInfoRespDTO accountInfo = new AppAccountInfoRespDTO();
//        // 3 查询当前用户的账户Id 查询账户信息
//        Long accountId = SecurityFrameworkUtils.getLoginUserId();
//        Long socialUserId = 0L;
//
//        String mobile = "";
//        // 查询账户的信息
//        if (ObjectUtil.isNotEmpty(accountId)) {
//            accountInfo= accountAuthApi.getAccountInfoById(accountId);
//        }else {
//            throw exception(SOCIAL_USER_NOT_EXISTS);
//        }
//
//
//        // 4 根据唯一标识 处理微信小程序社交用户
//            // 4.1 查询微信小程序社交用户用户
//            SocialUserDO socialUser = new SocialUserDO();
//            if (socialUserRedisDao.hasUserWxUnKey(unionid)){
//                socialUser = socialUserRedisDao.getUserWxUnKey(unionid);
//            }else {
//                socialUser=socialUserService.getUserByCode(unionid);
//                if (ObjectUtil.isNotEmpty(socialUser)) {
//                    socialUserRedisDao.setUserWxUser(socialUser);
//                }
//            }
//
//            // 当前微信小程序社交用户为null 则新增
//            if (ObjectUtil.isNull(socialUser)) {
//                SocialUserSaveReqVO saveReqVO = new SocialUserSaveReqVO();
//                saveReqVO.setOpenId(loginRespDto.getOpenid());
//                saveReqVO.setUserCode(unionid);
//                saveReqVO.setType(socialPlatform.getId());
//                saveReqVO.setCode(unionid);
//                saveReqVO.setToken(UUID.randomUUID().toString().replace("-", ""));
//                saveReqVO.setTenantCode("1");
//                saveReqVO.setAvatar(userAvatar);
//                name ="用户"+saveReqVO.hashCode();
//                saveReqVO.setNickName(name);
//
//                socialUserId = socialUserService.createUser(saveReqVO);
//
//                if (!socialUserRedisDao.hasUserWxUnKey(unionid)){
//                    socialUserRedisDao.setUserWxUser(this.socialUserService.getUser(socialUserId));
//                }
//            }else {
//                // 当前小程序社交用户不为null，处理小程序社交用户Id 和手机号
//                socialUserId = socialUser.getId();
//                // 处理手机号，为空时提示用户补全手机号
//                mobile = socialUser.getMobile();
//                if (StringUtils.isNotBlank(mobile)) {
//                    updateMobile = Boolean.FALSE;
//                }else {
//                    updateMobile = Boolean.TRUE;
//                }
//            }
//            // 4.2 查询社交绑定关系
//            SocialBindDO socialBind = socialBindService.getBindByUserId(socialUserId);
//            if (ObjectUtil.isNull(socialBind)) {
//                // 如果绑定关系不存在 说明需要新建绑定关系
//                SocialBindSaveReqVO socialBindSaveReqVO = new SocialBindSaveReqVO();
//                socialBindSaveReqVO.setAccountId(accountId);
//                socialBindSaveReqVO.setSocialType(socialPlatform.getId());
//                socialBindSaveReqVO.setUserId(socialUserId);
//                socialBindSaveReqVO.setTenantCode("1");
//                socialBindSaveReqVO.setUserType(Long.valueOf(UserTypeEnum.MEMBER.getValue()));
//                socialBindService.createBind(socialBindSaveReqVO);
//
//                // 社交关系不存在 说明 当前用户需要升级 执行账户更新操作
//                // 判断当前用户是游客 还是其他用户
//                Long rightId = accountInfo.getRightsId();
//                if (ObjectUtil.isEmpty(rightId) || rightId<0L) {
//                    // 游客信息需要修改
//                    accountInfo.setRightsId(0L);
//                    accountInfo.setRightsLevel(0);
//                    accountInfo.setName(name);
//
//                }
//
//                accountAuthApi.updateAccountInfo(accountInfo);
//            }else {
//                // 说明小程序社交用户已存在
//                Long wxAccountId = socialBind.getAccountId();
//                accountInfo= accountAuthApi.getAccountInfoById(wxAccountId);
//            }
//
//        if (ObjectUtil.isNotEmpty(accountInfo)) {
//            OAuth2AccessTokenDO oAuth2AccessToken = createAccessToken(accountInfo);
//            authLoginRespVO=BeanUtils.toBean(oAuth2AccessToken, AppAuthLoginRespVO.class);
//            authLoginRespVO.setOpenid(loginRespDto.getOpenid());
//            authLoginRespVO.setUpdateMobile(updateMobile);
//            if (StringUtils.isNotEmpty(scene)){
//                String key = WX_LOGIN_SCENE+scene;
//                if (StringUtils.isNotEmpty(key)) {
//                    Map map = (Map) redisTemplate.opsForValue().get(key);
//                    if (map == null) {
//                        map.put("token",JSONUtil.toJsonStr(oAuth2AccessToken));
//                        redisTemplate.opsForValue().set(key, map);
//                    }
//                }
//
//            }
//        }
//        return authLoginRespVO;
//    }
//
//    /**
//     * 扫码登录获取小程序验证码
//     */
//    @Override
//    public AppQRCodeRespVO getQRCode() {
//        String scene = UUID.randomUUID().toString().replace("-", "");
//        String env = "develop";
//        String qrResult = wechatAppletUtil.getAppletQrcodeStr(null,scene,env);
//        AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
//        authLoginRespVO.setScene(scene);
//        Map map = new HashMap();
//        map.put("scene",scene);
//        map.put("env",env);
//        map.put("status",0);
//        map.put("token", JSONUtil.toJsonStr(authLoginRespVO));
//        String key = WX_LOGIN_SCENE+scene;
//        redisTemplate.opsForValue().set(key,map,5, TimeUnit.MINUTES);
//        return new AppQRCodeRespVO(scene,env,qrResult);
//    }
//
//    /**
//     * 短信认证时 发送短信验证码
//     *
//     * @param reqVO
//     */
//    @Override
//    public void sendSmsCode(AppAuthSmsSendReqVO reqVO) {
//        AppAuthSmsSendDTO dto = BeanUtils.toBean(reqVO,AppAuthSmsSendDTO.class);
//        accountAuthApi.sendSmsCode(dto);
//    }
//
//    /**
//     * @param reqVO
//     * @return
//     */
//    @Override
//    public BrandRespVO miniappBrand(AppAuthWxMiniAppLoginReqVO reqVO) {
//        LoginRespDto loginRespDto = wechatAppletUtil.appletLogin(reqVO.getLoginCode());
//        return BeanUtils.toBean(loginRespDto, BrandRespVO.class);
//    }
//
//    /**
//     * @param reqVO
//     * @return
//     */
//    @Override
//    public AppAuthLoginRespVO h5Login(AppAuthH5LoginReqVO reqVO) {
//        AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
//        String code = reqVO.getCode();
//        if (StringUtils.isEmpty(code)) {
//            throw new RuntimeException("游客标志不可为空");
//        }
//
//        // 查询 社交渠道
//        SocialPlatformDO socialPlatform = socialPlatformService.getPlatform(Long.valueOf(SocialPlatformEnum.H5.getType()));
//        // 1新增社交用户
//        SocialUserSaveReqVO saveReqVO = new SocialUserSaveReqVO();
//        saveReqVO.setOpenId(code);
//
//        saveReqVO.setUserCode(code);
//        saveReqVO.setUserCode(code);
//        saveReqVO.setType(socialPlatform.getId());
//        saveReqVO.setCode(code);
//        saveReqVO.setToken(UUID.randomUUID().toString().replace("-", ""));
//        saveReqVO.setTenantCode("1");
//        // 设置头像
//        saveReqVO.setNickName("游客"+saveReqVO.hashCode());
//        saveReqVO.setAvatar(userAvatar);
//        Long socialUserId = socialUserService.createUser(saveReqVO);
//        System.out.println("新增社交用户："+socialUserId);
//
//        // 新增账户信息
//        AppAccountInfoRespDTO accountInfo= new AppAccountInfoRespDTO();
//        accountInfo.setAccountType(-1);
//        accountInfo.setMobile(code);
//        accountInfo.setCode(code);
//        accountInfo.setSex(0);
//
//        accountInfo.setRightsId(-1L);
//        accountInfo.setRightsLevel(-1);
//        accountInfo.setRegisterTime(LocalDateTime.now());
//        String name= "游客"+accountInfo.hashCode();
//        accountInfo.setNickname(name);
//        accountInfo.setName(name);
//        accountInfo.setAvatar(userAvatar);
//        Long accountUserId = accountAuthApi.createAccount(accountInfo);
//        System.out.println("新增账户："+accountUserId);
//        accountInfo.setId(accountUserId);
//        // 新增绑定关系
//        SocialBindSaveReqVO socialBindSaveReqVO = new SocialBindSaveReqVO();
//        socialBindSaveReqVO.setAccountId(accountUserId);
//        socialBindSaveReqVO.setSocialType(socialPlatform.getId());
//        socialBindSaveReqVO.setUserId(socialUserId);
//        socialBindSaveReqVO.setTenantCode("1");
//        socialBindSaveReqVO.setUserType(Long.valueOf(UserTypeEnum.VISITOR.getValue()));
//        Long bindId = socialBindService.createBind(socialBindSaveReqVO);
//        System.out.println("新增绑定关系："+bindId);
//        if (ObjectUtil.isNotEmpty(accountInfo)) {
//        OAuth2AccessTokenDO oAuth2AccessToken = createAccessToken(accountInfo);
//        authLoginRespVO=BeanUtils.toBean(oAuth2AccessToken, AppAuthLoginRespVO.class);
//    }
//        return authLoginRespVO;
//    }

//    /**
//     * @param reqVO
//     * @return
//     */
//    @Override
//    public AppMobileRespVO appMobile(AppMobileLoginReqVO reqVO) {
//        AppMobileRespVO authLoginRespVO = new AppMobileRespVO();
//
//        String openId = reqVO.getOpenid();
//        String scene = reqVO.getScene();
//        String socialUserCode = reqVO.getCode();
//
//        String mobile = wechatAppletUtil.appletMobile(socialUserCode);
//        if (StringUtils.isEmpty(mobile)) {
//            throw new RuntimeException("手机号不存在");
//        }
//
//        // 根据openId 获取 社交用户
//        SocialUserDO socialUser = socialUserService.getUserByOpenId(openId);
//        if (ObjectUtil.isEmpty(socialUser)) {
//            throw new RuntimeException("社交用户不存在");
//        }
//        SocialUserSaveReqVO saveReqVO =BeanUtils.toBean(socialUser, SocialUserSaveReqVO.class);
//        saveReqVO.setMobile(mobile);
//        socialUserService.updateUser(saveReqVO);
//
//        // 查询 社交渠道
//        SocialPlatformDO socialPlatform = socialPlatformService.getPlatform(Long.valueOf(SocialPlatformEnum.YM_APP.getType()));
//        SocialBindDO socialBind = socialBindService.getBindByUserId(socialUser.getId());
//        if (ObjectUtil.isEmpty(socialBind)) {
//            throw new RuntimeException("<UNK>");
//        }
//        AppAccountInfoRespDTO accountInfo = accountAuthApi.getAccountInfoById(socialBind.getAccountId());
//
//        if (ObjectUtil.isEmpty(accountInfo)) {
//            throw new RuntimeException("<UNK>");
//        }
//        accountInfo.setMobile(mobile);
//        accountAuthApi.updateAccountInfo(accountInfo);
//        authLoginRespVO.setMobile(mobile);
//
//        if (StringUtils.isNotEmpty(scene)) {
//            String key = WX_LOGIN_SCENE+scene;
//            if (redisTemplate.hasKey(key)) {
//                Map<String,Object> map = redisTemplate.opsForHash().entries(key);
//                map.put("status",1);
//                redisTemplate.opsForValue().set(key,map);
//            }
//        }
//        return authLoginRespVO;
//    }

//    /**
//     * @param scene
//     * @return
//     */
//    @Override
//    public AppAuthLoginRespVO getScene(String scene) {
//        String key = WX_LOGIN_SCENE+scene;
//        AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
//        if (redisTemplate.hasKey(key)) {
//            Map<String, Object> map = redisTemplate.opsForHash().entries(key);
//            Integer status = 0;
//            if(map.containsKey("status")){
//               status= (Integer)map.get("status");
//            }
//
//            if (map.containsKey("token") && status==1){
//                String token = (String)map.get("token");
//                 authLoginRespVO= JSONUtil.toBean(token,AppAuthLoginRespVO.class);
//            }
//            return authLoginRespVO;
//        }else {
//            throw new RuntimeException("已失效");
//        }
//    }

/**
 * @param token
 * @param type
 */
@Override
public void logout(String token, Integer type) {
    // 删除访问令牌
    OAuth2AccessTokenDO accessTokenDO = oAuth2TokenService.removeAccessToken(token);
    if (accessTokenDO == null) {
        return;
    }
    // 删除成功，则记录登出日志
    createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), type);
}

    /**
     * @param vo
     *
     * @return
     */
    @Override
    public Long register(AppAuthRegisterVO vo) {
        Integer accountType = vo.getAccountType();
        if (ObjectUtil.isEmpty(accountType)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        vo.setRightsId(0L);
        vo.setRightsLevel(0);
        
        if (accountType == SocialPlatformEnum.YM_APP.getType()) {
            vo.setRegisterChannel(SocialPlatformEnum.YM_APP.getCode());
        }
        
        if (accountType == SocialPlatformEnum.APP_MOBILE.getType()) {
            vo.setRegisterChannel(SocialPlatformEnum.APP_MOBILE.getCode());
        }
        if (accountType == SocialPlatformEnum.TIKTOK.getType()){
            vo.setRegisterChannel(SocialPlatformEnum.TIKTOK.getCode());
        }
        vo.setRegistDevice(getUserAgent());
        vo.setRegistIp(getClientIP());
        vo.setAvatar(userAvatar);
        vo.setSex(3);
        vo.setGuidStatus("0");
        vo.setAccountStatus(0);
        vo.setPassword("123456");
        vo.setRegistTime(LocalDateTime.now());
        vo.setRegistDate(LocalDate.now());
        String name= "用户"+vo.hashCode();
        vo.setNickname(name);
        vo.setName(name);
        if (accountType == H5.getType()) {
            vo.setRegisterChannel(H5.getCode());
            vo.setRightsId(-1L);
            vo.setRightsLevel(-1);
            name= "游客"+vo.hashCode();
            vo.setName(name);
            vo.setNickname(name);
        }
        
        // 创建 账户信息
        CreateAccountDTO createAccountDTO = BeanUtils.toBean(vo, CreateAccountDTO.class);
        Long accountId = accountApi.createAccount(createAccountDTO);
        
        HumanDTO humanDTO = new HumanDTO();
        humanDTO.setAccountId(accountId);
        humanDTO.setHumanId(2L);
        // 创建数字人
        if (accountType == H5.getType()) {
            humanDTO.setHumanId(1L);
        }
        humanApi.createHuman(humanDTO);
        return accountId;
    }

    /**
     * @param type
     *
     * @return
     */
    @Override
    public AppAuthLoginRespVO login(Long accountId,Integer type) {
        OAuth2AccessTokenDO accessTokenDO =  oAuth2TokenService.createAccessToken(accountId, type,  OAuth2ClientConstants.CLIENT_ID_DEFAULT, null);
        return BeanUtils.toBean(accessTokenDO, AppAuthLoginRespVO.class);
    }
    
    /**
     * @param code
     *
     * @return
     */
    @Override
    public SocialPlatformDTO getPlatformByCode(String code) {
        return socialApi.getPlatformByCode(code);
    }

    /**
     * @param socialUserMessageDTO
     */
    @Override
    public void userBind(SocialUserMessageDTO socialUserMessageDTO) {
        socialApi.createUserBind(socialUserMessageDTO);
    }
    
    /**
     * @param appAuthRegisterVO
     */
    @Override
    public void updateAccount(AppAuthRegisterVO appAuthRegisterVO) {
        // 登录后 更新账户信息
       
        // 查询 当前用的 权限
        AccountBaseInfoDTO accountInfoDTO = accountApi.queryAccountBaseInfoById(appAuthRegisterVO.getId());
        
        UpdateAccountDTO updateAccountDTO = BeanUtils.toBean(appAuthRegisterVO, UpdateAccountDTO.class);
        // 判断 权益
        if (ObjectUtil.isNotEmpty(accountInfoDTO)) {
            if (accountInfoDTO.getRightsId()==-1L){
                updateAccountDTO.setRightsId(0L);
                updateAccountDTO.setRightsLevel(0);
                updateAccountDTO.setName(accountInfoDTO.getName().substring(2));
            }
        }else {
            updateAccountDTO.setRightsId(0L);
            updateAccountDTO.setRightsLevel(0);
        }
        
        
        updateAccountDTO.setLastLoginDevice(getUserAgent());
        updateAccountDTO.setLastLoginTime(LocalDateTime.now());
        accountApi.updateAccountInfo(updateAccountDTO);
    }

/**
 * @param code
 *
 * @return
 */
@Override
public SocialUserDTO getSocialUserByCode(String code) {
    return socialApi.querySocialUserByCode(code);
}

private void createLogoutLog(Long userId, Integer userType, Integer logType) {
    LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
    reqDTO.setLogType(logType);
    reqDTO.setTraceId(TracerUtils.getTraceId());
    reqDTO.setUserId(userId);
    reqDTO.setUserType(userType);
    if (ObjectUtil.equal(getUserType().getValue(), userType)) {
        reqDTO.setUsername(getUsername());
    }
    reqDTO.setUserAgent(ServletUtils.getUserAgent());
    reqDTO.setUserIp(getClientIP());
    reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
    loginLogApi.createLoginLog(reqDTO);
}

    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER;
    }
    private String getUsername(){
       return WebFrameworkUtils.getLoginUserName();
    }
}



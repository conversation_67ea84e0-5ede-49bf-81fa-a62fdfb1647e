package com.fozmo.ym.module.auth.service.auth.app.h5;

import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthH5LoginReqVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import jakarta.validation.Valid;

public interface AppAuthH5Service {
	/**
	 * h5 登录
	 */
	AppAuthLoginRespVO h5Login(@Valid AppAuthH5LoginReqVO reqVO);

    void logout(String token, Integer type);
}

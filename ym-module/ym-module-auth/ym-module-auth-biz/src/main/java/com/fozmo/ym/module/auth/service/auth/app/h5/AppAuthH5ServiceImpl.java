package com.fozmo.ym.module.auth.service.auth.app.h5;

import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthH5LoginReqVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthRegisterVO;
import com.fozmo.ym.module.auth.service.auth.app.AppAuthService;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.fozmo.ym.framework.common.enums.UserTypeEnum.MEMBER;
import static com.fozmo.ym.module.social.enums.social.SocialPlatformEnum.H5;

@Service
public class AppAuthH5ServiceImpl implements AppAuthH5Service {
	
	@Resource
	private AppAuthService appAuthService;
	/**
	 * h5 登录
	 *
	 * @param reqVO
	 */
	@Override
	public AppAuthLoginRespVO h5Login(AppAuthH5LoginReqVO reqVO) {
		// 判断游客code 是否为空
		AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
		String code = reqVO.getCode();
		if (StringUtils.isEmpty(code)) {
			throw new RuntimeException("游客标志不可为空");
		}
		
		// 注册账户信息
		
		AppAuthRegisterVO appAuthRegisterVO = new AppAuthRegisterVO();
		appAuthRegisterVO.setAccountType(H5.getType());
		appAuthRegisterVO.setCode(code);
		appAuthRegisterVO.setMobile(code);
		Long accountId = appAuthService.register(appAuthRegisterVO);
		SocialUserMessageDTO socialUserMessageDTO = new SocialUserMessageDTO();
		socialUserMessageDTO.setAccountId(accountId);
		socialUserMessageDTO.setOperType(0);
		socialUserMessageDTO.setMobile(code);
		socialUserMessageDTO.setOpenId(code);
		socialUserMessageDTO.setToken(code);
		socialUserMessageDTO.setUserCode(code);
		socialUserMessageDTO.setSocialType(H5.getType().longValue());
		
		// 创建绑定关系
		appAuthService.userBind(socialUserMessageDTO);
		// 登录
		authLoginRespVO = appAuthService.login(accountId,MEMBER.getValue());
		return authLoginRespVO;
	}

	/**
	 * @param token
	 * @param type
	 */
	@Override
	public void logout(String token, Integer type) {
		appAuthService.logout(token, type);
	}
}

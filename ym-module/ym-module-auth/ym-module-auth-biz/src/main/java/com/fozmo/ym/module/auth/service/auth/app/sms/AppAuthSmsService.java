package com.fozmo.ym.module.auth.service.auth.app.sms;

import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthSmsLoginReqVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthSmsSendReqVO;
import jakarta.validation.Valid;

/**
 *
 * 短信登录鉴权接口
 */
public interface AppAuthSmsService {
	/***
	 * @param reqVO
	 * 短信发送接口
	 */
	void sendSmsCode(@Valid AppAuthSmsSendReqVO reqVO);
	/***
	 * 短信登录接口
	 */
    AppAuthLoginRespVO smsLogin(@Valid AppAuthSmsLoginReqVO reqVO);
}

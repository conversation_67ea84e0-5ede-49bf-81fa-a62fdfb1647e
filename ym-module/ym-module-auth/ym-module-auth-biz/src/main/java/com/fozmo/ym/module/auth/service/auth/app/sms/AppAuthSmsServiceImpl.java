package com.fozmo.ym.module.auth.service.auth.app.sms;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.enums.UserTypeEnum;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthLoginRespVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthRegisterVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthSmsLoginReqVO;
import com.fozmo.ym.module.auth.controller.app.auth.vo.AppAuthSmsSendReqVO;
import com.fozmo.ym.module.auth.service.auth.app.AppAuthService;
import com.fozmo.ym.module.sms.SmsCodeApi;
import com.fozmo.ym.module.sms.dto.code.SmsCodeSendReqDTO;
import com.fozmo.ym.module.sms.dto.code.SmsCodeUseReqDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.fozmo.ym.framework.common.util.servlet.ServletUtils.getClientIP;
import static com.fozmo.ym.module.sms.enums.SmsSceneEnum.MEMBER_LOGIN;
import static com.fozmo.ym.module.social.enums.social.SocialPlatformEnum.APP_MOBILE;

@Slf4j
@Service
public class AppAuthSmsServiceImpl implements AppAuthSmsService {

	@Resource
	private AccountApi accountAuthApi;
	
	@Resource
	private AppAuthService appAuthService;

	@Resource
	private SmsCodeApi smsCodeApi;

	@Resource
	private AccountApi accountApi;

	/***
	 * @param reqVO
	 * 短信发送接口
	 */
	@Override
	public void sendSmsCode(AppAuthSmsSendReqVO reqVO) {
		// 情况 1：如果是修改手机场景，需要校验新手机号是否已经注册，说明不能使用该手机了
//		if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene())) {
//			AppAccountInfoRespDTO account = accountAuthApi.getAccountInfoByMobile(reqVO.getMobile());
//			if (account != null) {
//				throw exception(AUTH_MOBILE_USED);
//			}
//		}
       // 情况 2：如果是重置密码场景，需要校验手机号是存在的
//		if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_RESET_PASSWORD.getScene())) {
//			AppAccountInfoRespDTO account = accountAuthApi.getAccountInfoByMobile(reqVO.getMobile());
//			if (account == null) {
//				throw exception(USER_MOBILE_NOT_EXISTS);
//			}
//		}
        // 情况 3：如果是修改密码场景，需要查询手机号，无需前端传递
//		if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_PASSWORD.getScene())) {
//			AppAccountInfoRespDTO account = accountAuthApi.getAccountInfoByMobile(reqVO.getMobile());
//			// TODO 芋艿：后续 member user 手机非强绑定，这块需要做下调整；
//			reqVO.setMobile(account.getMobile());
//		}
        // 执行发送
		SmsCodeSendReqDTO sendReqDTO = new SmsCodeSendReqDTO().setMobile(reqVO.getMobile()).setCreateIp(getClientIP()).setScene(reqVO.getScene());
		smsCodeApi.sendSmsCode(sendReqDTO);
	}
	
	/***
	 * 短信登录接口
	 * @param reqVO
	 */
	@Override
	public AppAuthLoginRespVO smsLogin(AppAuthSmsLoginReqVO reqVO) {
		AppAuthLoginRespVO respVO = new AppAuthLoginRespVO();
		
		// 校验验证码
		SmsCodeUseReqDTO smsCodeUseReqDTO = new SmsCodeUseReqDTO();
		smsCodeUseReqDTO.setMobile(reqVO.getMobile());
		smsCodeUseReqDTO.setCode(reqVO.getCode());
		smsCodeUseReqDTO.setScene(MEMBER_LOGIN.getScene());
		smsCodeUseReqDTO.setUsedIp(getClientIP());
		smsCodeApi.useSmsCode(smsCodeUseReqDTO);
		AppAuthRegisterVO appAuthRegisterVO = new AppAuthRegisterVO();
		appAuthRegisterVO.setAccountType(APP_MOBILE.getType());
		appAuthRegisterVO.setMobile(reqVO.getMobile());
		appAuthRegisterVO.setCode(reqVO.getMobile());
		
		Long accountId = 0L;
		// 1先缓存获取账户Id
		if (accountApi.checkAccountMobile(reqVO.getMobile())){
			accountId= accountApi.getAccountByMobile(reqVO.getMobile());
		}

		// 2 缓存没有 则获取 当前登录信息
		if (ObjectUtil.isEmpty(accountId) || accountId == 0L) {
			accountId = WebFrameworkUtils.getLoginUserId();
		}

		if (ObjectUtil.isNotEmpty(accountId) && accountId != 0L) {
			// 升级用户
			appAuthRegisterVO.setId(accountId);
			appAuthService.updateAccount(appAuthRegisterVO);
			respVO = appAuthService.login(accountId, UserTypeEnum.MEMBER.getValue());
		}else {
			// 1 注册用户
			 accountId = appAuthService.register(appAuthRegisterVO);
			// 2 绑定用户
			SocialUserMessageDTO socialUserMessageDTO = new SocialUserMessageDTO();
			socialUserMessageDTO.setOpenId(reqVO.getMobile());
			socialUserMessageDTO.setAccountId(accountId);
			socialUserMessageDTO.setOperType(0);
			socialUserMessageDTO.setSocialType(APP_MOBILE.getType().longValue());
			socialUserMessageDTO.setMobile(reqVO.getMobile());
			socialUserMessageDTO.setUserCode(reqVO.getMobile());
			socialUserMessageDTO.setToken(reqVO.getMobile());
			appAuthService.userBind(socialUserMessageDTO);
			// 3登录
			respVO = appAuthService.login(accountId, UserTypeEnum.MEMBER.getValue());
		}
		accountApi.saveAccountMobile(accountId,reqVO.getMobile());
		return respVO;
	}
}

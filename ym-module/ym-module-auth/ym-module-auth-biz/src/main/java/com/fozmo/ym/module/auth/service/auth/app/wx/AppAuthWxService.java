package com.fozmo.ym.module.auth.service.auth.app.wx;

import com.fozmo.ym.module.auth.controller.app.auth.vo.*;
import jakarta.validation.Valid;

public interface AppAuthWxService {
	/**
	 * 获取扫码结果
	 */
	AppAuthLoginRespVO getScene(String scene);
	
	/**
	 * 微信补全手机号
	 */
	AppAuthLoginRespVO appMobile(@Valid AppMobileLoginReqVO reqVO);

	/**
	 * 微信登录
	 */
	AppAuthLoginRespVO weixinMiniAppLogin(@Valid AppAuthWxMiniAppLoginReqVO reqVO);
	
	
	/**
	 * 微信印记
	 */
	BrandRespVO miniappBrand(@Valid AppAuthWxMiniAppLoginReqVO reqVO);

	/***
	 * 获取 微信二维码
	 */
	AppQRCodeRespVO getQRCode();
}

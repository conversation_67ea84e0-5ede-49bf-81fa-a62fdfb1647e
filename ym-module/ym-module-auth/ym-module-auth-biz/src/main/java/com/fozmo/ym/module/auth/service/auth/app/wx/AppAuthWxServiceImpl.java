package com.fozmo.ym.module.auth.service.auth.app.wx;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fozmo.ym.framework.common.enums.UserTypeEnum;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.auth.controller.app.auth.vo.*;
import com.fozmo.ym.module.auth.service.auth.app.AppAuthService;
import com.fozmo.ym.module.auth.util.oauth2.LoginRespDto;
import com.fozmo.ym.module.auth.util.oauth2.WechatAppletUtil;
import com.fozmo.ym.module.social.api.social.SocialApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.dal.redis.RedisKeyConstants.WX_LOGIN_SCENE;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.SOCIAL_USER_AUTH_FAILURE;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.SOCIAL_USER_PHONE_CODE_ERROR;
import static com.fozmo.ym.module.social.enums.social.SocialPlatformEnum.YM_APP;

@Service
@Slf4j
public class AppAuthWxServiceImpl implements AppAuthWxService {

	@Resource
	private RedisTemplate redisTemplate;
	
	@Resource
	private WechatAppletUtil wechatAppletUtil;
	
	@Resource
	private AppAuthService appAuthService;
	
	@Resource
	private AccountApi accountApi;

    @Resource
    private SocialApi socialApi;
	/**
	 * 获取扫码结果
	 *
	 * @param scene
	 */
	@Override
	public AppAuthLoginRespVO getScene(String scene) {
		String key = WX_LOGIN_SCENE+scene;
		AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
		if (redisTemplate.hasKey(key)) {
			Map<String, Object> map = redisTemplate.opsForHash().entries(key);
			Integer status = 0;
			if(map.containsKey("status")){
				status= (Integer)map.get("status");
			}
			
			if (map.containsKey("token") && status==1){
				String token = (String)map.get("token");
				authLoginRespVO= JSONUtil.toBean(token,AppAuthLoginRespVO.class);
			}
			return authLoginRespVO;
		}else {
			throw new RuntimeException("已失效");
		}
	}
	
	/**
	 * 微信补全手机号
	 *
	 * @param reqVO
	 */
	@Override
	public AppAuthLoginRespVO appMobile(AppMobileLoginReqVO reqVO) {
		AppAuthLoginRespVO respVO = new AppAuthLoginRespVO();
		Boolean updateMobile = Boolean.TRUE;
		String openId = reqVO.getOpenid();
        String scene = reqVO.getScene();
		String code = reqVO.getCode();
		
		// 第一步 获取微信手机号
        String mobile = wechatAppletUtil.appletMobile(code);
        if (StringUtils.isEmpty(mobile)) {
            throw exception(SOCIAL_USER_PHONE_CODE_ERROR);
        }
		// 获取当前用户Id
		Long accountId = 0L;
		if (ObjectUtil.isEmpty(accountId) || accountId == 0L) {
			accountId=WebFrameworkUtils.getLoginUserId();
		}
		// 更新注册信息回填
		AppAuthRegisterVO appAuthRegisterVO = new AppAuthRegisterVO();
		appAuthRegisterVO.setCode(openId);
		appAuthRegisterVO.setMobile(mobile);
		appAuthRegisterVO.setAccountType(YM_APP.getType());
		if (ObjectUtil.isEmpty(accountId) || accountId == 0L) {
			// 注册用户
			accountId = appAuthService.register(appAuthRegisterVO);
			
		}
			appAuthRegisterVO.setId(accountId);
			respVO = appAuthService.login(accountId,UserTypeEnum.MEMBER.getValue());
		    appAuthService.updateAccount(appAuthRegisterVO);
			accountApi.saveAccountMobile(accountId,mobile);
			accountApi.saveAccountOpenId(accountId,openId);
			respVO.setOpenid(openId);
			respVO.setUpdateMobile(Boolean.FALSE);
	        return respVO;
	}
	
	/**
	 * 微信登录
	 *
	 * @param reqVO
	 */
	@Override
	public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWxMiniAppLoginReqVO reqVO) {
		
		AppAuthLoginRespVO respVO = new AppAuthLoginRespVO();
		Boolean updateMobile = Boolean.TRUE;
        // 1、执行小程序登录
        LoginRespDto loginRespDto = wechatAppletUtil.appletLogin(reqVO.getLoginCode());
        if (ObjectUtil.isNull(loginRespDto)) {
            throw exception(SOCIAL_USER_AUTH_FAILURE,"小程授权失败");
        }
        String openid = loginRespDto.getOpenid();
        String unionid = loginRespDto.getUnionid();
		Long accountId = 0L;
		// 1先缓存获取账户Id
		respVO.setOpenid(openid);
		respVO.setUpdateMobile(updateMobile);
		if (!accountApi.checkAccountOpenId(openid)){
			return respVO;
		}else {
			accountId= accountApi.getAccountByOpenId(openid);
			if (ObjectUtil.isEmpty(accountId) || accountId == 0L) {
				return respVO;
			}else {
				respVO = appAuthService.login(accountId, UserTypeEnum.MEMBER.getValue());
				AppAuthRegisterVO appAuthRegisterVO = new AppAuthRegisterVO();
				appAuthRegisterVO.setId(accountId);
				appAuthService.updateAccount(appAuthRegisterVO);
				respVO.setUpdateMobile(Boolean.FALSE);
				respVO.setOpenid(openid);
				return respVO;
			}
		}

	}
	
	/**
	 * 微信印记
	 *
	 * @param reqVO
	 */
	@Override
	public BrandRespVO miniappBrand(AppAuthWxMiniAppLoginReqVO reqVO) {
		LoginRespDto loginRespDto = wechatAppletUtil.appletLogin(reqVO.getLoginCode());
        return BeanUtils.toBean(loginRespDto, BrandRespVO.class);

	}
	
	/***
	 * 获取 微信二维码
	 */
	@Override
	public AppQRCodeRespVO getQRCode() {
		String scene = UUID.randomUUID().toString().replace("-", "");
        String env = "develop";
        String qrResult = wechatAppletUtil.getAppletQrcodeStr(null,scene,env);
        AppAuthLoginRespVO authLoginRespVO = new AppAuthLoginRespVO();
        authLoginRespVO.setScene(scene);
        Map map = new HashMap();
        map.put("scene",scene);
        map.put("env",env);
        map.put("status",0);
        map.put("token", JSONUtil.toJsonStr(authLoginRespVO));
        String key = WX_LOGIN_SCENE+scene;
        redisTemplate.opsForValue().set(key,map,5, TimeUnit.MINUTES);
        return new AppQRCodeRespVO(scene,env,qrResult);
	}
}

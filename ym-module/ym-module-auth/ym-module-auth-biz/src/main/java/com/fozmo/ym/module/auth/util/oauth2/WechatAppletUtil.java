package com.fozmo.ym.module.auth.util.oauth2;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fozmo.ym.framework.common.exception.ServerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * User: AIR
 * Datetime: 2024/10/18 下午5:12
 * Description: Null
 */

@Slf4j
@Component
public class WechatAppletUtil {

//    @Value("${wechat.applet.id}")
private final String id = "wx01dfa7651fd3908a";

//    @Value("${wechat.applet.keys}")
private final String keys = "536ab9524b196c51c68901cbbefdf31b";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    /**
     * 小程序登录
     * @param code
     * @return
     */
    public LoginRespDto appletLogin(String code) {
        String url = StrUtil.format("https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code", id, keys, code);

        String resultStr = HttpUtil.get(url);
        JSONObject resultObj = JSONUtil.parseObj(resultStr);
        log.info("微信登录-小程序返回：{}",resultObj);
        if (resultObj.containsKey("errcode")) {
            log.error("WechatAppletUtil.appletLogin 错误, 参数: {}, 响应: {}", url, resultStr);
            throw new RuntimeException("WechatAppletUtil.appletLogin <UNK>, <UNK>: " + resultStr);
        }
        LoginRespDto loginRespDto = new LoginRespDto();
        loginRespDto.setOpenid( resultObj.getStr("openid"));
        loginRespDto.setUnionid(resultObj.getStr("unionid"));
        loginRespDto.setSession_key(resultObj.getStr("session_key"));
        log.info("微信登录-解析返回：{}",resultObj);
        return loginRespDto;
    }

    /**
     * 小程序获取用户手机号
     * @param code
     * @return
     */
    public String appletMobile(String code) {
        String url = StrUtil.format("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={}", getAccessToken());

        String resultStr = HttpUtil.post(url, JSONUtil.createObj().set("code", code).toString());
        JSONObject resultObj = JSONUtil.parseObj(resultStr);

        if (resultObj.containsKey("errcode") && !"0".equals(resultObj.getStr("errcode"))) {
            log.error("WechatAppletUtil.appletMobile 错误, 参数: {}, 响应: {}", url + ", code: " + code, resultStr);
            throw new RuntimeException("WechatAppletUtil.appletMobile <UNK>, <UNK>: " + resultStr);
        }

        return resultObj.getByPath("phone_info.purePhoneNumber", String.class);
    }

    /**
     * 获取小程序二维码
     * @param page
     * @param scene
     * @param env   正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版
     * @return
     */
    public InputStream getAppletQrcode(String page, String scene, String env) {
        if (StrUtil.isBlank(env)) {
            env = "release";
        }
        String url = StrUtil.format("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={}", getAccessToken());
        String param = JSONUtil.createObj().set("page", page).set("scene", scene).set("env_version", env).set("check_path",false).toString();
        log.info("获取小程序二维码-url:{},参数：{}",url,param);
        HttpResponse httpResponse =  HttpUtil.createPost(url).body(param).execute();
        log.info("获取小程序二维码返回");
        if (httpResponse.isOk()){
            return httpResponse.bodyStream();
        }else {
            throw new ServerException(httpResponse.getStatus(),httpResponse.body());
        }
    }

    /**
     * 获取小程序二维码
     * @param page
     * @param scene
     * @param env   正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版
     * @return
     */
    public String getAppletQrcodeStr(String page, String scene, String env) {
        InputStream appletQrcode = getAppletQrcode(page, scene, env);
        byte[] bytes = IoUtil.readBytes(appletQrcode);
        return Base64.encode(bytes);
    }

    /**
     * 获取 accessToken 接口调用凭据
     * @return
     */
    public String getAccessToken() {
        String cacheKey ="ym:applet:accessToken";

        Object cache = redisTemplate.opsForValue().get(cacheKey);
        if (cache != null) {
            return (String) cache;
        }

        String url = "https://api.weixin.qq.com/cgi-bin/stable_token";
        String resultStr = HttpUtil.post(url, JSONUtil.createObj().set("grant_type", "client_credential").set("appid", id).set("secret", keys).set("force_refresh", false).toString());
        JSONObject resultObj = JSONUtil.parseObj(resultStr);

        if (resultObj.containsKey("errcode")) {
            log.error("WechatAppletUtil.getAccessToken 错误, 参数: {}, 响应: {}", url, resultStr);
            throw new RuntimeException("WechatAppletUtil.getAccessToken 错误");
        }

        String accessToken = resultObj.getStr("access_token");
        redisTemplate.opsForValue().set(cacheKey, accessToken, resultObj.getInt("expires_in"), TimeUnit.SECONDS);

        return accessToken;
    }

}

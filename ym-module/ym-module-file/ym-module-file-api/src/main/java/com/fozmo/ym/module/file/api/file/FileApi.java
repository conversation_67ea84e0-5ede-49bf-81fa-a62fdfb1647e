package com.fozmo.ym.module.file.api.file;

import com.fozmo.ym.module.file.api.file.dto.FilePresignedFileDto;
import com.fozmo.ym.module.file.api.file.dto.FilePresignedUrlDto;

/**
 * 文件 API 接口
 *
 * <AUTHOR>
 */
public interface FileApi {

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content);


    /**
     * 创建预签名文件，并返回
     *
     * @param path 文件路径
     * @return 上传参数
     */

    FilePresignedUrlDto createFilePresignedUrl(String path);

    /**
     *
     * 上传文件成功之后，创建文件记录
     * @
     *
     */
    Long addFile(FilePresignedFileDto filePresignedFileDto);



}

package com.fozmo.ym.module.file.enums;


import com.fozmo.ym.framework.common.exception.ErrorCode;

/**
 * Infra 错误码枚举类
 *
 * infra 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {
    // ========= 文件相关 1-001-003-000 =================
    ErrorCode FILE_PATH_EXISTS = new ErrorCode(210001, "文件路径已存在");
    ErrorCode FILE_NOT_EXISTS = new ErrorCode(210002, "文件不存在");
    ErrorCode FILE_IS_EMPTY = new ErrorCode(210003, "文件为空");
    // ========== 文件配置 1-001-006-000 ==========
    ErrorCode FILE_CONFIG_NOT_EXISTS = new ErrorCode(210004, "文件配置不存在");
    ErrorCode FILE_CONFIG_DELETE_FAIL_MASTER = new ErrorCode(210005, "该文件配置不允许删除，原因：它是主配置，删除会导致无法上传文件");

}

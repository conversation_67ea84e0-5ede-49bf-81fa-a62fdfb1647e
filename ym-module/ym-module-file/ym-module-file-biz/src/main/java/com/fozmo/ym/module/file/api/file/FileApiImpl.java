package com.fozmo.ym.module.file.api.file;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.file.api.file.dto.FilePresignedFileDto;
import com.fozmo.ym.module.file.api.file.dto.FilePresignedUrlDto;
import com.fozmo.ym.module.file.controller.admin.file.vo.file.FileCreateReqVO;
import com.fozmo.ym.module.file.service.file.FileService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;

    @Override
    public String createFile(String name, String path, byte[] content) {
        return fileService.createFile(name, path, content);
    }

    /**
     */
    @Override
    public FilePresignedUrlDto createFilePresignedUrl(String path) {
        try {
            Map map = Map.of(path, path);
            return BeanUtils.toBean(fileService.getFilePresignedUrl(map),FilePresignedUrlDto.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传文件成功之后，创建文件记录
     *
     * @param filePresignedFileDto
     * @
     */
    @Override
    public Long addFile(FilePresignedFileDto filePresignedFileDto) {

        return fileService.createFile(BeanUtils.toBean(filePresignedFileDto, FileCreateReqVO.class));
    }


}

package com.fozmo.ym.module.file.controller.admin.file;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.file.service.file.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "后管-文件模块-文件上传回调")
@RestController
@RequestMapping("/file/call")
//@Validated
@Slf4j
public class FileCallBackController {

    @Resource
    private FileService fileService;


    @PostMapping(value = "/callBack",consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    @Operation(summary = "oss文件上传回调", description = "oss文件上传回调")
    @PermitAll
    @ApiAccessLog
    public CommonResult callback(@RequestParam Map<String, String> params) {
        return success(fileService.callback(params));
    }
}

package com.fozmo.ym.module.file.controller.admin.file.vo.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "管理后台 - 文件预签名地址 Response VO")
@Accessors(chain = true)
@Data
public class FilePresignedUrlRespVO {

    /**
     * 文件上传 URL（用于上传）
     * <p>
     * 例如说：
     */
    private String uploadUrl;

    /**
     * 文件 URL（用于读取、下载等）
     */
    private String url;
    /**
     *  accessKey
     */
    private String accessKey;

    private String policy;

    private String signature;

    private String expires;

    private String callBack;

    private Integer status;

    private String key;

    private Long configId;

    private Long fileId;

}

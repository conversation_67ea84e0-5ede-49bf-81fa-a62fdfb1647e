package com.fozmo.ym.module.file.controller.app.file;

import cn.hutool.core.io.IoUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.file.controller.admin.file.vo.file.FileCreateReqVO;
import com.fozmo.ym.module.file.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import com.fozmo.ym.module.file.controller.app.file.vo.AppFileUploadReqVO;
import com.fozmo.ym.module.file.service.file.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-文件模块-文件")
@RestController
@RequestMapping("/file")
@Validated
@Slf4j
public class AppFileController {

    @Resource
    private FileService fileService;

    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    @PermitAll
    @ApiAccessLog
    public CommonResult<String> uploadFile(AppFileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
        String path = uploadReqVO.getPath();
        return success(fileService.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

    @GetMapping("/presigned")
    @Operation(summary = "通用预签名地址", description = "模式二：前端上传文件：用于前端直接上传七牛、阿里云 OSS 等文件存储器")
    @PermitAll
    @ApiAccessLog
    public CommonResult<FilePresignedUrlRespVO> getFilePresignedUrl(@RequestParam("path") String path) throws Exception {
        Map<String, Object> map =Map.of(path,path);
        return success(fileService.getFilePresignedUrl(map));
    }

    @GetMapping("/uploadUrl")
    @Operation(summary = "web表单上传文件", description = "web表单上传文件")
    @PermitAll
    @ApiAccessLog
    public CommonResult<FilePresignedUrlRespVO> uploadUrl() throws Exception {
        return success(fileService.uploadUrl());
    }

    @PostMapping("/create")
    @Operation(summary = "创建文件", description = "模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> createFile(@Valid @RequestBody FileCreateReqVO createReqVO) {
        return success(fileService.createFile(createReqVO));
    }
}

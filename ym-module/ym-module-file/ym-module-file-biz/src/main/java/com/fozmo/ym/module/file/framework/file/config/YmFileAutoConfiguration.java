package com.fozmo.ym.module.file.framework.file.config;

import com.fozmo.ym.module.file.framework.file.core.client.FileClientFactory;
import com.fozmo.ym.module.file.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class YmFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}

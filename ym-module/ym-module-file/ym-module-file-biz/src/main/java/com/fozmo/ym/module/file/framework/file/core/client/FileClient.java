package com.fozmo.ym.module.file.framework.file.core.client;

import com.fozmo.ym.module.file.framework.file.core.client.s3.FilePresignedUrlRespDTO;

import java.util.Map;

/**
 * 文件客户端
 *
 * <AUTHOR>
 */
public interface FileClient {

    /**
     * 获得客户端编号
     *
     * @return 客户端编号
     */
    Long getId();

    /**
     * 上传文件
     *
     * @param content 文件流
     * @param path    相对路径
     * @return 完整路径，即 HTTP 访问地址
     * @throws Exception 上传文件时，抛出 Exception 异常
     */
    String upload(byte[] content, String path, String type) throws Exception;

    /**
     * 删除文件
     *
     * @param path 相对路径
     * @throws Exception 删除文件时，抛出 Exception 异常
     */
    void delete(String path) throws Exception;

    /**
     * 获得文件的内容
     *
     * @param path 相对路径
     * @return 文件的内容
     */
    byte[] getContent(String path) throws Exception;
    
    /**
     * 获取文件元数据
     */
    String getFileMedia(String type,String url,String bucketName) throws Exception;

    /**
     * 获得文件预签名地址
     *
     * @return 文件预签名地址
     */
    default FilePresignedUrlRespDTO getPresignedObjectUrl(Map<String,Object> maps) throws Exception {
        throw new UnsupportedOperationException("不支持的操作");
    }

}

package com.fozmo.ym.module.file.framework.file.core.client.oss;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.common.utils.DateUtil;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PolicyConditions;
import com.fozmo.ym.module.file.framework.file.core.client.AbstractFileClient;
import com.fozmo.ym.module.file.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

@Slf4j
public class FileOssClient extends AbstractFileClient<FileOssClientConfig> {

    private OSS ossClient;

    public FileOssClient(Long id, FileOssClientConfig config) {
        super(id, config);
    }

    /**
     * 自定义初始化
     */
    @Override
    protected void doInit() {
        if (StrUtil.isEmpty(config.getDomain())){
            config.setDomain(buildDomain());
        }

        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setProtocol(Protocol.HTTPS);
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
         ossClient = OSSClientBuilder.create()
                .endpoint(config.getEndpoint())
                .credentialsProvider(new DefaultCredentialProvider(config.getAccessKey(), config.getAccessSecret()))
                .clientConfiguration(clientBuilderConfiguration)
                .region("cn-hangzhou")
                .build();
    }
    /**
     * 删除文件
     *
     * @param path 相对路径
     * @throws Exception 删除文件时，抛出 Exception 异常
     */
    @Override
    public void delete(String path) throws Exception {

    }

    /**
     * 获得文件的内容
     *
     * @param path 相对路径
     * @return 文件的内容
     */
    @Override
    public byte[] getContent(String path) throws Exception {
        return new byte[0];
    }

/**
 * 获取文件元数据
 *
 */
@Override
public String getFileMedia(String type, String url, String bucketName) throws Exception {
    log.info("获取文件元数据请求数据-文件类型：{},文件：{},桶名{}", type, url, bucketName);
    String result = "";
    if ("image".equals(type)) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, url);
        getObjectRequest.setProcess("image/info");
        
        // 使用getObject方法，并通过process参数传入处理指令。
        OSSObject ossObject = ossClient.getObject(getObjectRequest);
        
        // 读取并打印信息。
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = ossObject.getObjectContent().read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        result = baos.toString(StandardCharsets.UTF_8);
    }

    if ("video".equals(type)) {
        // 构建视频信息提取的处理指令。
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, url);
        getObjectRequest.setProcess("video/info");
        
        // 使用getObject方法，并通过process参数传入处理指令。
        OSSObject ossObject = ossClient.getObject(getObjectRequest);
        
        // 读取并打印视频信息。
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = ossObject.getObjectContent().read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        result = baos.toString(StandardCharsets.UTF_8);
    }
    
    log.info("获取文件元数据返回--返回结果：{}",result);
    return result;
}

/**
     * 获得文件预签名地址
     *
     * @param maps 参数
     * @return 文件预签名地址
     */
    @Override
    public FilePresignedUrlRespDTO getPresignedObjectUrl(Map<String,Object> maps) throws Exception {

        FilePresignedUrlRespDTO urlRespDTO = new FilePresignedUrlRespDTO();
        // 以华东1（杭州）的外网Endpoint为例，其它Region请按实际情况填写。
        String endpoint = config.getEndpoint();
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        // 填写Bucket名称，例如examplebucket。
        String bucketName = config.getBucket();
        // 填写Object完整路径，例如exampleobject.txt。Object完整路径中不能包含Bucket名称。
        PolicyConditions policyConds = new PolicyConditions();
        policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
        Date expireTime = new Date(System.currentTimeMillis() + 3600 * 1000);
        String postPolicy = ossClient.generatePostPolicy(expireTime, policyConds);
        String signature = ossClient.calculatePostSignature(postPolicy);
        String policy = BinaryUtil.toBase64String(postPolicy.getBytes(StandardCharsets.UTF_8));

        JSONObject callbackJson = new JSONObject()
                .set("callbackUrl", maps.get("callbackUrl"))
                .set("callbackBody", "bucket=${bucket}&object=${object}")
                .set("callbackBodyType", "application/x-www-form-urlencoded");
        String callbackStr = callbackJson.toString();
        String callbackBase64Str = BinaryUtil.toBase64String(callbackStr.getBytes());

        urlRespDTO.setUrl(config.getDomain());
        urlRespDTO.setKey(maps.get("allName").toString());
        urlRespDTO.setAccessKey(config.getAccessKey());
        urlRespDTO.setSignature(signature);
        urlRespDTO.setStatus(200);
        urlRespDTO.setPolicy(policy);
        urlRespDTO.setExpires(DateUtil.formatIso8601Date(expireTime));
        urlRespDTO.setCallBack(callbackBase64Str);
        return urlRespDTO;
    }


    /**
     * 基于 bucket + endpoint 构建访问的 Domain 地址
     *
     * @return Domain 地址
     */
    private String buildDomain() {
        // 如果已经是 http 或者 https，则不进行拼接.主要适配 MinIO
        if (HttpUtil.isHttp(config.getEndpoint()) || HttpUtil.isHttps(config.getEndpoint())) {
            return StrUtil.format("{}/{}", config.getEndpoint(), config.getBucket());
        }
        // 阿里云、腾讯云、华为云都适合。七牛云比较特殊，必须有自定义域名
        return StrUtil.format("https://{}.{}", config.getBucket(), config.getEndpoint());
    }


    /**
     * 上传文件
     *
     * @param content 文件流
     * @param path    相对路径
     * @return 完整路径，即 HTTP 访问地址
     * @throws Exception 上传文件时，抛出 Exception 异常
     */
    @Override
    public String upload(byte[] content, String path, String type) throws Exception {
        return "";
    }
//    /**
//     *  小程序临时凭证上传文件
//     */
//    @Override
//    public FilePresignedUrlRespDTO getUploadObjectUrl(Map<String,Object> maps) throws Exception {
//        String regionId = "cn-hangzhou";
//        String accessKeyId = config.getAccessKey();
//        String accessKeySecret = config.getAccessSecret();
//
//        Long durationSeconds = 3600L;   //临时访问凭证的有效时间
//
//        // 初始化客户端
//        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
//        IAcsClient iAcsClient = new DefaultAcsClient(profile);
//        AssumeRoleRequest request = new AssumeRoleRequest();
//        request.setRoleArn(roleArnForOssUpload);
//        request.setRoleSessionName(roleSessionName);
//        request.setDurationSeconds(durationSeconds);
//    }
}

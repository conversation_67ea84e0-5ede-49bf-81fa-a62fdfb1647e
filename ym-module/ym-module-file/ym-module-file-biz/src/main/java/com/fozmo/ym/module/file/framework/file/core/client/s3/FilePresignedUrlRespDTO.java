package com.fozmo.ym.module.file.framework.file.core.client.s3;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 文件预签名地址 Response DTO
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FilePresignedUrlRespDTO {

    /**
     * 文件上传 URL（用于上传）
     * <p>
     * 例如说：
     */
    private String uploadUrl;

    /**
     * 文件 URL（用于读取、下载等）
     */
    private String url;
    /**
     *  accessKey
     */
    private String accessKey;

    private String policy;

    private String signature;

    private String expires;

    private String callBack;

    private Integer status;

    private String key;

    private Long configId;

    private Long fileId;



}

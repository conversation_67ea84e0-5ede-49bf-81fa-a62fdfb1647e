package com.fozmo.ym.module.file.service.file;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.date.DateUtils;
import com.fozmo.ym.framework.common.util.io.FileUtils;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.file.controller.admin.file.vo.file.FileCreateReqVO;
import com.fozmo.ym.module.file.controller.admin.file.vo.file.FilePageReqVO;
import com.fozmo.ym.module.file.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import com.fozmo.ym.module.file.dal.dataobject.file.FileDO;
import com.fozmo.ym.module.file.dal.mysql.file.FileMapper;
import com.fozmo.ym.module.file.framework.file.core.client.FileClient;
import com.fozmo.ym.module.file.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import com.fozmo.ym.module.file.framework.file.core.utils.FileTypeUtils;
import com.fozmo.ym.module.tenant.api.tenant.enums.TenantEnum;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.file.enums.ErrorCodeConstants.FILE_NOT_EXISTS;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {
    
    @Value("${ym.file.callbackUrl}")
    private String callBackUrl;
    
    @Value("${ym.file.ossUrl}")
    private String ossUrl;

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @Override
    public Long createFile(FileCreateReqVO createReqVO) {
        FileDO file = BeanUtils.toBean(createReqVO, FileDO.class);
        fileMapper.insert(file);
        return file.getId();
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client.getContent(path);
    }

    @Override
    public FilePresignedUrlRespVO getFilePresignedUrl(Map map) throws Exception {
        FileClient fileClient = fileConfigService.getMasterFileClient();
        FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(map);
        return BeanUtils.toBean(presignedObjectUrl, FilePresignedUrlRespVO.class,
                object -> object.setConfigId(fileClient.getId()));
    }

    /**
     * 表单上传文件 接口
     *
     */
    @Override
    public FilePresignedUrlRespVO uploadUrl() {

        FileClient fileClient = fileConfigService.getMasterFileClient();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            throw new RuntimeException("用户未登录");
        }
        Integer tenantId = Math.toIntExact(loginUser.getTenantId());
        String dateTime = DateUtils.getYearMonthDay();
        String path = buildFilePath(TenantEnum.valueOfType(tenantId).getCode(), loginUser.getId(), dateTime);
        Map<String, Object> params = new HashMap<>();

        params.put("dateTime", dateTime);
        params.put("path", path);
        params.put("allName", path);
        params.put("callbackUrl",callBackUrl );

        try {
            FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(params);
            System.out.println(presignedObjectUrl);
            return BeanUtils.toBean(presignedObjectUrl,FilePresignedUrlRespVO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     */
    @Override
    public Map callback(Map<String, String> params) {

        Map map = new HashMap();
        if (ObjectUtil.isNotEmpty(params)) {
            
            String key = params.get("object");
            String bucket = params.get("bucket");
            FileClient client = fileConfigService.getMasterFileClient();
            if (params.containsKey("object")) {
                String url =ossUrl+"/"+params.get("object");
                map.put("url",url);
                map.put("height",-1);
                map.put("width",-1);
               
                List<String> imageSuffix = Arrays.asList(".png", ".jpg", ".jpeg", ".bmp", ".gif", ".webp", ".tiff", ".heic");
                List<String> videoSuffix =  Arrays.asList(".mp4", ".avi", ".mkv", ".mpeg",".mov");
                if (StrUtil.isNotEmpty(key) && imageSuffix.stream().anyMatch(key::endsWith)) {
	                try {
		                String imageResult = client.getFileMedia("image",key,bucket);
                        if (StrUtil.isNotEmpty(imageResult)) {
                            JSONObject jsonObject = JSON.parseObject(imageResult);
                            map.put("info",jsonObject);
                            map.put("url",url);
                            
                            if (jsonObject.containsKey("ImageHeight")) {
                                map.put("height",jsonObject.getJSONObject("ImageHeight").get("value"));
                            }
                           if (jsonObject.containsKey("ImageWidth")) {
                               map.put("width",jsonObject.getJSONObject("ImageWidth").get("value"));
                           }
                           if (jsonObject.containsKey("FileSize")) {
                               map.put("size",jsonObject.getJSONObject("FileSize").get("value"));
                           }
                           
                           if (jsonObject.containsKey("Format")) {
                               map.put("type",jsonObject.getJSONObject("Format").get("value"));
                           }
                        }
	                } catch (Exception e) {
		                throw new RuntimeException(e);
	                }
                }
                
                if (StrUtil.isNotEmpty(url) && videoSuffix.stream().anyMatch(url::endsWith)) {
	                try {
		                String videoResult = client.getFileMedia("video",key,bucket);
                        if (StrUtil.isNotEmpty(videoResult)) {
                            JSONObject jsonObject = JSON.parseObject(videoResult);
                            if (jsonObject.containsKey("VideoWidth")){
                                map.put("width",jsonObject.getInteger("VideoWidth"));
                            }
                            
                            if (jsonObject.containsKey("VideoHeight")){
                                map.put("height",jsonObject.getInteger("VideoHeight"));
                            }
                           
                            if (jsonObject.containsKey("Duration")){
                                map.put("duration",jsonObject.getBigDecimal("Duration"));
                            }
                            if (jsonObject.containsKey("Bitrate")){
                                map.put("bitrate",jsonObject.getInteger("Bitrate"));
                            }
                            map.put("info",jsonObject);
                            map.put("url",url);
                        }
	                } catch (Exception e) {
		                throw new RuntimeException(e);
	                }
                }
                
                
                
                
               

            }
        }
        return map;
    }

/**
 */
@Override
public Map getVideoInfo() {
    FileClient client = fileConfigService.getMasterFileClient();
    
    return Map.of();
}


/**
     * 构建文件路径
     */
    private String buildFilePath(String tenantCode, Long userId, String date) {
        return tenantCode + "/" + userId + "/" + date + "/";
    }
}

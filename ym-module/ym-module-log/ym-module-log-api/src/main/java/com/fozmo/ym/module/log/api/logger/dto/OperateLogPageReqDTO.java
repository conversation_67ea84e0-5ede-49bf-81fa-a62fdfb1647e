package com.fozmo.ym.module.log.api.logger.dto;

import com.fozmo.ym.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 操作日志分页 Request DTO
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class OperateLogPageReqDTO extends PageParam {

    /**
     * 模块类型
     */
    private String type;
    /**
     * 模块数据编号
     */
    private Long bizId;

    /**
     * 用户编号
     */
    private Long userId;

}

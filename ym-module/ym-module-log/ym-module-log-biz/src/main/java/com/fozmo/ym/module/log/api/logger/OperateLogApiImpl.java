package com.fozmo.ym.module.log.api.logger;

import com.fhs.core.trans.anno.TransMethodResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.log.api.logger.dto.OperateLogCreateReqDTO;
import com.fozmo.ym.module.log.api.logger.dto.OperateLogPageReqDTO;
import com.fozmo.ym.module.log.api.logger.dto.OperateLogRespDTO;
import com.fozmo.ym.module.log.dal.dataobject.logger.OperateLogDO;
import com.fozmo.ym.module.log.service.logger.OperateLogService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 操作日志 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OperateLogApiImpl implements OperateLogApi {

    @Resource
    private OperateLogService operateLogService;

    @Override
    public void createOperateLog(OperateLogCreateReqDTO createReqDTO) {
        operateLogService.createOperateLog(createReqDTO);
    }

    @Override
    @TransMethodResult
    public PageResult<OperateLogRespDTO> getOperateLogPage(OperateLogPageReqDTO pageReqDTO) {
        PageResult<OperateLogDO> operateLogPage = operateLogService.getOperateLogPage(pageReqDTO);
        return BeanUtils.toBean(operateLogPage, OperateLogRespDTO.class);
    }

}

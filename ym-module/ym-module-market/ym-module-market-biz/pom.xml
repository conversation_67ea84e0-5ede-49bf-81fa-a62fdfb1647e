<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-module-market</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>ym-module-market-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 公共模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-common</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-market-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-excel</artifactId>
        </dependency>

    </dependencies>

</project>
package com.fozmo.ym.module.market.controller.admin.banner;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageParam;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerPageReqVO;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerRespVO;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerSaveReqVO;
import com.fozmo.ym.module.market.dal.dataobject.banner.BannerDO;
import com.fozmo.ym.module.market.service.banner.BannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 营销banner")
@RestController
@RequestMapping("/market/banner")
@Validated
public class BannerController {

    @Resource
    private BannerService bannerService;

    @PostMapping("/create")
    @Operation(summary = "创建营销banner")
    @PreAuthorize("@ss.hasPermission('market:banner:create')")
    public CommonResult<Long> createBanner(@Valid @RequestBody BannerSaveReqVO createReqVO) {
        return success(bannerService.createBanner(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新营销banner")
    @PreAuthorize("@ss.hasPermission('market:banner:update')")
    public CommonResult<Boolean> updateBanner(@Valid @RequestBody BannerSaveReqVO updateReqVO) {
        bannerService.updateBanner(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除营销banner")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('market:banner:delete')")
    public CommonResult<Boolean> deleteBanner(@RequestParam("id") Long id) {
        bannerService.deleteBanner(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得营销banner")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('market:banner:query')")
    public CommonResult<BannerRespVO> getBanner(@RequestParam("id") Long id) {
        BannerDO banner = bannerService.getBanner(id);
        return success(BeanUtils.toBean(banner, BannerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得营销banner分页")
    @PreAuthorize("@ss.hasPermission('market:banner:query')")
    public CommonResult<PageResult<BannerRespVO>> getBannerPage(@Valid BannerPageReqVO pageReqVO) {
        PageResult<BannerDO> pageResult = bannerService.getBannerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BannerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出营销banner Excel")
    @PreAuthorize("@ss.hasPermission('market:banner:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBannerExcel(@Valid BannerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BannerDO> list = bannerService.getBannerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "营销banner.xls", "数据", BannerRespVO.class,
                        BeanUtils.toBean(list, BannerRespVO.class));
    }

}
package com.fozmo.ym.module.market.controller.admin.banner.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 营销banner分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BannerPageReqVO extends PageParam {

    @Schema(description = "名称", example = "王五")
    private String name;

    @Schema(description = "文件路径", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "跳转链接")
    private String jumpLink;

    @Schema(description = "应用类型：pc H5页 、miniapp 微信小程序、 system后管系统", example = "2")
    private String appType;

    @Schema(description = "备注说明", example = "你说的对")
    private String description;

    @Schema(description = "none 无跳转、URL 外链 page 内部  space 空间  works 作品、template 模版 ", example = "2")
    private String jumpType;

    @Schema(description = "跳转目标（none 不处理、URL值、page也路径、spaceId、worksId等）")
    private String jumpValue;

    @Schema(description = "0 启用 1 禁用", example = "1")
    private Integer status;

    @Schema(description = "展示位置（home 首页、space 热门空间、works 热门作品、template 热门模版、market 营销活动）")
    private String position;

    @Schema(description = "权重 越大越靠前")
    private Integer weight;

    @Schema(description = "创建人id", example = "22021")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "18137")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;


    private LocalDateTime currentTime;

}
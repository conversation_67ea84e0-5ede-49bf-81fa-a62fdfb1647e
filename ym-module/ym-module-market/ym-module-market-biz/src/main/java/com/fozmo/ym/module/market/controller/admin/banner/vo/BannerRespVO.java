package com.fozmo.ym.module.market.controller.admin.banner.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 营销banner Response VO")
@Accessors(chain = true)
@Data
public class BannerRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27129")
    private Long id;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String name;

    @Schema(description = "文件路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "跳转链接")
    private String jumpLink;

    @Schema(description = "应用类型：pc H5页 、miniapp 微信小程序、 system后管系统", example = "2")
    private String appType;

    @Schema(description = "备注说明", example = "你说的对")
    private String description;

    @Schema(description = "none 无跳转、URL 外链 page 内部  space 空间  works 作品、template 模版 ", example = "2")
    private String jumpType;

    @Schema(description = "跳转目标（none 不处理、URL值、page也路径、spaceId、worksId等）")
    private String jumpValue;

    @Schema(description = "0 启用 1 禁用", example = "1")
    private Integer status;

    @Schema(description = "展示位置（home 首页、space 热门空间、works 热门作品、template 热门模版、market 营销活动）")
    private String position;

    @Schema(description = "权重 越大越靠前")
    private Integer weight;

    @Schema(description = "开始时间 针对特定活动设计 如营销")
    private LocalDateTime startTime;

    @Schema(description = "结束时间 针对特定活动 如营销")
    private LocalDateTime endTime;

    @Schema(description = "创建人id", example = "22021")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "18137")

    private Long updaterId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
package com.fozmo.ym.module.market.controller.app;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerPageReqVO;
import com.fozmo.ym.module.market.controller.app.vo.AppBannerPageReqVO;
import com.fozmo.ym.module.market.controller.app.vo.AppBannerRespVO;
import com.fozmo.ym.module.market.dal.dataobject.banner.BannerDO;
import com.fozmo.ym.module.market.service.banner.BannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-营销模块-banner图")
@RestController
@RequestMapping("/market/banner")
@Validated
public class AppBannerController {
	
   @Resource
   private BannerService bannerService;


	@GetMapping("/page")
	@Operation(summary = "获得营销banner分页")
	@PermitAll
	@ApiAccessLog
	public CommonResult<PageResult<AppBannerRespVO>> getBannerPage(@Valid AppBannerPageReqVO pageReqVO) {
		
		BannerPageReqVO reqVO = BeanUtils.toBean(pageReqVO, BannerPageReqVO.class);
		reqVO.setStatus(0);
		reqVO.setCurrentTime(LocalDateTime.now());
		PageResult<BannerDO> pageResult = bannerService.getBannerPage(reqVO);
		return success(BeanUtils.toBean(pageResult, AppBannerRespVO.class));
	}
}

package com.fozmo.ym.module.market.controller.app.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 营销banner分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppBannerPageReqVO extends PageParam {

    @Schema(description = "名称", example = "王五")
    private String name;
    @NotNull(message = "应用类型不能为空")
    @Schema(description = "应用类型：pc H5页 、miniapp 微信小程序、 system后管系统", example = "2")
    private String appType;
    @NotNull(message = "展示位置不能为空")
    @Schema(description = "展示位置（home 首页、space 热门空间、works 热门作品、template 热门模版、market 营销活动）")
    private String position;
    
   
    

}
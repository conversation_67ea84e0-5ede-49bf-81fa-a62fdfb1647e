package com.fozmo.ym.module.market.dal.dataobject.banner;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 营销banner DO
 *
 * <AUTHOR>
 */
@TableName("market_banner")
@KeySequence("market_banner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BannerDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 文件路径
     */
    private String fileUrl;
    /**
     * 跳转链接
     */
    private String jumpLink;
    /**
     * 应用类型：pc H5页 、miniapp 微信小程序、 system后管系统
     */
    private String appType;
    /**
     * 备注说明
     */
    private String description;
    /**
     * none 无跳转、URL 外链 page 内部  space 空间  works 作品、template 模版 
     */
    private String jumpType;
    /**
     * 跳转目标（none 不处理、URL值、page也路径、spaceId、worksId等）
     */
    private String jumpValue;
    /**
     * 0 启用 1 禁用
     */
    private Integer status;
    /**
     * 展示位置（home 首页、space 热门空间、works 热门作品、template 热门模版、market 营销活动）
     */
    private String position;
    /**
     * 权重 越大越靠前
     */
    private Integer weight;
    /**
     * 开始时间 针对特定活动设计 如营销
     */
    private LocalDateTime startTime;
    /**
     * 结束时间 针对特定活动 如营销
     */
    private LocalDateTime endTime;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

}
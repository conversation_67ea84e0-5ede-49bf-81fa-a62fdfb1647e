package com.fozmo.ym.module.market.dal.mysql.banner;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerPageReqVO;
import com.fozmo.ym.module.market.dal.dataobject.banner.BannerDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 营销banner Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BannerMapper extends BaseMapperX<BannerDO> {

    default PageResult<BannerDO> selectPage(BannerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BannerDO>()
                .likeIfPresent(BannerDO::getName, reqVO.getName())
                .eqIfPresent(BannerDO::getFileUrl, reqVO.getFileUrl())
                .eqIfPresent(BannerDO::getJumpLink, reqVO.getJumpLink())
                .eqIfPresent(BannerDO::getAppType, reqVO.getAppType())
                .eqIfPresent(BannerDO::getDescription, reqVO.getDescription())
                .eqIfPresent(BannerDO::getJumpType, reqVO.getJumpType())
                .eqIfPresent(BannerDO::getJumpValue, reqVO.getJumpValue())
                .eqIfPresent(BannerDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BannerDO::getPosition, reqVO.getPosition())
                .eqIfPresent(BannerDO::getWeight, reqVO.getWeight())
                .leIfPresent(BannerDO::getStartTime, reqVO.getCurrentTime())
                .geIfPresent(BannerDO::getEndTime, reqVO.getCurrentTime())
                .eqIfPresent(BannerDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(BannerDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(BannerDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(BannerDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(BannerDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(BannerDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(BannerDO::getId));
    }

}
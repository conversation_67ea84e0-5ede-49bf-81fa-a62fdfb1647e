package com.fozmo.ym.module.market.service.banner;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerPageReqVO;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerSaveReqVO;
import com.fozmo.ym.module.market.dal.dataobject.banner.BannerDO;
import jakarta.validation.Valid;

/**
 * 营销banner Service 接口
 *
 * <AUTHOR>
 */
public interface BannerService {

    /**
     * 创建营销banner
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBanner(@Valid BannerSaveReqVO createReqVO);

    /**
     * 更新营销banner
     *
     * @param updateReqVO 更新信息
     */
    void updateBanner(@Valid BannerSaveReqVO updateReqVO);

    /**
     * 删除营销banner
     *
     * @param id 编号
     */
    void deleteBanner(Long id);

    /**
     * 获得营销banner
     *
     * @param id 编号
     * @return 营销banner
     */
    BannerDO getBanner(Long id);

    /**
     * 获得营销banner分页
     *
     * @param pageReqVO 分页查询
     * @return 营销banner分页
     */
    PageResult<BannerDO> getBannerPage(BannerPageReqVO pageReqVO);

}
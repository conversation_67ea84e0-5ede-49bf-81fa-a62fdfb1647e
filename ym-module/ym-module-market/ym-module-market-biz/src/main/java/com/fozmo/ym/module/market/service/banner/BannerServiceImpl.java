package com.fozmo.ym.module.market.service.banner;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerPageReqVO;
import com.fozmo.ym.module.market.controller.admin.banner.vo.BannerSaveReqVO;
import com.fozmo.ym.module.market.dal.dataobject.banner.BannerDO;
import com.fozmo.ym.module.market.dal.mysql.banner.BannerMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.market.enums.ErrorCodeConstants.*;

/**
 * 营销banner Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BannerServiceImpl implements BannerService {

    @Resource
    private BannerMapper bannerMapper;

    @Override
    public Long createBanner(BannerSaveReqVO createReqVO) {
        // 插入
        BannerDO banner = BeanUtils.toBean(createReqVO, BannerDO.class);
        bannerMapper.insert(banner);
        // 返回
        return banner.getId();
    }

    @Override
    public void updateBanner(BannerSaveReqVO updateReqVO) {
        // 校验存在
        validateBannerExists(updateReqVO.getId());
        // 更新
        BannerDO updateObj = BeanUtils.toBean(updateReqVO, BannerDO.class);
        bannerMapper.updateById(updateObj);
    }

    @Override
    public void deleteBanner(Long id) {
        // 校验存在
        validateBannerExists(id);
        // 删除
        bannerMapper.deleteById(id);
    }

    private void validateBannerExists(Long id) {
        if (bannerMapper.selectById(id) == null) {
            throw exception(BANNER_NOT_EXISTS);
        }
    }

    @Override
    public BannerDO getBanner(Long id) {
        return bannerMapper.selectById(id);
    }

    @Override
    public PageResult<BannerDO> getBannerPage(BannerPageReqVO pageReqVO) {
        return bannerMapper.selectPage(pageReqVO);
    }

}
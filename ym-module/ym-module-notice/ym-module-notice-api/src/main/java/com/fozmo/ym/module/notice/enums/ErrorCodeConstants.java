package com.fozmo.ym.module.notice.enums;

import com.fozmo.ym.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== 站内信模版 1-002-026-000 ==========
    ErrorCode NOTICE_TEMPLATE_NOT_EXISTS = new ErrorCode(840000, "站内信模版不存在");
    ErrorCode NOTICE_TEMPLATE_CODE_DUPLICATE = new ErrorCode(840001, "已经存在编码为【{}】的站内信模板");

    ErrorCode NOTICE_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(840002, "模板参数({})缺失");

    ErrorCode NOTICE_SEND_TEMPLATE_PARAM_ERROR = new ErrorCode(840003, "模板参数({})错误");

    ErrorCode NOTICE_TYPE_NOT_EXISTS = new ErrorCode(840004, "模板不存在");
    ErrorCode NOTICE_CHANNEL_NOT_EXISTS = new ErrorCode(840005, "站内信模板不存在");
}

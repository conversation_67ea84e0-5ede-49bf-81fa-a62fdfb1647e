<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-module-notice</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>ym-module-notice-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!-- 公共模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-notice-api</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- mybatis-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- web模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-web</artifactId>
        </dependency>

        <!-- 消息队列模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-mq</artifactId>
        </dependency>
        <!-- redis 模块 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 鉴权模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-security</artifactId>
        </dependency>
        <!-- 权限模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-account-api</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
    </dependencies>
</project>
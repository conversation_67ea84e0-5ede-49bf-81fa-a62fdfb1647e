package com.fozmo.ym.module.notice.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.api.dto.NoticeMessageDTO;
import com.fozmo.ym.module.notice.framwork.mq.NoticeProduce;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class NoticeMessageApiImpl implements NoticeMessageApi {

    @Resource
    private NoticeProduce noticeMessageProducer;
    @Override
    public void createMessage(NoticeMessageDTO messageDTO){
        log.info("createMessage:{}",messageDTO);
        NoticeSendMessage noticeSendMessage = BeanUtils.toBean(messageDTO, NoticeSendMessage.class);
        noticeMessageProducer.sendNotice(noticeSendMessage);
    }
}

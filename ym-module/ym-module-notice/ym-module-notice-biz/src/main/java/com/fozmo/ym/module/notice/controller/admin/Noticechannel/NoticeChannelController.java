package com.fozmo.ym.module.notice.controller.admin.Noticechannel;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelPageReqVO;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelRespVO;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;
import com.fozmo.ym.module.notice.service.Noticechannel.NoticeChannelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站内信发送通道")
@RestController
@RequestMapping("/notice/channel")
@Validated
public class NoticeChannelController {

    @Resource
    private NoticeChannelService channelService;

    @PostMapping("/create")
    @Operation(summary = "创建站内信发送通道")
    @PreAuthorize("@ss.hasPermission('notice:channel:create')")
    public CommonResult<Long> createChannel(@Valid @RequestBody NoticeChannelSaveReqVO createReqVO) {
        return success(channelService.createChannel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站内信发送通道")
    @PreAuthorize("@ss.hasPermission('notice:channel:update')")
    public CommonResult<Boolean> updateChannel(@Valid @RequestBody NoticeChannelSaveReqVO updateReqVO) {
        channelService.updateChannel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站内信发送通道")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('notice:channel:delete')")
    public CommonResult<Boolean> deleteChannel(@RequestParam("id") Long id) {
        channelService.deleteChannel(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除站内信发送通道")
    @PreAuthorize("@ss.hasPermission('notice:channel:delete')")
    public CommonResult<Boolean> deleteChannelList(@RequestParam("ids") List<Long> ids) {
        channelService.deleteChannelListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站内信发送通道")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('notice:channel:query')")
    public CommonResult<NoticeChannelRespVO> getChannel(@RequestParam("id") Long id) {
        NoticeChannelDO channel = channelService.getChannel(id);
        return success(BeanUtils.toBean(channel, NoticeChannelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得站内信发送通道分页")
    @PreAuthorize("@ss.hasPermission('notice:channel:query')")
    public CommonResult<PageResult<NoticeChannelRespVO>> getChannelPage(@Valid NoticeChannelPageReqVO pageReqVO) {
        PageResult<NoticeChannelDO> pageResult = channelService.getChannelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NoticeChannelRespVO.class));
    }
}
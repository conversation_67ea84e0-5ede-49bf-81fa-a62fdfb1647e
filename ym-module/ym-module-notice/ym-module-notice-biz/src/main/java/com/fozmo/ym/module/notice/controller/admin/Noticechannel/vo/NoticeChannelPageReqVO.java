package com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 站内信发送通道分页 Request VO")
@Data
public class NoticeChannelPageReqVO extends PageParam {

    @Schema(description = "站内信渠道名", example = "赵六")
    private String channelName;

    @Schema(description = "站内信渠道码值")
    private String channelCode;

    @Schema(description = "0 内部渠道 1 外部渠道", example = "1")
    private Integer channelType;

    @Schema(description = "说明", example = "随便")
    private String description;

    @Schema(description = "站内信发送地址", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "0正常 1禁用", example = "1")
    private Integer status;

    @Schema(description = "应用id", example = "18296")
    private String appId;

    @Schema(description = "密钥")
    private String appSecret;

    @Schema(description = "应用环境、版本")
    private String appEnv;

    @Schema(description = "应用名称", example = "芋艿")
    private String appName;

    @Schema(description = "创建人id", example = "15358")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "30016")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
package com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "管理后台 - 站内信发送通道新增/修改 Request VO")
@Data
public class NoticeChannelSaveReqVO {

    @Schema(description = "id站内信渠道id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31844")
    private Long channelId;

    @Schema(description = "站内信渠道名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "站内信渠道名不能为空")
    private String channelName;

    @Schema(description = "站内信渠道码值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "站内信渠道码值不能为空")
    private String channelCode;

    @Schema(description = "0 内部渠道 1 外部渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "0 内部渠道 1 外部渠道不能为空")
    private Integer channelType;

    @Schema(description = "说明", example = "随便")
    private String description;

    @Schema(description = "站内信发送地址", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "0正常 1禁用", example = "1")
    private Integer status;

    @Schema(description = "应用id", example = "18296")
    private String appId;

    @Schema(description = "密钥")
    private String appSecret;

    @Schema(description = "应用环境、版本")
    private String appEnv;

    @Schema(description = "应用名称", example = "芋艿")
    private String appName;

    @Schema(description = "创建人id", example = "15358")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "30016")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
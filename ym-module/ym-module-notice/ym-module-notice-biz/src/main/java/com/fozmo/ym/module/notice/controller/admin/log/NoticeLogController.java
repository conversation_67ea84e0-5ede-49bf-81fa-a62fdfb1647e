package com.fozmo.ym.module.notice.controller.admin.log;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogPageReqVO;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogRespVO;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.log.NoticeLogDO;
import com.fozmo.ym.module.notice.service.log.NoticeLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "后管-站内信模块 - 通知日志")
@RestController
@RequestMapping("/notice/log")
@Validated
public class NoticeLogController {

    @Resource
    private NoticeLogService noticeLogService;

    @PostMapping("/create")
    @Operation(summary = "创建通知日志")
    @PreAuthorize("@ss.hasPermission('notice:log:create')")
    public CommonResult<Long> createLog(@Valid @RequestBody NoticeLogSaveReqVO createReqVO) {
        return success(noticeLogService.createLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新通知日志")
    @PreAuthorize("@ss.hasPermission('notice:log:update')")
    public CommonResult<Boolean> updateLog(@Valid @RequestBody NoticeLogSaveReqVO updateReqVO) {
        noticeLogService.updateLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除通知日志")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('notice:log:delete')")
    public CommonResult<Boolean> deleteLog(@RequestParam("id") Long id) {
        noticeLogService.deleteLog(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除通知日志")
    @PreAuthorize("@ss.hasPermission('notice:log:delete')")
    public CommonResult<Boolean> deleteLogList(@RequestParam("ids") List<Long> ids) {
        noticeLogService.deleteLogListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得通知日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('notice:log:query')")
    public CommonResult<NoticeLogRespVO> getLog(@RequestParam("id") Long id) {
        NoticeLogDO log = noticeLogService.getLog(id);
        return success(BeanUtils.toBean(log, NoticeLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得通知日志分页")
    @PreAuthorize("@ss.hasPermission('notice:log:query')")
    public CommonResult<PageResult<NoticeLogRespVO>> getLogPage(@Valid NoticeLogPageReqVO pageReqVO) {
        PageResult<NoticeLogDO> pageResult = noticeLogService.getLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NoticeLogRespVO.class));
    }

}
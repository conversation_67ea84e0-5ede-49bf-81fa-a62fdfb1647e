package com.fozmo.ym.module.notice.controller.admin.log.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 通知日志分页 Request VO")
@Data
public class NoticeLogPageReqVO extends PageParam {

    @Schema(description = "渠道ID", example = "29285")
    private Long channelId;

    @Schema(description = "模板ID", example = "24676")
    private Long templateId;

    @Schema(description = "接收用户")
    private String toUser;

    @Schema(description = "发送来源")
    private String sendResource;

    @Schema(description = "类型ID", example = "17807")
    private Long typeId;

    @Schema(description = "类型父ID", example = "31502")
    private Long typePid;

    @Schema(description = "发送应用")
    private String sendApp;

    @Schema(description = "发送内容")
    private String sendContent;

    @Schema(description = "创建人ID", example = "12266")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人ID", example = "32440")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户编码")
    private String tenantCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
package com.fozmo.ym.module.notice.controller.admin.log.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 通知日志新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class NoticeLogSaveReqVO {

    @Schema(description = "日志ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5136")
    private Long logId;

    @Schema(description = "渠道ID", example = "29285")
    private Long channelId;

    @Schema(description = "模板ID", example = "24676")
    private Long templateId;

    @Schema(description = "接收用户")
    private String toUser;

    @Schema(description = "发送来源")
    private String sendResource;

    @Schema(description = "类型ID", example = "17807")
    private Long typeId;

    @Schema(description = "类型父ID", example = "31502")
    private Long typePid;

    @Schema(description = "发送应用")
    private String sendApp;

    @Schema(description = "发送内容")
    private String sendContent;

    @Schema(description = "创建人ID", example = "12266")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人ID", example = "32440")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户编码")
    private String tenantCode;

}
package com.fozmo.ym.module.notice.controller.admin.message;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageRespVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import com.fozmo.ym.module.notice.service.message.NoticeMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "后管-站内信模块 - 站内信信息")
@RestController
@RequestMapping("/notice/message")
@Validated
public class NoticeMessageController {

    @Resource
    private NoticeMessageService messageService;

    @PostMapping("/create")
    @Operation(summary = "创建站内信信息")
    @PreAuthorize("@ss.hasPermission('notice:message:create')")
    public CommonResult<Long> createMessage(@Valid @RequestBody NoticeMessageSaveReqVO createReqVO) {
        return success(messageService.createMessage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站内信信息")
    @PreAuthorize("@ss.hasPermission('notice:message:update')")
    public CommonResult<Boolean> updateMessage(@Valid @RequestBody NoticeMessageSaveReqVO updateReqVO) {
        messageService.updateMessage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站内信信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('notice:message:delete')")
    public CommonResult<Boolean> deleteMessage(@RequestParam("id") Long id) {
        messageService.deleteMessage(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除站内信信息")
    @PreAuthorize("@ss.hasPermission('notice:message:delete')")
    public CommonResult<Boolean> deleteMessageList(@RequestParam("ids") List<Long> ids) {
        messageService.deleteMessageListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站内信信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('notice:message:query')")
    public CommonResult<NoticeMessageRespVO> getMessage(@RequestParam("id") Long id) {
        NoticeMessageDO message = messageService.getMessage(id);
        return success(BeanUtils.toBean(message, NoticeMessageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得站内信信息分页")
    @PreAuthorize("@ss.hasPermission('notice:message:query')")
    public CommonResult<PageResult<NoticeMessageRespVO>> getMessagePage(@Valid NoticeMessagePageReqVO pageReqVO) {
        PageResult<NoticeMessageDO> pageResult = messageService.getMessagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NoticeMessageRespVO.class));
    }

}
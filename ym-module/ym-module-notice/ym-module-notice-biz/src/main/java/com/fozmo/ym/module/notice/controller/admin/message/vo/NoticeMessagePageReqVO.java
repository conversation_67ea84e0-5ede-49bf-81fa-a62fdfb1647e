package com.fozmo.ym.module.notice.controller.admin.message.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 站内信信息分页 Request VO")
@Data
public class NoticeMessagePageReqVO extends PageParam {

    @Schema(description = "模板id", example = "31848")
    private Long templateId;

    @Schema(description = "发送时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sendTime;

    @Schema(description = "发送参数")
    private String sendParams;

    @Schema(description = "发送内容")
    private String sendContent;

    @Schema(description = "发送方")
    private String sendApp;

    @Schema(description = "接收账户", example = "21095")
    private Long toAccount;

    @Schema(description = "0 未读 1 已读")
    private Integer isRead;

    @Schema(description = "已读时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] readTime;

    @Schema(description = "发送来源：异步任务 具体某个job 如 支付订单任务 payJob ， 事件event 例如 作品点赞事件触发 worksLikeEvent ")
    private String sendResource;

    @Schema(description = "类别", example = "15827")
    private Long typeId;

    @Schema(description = "父类别", example = "11033")
    private Long typePid;

    @Schema(description = "创建人id", example = "9306")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "26063")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
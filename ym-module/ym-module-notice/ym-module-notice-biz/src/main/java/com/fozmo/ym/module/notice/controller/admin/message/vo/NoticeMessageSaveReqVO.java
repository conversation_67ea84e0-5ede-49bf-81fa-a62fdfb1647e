package com.fozmo.ym.module.notice.controller.admin.message.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 站内信信息新增/修改 Request VO")
@Data
public class NoticeMessageSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12712")
    private Long id;

    @Schema(description = "模板id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31848")
    @NotNull(message = "模板id不能为空")
    private Long templateId;

    @Schema(description = "发送时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "发送时间不能为空")
    private LocalDateTime sendTime;

    @Schema(description = "发送参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "发送参数不能为空")
    private String sendParams;

    @Schema(description = "发送内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "发送内容不能为空")
    private String sendContent;

    @Schema(description = "发送方", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "发送方不能为空")
    private String sendApp;

    @Schema(description = "接收账户", example = "21095")
    private Long toAccount;

    @Schema(description = "0 未读 1 已读")
    private Integer isRead;

    @Schema(description = "已读时间")
    private LocalDateTime readTime;

    @Schema(description = "发送来源：异步任务 具体某个job 如 支付订单任务 payJob ， 事件event 例如 作品点赞事件触发 worksLikeEvent ")
    private String sendResource;

    @Schema(description = "类别", example = "15827")
    private Long typeId;

    @Schema(description = "父类别", example = "11033")
    private Long typePid;

    @Schema(description = "创建人id", example = "9306")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "26063")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
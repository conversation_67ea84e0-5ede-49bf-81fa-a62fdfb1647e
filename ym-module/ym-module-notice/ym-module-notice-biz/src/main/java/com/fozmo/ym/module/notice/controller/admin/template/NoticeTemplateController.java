package com.fozmo.ym.module.notice.controller.admin.template;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplatePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplateRespVO;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplateSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import com.fozmo.ym.module.notice.service.template.NoticeTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "后管-站内信模块-模板")
@RestController
@RequestMapping("/notice/template")
@Validated
public class NoticeTemplateController {

    @Resource
    private NoticeTemplateService templateService;

    @PostMapping("/create")
    @Operation(summary = "创建站内信模板")
    @PreAuthorize("@ss.hasPermission('notice:template:create')")
    public CommonResult<Long> createTemplate(@Valid @RequestBody NoticeTemplateSaveReqVO createReqVO) {
        return success(templateService.createTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站内信模板")
    @PreAuthorize("@ss.hasPermission('notice:template:update')")
    public CommonResult<Boolean> updateTemplate(@Valid @RequestBody NoticeTemplateSaveReqVO updateReqVO) {
        templateService.updateTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站内信模板")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('notice:template:delete')")
    public CommonResult<Boolean> deleteTemplate(@RequestParam("id") Long id) {
        templateService.deleteTemplate(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除站内信模板")
    @PreAuthorize("@ss.hasPermission('notice:template:delete')")
    public CommonResult<Boolean> deleteTemplateList(@RequestParam("ids") List<Long> ids) {
        templateService.deleteTemplateListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站内信模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('notice:template:query')")
    public CommonResult<NoticeTemplateRespVO> getTemplate(@RequestParam("id") Long id) {
        NoticeTemplateDO template = templateService.getTemplate(id);
        return success(BeanUtils.toBean(template, NoticeTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得站内信模板分页")
    @PreAuthorize("@ss.hasPermission('notice:template:query')")
    public CommonResult<PageResult<NoticeTemplateRespVO>> getTemplatePage(@Valid NoticeTemplatePageReqVO pageReqVO) {
        PageResult<NoticeTemplateDO> pageResult = templateService.getTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NoticeTemplateRespVO.class));
    }

}
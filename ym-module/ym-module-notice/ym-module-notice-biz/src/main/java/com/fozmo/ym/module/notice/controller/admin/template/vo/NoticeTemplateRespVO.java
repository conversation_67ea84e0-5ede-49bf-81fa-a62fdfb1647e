package com.fozmo.ym.module.notice.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 站内信模板 Response VO")
@Data
public class NoticeTemplateRespVO {

    @Schema(description = "站内信模板id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21588")
    private Long templateId;

    @Schema(description = "发送渠道id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32086")
    private Long channelId;

    @Schema(description = "站内信二级分类id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20410")
    private Long typeId;

    @Schema(description = "站内信一级分类Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17842")
    private Long typePid;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String templateName;

    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateCode;

    @Schema(description = "简介说明", example = "随便")
    private String description;

    @Schema(description = "外部模板Id（如微信小程序）", example = "32232")
    private String apiTemplateId;

    @Schema(description = "发送方标识 取 渠道app字段  系统为ym  三方比如 miniapp ", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sendApp;

    @Schema(description = "-1 全部用户发送    0 一对多发送    1 一对一发送", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sendType;

    @Schema(description = "0 被动接受  1 服务端主动推送", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sendWay;

    @Schema(description = "模板内容")
    private String content;

    @Schema(description = "模板参数")
    private String params;

    @Schema(description = "创建人id", example = "29537")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "15805")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
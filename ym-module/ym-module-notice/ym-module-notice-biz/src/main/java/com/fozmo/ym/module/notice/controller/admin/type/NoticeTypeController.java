package com.fozmo.ym.module.notice.controller.admin.type;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypeRespVO;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypeSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.type.NoticeTypeDO;
import com.fozmo.ym.module.notice.service.type.NoticeTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "后管-站内信模块-类型")
@RestController
@RequestMapping("/notice/type")
@Validated
public class NoticeTypeController {

    @Resource
    private NoticeTypeService typeService;

    @PostMapping("/create")
    @Operation(summary = "创建站内信类型")
    @PreAuthorize("@ss.hasPermission('notice:type:create')")
    public CommonResult<Long> createType(@Valid @RequestBody NoticeTypeSaveReqVO createReqVO) {
        return success(typeService.createType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站内信类型")
    @PreAuthorize("@ss.hasPermission('notice:type:update')")
    public CommonResult<Boolean> updateType(@Valid @RequestBody NoticeTypeSaveReqVO updateReqVO) {
        typeService.updateType(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站内信类型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('notice:type:delete')")
    public CommonResult<Boolean> deleteType(@RequestParam("id") Long id) {
        typeService.deleteType(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除站内信类型")
    @PreAuthorize("@ss.hasPermission('notice:type:delete')")
    public CommonResult<Boolean> deleteTypeList(@RequestParam("ids") List<Long> ids) {
        typeService.deleteTypeListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站内信类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('notice:type:query')")
    public CommonResult<NoticeTypeRespVO> getType(@RequestParam("id") Long id) {
        NoticeTypeDO type = typeService.getType(id);
        return success(BeanUtils.toBean(type, NoticeTypeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得站内信类型分页")
    @PreAuthorize("@ss.hasPermission('notice:type:query')")
    public CommonResult<PageResult<NoticeTypeRespVO>> getTypePage(@Valid NoticeTypePageReqVO pageReqVO) {
        PageResult<NoticeTypeDO> pageResult = typeService.getTypePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NoticeTypeRespVO.class));
    }

}
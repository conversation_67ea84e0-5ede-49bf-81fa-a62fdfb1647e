package com.fozmo.ym.module.notice.controller.admin.type.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 站内信类型分页 Request VO")
@Data
public class NoticeTypePageReqVO extends PageParam {

    @Schema(description = "code")
    private String code;

    @Schema(description = "类型名称", example = "李四")
    private String name;

    @Schema(description = "简介", example = "你说的对")
    private String description;

    @Schema(description = "父id", example = "5594")
    private Long pid;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建人id", example = "26525")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "29522")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
package com.fozmo.ym.module.notice.controller.admin.type.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 站内信类型 Response VO")
@Data
public class NoticeTypeRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13060")
    private Long id;

    @Schema(description = "code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "类型名称", example = "李四")
    private String name;

    @Schema(description = "简介", example = "你说的对")
    private String description;

    @Schema(description = "父id", example = "5594")
    private Long pid;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "创建人id", example = "26525")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "29522")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
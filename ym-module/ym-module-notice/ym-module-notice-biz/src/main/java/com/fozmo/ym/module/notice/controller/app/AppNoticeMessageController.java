package com.fozmo.ym.module.notice.controller.app;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageRespVO;
import com.fozmo.ym.module.notice.controller.app.vo.AppNoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import com.fozmo.ym.module.notice.service.message.NoticeMessageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/08/03 17:03
 */
@Tag(name = "APP-站内信模块-站内信信息")
@RestController
@RequestMapping("/notice/message")
@Validated
public class AppNoticeMessageController {

    @Resource
    private NoticeMessageService noticeMessageService;

    @GetMapping("/page")
    @PermitAll
    public CommonResult<PageResult<NoticeMessageRespVO>> getMessagePage(@Valid AppNoticeMessagePageReqVO pageReqVO) {
        NoticeMessagePageReqVO noticeMessagePageReqVO = BeanUtils.toBean(pageReqVO, NoticeMessagePageReqVO.class);
        noticeMessagePageReqVO.setToAccount(SecurityFrameworkUtils.getLoginUserId());
        PageResult<NoticeMessageDO> pageResult = noticeMessageService.getMessagePage(noticeMessagePageReqVO);
        return success(BeanUtils.toBean(pageResult, NoticeMessageRespVO.class));
    }
}

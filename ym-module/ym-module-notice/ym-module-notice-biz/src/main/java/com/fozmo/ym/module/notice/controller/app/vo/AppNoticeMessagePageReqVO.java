package com.fozmo.ym.module.notice.controller.app.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "APP - 站内信信息分页 Request VO")
@Data
public class AppNoticeMessagePageReqVO extends PageParam {

    @Schema(description = "0 未读 1 已读")
    private Integer isRead;

    @Schema(description = "父类别", example = "11033")
    private Long typePid;

}

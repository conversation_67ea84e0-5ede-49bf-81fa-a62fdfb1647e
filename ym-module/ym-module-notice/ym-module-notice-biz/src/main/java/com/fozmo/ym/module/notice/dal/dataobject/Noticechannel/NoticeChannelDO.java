package com.fozmo.ym.module.notice.dal.dataobject.Noticechannel;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 站内信发送通道 DO
 *
 * <AUTHOR>
 */
@TableName("notice_channel")
@KeySequence("notice_channel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeChannelDO extends TenantBaseDO {

    /**
     * id站内信渠道id
     */
    @TableId
    private Long channelId;
    /**
     * 站内信渠道名
     */
    private String channelName;
    /**
     * 站内信渠道码值
     */
    private String channelCode;
    /**
     * 0 内部渠道 1 外部渠道
     */
    private Integer channelType;
    /**
     * 说明
     */
    private String description;
    /**
     * 站内信发送地址
     */
    private String url;
    /**
     * 0正常 1禁用
     */
    private Integer status;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 密钥
     */
    private String appSecret;
    /**
     * 应用环境、版本
     */
    private String appEnv;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;


}
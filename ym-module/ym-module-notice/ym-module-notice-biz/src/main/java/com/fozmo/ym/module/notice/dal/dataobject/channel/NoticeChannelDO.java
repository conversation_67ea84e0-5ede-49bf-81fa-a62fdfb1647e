package com.fozmo.ym.module.notice.dal.dataobject.channel;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@TableName("notice_channel")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeChannelDO extends TenantBaseDO {
    /**
     * id
     */
    private Long channelId;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 渠道编码
     *
     */
    private String channelCode;
    /**
     * 渠道类型
     *
     */
    private String channelType;
    /**
     * 渠道描述
     *
     */
    private String description;
    /**
     * 接口地址
     */
    private String url;
    /**
     * 渠道状态
     */
    private String status;
    /**
     * appid
     */
    private String appId;
    /**
     * 密钥
     */
    private String appSecret;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 应用环境
     */
    private String appEnv;


    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;


}

package com.fozmo.ym.module.notice.dal.dataobject.log;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

@TableName("notice_log")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeLogDO extends TenantBaseDO {

    private Long id;

    private Long channelId;

    private String channelCode;

    private Long templateId;

    private String templateCode;

    private Long typeId;

    private Long typePid;

    private String typeCode;

    private String messageContent;

    private String messageParams;

    private String messageStatus;

    private String messageResult;




}

package com.fozmo.ym.module.notice.dal.dataobject.log;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 通知日志 DO
 *
 * <AUTHOR>
 */
@TableName("notice_log")
@KeySequence("notice_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeLogDO extends TenantBaseDO {

    /**
     * 日志ID
     */
    @TableId
    private Long logId;
    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * 模板ID
     */
    private Long templateId;
    /**
     * 接收用户
     */
    private String toUser;
    /**
     * 发送来源
     */
    private String sendResource;
    /**
     * 类型ID
     */
    private Long typeId;
    /**
     * 类型父ID
     */
    private Long typePid;
    /**
     * 发送应用
     */
    private String sendApp;
    /**
     * 发送内容
     */
    private String sendContent;
    /**
     * 创建人ID
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户编码
     */
    private String tenantCode;


}
package com.fozmo.ym.module.notice.dal.dataobject.message;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 站内信信息 DO
 *
 * <AUTHOR>
 */
@TableName("notice_message")
@KeySequence("notice_message_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeMessageDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    /**
     * 发送参数
     */
    private String sendParams;
    /**
     * 发送内容
     */
    private String sendContent;
    /**
     * 发送方
     */
    private String sendApp;
    /**
     * 接收账户
     */
    private Long toAccount;
    /**
     * 0 未读 1 已读
     */
    private Integer isRead;
    /**
     * 已读时间
     */
    private LocalDateTime readTime;
    /**
     * 发送来源：异步任务 具体某个job 如 支付订单任务 payJob ， 事件event 例如 作品点赞事件触发 worksLikeEvent
     */
    private String sendResource;
    /**
     * 类别
     */
    private Long typeId;
    /**
     * 父类别
     */
    private Long typePid;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;


}
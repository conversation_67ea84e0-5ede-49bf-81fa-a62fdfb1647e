package com.fozmo.ym.module.notice.dal.dataobject.template;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 站内信模板 DO
 *
 * <AUTHOR>
 */
@TableName("notice_template")
@KeySequence("notice_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeTemplateDO extends TenantBaseDO {

    /**
     * 站内信模板id
     */
    @TableId
    private Long templateId;
    /**
     * 发送渠道id
     */
    private Long channelId;
    /**
     * 站内信二级分类id
     */
    private Long typeId;
    /**
     * 站内信一级分类Id
     */
    private Long typePid;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板编码
     */
    private String templateCode;
    /**
     * 简介说明
     */
    private String description;
    /**
     * 外部模板Id（如微信小程序）
     */
    private String apiTemplateId;
    /**
     * 发送方标识 取 渠道app字段  系统为ym  三方比如 miniapp
     */
    private String sendApp;
    /**
     * -1 全部用户发送    0 一对多发送    1 一对一发送
     */
    private Integer sendType;
    /**
     * 0 被动接受  1 服务端主动推送
     */
    private Integer sendWay;
    /**
     * 模板内容
     */
    private String content;
    /**
     * 模板参数
     */
    private String params;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;


}
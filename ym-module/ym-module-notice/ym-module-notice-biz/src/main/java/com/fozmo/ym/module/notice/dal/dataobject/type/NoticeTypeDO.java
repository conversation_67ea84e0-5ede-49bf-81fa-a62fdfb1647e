package com.fozmo.ym.module.notice.dal.dataobject.type;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 站内信类型 DO
 *
 * <AUTHOR>
 */
@TableName("notice_type")
@KeySequence("notice_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeTypeDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * code
     */
    private String code;
    /**
     * 类型名称
     */
    private String name;
    /**
     * 简介
     */
    private String description;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;


}
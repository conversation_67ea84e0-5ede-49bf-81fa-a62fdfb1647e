package com.fozmo.ym.module.notice.dal.mysql.Noticechannel;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelPageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 站内信发送通道 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeChannelMapper extends BaseMapperX<NoticeChannelDO> {

    default PageResult<NoticeChannelDO> selectPage(NoticeChannelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeChannelDO>()
                .likeIfPresent(NoticeChannelDO::getChannelName, reqVO.getChannelName())
                .eqIfPresent(NoticeChannelDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(NoticeChannelDO::getChannelType, reqVO.getChannelType())
                .eqIfPresent(NoticeChannelDO::getDescription, reqVO.getDescription())
                .eqIfPresent(NoticeChannelDO::getUrl, reqVO.getUrl())
                .eqIfPresent(NoticeChannelDO::getStatus, reqVO.getStatus())
                .eqIfPresent(NoticeChannelDO::getAppId, reqVO.getAppId())
                .eqIfPresent(NoticeChannelDO::getAppSecret, reqVO.getAppSecret())
                .eqIfPresent(NoticeChannelDO::getAppEnv, reqVO.getAppEnv())
                .likeIfPresent(NoticeChannelDO::getAppName, reqVO.getAppName())
                .eqIfPresent(NoticeChannelDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(NoticeChannelDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(NoticeChannelDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NoticeChannelDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(NoticeChannelDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(NoticeChannelDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(NoticeChannelDO::getChannelId));
    }

}
package com.fozmo.ym.module.notice.dal.mysql.log;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogPageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.log.NoticeLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通知日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeLogMapper extends BaseMapperX<NoticeLogDO> {

    default PageResult<NoticeLogDO> selectPage(NoticeLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeLogDO>()
                .eqIfPresent(NoticeLogDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(NoticeLogDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(NoticeLogDO::getToUser, reqVO.getToUser())
                .eqIfPresent(NoticeLogDO::getSendResource, reqVO.getSendResource())
                .eqIfPresent(NoticeLogDO::getTypeId, reqVO.getTypeId())
                .eqIfPresent(NoticeLogDO::getTypePid, reqVO.getTypePid())
                .eqIfPresent(NoticeLogDO::getSendApp, reqVO.getSendApp())
                .eqIfPresent(NoticeLogDO::getSendContent, reqVO.getSendContent())
                .eqIfPresent(NoticeLogDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(NoticeLogDO::getCreateData, reqVO.getCreateData())
                .eqIfPresent(NoticeLogDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(NoticeLogDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(NoticeLogDO::getTenantCode, reqVO.getTenantCode())
                .betweenIfPresent(NoticeLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(NoticeLogDO::getCreateTime));
    }

}
package com.fozmo.ym.module.notice.dal.mysql.message;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 站内信信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeMessageMapper extends BaseMapperX<NoticeMessageDO> {

    default PageResult<NoticeMessageDO> selectPage(NoticeMessagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeMessageDO>()
                .eqIfPresent(NoticeMessageDO::getTemplateId, reqVO.getTemplateId())
                .betweenIfPresent(NoticeMessageDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(NoticeMessageDO::getSendParams, reqVO.getSendParams())
                .eqIfPresent(NoticeMessageDO::getSendContent, reqVO.getSendContent())
                .eqIfPresent(NoticeMessageDO::getSendApp, reqVO.getSendApp())
                .eqIfPresent(NoticeMessageDO::getToAccount, reqVO.getToAccount())
                .eqIfPresent(NoticeMessageDO::getIsRead, reqVO.getIsRead())
                .betweenIfPresent(NoticeMessageDO::getReadTime, reqVO.getReadTime())
                .eqIfPresent(NoticeMessageDO::getSendResource, reqVO.getSendResource())
                .eqIfPresent(NoticeMessageDO::getTypeId, reqVO.getTypeId())
                .eqIfPresent(NoticeMessageDO::getTypePid, reqVO.getTypePid())
                .eqIfPresent(NoticeMessageDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(NoticeMessageDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(NoticeMessageDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NoticeMessageDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(NoticeMessageDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(NoticeMessageDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(NoticeMessageDO::getId));
    }

}
package com.fozmo.ym.module.notice.dal.mysql.template;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplatePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 站内信模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeTemplateMapper extends BaseMapperX<NoticeTemplateDO> {

    default PageResult<NoticeTemplateDO> selectPage(NoticeTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeTemplateDO>()
                .eqIfPresent(NoticeTemplateDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(NoticeTemplateDO::getTypeId, reqVO.getTypeId())
                .eqIfPresent(NoticeTemplateDO::getTypePid, reqVO.getTypePid())
                .likeIfPresent(NoticeTemplateDO::getTemplateName, reqVO.getTemplateName())
                .eqIfPresent(NoticeTemplateDO::getTemplateCode, reqVO.getTemplateCode())
                .eqIfPresent(NoticeTemplateDO::getDescription, reqVO.getDescription())
                .eqIfPresent(NoticeTemplateDO::getApiTemplateId, reqVO.getApiTemplateId())
                .eqIfPresent(NoticeTemplateDO::getSendApp, reqVO.getSendApp())
                .eqIfPresent(NoticeTemplateDO::getSendType, reqVO.getSendType())
                .eqIfPresent(NoticeTemplateDO::getSendWay, reqVO.getSendWay())
                .eqIfPresent(NoticeTemplateDO::getContent, reqVO.getContent())
                .eqIfPresent(NoticeTemplateDO::getParams, reqVO.getParams())
                .eqIfPresent(NoticeTemplateDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(NoticeTemplateDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(NoticeTemplateDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NoticeTemplateDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(NoticeTemplateDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(NoticeTemplateDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(NoticeTemplateDO::getTemplateId));
    }

}
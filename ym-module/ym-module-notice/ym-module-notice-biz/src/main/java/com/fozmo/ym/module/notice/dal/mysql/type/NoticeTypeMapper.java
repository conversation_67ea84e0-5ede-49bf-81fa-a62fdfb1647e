package com.fozmo.ym.module.notice.dal.mysql.type;


import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.type.NoticeTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 站内信类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeTypeMapper extends BaseMapperX<NoticeTypeDO> {

    default PageResult<NoticeTypeDO> selectPage(NoticeTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeTypeDO>()
                .eqIfPresent(NoticeTypeDO::getCode, reqVO.getCode())
                .likeIfPresent(NoticeTypeDO::getName, reqVO.getName())
                .eqIfPresent(NoticeTypeDO::getDescription, reqVO.getDescription())
                .eqIfPresent(NoticeTypeDO::getPid, reqVO.getPid())
                .eqIfPresent(NoticeTypeDO::getStatus, reqVO.getStatus())
                .eqIfPresent(NoticeTypeDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(NoticeTypeDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(NoticeTypeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NoticeTypeDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(NoticeTypeDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(NoticeTypeDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(NoticeTypeDO::getId));
    }

}
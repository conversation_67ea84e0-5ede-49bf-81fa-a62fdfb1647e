package com.fozmo.ym.module.notice.dal.redis.channel;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelPageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fozmo.ym.module.notice.constans.NoticeRedisConstans.NOTICE_CHANNEL_CODE;

@Repository
@Slf4j
public class NoticeChannelRedisDao {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    // 添加通知类型
    public void addNoticeChannel(NoticeChannelDO noticeChannelDO) {
        String hk = noticeChannelDO.getChannelId().toString();
        redisTemplate.opsForHash().put(NOTICE_CHANNEL_CODE, hk, noticeChannelDO);
    }

    // 将通知类型添加到Redis中
    public void removeNoticeChannel(Long id) {
        String hk = id.toString();
        redisTemplate.opsForHash().delete(NOTICE_CHANNEL_CODE, hk);
    }

    // 根据id从Redis中删除通知类型
    public NoticeChannelDO getNoticeChannel(Long id) {
        String hk = id.toString();
        return (NoticeChannelDO) redisTemplate.opsForHash().get(NOTICE_CHANNEL_CODE, hk);
    }

    // 根据id从Redis中获取通知类型
    public boolean hasNoticeChannel(Long id) {
        String hk = id.toString();
        return redisTemplate.opsForHash().hasKey(NOTICE_CHANNEL_CODE, hk);
    }

    // 分页查询通知类型
    public PageResult<NoticeChannelDO> getNoticeChannelPage(NoticeChannelPageReqVO reqVO) {
        Integer pageNo = reqVO.getPageNo();
        Integer pageSize = reqVO.getPageSize();

        PageResult<NoticeChannelDO> pageResult = new PageResult<>();

        HashOperations<String, String, NoticeChannelDO> hashOps = redisTemplate.opsForHash();

        // 3. 优化扫描参数
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(100) // 减少每批数量，降低内存压力
                .build();

        List<NoticeChannelDO> NoticeChannelList = new ArrayList<>();

        try (Cursor<Map.Entry<String, NoticeChannelDO>> cursor = hashOps.scan(NOTICE_CHANNEL_CODE, scanOptions)) {
            while (cursor.hasNext()) {
                NoticeChannelDO NoticeChannel = cursor.next().getValue();
                NoticeChannelList.add(NoticeChannel);
            }
        } catch (Exception e) {
            log.error("扫描异常: {}", e.getMessage());
        }

        if (ObjectUtil.isNotEmpty(NoticeChannelList)) {
            int start = Math.max(0, (pageNo - 1) * pageSize);
            int end = Math.min(start + pageSize, NoticeChannelList.size());

            pageResult.setTotal((long) NoticeChannelList.size());
            pageResult.setList(NoticeChannelList.subList(start, end));
        }
        return pageResult;
    }
}

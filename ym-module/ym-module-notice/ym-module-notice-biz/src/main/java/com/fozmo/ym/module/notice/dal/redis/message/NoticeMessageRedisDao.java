package com.fozmo.ym.module.notice.dal.redis.message;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import static com.fozmo.ym.module.notice.constans.NoticeRedisConstans.NOTICE_MESSAGE_CODE;

@Repository
@Slf4j
public class NoticeMessageRedisDao {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;


    public void addNoticeMessage(NoticeMessageDO noticeMessageDO) {
        String key = NOTICE_MESSAGE_CODE + noticeMessageDO.getToAccount();
        String hk = noticeMessageDO.getId().toString();
        redisTemplate.opsForHash().put(key, hk, noticeMessageDO);
    }


    public void removeNoticeMessage(Long accountId, Long id) {
        String key = NOTICE_MESSAGE_CODE + accountId;
        String hk = id.toString();
        redisTemplate.opsForHash().delete(key, hk);
    }

    public NoticeMessageDO getNoticeMessage(Long accountId, Long id) {
        String key = NOTICE_MESSAGE_CODE + accountId;
        String hk = id.toString();
        return (NoticeMessageDO) redisTemplate.opsForHash().get(key, hk);
    }

    public boolean hasNoticeMessage(Long accountId, Long id) {
        String key = NOTICE_MESSAGE_CODE + accountId;
        String hk = id.toString();
        return redisTemplate.opsForHash().hasKey(key, hk);
    }

    public PageResult<NoticeMessageDO> getNoticeMessagePage(Long accountId, NoticeMessagePageReqVO reqVO) {
        return PageResult.empty();
    }

    public boolean hasAccountMessage(Long toAccount) {
        return redisTemplate.hasKey(NOTICE_MESSAGE_CODE + toAccount);
    }
}

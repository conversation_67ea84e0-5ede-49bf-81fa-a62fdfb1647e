package com.fozmo.ym.module.notice.dal.redis.template;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplatePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fozmo.ym.module.notice.constans.NoticeRedisConstans.NOTICE_TEMPLATE_CODE;

@Repository
@Slf4j
public class NoticeTemplateRedisDao {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    // 添加通知类型
    public void addNoticeTemplate(NoticeTemplateDO NoticeTemplateDO) {
        String hk = NoticeTemplateDO.getTemplateId().toString();
        redisTemplate.opsForHash().put(NOTICE_TEMPLATE_CODE, hk, NoticeTemplateDO);
    }

    // 将通知类型添加到Redis中
    public void removeNoticeTemplate(Long id) {
        String hk = id.toString();
        redisTemplate.opsForHash().delete(NOTICE_TEMPLATE_CODE, hk);
    }

    // 根据id从Redis中删除通知类型
    public NoticeTemplateDO getNoticeTemplate(Long id) {
        String hk = id.toString();
        return (NoticeTemplateDO) redisTemplate.opsForHash().get(NOTICE_TEMPLATE_CODE, hk);
    }

    // 根据id从Redis中获取通知类型
    public boolean hasNoticeTemplate(Long id) {
        String hk = id.toString();
        return redisTemplate.opsForHash().hasKey(NOTICE_TEMPLATE_CODE, hk);
    }

    // 分页查询通知类型
    public PageResult<NoticeTemplateDO> getNoticeTemplatePage(NoticeTemplatePageReqVO reqVO) {
        Integer pageNo = reqVO.getPageNo();
        Integer pageSize = reqVO.getPageSize();

        PageResult<NoticeTemplateDO> pageResult = new PageResult<>();

        HashOperations<String, String, NoticeTemplateDO> hashOps = redisTemplate.opsForHash();

        // 3. 优化扫描参数
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(100) // 减少每批数量，降低内存压力
                .build();

        List<NoticeTemplateDO> NoticeTemplateList = new ArrayList<>();

        try (Cursor<Map.Entry<String, NoticeTemplateDO>> cursor = hashOps.scan(NOTICE_TEMPLATE_CODE, scanOptions)) {
            while (cursor.hasNext()) {
                NoticeTemplateDO NoticeTemplate = cursor.next().getValue();
                NoticeTemplateList.add(NoticeTemplate);
            }
        } catch (Exception e) {
            log.error("扫描异常: {}", e.getMessage());
        }

        if (ObjectUtil.isNotEmpty(NoticeTemplateList)) {
            int start = Math.max(0, (pageNo - 1) * pageSize);
            int end = Math.min(start + pageSize, NoticeTemplateList.size());

            pageResult.setTotal((long) NoticeTemplateList.size());
            pageResult.setList(NoticeTemplateList.subList(start, end));
        }
        return pageResult;
    }
}

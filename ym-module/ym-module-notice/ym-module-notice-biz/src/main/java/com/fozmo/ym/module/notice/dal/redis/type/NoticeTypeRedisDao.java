package com.fozmo.ym.module.notice.dal.redis.type;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypePageReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.type.NoticeTypeDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fozmo.ym.module.notice.constans.NoticeRedisConstans.NOTICE_TYPE_CODE;

@Repository
@Slf4j
public class NoticeTypeRedisDao {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    // 添加通知类型
    public void addNoticeType(NoticeTypeDO noticeTypeDO) {
        String hk = noticeTypeDO.getId().toString();
        redisTemplate.opsForHash().put(NOTICE_TYPE_CODE, hk, noticeTypeDO);
    }

    // 将通知类型添加到Redis中
    public void removeNoticeType(Long id) {
        String hk = id.toString();
        redisTemplate.opsForHash().delete(NOTICE_TYPE_CODE, hk);
    }

    // 根据id从Redis中删除通知类型
    public NoticeTypeDO getNoticeType(Long id) {
        String hk = id.toString();
        return (NoticeTypeDO) redisTemplate.opsForHash().get(NOTICE_TYPE_CODE, hk);
    }

    // 根据id从Redis中获取通知类型
    public boolean hasNoticeType(Long id) {
        String hk = id.toString();
        return redisTemplate.opsForHash().hasKey(NOTICE_TYPE_CODE, hk);
    }

    // 分页查询通知类型
    public PageResult<NoticeTypeDO> getNoticeTypePage(NoticeTypePageReqVO reqVO) {
        Integer pageNo = reqVO.getPageNo();
        Integer pageSize = reqVO.getPageSize();

        PageResult<NoticeTypeDO> pageResult = new PageResult<>();

        HashOperations<String, String, NoticeTypeDO> hashOps = redisTemplate.opsForHash();

        // 3. 优化扫描参数
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(100) // 减少每批数量，降低内存压力
                .build();

        List<NoticeTypeDO> noticeTypeList = new ArrayList<>();

        try (Cursor<Map.Entry<String, NoticeTypeDO>> cursor = hashOps.scan(NOTICE_TYPE_CODE, scanOptions)) {
            while (cursor.hasNext()) {
                NoticeTypeDO noticeType = cursor.next().getValue();
                noticeTypeList.add(noticeType);
            }
        } catch (Exception e) {
            log.error("扫描异常: {}", e.getMessage());
        }

        if (ObjectUtil.isNotEmpty(noticeTypeList)) {
            int start = Math.max(0, (pageNo - 1) * pageSize);
            int end = Math.min(start + pageSize, noticeTypeList.size());

            pageResult.setTotal((long) noticeTypeList.size());
            pageResult.setList(noticeTypeList.subList(start, end));
        }
        return pageResult;
    }

}

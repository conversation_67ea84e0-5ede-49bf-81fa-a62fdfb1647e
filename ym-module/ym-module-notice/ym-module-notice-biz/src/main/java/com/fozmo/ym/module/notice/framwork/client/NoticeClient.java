package com.fozmo.ym.module.notice.framwork.client;

import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;

/**
 * 通知客户端接口
 *
 * @param <Config> 配置类型，必须实现Serializable接口
 * <AUTHOR>
 */
public interface NoticeClient<Config extends java.io.Serializable> {

    /**
     * 发送通知
     *
     * @param noticeSendMessage 通知消息
     * @throws Exception 发送异常
     */
    void sendNotice(NoticeSendMessage noticeSendMessage) throws Exception;

    /**
     * 获取渠道信息
     *
     * @return 渠道ID
     */
    Long getNoticeChannelId();

    /**
     * 获得渠道配置
     *
     * @return 渠道配置
     */
    Config getConfig();

    /**
     * 验证配置是否有效
     *
     * @param config 配置对象
     * @return 验证结果
     */
    default boolean validateConfig(Config config) {
        return config != null;
    }

    /**
     * 获取客户端类型
     *
     * @return 客户端类型
     */
    default String getClientType() {
        return this.getClass().getSimpleName();
    }
}

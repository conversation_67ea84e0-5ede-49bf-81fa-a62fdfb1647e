package com.fozmo.ym.module.notice.framwork.client;

import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;

/***
 *
 *  站内信模块客户端工厂
 * <AUTHOR>
 */
public interface NoticeClientFactory {

    /***
     *  根据频道ID获取NoticeClient
     */
    NoticeClient getNoticeClient(Long channelId);

    /**
     * 根据频道编码获取NoticeClient
     */
    NoticeClient getNoticeClient(String channelCode);

    /**
     * 创建或更新NoticeClient
     */
    NoticeClient createOrUpdateNoticeClient(NoticeChannelDO noticeChannelDO);



}

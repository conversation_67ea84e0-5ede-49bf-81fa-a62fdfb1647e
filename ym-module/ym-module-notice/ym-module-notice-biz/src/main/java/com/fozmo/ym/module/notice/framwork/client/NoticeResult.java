package com.fozmo.ym.module.notice.framwork.client;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 通知发送结果
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class NoticeResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 日志ID
     */
    private Long logId;
    
    /**
     * 消息ID
     */
    private Long messageId;
    
    /**
     * 扩展信息
     */
    private Object extra;
    
    /**
     * 创建成功结果
     */
    public static NoticeResult success() {
        return new NoticeResult()
                .setSuccess(true)
                .setSendTime(LocalDateTime.now());
    }
    
    /**
     * 创建成功结果
     */
    public static NoticeResult success(Long logId, Long messageId) {
        return new NoticeResult()
                .setSuccess(true)
                .setSendTime(LocalDateTime.now())
                .setLogId(logId)
                .setMessageId(messageId);
    }
    
    /**
     * 创建失败结果
     */
    public static NoticeResult failure(String errorCode, String errorMessage) {
        return new NoticeResult()
                .setSuccess(false)
                .setErrorCode(errorCode)
                .setErrorMessage(errorMessage)
                .setSendTime(LocalDateTime.now());
    }
    
    /**
     * 创建失败结果
     */
    public static NoticeResult failure(String errorMessage) {
        return failure("SEND_FAILED", errorMessage);
    }
}

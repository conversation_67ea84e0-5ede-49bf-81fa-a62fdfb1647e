package com.fozmo.ym.module.notice.framwork.client.impl;

import com.fozmo.ym.module.notice.framwork.client.NoticeClient;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Slf4j
/**
 * 通知客户端的抽象类，提供模板方法，减少子类的冗余代码
 *
 * <AUTHOR>
 */
public abstract class AbstractNoticeClient<Config extends Serializable> implements NoticeClient<Config> {
    /**
     * 渠道编号
     */
    private final Long channelId;
    /**
     * 渠道编码
     */
    @SuppressWarnings("FieldCanBeLocal")
    private final String channelCode;
    /**
     * 支付配置
     */
    protected Config config;

    public AbstractNoticeClient(Long channelId, String channelCode, Config config) {
        this.channelId = channelId;
        this.channelCode = channelCode;
        this.config = config;
    }

    /**
     * 站内信发送逻辑
     */
    public abstract void sendNotice(NoticeSendMessage noticeSendMessage);

    /**
     * 初始化
     */
    public final void init() {
        doInit();
        log.debug("[init][客户端({}) 初始化完成]", getNoticeChannelId());
    }

    /**
     * 自定义初始化
     */
    protected abstract void doInit();

    public final void refresh(Config config) {
        // 判断是否更新
        if (config.equals(this.config)) {
            return;
        }
        log.info("[refresh][客户端({})发生变化，重新初始化]", getConfig());
        this.config = config;
        // 初始化
        this.init();
    }

    @Override
    public Long getNoticeChannelId() {
        return channelId;
    }


}

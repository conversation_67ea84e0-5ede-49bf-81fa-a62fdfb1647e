package com.fozmo.ym.module.notice.framwork.client.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.module.notice.framwork.client.NoticeClient;
import com.fozmo.ym.module.notice.framwork.client.NoticeClientFactory;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 默认通知客户端实现
 * 通过工厂模式委托给具体的客户端实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class DefaultNoticeClient implements NoticeClient<Serializable> {
    
    private final NoticeClientFactory noticeClientFactory;
    
    public DefaultNoticeClient(NoticeClientFactory noticeClientFactory) {
        this.noticeClientFactory = noticeClientFactory;
    }
    
    @Override
    public void sendNotice(NoticeSendMessage noticeSendMessage) throws Exception {
        if (ObjectUtil.isEmpty(noticeSendMessage)) {
            log.warn("NoticeSendMessage is null");
            return;
        }
        
        Long channelId = noticeSendMessage.getChannelId();
        if (ObjectUtil.isEmpty(channelId)) {
            log.warn("ChannelId is null in NoticeSendMessage");
            return;
        }
        
        // 通过工厂获取具体的客户端实现
        NoticeClient actualClient = noticeClientFactory.getNoticeClient(channelId);
        if (actualClient == null) {
            log.error("No notice client found for channelId: {}", channelId);
            throw new Exception("No notice client found for channelId: " + channelId);
        }
        
        // 委托给具体的客户端实现
        try {
            actualClient.sendNotice(noticeSendMessage);
            log.info("Notice sent successfully via client: {}", actualClient.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("Failed to send notice via channelId: {}", channelId, e);
            throw e;
        }
    }
    
    @Override
    public Long getNoticeChannelId() {
        // 默认客户端没有固定的渠道ID
        return null;
    }
    
    @Override
    public Serializable getConfig() {
        // 默认客户端没有配置
        return null;
    }
    
    @Override
    public boolean validateConfig(Serializable config) {
        // 默认验证通过
        return true;
    }
    
    @Override
    public String getClientType() {
        return "DEFAULT";
    }
}

package com.fozmo.ym.module.notice.framwork.client.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;
import com.fozmo.ym.module.notice.framwork.client.NoticeClient;
import com.fozmo.ym.module.notice.framwork.client.NoticeClientFactory;
import com.fozmo.ym.module.notice.service.Noticechannel.NoticeChannelService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知客户端工厂实现类
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class NoticeClientFactoryImpl implements NoticeClientFactory {
    
    /**
     * 客户端缓存
     * key: channelId, value: NoticeClient
     */
    private final ConcurrentHashMap<Long, NoticeClient> clientCache = new ConcurrentHashMap<>();
    
    /**
     * 渠道编码到ID的映射
     */
    private final ConcurrentHashMap<String, Long> channelCodeToIdMap = new ConcurrentHashMap<>();
    
    @Resource
    private NoticeChannelService noticeChannelService;

    @Autowired
    private ApplicationContext applicationContext;
    
    @Override
    public NoticeClient getNoticeClient(Long channelId) {
        if (ObjectUtil.isEmpty(channelId)) {
            log.warn("channelId is null");
            return null;
        }
        
        NoticeClient client = clientCache.get(channelId);
        if (client == null) {
            // 从数据库加载渠道信息并创建客户端
            NoticeChannelDO channelDO = noticeChannelService.getChannel(channelId);
            if (channelDO != null) {
                client = createOrUpdateNoticeClient(channelDO);
            }
        }
        return client;
    }
    
    @Override
    public NoticeClient getNoticeClient(String channelCode) {
        if (ObjectUtil.isEmpty(channelCode)) {
            log.warn("channelCode is null");
            return null;
        }
        
        Long channelId = channelCodeToIdMap.get(channelCode);
        if (channelId != null) {
            return getNoticeClient(channelId);
        }
        
        // TODO: 根据channelCode查询数据库获取channelId
        log.warn("Channel not found for code: {}", channelCode);
        return null;
    }
    
    @Override
    public NoticeClient createOrUpdateNoticeClient(NoticeChannelDO noticeChannelDO) {
        if (ObjectUtil.isEmpty(noticeChannelDO) || ObjectUtil.isEmpty(noticeChannelDO.getChannelId())) {
            log.warn("NoticeChannelDO or channelId is null");
            return null;
        }
        
        try {
            NoticeClient client = createClientByType(noticeChannelDO);
            if (client != null) {
                // 缓存客户端
                clientCache.put(noticeChannelDO.getChannelId(), client);
                channelCodeToIdMap.put(noticeChannelDO.getChannelCode(), noticeChannelDO.getChannelId());
                
                // 初始化客户端
                if (client instanceof AbstractNoticeClient) {
                    ((AbstractNoticeClient<?>) client).init();
                }
                
                log.info("Created notice client for channel: {}", noticeChannelDO.getChannelId());
            }
            return client;
        } catch (Exception e) {
            log.error("Failed to create notice client for channel: {}", noticeChannelDO.getChannelId(), e);
            return null;
        }
    }
    
    /**
     * 根据渠道类型创建对应的客户端
     */
    private NoticeClient createClientByType(NoticeChannelDO channelDO) {
        Long channelId = channelDO.getChannelId();

        if (channelId == 1) {
            NoticeSystemClient systemClient = new NoticeSystemClient(
                    channelDO.getChannelId(),
                    channelDO.getChannelCode(),
                    channelDO
            );
            // 注入Spring依赖
            systemClient.setSpringDependencies(applicationContext);
            return systemClient;
        } else if (channelId == 2) {
            return new NoticeMaClient(
                    channelDO.getChannelId(),
                    channelDO.getChannelCode(),
                    channelDO
            );
        }
        log.warn("Unsupported channel type: {}", channelId);
        return null;
    }
    
    /**
     * 移除客户端缓存
     */
    public void removeClient(Long channelId) {
        clientCache.remove(channelId);
        // 同时移除编码映射
        channelCodeToIdMap.entrySet().removeIf(entry -> entry.getValue().equals(channelId));
    }
    
    /**
     * 清空所有缓存
     */
    public void clearCache() {
        clientCache.clear();
        channelCodeToIdMap.clear();
    }
}

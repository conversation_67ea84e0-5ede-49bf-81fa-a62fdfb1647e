package com.fozmo.ym.module.notice.framwork.client.impl;

import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Slf4j

public class NoticeMaClient extends AbstractNoticeClient {
    public NoticeMaClient(Long channelId, String channelCode, Object o) {
        super(channelId, channelCode, (Serializable) o);
    }

    /**
     * @param noticeSendMessage
     */
    @Override
    public void sendNotice(NoticeSendMessage noticeSendMessage) throws Exception {
        log.info("NoticeMaClient sending notice: {}", noticeSendMessage);
        // TODO: 实现邮件发送逻辑
    }

    /**
     * @return 
     */
    @Override
    public Serializable getConfig() {
        return null;
    }

    /**
     * @param serializable 配置对象 
     * @return
     */
    @Override
    public boolean validateConfig(Serializable serializable) {
        return super.validateConfig(serializable);
    }

    /**
     * @return 
     */
    @Override
    public String getClientType() {
        return super.getClientType();
    }

    /**
     *
     */
    @Override
    protected void doInit() {

    }
}

package com.fozmo.ym.module.notice.framwork.client.impl;

import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j

public class NoticeMaClient extends AbstractNoticeClient {
    public NoticeMaClient(Long channelId, String channelCode, Object o) {
        super(channelId, channelCode, o);
    }

    /**
     * @param noticeSendMessage
     */
    @Override
    public void sendNotice(NoticeSendMessage noticeSendMessage) {

    }

    /**
     *
     */
    @Override
    protected void doInit() {

    }
}

package com.fozmo.ym.module.notice.framwork.client.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.util.json.JsonUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogSaveReqVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import com.fozmo.ym.module.notice.service.Noticechannel.NoticeChannelService;
import com.fozmo.ym.module.notice.service.log.NoticeLogService;
import com.fozmo.ym.module.notice.service.message.NoticeMessageService;
import com.fozmo.ym.module.notice.service.template.NoticeTemplateService;
import jakarta.annotation.Resource;

import java.io.Serializable;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.notice.enums.ErrorCodeConstants.NOTICE_TEMPLATE_NOT_EXISTS;

public class NoticeSystemClient  extends AbstractNoticeClient{

    @Resource
    private NoticeLogService noticeLogService;

    @Resource
    private NoticeTemplateService noticeTemplateService;
    @Resource
    private NoticeChannelService noticeChannelService;
    @Resource
    private NoticeMessageService noticeMessageService;

    @Resource
    private AccountApi accountApi;

    public NoticeSystemClient(Long channelId, String channelCode, Object o) {
        super(channelId, channelCode, (Serializable) o);
    }

    /**
     * @param noticeSendMessage
     */
    @Override
    public void sendNotice(NoticeSendMessage noticeSendMessage) {
        // 1 创建发送日志
        Long logId = sendNoticeLog(noticeSendMessage);

        // 2发送消息
        sendNoticeMessage(noticeSendMessage,logId);
    }

    /**
     * @return 
     */
    @Override
    public Serializable getConfig() {
        return null;
    }

    /**
     * @param serializable 配置对象 
     * @return
     */
    @Override
    public boolean validateConfig(Serializable serializable) {
        return super.validateConfig(serializable);
    }

    /**
     * @return 
     */
    @Override
    public String getClientType() {
        return super.getClientType();
    }

    private void sendNoticeMessage(NoticeSendMessage noticeSendMessage,Long logId) {

        NoticeTemplateDO noticeTemplateDO = getTemplateDO(noticeSendMessage);

        NoticeMessageSaveReqVO noticeMessageSaveReqVO = new NoticeMessageSaveReqVO();
        noticeMessageSaveReqVO.setLogId(logId);
        noticeMessageSaveReqVO.setTemplateId(noticeTemplateDO.getTemplateId());
        noticeMessageSaveReqVO.setSendApp(noticeSendMessage.getSendApp());
        noticeMessageSaveReqVO.setSendResource(noticeSendMessage.getSendResource());
        noticeMessageSaveReqVO.setSendParams(JsonUtils.toJsonString(noticeSendMessage.getParams()));
        noticeMessageSaveReqVO.setIsRead(0);
        if (ObjectUtil.isNotEmpty(noticeTemplateDO)) {
            noticeMessageSaveReqVO.setTypePid(noticeTemplateDO.getTypePid());
            noticeMessageSaveReqVO.setTypeId(noticeTemplateDO.getTypeId());

            if (noticeTemplateDO.getSendType()==-1){
                noticeMessageSaveReqVO.setAccountIds(getAllUser());
            }

            if (noticeTemplateDO.getSendType()==1){
                noticeMessageSaveReqVO.setAccountIds(noticeSendMessage.getToUser());
            }

            if (noticeTemplateDO.getSendType()==0){
                noticeMessageSaveReqVO.setAccountIds(noticeSendMessage.getToUser());
            }
        }



        noticeMessageService.createMessage(noticeMessageSaveReqVO);
    }

    private Long sendNoticeLog(NoticeSendMessage noticeSendMessage) {
        NoticeLogSaveReqVO noticeLogSaveReqVO = new NoticeLogSaveReqVO();
        noticeLogSaveReqVO.setChannelId(noticeSendMessage.getChannelId());
        noticeLogSaveReqVO.setTemplateId(noticeSendMessage.getTemplateId());
        NoticeTemplateDO noticeTemplateDO = getTemplateDO(noticeSendMessage);
        if (ObjectUtil.isNotEmpty(noticeTemplateDO)) {
            noticeLogSaveReqVO.setTypePid(noticeTemplateDO.getTypePid());
            noticeLogSaveReqVO.setSendApp(noticeSendMessage.getSendApp());
            noticeLogSaveReqVO.setTypeId(noticeTemplateDO.getTypeId());
            if (noticeTemplateDO.getSendType()==-1){
                noticeLogSaveReqVO.setToUser(JsonUtils.toJsonString(getAllUser()));
            }

        }
        noticeLogSaveReqVO.setSendResource(noticeSendMessage.getSendResource());
        return noticeLogService.createLog(noticeLogSaveReqVO);
    }

    private NoticeTemplateDO getTemplateDO(NoticeSendMessage noticeSendMessage) {
        Long templateId = noticeSendMessage.getTemplateId();
        if (ObjectUtil.isNotEmpty(templateId)) {
            return noticeTemplateService.getTemplate(templateId);
        }
        throw exception(NOTICE_TEMPLATE_NOT_EXISTS);

    }

    private List<Long> getAllUser(){
       return accountApi.getAccountId();
    }

    private String processContent(String content, String type) {
        if ("image".equals(type)) {
            return content;
        }
        return content;
    }



    /**
     *
     */
    @Override
    protected void doInit() {
    }
}

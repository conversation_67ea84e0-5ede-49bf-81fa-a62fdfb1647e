package com.fozmo.ym.module.notice.framwork.client.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.util.json.JsonUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogSaveReqVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import com.fozmo.ym.module.notice.framwork.util.NoticeTemplateProcessor;
import com.fozmo.ym.module.notice.service.Noticechannel.NoticeChannelService;
import com.fozmo.ym.module.notice.service.log.NoticeLogService;
import com.fozmo.ym.module.notice.service.message.NoticeMessageService;
import com.fozmo.ym.module.notice.service.template.NoticeTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.io.Serializable;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.notice.enums.ErrorCodeConstants.NOTICE_TEMPLATE_NOT_EXISTS;

@Slf4j
public class NoticeSystemClient extends AbstractNoticeClient<Serializable> {

    private NoticeLogService noticeLogService;
    private NoticeTemplateService noticeTemplateService;
    private NoticeChannelService noticeChannelService;
    private NoticeMessageService noticeMessageService;
    private AccountApi accountApi;

    public NoticeSystemClient(Long channelId, String channelCode, Object o) {
        super(channelId, channelCode, (Serializable) o);
    }

    /**
     * 设置Spring依赖（通过ApplicationContext获取）
     */
    public void setSpringDependencies(ApplicationContext applicationContext) {
        this.noticeLogService = applicationContext.getBean(NoticeLogService.class);
        this.noticeTemplateService = applicationContext.getBean(NoticeTemplateService.class);
        this.noticeChannelService = applicationContext.getBean(NoticeChannelService.class);
        this.noticeMessageService = applicationContext.getBean(NoticeMessageService.class);
        this.accountApi = applicationContext.getBean(AccountApi.class);
    }

    /**
     * @param noticeSendMessage
     */
    @Override
    public void sendNotice(NoticeSendMessage noticeSendMessage) throws Exception {

        // 内容 处理
        NoticeTemplateDO noticeTemplateDO = getTemplateDO(noticeSendMessage);
        String content = processContent(noticeTemplateDO.getContent(), noticeTemplateDO.getParams(), noticeSendMessage.getParams());

        // 1 创建发送日志
        Long logId = sendNoticeLog(noticeSendMessage, noticeTemplateDO, content);

        // 2发送消息
        sendNoticeMessage(noticeSendMessage, logId, noticeTemplateDO, content);


    }

    /**
     * @return
     */
    @Override
    public Serializable getConfig() {
        return null;
    }

    /**
     * @param serializable 配置对象
     * @return
     */
    @Override
    public boolean validateConfig(Serializable serializable) {
        return super.validateConfig(serializable);
    }

    /**
     * @return
     */
    @Override
    public String getClientType() {
        return super.getClientType();
    }

    private void sendNoticeMessage(NoticeSendMessage noticeSendMessage, Long logId, NoticeTemplateDO noticeTemplateDO, String content) {


        NoticeMessageSaveReqVO noticeMessageSaveReqVO = new NoticeMessageSaveReqVO();
        noticeMessageSaveReqVO.setLogId(logId);
        noticeMessageSaveReqVO.setTemplateId(noticeTemplateDO.getTemplateId());
        noticeMessageSaveReqVO.setSendApp(noticeSendMessage.getSendApp());
        noticeMessageSaveReqVO.setSendResource(noticeSendMessage.getSendResource());
        noticeMessageSaveReqVO.setSendParams(JsonUtils.toJsonString(noticeSendMessage.getParams()));
        noticeMessageSaveReqVO.setIsRead(0);
        noticeMessageSaveReqVO.setSendUserInfo(JsonUtils.toJsonString(noticeSendMessage.getSendUserInfo()));
        noticeMessageSaveReqVO.setSendObjectInfo(JsonUtils.toJsonString(noticeSendMessage.getSendObjectInfo()));
        if (ObjectUtil.isNotEmpty(noticeTemplateDO)) {
            noticeMessageSaveReqVO.setTypePid(noticeTemplateDO.getTypePid());
            noticeMessageSaveReqVO.setTypeId(noticeTemplateDO.getTypeId());

            if (noticeTemplateDO.getSendType() == -1) {
                noticeMessageSaveReqVO.setAccountIds(getAllUser());
            }

            if (noticeTemplateDO.getSendType() == 1) {
                noticeMessageSaveReqVO.setAccountIds(noticeSendMessage.getToUser());
            }

            if (noticeTemplateDO.getSendType() == 0) {
                noticeMessageSaveReqVO.setAccountIds(noticeSendMessage.getToUser());
            }

            noticeMessageSaveReqVO.setSendContent(content);
        }


        noticeMessageService.createMessage(noticeMessageSaveReqVO);
    }

    private Long sendNoticeLog(NoticeSendMessage noticeSendMessage, NoticeTemplateDO noticeTemplateDO, String content) {
        NoticeLogSaveReqVO noticeLogSaveReqVO = new NoticeLogSaveReqVO();
        noticeLogSaveReqVO.setChannelId(noticeSendMessage.getChannelId());
        noticeLogSaveReqVO.setTemplateId(noticeSendMessage.getTemplateId());
        if (ObjectUtil.isNotEmpty(noticeTemplateDO)) {
            noticeLogSaveReqVO.setTypePid(noticeTemplateDO.getTypePid());
            noticeLogSaveReqVO.setSendApp(noticeSendMessage.getSendApp());
            noticeLogSaveReqVO.setTypeId(noticeTemplateDO.getTypeId());
            if (noticeTemplateDO.getSendType() == -1) {
                noticeLogSaveReqVO.setToUser(JsonUtils.toJsonString(getAllUser()));
            } else {
                noticeLogSaveReqVO.setToUser(JsonUtils.toJsonString(noticeSendMessage.getToUser()));
            }

        }
        noticeLogSaveReqVO.setSendResource(noticeSendMessage.getSendResource());
        noticeLogSaveReqVO.setSendContent(content);
        return noticeLogService.createLog(noticeLogSaveReqVO);
    }

    private NoticeTemplateDO getTemplateDO(NoticeSendMessage noticeSendMessage) {
        Long templateId = noticeSendMessage.getTemplateId();
        if (ObjectUtil.isNotEmpty(templateId)) {
            return noticeTemplateService.getTemplate(templateId);
        }
        throw exception(NOTICE_TEMPLATE_NOT_EXISTS);

    }

    private List<Long> getAllUser() {
        return accountApi.getAccountId();
    }

    /**
     * 处理内容模板，替换占位符
     *
     * @param content                 模板内容，如：用户：{nickName}，点赞了你的作品:{spaceName}
     * @param params                  模板参数JSON字符串，如：["nickName","spaceName"]
     * @param noticeSendMessageParams 实际参数值列表，如：["张三","我的作品"]
     * @return 替换后的内容
     */
    private String processContent(String content, String params, List<String> noticeSendMessageParams) {
        return NoticeTemplateProcessor.processContent(content, params, noticeSendMessageParams);
    }

    @Override
    protected void doInit() {
    }
}

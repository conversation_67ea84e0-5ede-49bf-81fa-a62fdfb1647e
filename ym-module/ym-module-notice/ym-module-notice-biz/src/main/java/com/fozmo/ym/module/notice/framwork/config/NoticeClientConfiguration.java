package com.fozmo.ym.module.notice.framwork.config;

import com.fozmo.ym.module.notice.framwork.client.NoticeClient;
import com.fozmo.ym.module.notice.framwork.client.NoticeClientFactory;
import com.fozmo.ym.module.notice.framwork.client.impl.DefaultNoticeClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 通知客户端配置类
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class NoticeClientConfiguration {

    /**
     * 默认的NoticeClient Bean
     * 当没有其他NoticeClient实现时，使用此默认实现
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "primaryNoticeClient")
    public NoticeClient primaryNoticeClient(NoticeClientFactory noticeClientFactory) {
        log.info("Creating primary NoticeClient bean");
        return new DefaultNoticeClient(noticeClientFactory);
    }
}

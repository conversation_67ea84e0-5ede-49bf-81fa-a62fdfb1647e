package com.fozmo.ym.module.notice.framwork.mq;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import com.fozmo.ym.module.notice.framwork.client.NoticeClient;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
@Slf4j
public class NoticeConsumer extends AbstractRedisStreamMessageListener<NoticeSendMessage> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Resource
    private NoticeClient noticeClient;

    /**
     * @param message 消息
     */
    @Override
    public void onMessage(NoticeSendMessage message) {
        // 处理消息
        try {
            processMessage(message);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void processMessage(NoticeSendMessage message) throws Exception {
        noticeClient.sendNotice(message);
    }
}

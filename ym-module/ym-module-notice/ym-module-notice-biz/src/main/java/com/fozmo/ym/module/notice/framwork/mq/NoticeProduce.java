package com.fozmo.ym.module.notice.framwork.mq;

import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.notice.framwork.mq.message.NoticeSendMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *  站内信发送的 MQ 生产者
 */
@Slf4j
// 标记为Spring组件
@Component
public class NoticeProduce {


    @Resource
    private RedisMQTemplate redisMQTemplate; // 重点：注入 RedisMQTemplate 对象

    public void sendNotice(Long channelId, Long templateId, List<String> params, String sendApp, String sendResource) {
        // 创建站内信发送的 MQ 消息
        NoticeSendMessage noticeSendMessage = new NoticeSendMessage()
                .setChannelId(channelId)
                .setTemplateId(templateId)
                .setParams(params)
                .setSendApp(sendApp)
                .setSendResource(sendResource);


        redisMQTemplate.send(noticeSendMessage);
    }

}

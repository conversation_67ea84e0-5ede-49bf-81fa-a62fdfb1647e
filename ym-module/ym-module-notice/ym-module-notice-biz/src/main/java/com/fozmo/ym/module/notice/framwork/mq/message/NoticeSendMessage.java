package com.fozmo.ym.module.notice.framwork.mq.message;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class NoticeSendMessage extends AbstractRedisStreamMessage {

    private Long channelId;

    private Long templateId;

    private List<String> params;

    private String sendResource;

    private String sendApp;

    private List<Long> toUser;

    private Map sendUserInfo;

    private Map sendObjectInfo;
}

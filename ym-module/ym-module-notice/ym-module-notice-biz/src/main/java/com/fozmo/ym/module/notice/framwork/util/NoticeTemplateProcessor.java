package com.fozmo.ym.module.notice.framwork.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fozmo.ym.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通知模板处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class NoticeTemplateProcessor {

    /**
     * 占位符正则表达式：匹配 {参数名}
     */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    /**
     * 处理模板内容，替换占位符
     *
     * @param content        模板内容，如：用户：{nickName}，点赞了你的作品:{spaceName}
     * @param templateParams 模板参数JSON字符串，如：["nickName","spaceName"]
     * @param messageParams  实际参数值列表，如：["张三","我的作品"]
     * @return 替换后的内容
     */
    public static String processContent(String content, String templateParams, List<String> messageParams) {
        if (StrUtil.isEmpty(content)) {
            return content;
        }

        try {
            // 解析模板参数
            List<String> paramNames = JsonUtils.parseArray(templateParams, String.class);

            if (ObjectUtil.isEmpty(paramNames) || ObjectUtil.isEmpty(messageParams)) {
                log.warn("Template params or message params is empty");
                return content;
            }

            // 创建参数映射
            Map<String, String> paramMap = createParamMap(paramNames, messageParams);

            // 替换占位符
            return replacePlaceholders(content, paramMap);

        } catch (Exception e) {
            log.error("Failed to process content template: {}", content, e);
            return content; // 返回原内容，避免异常
        }
    }

    /**
     * 处理模板内容，使用Map参数
     *
     * @param content  模板内容
     * @param paramMap 参数映射
     * @return 替换后的内容
     */
    public static String processContent(String content, Map<String, String> paramMap) {
        if (StrUtil.isEmpty(content) || ObjectUtil.isEmpty(paramMap)) {
            return content;
        }

        try {
            return replacePlaceholders(content, paramMap);
        } catch (Exception e) {
            log.error("Failed to process content template: {}", content, e);
            return content;
        }
    }

    /**
     * 创建参数映射
     */
    private static Map<String, String> createParamMap(List<String> paramNames, List<String> paramValues) {
        Map<String, String> paramMap = new HashMap<>();

        int size = Math.min(paramNames.size(), paramValues.size());
        for (int i = 0; i < size; i++) {
            String paramName = paramNames.get(i);
            String paramValue = paramValues.get(i);

            if (StrUtil.isNotEmpty(paramName) && paramValue != null) {
                paramMap.put(paramName, paramValue);
            }
        }

        return paramMap;
    }

    /**
     * 替换占位符
     */
    private static String replacePlaceholders(String content, Map<String, String> paramMap) {
        if (StrUtil.isEmpty(content) || ObjectUtil.isEmpty(paramMap)) {
            return content;
        }

        StringBuffer result = new StringBuffer();
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(content);

        while (matcher.find()) {
            String paramName = matcher.group(1); // 获取占位符中的参数名
            String paramValue = paramMap.get(paramName);

            if (paramValue != null) {
                // 替换占位符为实际值
                matcher.appendReplacement(result, Matcher.quoteReplacement(paramValue));
                log.debug("Replaced placeholder {{{}}}} with value: {}", paramName, paramValue);
            } else {
                // 保留原占位符
                matcher.appendReplacement(result, Matcher.quoteReplacement(matcher.group(0)));
                log.warn("No value found for placeholder: {{{}}}", paramName);
            }
        }

        matcher.appendTail(result);

        String finalResult = result.toString();
        log.debug("Template processed: {} -> {}", content, finalResult);

        return finalResult;
    }

    /**
     * 提取模板中的所有占位符
     *
     * @param content 模板内容
     * @return 占位符列表
     */
    public static List<String> extractPlaceholders(String content) {
        if (StrUtil.isEmpty(content)) {
            return List.of();
        }

        Matcher matcher = PLACEHOLDER_PATTERN.matcher(content);
        return matcher.results()
                .map(matchResult -> matchResult.group(1))
                .distinct()
                .toList();
    }

    /**
     * 验证模板参数是否完整
     *
     * @param content  模板内容
     * @param paramMap 参数映射
     * @return 验证结果
     */
    public static boolean validateTemplate(String content, Map<String, String> paramMap) {
        List<String> placeholders = extractPlaceholders(content);

        for (String placeholder : placeholders) {
            if (!paramMap.containsKey(placeholder) || paramMap.get(placeholder) == null) {
                log.warn("Missing parameter for placeholder: {{{}}}", placeholder);
                return false;
            }
        }

        return true;
    }
}

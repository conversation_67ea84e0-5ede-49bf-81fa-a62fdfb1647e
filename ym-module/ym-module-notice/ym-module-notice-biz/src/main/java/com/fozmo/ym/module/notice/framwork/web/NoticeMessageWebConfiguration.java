//package com.fozmo.ym.module.notice.framwork.web;
//
//import com.fozmo.ym.framework.swagger.config.YmSwaggerAutoConfiguration;
//import org.springdoc.core.models.GroupedOpenApi;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
/// **
// * 站内信 模块的 web 组件的 Configuration
// *
// * <AUTHOR>
// */
//@Configuration(proxyBeanMethods = false)
//public class NoticeMessageWebConfiguration {
//
//    /**
//     * ai 模块的 API 分组
//     */
//    @Bean
//    public GroupedOpenApi noticeMessageWebConfiguration() {
//        return YmSwaggerAutoConfiguration.buildGroupedOpenApi("notice");
//    }
//
//}

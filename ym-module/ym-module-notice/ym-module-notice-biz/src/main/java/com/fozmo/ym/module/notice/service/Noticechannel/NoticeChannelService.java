package com.fozmo.ym.module.notice.service.Noticechannel;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelPageReqVO;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 站内信发送通道 Service 接口
 *
 * <AUTHOR>
 */
public interface NoticeChannelService {

    /**
     * 创建站内信发送通道
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createChannel(@Valid NoticeChannelSaveReqVO createReqVO);

    /**
     * 更新站内信发送通道
     *
     * @param updateReqVO 更新信息
     */
    void updateChannel(@Valid NoticeChannelSaveReqVO updateReqVO);

    /**
     * 删除站内信发送通道
     *
     * @param id 编号
     */
    void deleteChannel(Long id);

    /**
     * 批量删除站内信发送通道
     *
     * @param ids 编号
     */
    void deleteChannelListByIds(List<Long> ids);

    /**
     * 获得站内信发送通道
     *
     * @param id 编号
     * @return 站内信发送通道
     */
    NoticeChannelDO getChannel(Long id);

    /**
     * 获得站内信发送通道分页
     *
     * @param pageReqVO 分页查询
     * @return 站内信发送通道分页
     */
    PageResult<NoticeChannelDO> getChannelPage(NoticeChannelPageReqVO pageReqVO);

}
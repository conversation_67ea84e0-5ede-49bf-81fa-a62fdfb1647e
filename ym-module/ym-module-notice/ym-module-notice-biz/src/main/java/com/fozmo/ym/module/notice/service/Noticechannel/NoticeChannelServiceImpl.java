package com.fozmo.ym.module.notice.service.Noticechannel;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelPageReqVO;
import com.fozmo.ym.module.notice.controller.admin.Noticechannel.vo.NoticeChannelSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.Noticechannel.NoticeChannelDO;
import com.fozmo.ym.module.notice.dal.mysql.Noticechannel.NoticeChannelMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.notice.enums.ErrorCodeConstants.NOTICE_CHANNEL_NOT_EXISTS;

/**
 * 站内信发送通道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeChannelServiceImpl implements NoticeChannelService {

    @Resource
    private NoticeChannelMapper channelMapper;

    @Override
    public Long createChannel(NoticeChannelSaveReqVO createReqVO) {
        // 插入
        NoticeChannelDO channel = BeanUtils.toBean(createReqVO, NoticeChannelDO.class);
        channelMapper.insert(channel);
        // 返回
        return channel.getChannelId();
    }

    @Override
    public void updateChannel(NoticeChannelSaveReqVO updateReqVO) {
        // 校验存在
        validateChannelExists(updateReqVO.getChannelId());
        // 更新
        NoticeChannelDO updateObj = BeanUtils.toBean(updateReqVO, NoticeChannelDO.class);
        channelMapper.updateById(updateObj);
    }

    @Override
    public void deleteChannel(Long id) {
        // 校验存在
        validateChannelExists(id);
        // 删除
        channelMapper.deleteById(id);
    }

    @Override
    public void deleteChannelListByIds(List<Long> ids) {
        // 校验存在
        validateChannelExists(ids);
        // 删除
        channelMapper.deleteByIds(ids);
    }

    private void validateChannelExists(List<Long> ids) {
        List<NoticeChannelDO> list = channelMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(NOTICE_CHANNEL_NOT_EXISTS);
        }
    }

    private void validateChannelExists(Long id) {
        if (channelMapper.selectById(id) == null) {
            throw exception(NOTICE_CHANNEL_NOT_EXISTS);
        }
    }

    @Override
    public NoticeChannelDO getChannel(Long id) {
        return channelMapper.selectById(id);
    }

    @Override
    public PageResult<NoticeChannelDO> getChannelPage(NoticeChannelPageReqVO pageReqVO) {
        return channelMapper.selectPage(pageReqVO);
    }

}
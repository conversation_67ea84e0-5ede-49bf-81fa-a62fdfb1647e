package com.fozmo.ym.module.notice.service.log;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogPageReqVO;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.log.NoticeLogDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 通知日志 Service 接口
 *
 * <AUTHOR>
 */
public interface NoticeLogService {

    /**
     * 创建通知日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLog(@Valid NoticeLogSaveReqVO createReqVO);

    /**
     * 更新通知日志
     *
     * @param updateReqVO 更新信息
     */
    void updateLog(@Valid NoticeLogSaveReqVO updateReqVO);

    /**
     * 删除通知日志
     *
     * @param id 编号
     */
    void deleteLog(Long id);

    /**
     * 批量删除通知日志
     *
     * @param ids 编号
     */
    void deleteLogListByIds(List<Long> ids);

    /**
     * 获得通知日志
     *
     * @param id 编号
     * @return 通知日志
     */
    NoticeLogDO getLog(Long id);

    /**
     * 获得通知日志分页
     *
     * @param pageReqVO 分页查询
     * @return 通知日志分页
     */
    PageResult<NoticeLogDO> getLogPage(NoticeLogPageReqVO pageReqVO);

}
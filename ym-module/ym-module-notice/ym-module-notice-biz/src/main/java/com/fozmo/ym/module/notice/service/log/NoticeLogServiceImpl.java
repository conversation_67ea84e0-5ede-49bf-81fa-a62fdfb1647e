package com.fozmo.ym.module.notice.service.log;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogPageReqVO;
import com.fozmo.ym.module.notice.controller.admin.log.vo.NoticeLogSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.log.NoticeLogDO;
import com.fozmo.ym.module.notice.dal.mysql.log.NoticeLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeLogServiceImpl implements NoticeLogService {

    @Resource
    private NoticeLogMapper logMapper;
    @Resource
    private IdService idService;

    @Override
    public Long createLog(NoticeLogSaveReqVO createReqVO) {
        // 插入
        NoticeLogDO log = BeanUtils.toBean(createReqVO, NoticeLogDO.class);

        log.setLogId(idService.nextId("notice_log"));
        log.setTenantId(1L);
        log.setTenantCode("ym");
        log.setCreateData(LocalDate.now());
        log.setCreateTime(LocalDateTime.now());

        logMapper.insert(log);
        // 返回
        return log.getLogId();
    }

    @Override
    public void updateLog(NoticeLogSaveReqVO updateReqVO) {
        // 校验存在
        validateLogExists(updateReqVO.getLogId());
        // 更新
        NoticeLogDO updateObj = BeanUtils.toBean(updateReqVO, NoticeLogDO.class);
        logMapper.updateById(updateObj);
    }

    @Override
    public void deleteLog(Long id) {
        // 校验存在
        validateLogExists(id);
        // 删除
        logMapper.deleteById(id);
    }

    @Override
    public void deleteLogListByIds(List<Long> ids) {
        // 校验存在
        validateLogExists(ids);
        // 删除
        logMapper.deleteByIds(ids);
    }

    private void validateLogExists(List<Long> ids) {
        List<NoticeLogDO> list = logMapper.selectByIds(ids);
    }

    private void validateLogExists(Long id) {
    }

    @Override
    public NoticeLogDO getLog(Long id) {
        return logMapper.selectById(id);
    }

    @Override
    public PageResult<NoticeLogDO> getLogPage(NoticeLogPageReqVO pageReqVO) {
        return logMapper.selectPage(pageReqVO);
    }

}
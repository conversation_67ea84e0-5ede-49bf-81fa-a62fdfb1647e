package com.fozmo.ym.module.notice.service.message;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 站内信信息 Service 接口
 *
 * <AUTHOR>
 */
public interface NoticeMessageService {

    /**
     * 创建站内信信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMessage(@Valid NoticeMessageSaveReqVO createReqVO);

    /**
     * 更新站内信信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMessage(@Valid NoticeMessageSaveReqVO updateReqVO);

    /**
     * 删除站内信信息
     *
     * @param id 编号
     */
    void deleteMessage(Long id);

    /**
     * 批量删除站内信信息
     *
     * @param ids 编号
     */
    void deleteMessageListByIds(List<Long> ids);

    /**
     * 获得站内信信息
     *
     * @param id 编号
     * @return 站内信信息
     */
    NoticeMessageDO getMessage(Long id);

    /**
     * 获得站内信信息分页
     *
     * @param pageReqVO 分页查询
     * @return 站内信信息分页
     */
    PageResult<NoticeMessageDO> getMessagePage(NoticeMessagePageReqVO pageReqVO);

}
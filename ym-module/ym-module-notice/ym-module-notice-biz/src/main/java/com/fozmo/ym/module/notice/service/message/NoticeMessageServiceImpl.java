package com.fozmo.ym.module.notice.service.message;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessagePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper;
import com.fozmo.ym.module.notice.dal.redis.message.NoticeMessageRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 站内信信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeMessageServiceImpl implements NoticeMessageService {

    @Resource
    private NoticeMessageMapper messageMapper;
    @Resource
    private IdService idService;

    @Resource
    private NoticeMessageRedisDao messageRedisDao;

    @Override
    public Long createMessage(NoticeMessageSaveReqVO createReqVO) {
        // 插入

        List<NoticeMessageDO> messageList = new ArrayList<>();
        List<Long> accountIdList = createReqVO.getAccountIds();
        if (CollUtil.isEmpty(accountIdList)) {
            return null;
        } else {

            LocalDateTime nowTime = LocalDateTime.now();
            LocalDate nowDate = LocalDate.now();
            for (Long accountId : accountIdList) {
                NoticeMessageDO message = BeanUtils.toBean(createReqVO, NoticeMessageDO.class);
                message.setId(idService.nextId("notice_message"));
                message.setTenantId(1L);
                message.setCreateId(1L);
                message.setCreateData(nowDate);
                message.setUpdateData(nowDate);
                message.setDeleted(false);
                message.setSendTime(nowTime);
                message.setTenantCode("ym");
                message.setToAccount(accountId);
                messageList.add(message);

                messageRedisDao.addNoticeMessage(message);
            }
        }

        if (CollUtil.isNotEmpty(messageList)) {
            messageMapper.insertBatch(messageList);
        }
        return 0L;
    }

    @Override
    public void updateMessage(NoticeMessageSaveReqVO updateReqVO) {
        // 校验存在
        validateMessageExists(updateReqVO.getId());
        // 更新
        NoticeMessageDO updateObj = BeanUtils.toBean(updateReqVO, NoticeMessageDO.class);
        messageMapper.updateById(updateObj);
    }

    @Override
    public void deleteMessage(Long id) {
        // 校验存在
        validateMessageExists(id);
        // 删除
        messageMapper.deleteById(id);
    }

    @Override
    public void deleteMessageListByIds(List<Long> ids) {
        // 校验存在
        validateMessageExists(ids);
        // 删除
        messageMapper.deleteByIds(ids);
    }

    private void validateMessageExists(List<Long> ids) {
        List<NoticeMessageDO> list = messageMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {

        }
    }

    private void validateMessageExists(Long id) {
        if (messageMapper.selectById(id) == null) {

        }
    }

    @Override
    public NoticeMessageDO getMessage(Long id) {
        return messageMapper.selectById(id);
    }

    @Override
    public PageResult<NoticeMessageDO> getMessagePage(NoticeMessagePageReqVO pageReqVO) {

        // 分页查询 redis
        if (messageRedisDao.hasAccountMessage(pageReqVO.getToAccount())) {
            return messageRedisDao.getNoticeMessagePage(pageReqVO.getToAccount(), pageReqVO);
        }
        return messageMapper.selectPage(pageReqVO);
    }

}
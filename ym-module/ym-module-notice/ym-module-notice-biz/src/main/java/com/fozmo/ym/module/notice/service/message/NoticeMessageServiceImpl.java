package com.fozmo.ym.module.notice.service.message;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.message.vo.NoticeMessageSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.message.NoticeMessageDO;
import com.fozmo.ym.module.notice.dal.mysql.message.NoticeMessageMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 站内信信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeMessageServiceImpl implements NoticeMessageService {

    @Resource
    private NoticeMessageMapper messageMapper;

    @Override
    public Long createMessage(NoticeMessageSaveReqVO createReqVO) {
        // 插入
        NoticeMessageDO message = BeanUtils.toBean(createReqVO, NoticeMessageDO.class);
        messageMapper.insert(message);
        // 返回
        return message.getId();
    }

    @Override
    public void updateMessage(NoticeMessageSaveReqVO updateReqVO) {
        // 校验存在
        validateMessageExists(updateReqVO.getId());
        // 更新
        NoticeMessageDO updateObj = BeanUtils.toBean(updateReqVO, NoticeMessageDO.class);
        messageMapper.updateById(updateObj);
    }

    @Override
    public void deleteMessage(Long id) {
        // 校验存在
        validateMessageExists(id);
        // 删除
        messageMapper.deleteById(id);
    }

    @Override
    public void deleteMessageListByIds(List<Long> ids) {
        // 校验存在
        validateMessageExists(ids);
        // 删除
        messageMapper.deleteByIds(ids);
    }

    private void validateMessageExists(List<Long> ids) {
        List<NoticeMessageDO> list = messageMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(MESSAGE_NOT_EXISTS);
        }
    }

    private void validateMessageExists(Long id) {
        if (messageMapper.selectById(id) == null) {
            throw exception(MESSAGE_NOT_EXISTS);
        }
    }

    @Override
    public NoticeMessageDO getMessage(Long id) {
        return messageMapper.selectById(id);
    }

    @Override
    public PageResult<NoticeMessageDO> getMessagePage(NoticeMessagePageReqVO pageReqVO) {
        return messageMapper.selectPage(pageReqVO);
    }

}
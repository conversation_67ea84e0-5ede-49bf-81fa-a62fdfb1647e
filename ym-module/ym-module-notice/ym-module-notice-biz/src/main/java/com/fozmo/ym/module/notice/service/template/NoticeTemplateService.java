package com.fozmo.ym.module.notice.service.template;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplatePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplateSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 站内信模板 Service 接口
 *
 * <AUTHOR>
 */
public interface NoticeTemplateService {

    /**
     * 创建站内信模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplate(@Valid NoticeTemplateSaveReqVO createReqVO);

    /**
     * 更新站内信模板
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplate(@Valid NoticeTemplateSaveReqVO updateReqVO);

    /**
     * 删除站内信模板
     *
     * @param id 编号
     */
    void deleteTemplate(Long id);

    /**
     * 批量删除站内信模板
     *
     * @param ids 编号
     */
    void deleteTemplateListByIds(List<Long> ids);

    /**
     * 获得站内信模板
     *
     * @param id 编号
     * @return 站内信模板
     */
    NoticeTemplateDO getTemplate(Long id);

    /**
     * 获得站内信模板分页
     *
     * @param pageReqVO 分页查询
     * @return 站内信模板分页
     */
    PageResult<NoticeTemplateDO> getTemplatePage(NoticeTemplatePageReqVO pageReqVO);

}
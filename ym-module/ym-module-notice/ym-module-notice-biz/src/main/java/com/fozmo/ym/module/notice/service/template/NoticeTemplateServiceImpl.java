package com.fozmo.ym.module.notice.service.template;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplatePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.template.vo.NoticeTemplateSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.template.NoticeTemplateDO;
import com.fozmo.ym.module.notice.dal.mysql.template.NoticeTemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.notice.enums.ErrorCodeConstants.NOTICE_TEMPLATE_NOT_EXISTS;

/**
 * 站内信模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeTemplateServiceImpl implements NoticeTemplateService {

    @Resource
    private NoticeTemplateMapper templateMapper;

    @Override
    public Long createTemplate(NoticeTemplateSaveReqVO createReqVO) {
        // 插入
        NoticeTemplateDO template = BeanUtils.toBean(createReqVO, NoticeTemplateDO.class);
        templateMapper.insert(template);
        // 返回
        return template.getTemplateId();
    }

    @Override
    public void updateTemplate(NoticeTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateTemplateExists(updateReqVO.getTemplateId());
        // 更新
        NoticeTemplateDO updateObj = BeanUtils.toBean(updateReqVO, NoticeTemplateDO.class);
        templateMapper.updateById(updateObj);
    }

    @Override
    public void deleteTemplate(Long id) {
        // 校验存在
        validateTemplateExists(id);
        // 删除
        templateMapper.deleteById(id);
    }

    @Override
    public void deleteTemplateListByIds(List<Long> ids) {
        // 校验存在
        validateTemplateExists(ids);
        // 删除
        templateMapper.deleteByIds(ids);
    }

    private void validateTemplateExists(List<Long> ids) {
        List<NoticeTemplateDO> list = templateMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(NOTICE_TEMPLATE_NOT_EXISTS);
        }
    }

    private void validateTemplateExists(Long id) {
        if (templateMapper.selectById(id) == null) {
            throw exception(NOTICE_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public NoticeTemplateDO getTemplate(Long id) {
        return templateMapper.selectById(id);
    }

    @Override
    public PageResult<NoticeTemplateDO> getTemplatePage(NoticeTemplatePageReqVO pageReqVO) {
        return templateMapper.selectPage(pageReqVO);
    }

}
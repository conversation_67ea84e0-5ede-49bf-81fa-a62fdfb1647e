package com.fozmo.ym.module.notice.service.type;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypeSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.type.NoticeTypeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 站内信类型 Service 接口
 *
 * <AUTHOR>
 */
public interface NoticeTypeService {

    /**
     * 创建站内信类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createType(@Valid NoticeTypeSaveReqVO createReqVO);

    /**
     * 更新站内信类型
     *
     * @param updateReqVO 更新信息
     */
    void updateType(@Valid NoticeTypeSaveReqVO updateReqVO);

    /**
     * 删除站内信类型
     *
     * @param id 编号
     */
    void deleteType(Long id);

    /**
     * 批量删除站内信类型
     *
     * @param ids 编号
     */
    void deleteTypeListByIds(List<Long> ids);

    /**
     * 获得站内信类型
     *
     * @param id 编号
     * @return 站内信类型
     */
    NoticeTypeDO getType(Long id);

    /**
     * 获得站内信类型分页
     *
     * @param pageReqVO 分页查询
     * @return 站内信类型分页
     */
    PageResult<NoticeTypeDO> getTypePage(NoticeTypePageReqVO pageReqVO);

}
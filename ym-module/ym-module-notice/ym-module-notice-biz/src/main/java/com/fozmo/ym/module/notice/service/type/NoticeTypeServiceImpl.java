package com.fozmo.ym.module.notice.service.type;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypePageReqVO;
import com.fozmo.ym.module.notice.controller.admin.type.vo.NoticeTypeSaveReqVO;
import com.fozmo.ym.module.notice.dal.dataobject.type.NoticeTypeDO;
import com.fozmo.ym.module.notice.dal.mysql.type.NoticeTypeMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.notice.enums.ErrorCodeConstants.NOTICE_TYPE_NOT_EXISTS;


/**
 * 站内信类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeTypeServiceImpl implements NoticeTypeService {

    @Resource
    private NoticeTypeMapper typeMapper;

    @Override
    public Long createType(NoticeTypeSaveReqVO createReqVO) {
        // 插入
        NoticeTypeDO type = BeanUtils.toBean(createReqVO, NoticeTypeDO.class);
        typeMapper.insert(type);
        // 返回
        return type.getId();
    }

    @Override
    public void updateType(NoticeTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateTypeExists(updateReqVO.getId());
        // 更新
        NoticeTypeDO updateObj = BeanUtils.toBean(updateReqVO, NoticeTypeDO.class);
        typeMapper.updateById(updateObj);
    }

    @Override
    public void deleteType(Long id) {
        // 校验存在
        validateTypeExists(id);
        // 删除
        typeMapper.deleteById(id);
    }

    @Override
    public void deleteTypeListByIds(List<Long> ids) {
        // 校验存在
        validateTypeExists(ids);
        // 删除
        typeMapper.deleteByIds(ids);
    }

    private void validateTypeExists(List<Long> ids) {
        List<NoticeTypeDO> list = typeMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(NOTICE_TYPE_NOT_EXISTS);
        }
    }

    private void validateTypeExists(Long id) {
        if (typeMapper.selectById(id) == null) {
            throw exception(NOTICE_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public NoticeTypeDO getType(Long id) {
        return typeMapper.selectById(id);
    }

    @Override
    public PageResult<NoticeTypeDO> getTypePage(NoticeTypePageReqVO pageReqVO) {
        return typeMapper.selectPage(pageReqVO);
    }

}
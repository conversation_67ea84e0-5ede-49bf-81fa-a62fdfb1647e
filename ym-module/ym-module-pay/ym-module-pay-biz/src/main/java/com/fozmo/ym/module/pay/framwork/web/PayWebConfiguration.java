package com.fozmo.ym.module.pay.framwork.web;

import com.fozmo.ym.framework.swagger.config.YmSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付模块 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class PayWebConfiguration {

    /**
     * ai 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi payWebConfiguration() {
        return YmSwaggerAutoConfiguration.buildGroupedOpenApi("pay");
    }

}

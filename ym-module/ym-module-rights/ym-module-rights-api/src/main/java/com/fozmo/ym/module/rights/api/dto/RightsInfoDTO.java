package com.fozmo.ym.module.rights.api.dto;

import lombok.Data;

@Data
public class RightsInfoDTO {

    private Long id;

    private String name;

    private Integer level;

    private Integer flag;

    private Integer rightsStatus;

    private Long ruleId;
    /**
     * 规则名称
     */
    private String RuleName;
    /**
     * -1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推
     */
    private Integer humanRights;
    /**
     * 权益id
     */
    private Long rightsId;
    /**
     * 空间数量
     */
    private Integer spaceRights;
    /**
     * 作品数量
     */
    private Integer worksRights;
    /**
     * 注册元宝50
     */
    private Integer registerPoint;
    /**
     * vip1 100 vip200
     */
    private Integer rightsPoint;
    /**
     * 游客-1 用户0 vip1 1 vip2 2
     */
    private Integer templateRights;
    /**
     * 0 系统初始化 1其他
     */
    private Integer type;
    /**
     * 0 正常 9 停用
     */
    private Integer ruleStatus;
    /**
     * -1
     */
    private Integer musicRights;
    /**
     * -1 权益!K36
     */
    private Integer aiRights;
    /**
     * 权重：游客0 用户10 vip1 100 vip2 1000
     */
    private Integer weight;
}

package com.fozmo.ym.module.rights.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.rights.api.dto.RightsInfoDTO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsInfoRespVO;
import com.fozmo.ym.module.rights.service.rights.RightsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class RightsApiImpl implements RightsApi {

    @Resource
    private RightsService rightsService;
    /**
     */
    @Override
    public RightsInfoDTO getRightsInfo(Long rightsId) {
        RightsInfoRespVO respVO = rightsService.getRights(rightsId);
        respVO.setRightsId(rightsId);
        return BeanUtils.toBean(respVO, RightsInfoDTO.class);
    }
}

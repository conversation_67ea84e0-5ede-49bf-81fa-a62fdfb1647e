package com.fozmo.ym.module.rights.controller.admin.rights;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageParam;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsInfoRespVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsPageReqVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsRespVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsSaveReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rights.RightsDO;
import com.fozmo.ym.module.rights.service.rights.RightsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台- 权益模块-权益")
@RestController
@RequestMapping("/rights/rights")
@Validated
public class RightsController {

    @Resource
    private RightsService rightsService;

    @PostMapping("/create")
    @Operation(summary = "创建权益")
    @PreAuthorize("@ss.hasPermission('rights:rights:create')")
    public CommonResult<Long> createRights(@Valid @RequestBody RightsSaveReqVO createReqVO) {
        return success(rightsService.createRights(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新权益")
    @PreAuthorize("@ss.hasPermission('rights:rights:update')")
    public CommonResult<Boolean> updateRights(@Valid @RequestBody RightsSaveReqVO updateReqVO) {
        rightsService.updateRights(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除权益")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('rights:rights:delete')")
    public CommonResult<Boolean> deleteRights(@RequestParam("id") Long id) {
        rightsService.deleteRights(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得权益")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('rights:rights:query')")
    public CommonResult<RightsInfoRespVO> getRights(@RequestParam("id") Long id) {
        RightsInfoRespVO rights = rightsService.getRights(id);
        return success(rights);
    }

    @GetMapping("/page")
    @Operation(summary = "获得权益分页")
    @PreAuthorize("@ss.hasPermission('rights:rights:query')")
    public CommonResult<PageResult<RightsRespVO>> getRightsPage(@Valid RightsPageReqVO pageReqVO) {
        PageResult<RightsDO> pageResult = rightsService.getRightsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RightsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出权益 Excel")
    @PreAuthorize("@ss.hasPermission('rights:rights:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRightsExcel(@Valid RightsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RightsDO> list = rightsService.getRightsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "权益.xls", "数据", RightsRespVO.class,
                        BeanUtils.toBean(list, RightsRespVO.class));
    }

}
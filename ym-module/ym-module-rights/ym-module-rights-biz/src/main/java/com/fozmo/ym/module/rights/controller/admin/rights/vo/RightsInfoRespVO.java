package com.fozmo.ym.module.rights.controller.admin.rights.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "权益详情返回")
@Accessors(chain = true)
@Data
public class RightsInfoRespVO {

    @Schema(description = "权益id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31072")
    private Long id;

    @Schema(description = "权益名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String name;

    @Schema(description = "-1 游客 0 用户 1 vip1 等", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;

    @Schema(description = "-1 游客无 0 用户空白 1 vip 蓝V vip2 紫V", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer flag;

    @Schema(description = "0正常 9停用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer rightsStatus;

    @Schema(description = "权益规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31072")
    private Long ruleId;
    /**
     * 规则名称
     */
    private String RuleName;
    /**
     * -1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推
     */
    private Integer humanRights;
    /**
     * 权益id
     */
    private Long rightsId;
    /**
     * 空间数量
     */
    private Integer spaceRights;
    /**
     * 作品数量
     */
    private Integer worksRights;
    /**
     * 注册元宝50
     */
    private Integer registerPoint;
    /**
     * vip1 100 vip200
     */
    private Integer rightsPoint;
    /**
     * 游客-1 用户0 vip1 1 vip2 2
     */
    private Integer templateRights;
    /**
     * 0 系统初始化 1其他
     */
    private Integer type;
    /**
     * 0 正常 9 停用
     */
    private Integer ruleStatus;
    /**
     * -1
     */
    private Integer musicRights;
    /**
     * -1 权益!K36
     */
    private Integer aiRights;
    /**
     * 权重：游客0 用户10 vip1 100 vip2 1000
     */
    private Integer weight;
}

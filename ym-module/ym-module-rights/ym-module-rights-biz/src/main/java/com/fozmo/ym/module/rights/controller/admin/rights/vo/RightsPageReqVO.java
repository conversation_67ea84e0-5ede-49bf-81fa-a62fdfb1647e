package com.fozmo.ym.module.rights.controller.admin.rights.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 权益分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RightsPageReqVO extends PageParam {

    @Schema(description = "权益名称", example = "王五")
    private String name;

    @Schema(description = "-1 游客 0 用户 1 vip1 等")
    private Integer level;

    @Schema(description = "-1 游客无 0 用户空白 1 vip 蓝V vip2 紫V")
    private Integer flag;

    @Schema(description = "0正常 9停用", example = "2")
    private Integer rightsStatus;

    @Schema(description = "创建id", example = "19521")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人id", example = "24415")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码")
    private String tenantCode;

}
package com.fozmo.ym.module.rights.controller.admin.rights.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 权益 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class RightsRespVO {

    @Schema(description = "权益id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31072")
    @ExcelProperty("权益id")
    private Long id;

    @Schema(description = "权益名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("权益名称")
    private String name;

    @Schema(description = "-1 游客 0 用户 1 vip1 等", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("-1 游客 0 用户 1 vip1 等")
    private Integer level;

    @Schema(description = "-1 游客无 0 用户空白 1 vip 蓝V vip2 紫V", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("-1 游客无 0 用户空白 1 vip 蓝V vip2 紫V")
    private Integer flag;

    @Schema(description = "0正常 9停用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("0正常 9停用")
    private Integer rightsStatus;

    @Schema(description = "创建id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19521")
    @ExcelProperty("创建id")
    private Long createId;

    @Schema(description = "创建日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人id", example = "24415")
    @ExcelProperty("更新人id")
    private Long updaterId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("租户码")
    private String tenantCode;

}
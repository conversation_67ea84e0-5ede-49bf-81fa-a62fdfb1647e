package com.fozmo.ym.module.rights.controller.admin.rights.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 权益新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class RightsSaveReqVO {

    @Schema(description = "权益id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31072")
    private Long id;

    @Schema(description = "权益名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "权益名称不能为空")
    private String name;

    @Schema(description = "-1 游客 0 用户 1 vip1 等", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "-1 游客 0 用户 1 vip1 等不能为空")
    private Integer level;

    @Schema(description = "-1 游客无 0 用户空白 1 vip 蓝V vip2 紫V", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "-1 游客无 0 用户空白 1 vip 蓝V vip2 紫V不能为空")
    private Integer flag;

    @Schema(description = "0正常 9停用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "0正常 9停用不能为空")
    private Integer rightsStatus;

    @Schema(description = "创建id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19521")
    @NotNull(message = "创建id不能为空")
    private Long createId;

    @Schema(description = "创建日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "创建日期不能为空")
    private LocalDate createData;

    @Schema(description = "更新人id", example = "24415")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "租户码不能为空")
    private String tenantCode;

}
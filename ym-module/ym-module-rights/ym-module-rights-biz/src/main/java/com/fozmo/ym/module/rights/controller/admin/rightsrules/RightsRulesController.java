package com.fozmo.ym.module.rights.controller.admin.rightsrules;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageParam;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesPageReqVO;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesRespVO;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesSaveReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rightsrules.RightsRulesDO;
import com.fozmo.ym.module.rights.service.rightsrules.RightsRulesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台- 权益模块-权益规则")
@RestController
@RequestMapping("/rights/rules")
@Validated
public class RightsRulesController {

    @Resource
    private RightsRulesService rulesService;

    @PostMapping("/create")
    @Operation(summary = "创建权益规则")
    @PreAuthorize("@ss.hasPermission('rights:rules:create')")
    public CommonResult<Long> createRules(@Valid @RequestBody RightsRulesSaveReqVO createReqVO) {
        return success(rulesService.createRules(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新权益规则")
    @PreAuthorize("@ss.hasPermission('rights:rules:update')")
    public CommonResult<Boolean> updateRules(@Valid @RequestBody RightsRulesSaveReqVO updateReqVO) {
        rulesService.updateRules(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除权益规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('rights:rules:delete')")
    public CommonResult<Boolean> deleteRules(@RequestParam("id") Long id) {
        rulesService.deleteRules(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得权益规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('rights:rules:query')")
    public CommonResult<RightsRulesRespVO> getRules(@RequestParam("id") Long id) {
        RightsRulesDO rules = rulesService.getRules(id);
        return success(BeanUtils.toBean(rules, RightsRulesRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得权益规则分页")
    @PreAuthorize("@ss.hasPermission('rights:rules:query')")
    public CommonResult<PageResult<RightsRulesRespVO>> getRulesPage(@Valid RightsRulesPageReqVO pageReqVO) {
        PageResult<RightsRulesDO> pageResult = rulesService.getRulesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RightsRulesRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出权益规则 Excel")
    @PreAuthorize("@ss.hasPermission('rights:rules:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRulesExcel(@Valid RightsRulesPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RightsRulesDO> list = rulesService.getRulesPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "权益规则.xls", "数据", RightsRulesRespVO.class,
                        BeanUtils.toBean(list, RightsRulesRespVO.class));
    }

}
package com.fozmo.ym.module.rights.controller.admin.rightsrules.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 权益规则分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RightsRulesPageReqVO extends PageParam {

    @Schema(description = "规则名称", example = "李四")
    private String name;

    @Schema(description = "-1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推")
    private Integer humanRights;

    @Schema(description = "权益id", example = "32354")
    private Long rightsId;

    @Schema(description = "空间数量")
    private Integer spaceRights;

    @Schema(description = "作品数量")
    private Integer worksRights;

    @Schema(description = "注册元宝50")
    private Integer registerPoint;

    @Schema(description = "vip1 100 vip200")
    private Integer rightsPoint;

    @Schema(description = "游客-1 用户0 vip1 1 vip2 2")
    private Integer templateRights;

    @Schema(description = "0 系统初始化 1其他", example = "1")
    private Integer type;

    @Schema(description = "0 正常 9 停用", example = "2")
    private Integer ruleStatus;

    @Schema(description = "-1")
    private Integer musicRights;

    @Schema(description = "-1 权益!K36")
    private Integer aiRights;

    @Schema(description = "权重：游客0 用户10 vip1 100 vip2 1000")
    private Integer weight;

    @Schema(description = "创建id", example = "13375")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人id", example = "220")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码")
    private String tenantCode;

}
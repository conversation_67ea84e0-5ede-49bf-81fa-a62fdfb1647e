package com.fozmo.ym.module.rights.controller.admin.rightsrules.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 权益规则 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class RightsRulesRespVO {

    @Schema(description = "规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27195")
    @ExcelProperty("规则id")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("规则名称")
    private String name;

    @Schema(description = "-1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("-1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推")
    private Integer humanRights;

    @Schema(description = "权益id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32354")
    @ExcelProperty("权益id")
    private Long rightsId;

    @Schema(description = "空间数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("空间数量")
    private Integer spaceRights;

    @Schema(description = "作品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("作品数量")
    private Integer worksRights;

    @Schema(description = "注册元宝50", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("注册元宝50")
    private Integer registerPoint;

    @Schema(description = "vip1 100 vip200", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("vip1 100 vip200")
    private Integer rightsPoint;

    @Schema(description = "游客-1 用户0 vip1 1 vip2 2", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("游客-1 用户0 vip1 1 vip2 2")
    private Integer templateRights;

    @Schema(description = "0 系统初始化 1其他", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("0 系统初始化 1其他")
    private Integer type;

    @Schema(description = "0 正常 9 停用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("0 正常 9 停用")
    private Integer ruleStatus;

    @Schema(description = "-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("-1")
    private Integer musicRights;

    @Schema(description = "-1 权益!K36", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("-1 权益!K36")
    private Integer aiRights;

    @Schema(description = "权重：游客0 用户10 vip1 100 vip2 1000", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("权重：游客0 用户10 vip1 100 vip2 1000")
    private Integer weight;

    @Schema(description = "创建id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13375")
    @ExcelProperty("创建id")
    private Long createId;

    @Schema(description = "创建日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人id", example = "220")
    @ExcelProperty("更新人id")
    private Long updaterId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("租户码")
    private String tenantCode;

}
package com.fozmo.ym.module.rights.controller.admin.rightsrules.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 权益规则新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class RightsRulesSaveReqVO {

    @Schema(description = "规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27195")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "规则名称不能为空")
    private String name;

    @Schema(description = "-1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "-1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推不能为空")
    private Integer humanRights;

    @Schema(description = "权益id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32354")
    @NotNull(message = "权益id不能为空")
    private Long rightsId;

    @Schema(description = "空间数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "空间数量不能为空")
    private Integer spaceRights;

    @Schema(description = "作品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "作品数量不能为空")
    private Integer worksRights;

    @Schema(description = "注册元宝50", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "注册元宝50不能为空")
    private Integer registerPoint;

    @Schema(description = "vip1 100 vip200", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "vip1 100 vip200不能为空")
    private Integer rightsPoint;

    @Schema(description = "游客-1 用户0 vip1 1 vip2 2", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "游客-1 用户0 vip1 1 vip2 2不能为空")
    private Integer templateRights;

    @Schema(description = "0 系统初始化 1其他", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "0 系统初始化 1其他不能为空")
    private Integer type;

    @Schema(description = "0 正常 9 停用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "0 正常 9 停用不能为空")
    private Integer ruleStatus;

    @Schema(description = "-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "-1不能为空")
    private Integer musicRights;

    @Schema(description = "-1 权益!K36", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "-1 权益!K36不能为空")
    private Integer aiRights;

    @Schema(description = "权重：游客0 用户10 vip1 100 vip2 1000", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "权重：游客0 用户10 vip1 100 vip2 1000不能为空")
    private Integer weight;

    @Schema(description = "创建id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13375")
    @NotNull(message = "创建id不能为空")
    private Long createId;

    @Schema(description = "创建日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "创建日期不能为空")
    private LocalDate createData;

    @Schema(description = "更新人id", example = "220")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "租户码不能为空")
    private String tenantCode;

}
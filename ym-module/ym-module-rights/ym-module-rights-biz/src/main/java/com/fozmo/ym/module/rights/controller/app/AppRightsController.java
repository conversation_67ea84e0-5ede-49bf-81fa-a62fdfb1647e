package com.fozmo.ym.module.rights.controller.app;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsInfoRespVO;
import com.fozmo.ym.module.rights.service.rights.RightsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;


@Tag(name = "APP-权益模块- 权益详情")
@RestController
@RequestMapping("/rights/rights")
@Validated
public class AppRightsController {
	@Resource
	private RightsService rightsService;
	@GetMapping("/get")
	@Operation(summary = "获得权益")
	@Parameter(name = "id", description = "编号", required = true, example = "1024")
	@PermitAll
	@ApiAccessLog
	public CommonResult<RightsInfoRespVO> getRights(@RequestParam("id") Long id) {
		RightsInfoRespVO rights = rightsService.getRights(id);
		return success(rights);
	}
}

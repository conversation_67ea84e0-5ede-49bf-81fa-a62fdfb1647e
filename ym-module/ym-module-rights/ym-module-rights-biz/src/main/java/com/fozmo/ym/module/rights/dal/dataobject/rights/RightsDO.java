package com.fozmo.ym.module.rights.dal.dataobject.rights;

import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.*;
/**
 * 权益 DO
 *
 * <AUTHOR>
 */
@TableName("rights")
@KeySequence("rights_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RightsDO extends TenantBaseDO {

    /**
     * 权益id
     */
    @TableId
    private Long id;
    /**
     * 权益名称
     */
    private String name;
    /**
     * -1 游客 0 用户 1 vip1 等
     */
    private Integer level;
    /**
     * -1 游客无 0 用户空白 1 vip 蓝V vip2 紫V
     */
    private Integer flag;
    /**
     * 0正常 9停用
     */
    private Integer rightsStatus;
    /**
     * 创建id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户码
     */
    private String tenantCode;

}
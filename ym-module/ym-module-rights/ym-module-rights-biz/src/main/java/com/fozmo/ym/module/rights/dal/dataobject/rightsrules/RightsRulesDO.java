package com.fozmo.ym.module.rights.dal.dataobject.rightsrules;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 权益规则 DO
 *
 * <AUTHOR>
 */
@TableName("rights_rules")
@KeySequence("rights_rules_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RightsRulesDO extends TenantBaseDO {

    /**
     * 规则id
     */
    @TableId
    private Long id;
    /**
     * 规则名称
     */
    private String name;
    /**
     * -1无法使用 0 免费 1 vip1 使用0和1 类 vip2 使用vip2 1 和0 以此类推
     */
    private Integer humanRights;
    /**
     * 权益id
     */
    private Long rightsId;
    /**
     * 空间数量
     */
    private Integer spaceRights;
    /**
     * 作品数量
     */
    private Integer worksRights;
    /**
     * 注册元宝50
     */
    private Integer registerPoint;
    /**
     * vip1 100 vip200
     */
    private Integer rightsPoint;
    /**
     * 游客-1 用户0 vip1 1 vip2 2
     */
    private Integer templateRights;
    /**
     * 0 系统初始化 1其他
     */
    private Integer type;
    /**
     * 0 正常 9 停用
     */
    private Integer ruleStatus;
    /**
     * -1
     */
    private Integer musicRights;
    /**
     * -1 权益!K36
     */
    private Integer aiRights;
    /**
     * 权重：游客0 用户10 vip1 100 vip2 1000
     */
    private Integer weight;
    /**
     * 创建id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户码
     */
    private String tenantCode;

}
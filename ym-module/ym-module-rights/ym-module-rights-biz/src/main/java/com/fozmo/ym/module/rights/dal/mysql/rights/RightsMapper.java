package com.fozmo.ym.module.rights.dal.mysql.rights;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsPageReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rights.RightsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 权益 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RightsMapper extends BaseMapperX<RightsDO> {

    default PageResult<RightsDO> selectPage(RightsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RightsDO>()
                .likeIfPresent(RightsDO::getName, reqVO.getName())
                .eqIfPresent(RightsDO::getLevel, reqVO.getLevel())
                .eqIfPresent(RightsDO::getFlag, reqVO.getFlag())
                .eqIfPresent(RightsDO::getRightsStatus, reqVO.getRightsStatus())
                .eqIfPresent(RightsDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(RightsDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(RightsDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(RightsDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(RightsDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(RightsDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(RightsDO::getId));
    }

}
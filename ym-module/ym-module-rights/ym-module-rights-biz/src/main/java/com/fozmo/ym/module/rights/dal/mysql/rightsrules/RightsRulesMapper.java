package com.fozmo.ym.module.rights.dal.mysql.rightsrules;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesPageReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rightsrules.RightsRulesDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 权益规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RightsRulesMapper extends BaseMapperX<RightsRulesDO> {

    default PageResult<RightsRulesDO> selectPage(RightsRulesPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RightsRulesDO>()
                .likeIfPresent(RightsRulesDO::getName, reqVO.getName())
                .eqIfPresent(RightsRulesDO::getHumanRights, reqVO.getHumanRights())
                .eqIfPresent(RightsRulesDO::getRightsId, reqVO.getRightsId())
                .eqIfPresent(RightsRulesDO::getSpaceRights, reqVO.getSpaceRights())
                .eqIfPresent(RightsRulesDO::getWorksRights, reqVO.getWorksRights())
                .eqIfPresent(RightsRulesDO::getRegisterPoint, reqVO.getRegisterPoint())
                .eqIfPresent(RightsRulesDO::getRightsPoint, reqVO.getRightsPoint())
                .eqIfPresent(RightsRulesDO::getTemplateRights, reqVO.getTemplateRights())
                .eqIfPresent(RightsRulesDO::getType, reqVO.getType())
                .eqIfPresent(RightsRulesDO::getRuleStatus, reqVO.getRuleStatus())
                .eqIfPresent(RightsRulesDO::getMusicRights, reqVO.getMusicRights())
                .eqIfPresent(RightsRulesDO::getAiRights, reqVO.getAiRights())
                .eqIfPresent(RightsRulesDO::getWeight, reqVO.getWeight())
                .eqIfPresent(RightsRulesDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(RightsRulesDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(RightsRulesDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(RightsRulesDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(RightsRulesDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(RightsRulesDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(RightsRulesDO::getId));
    }

    default RightsRulesDO queryByRightsId(Long rightsId){
        return selectOne(RightsRulesDO::getRightsId, rightsId,RightsRulesDO::getDeleted,false);
    }
}
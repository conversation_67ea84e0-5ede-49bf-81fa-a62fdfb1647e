package com.fozmo.ym.module.rights.dal.redis.rigths;

import com.fozmo.ym.module.rights.dal.dataobject.rights.RightsDO;
import com.fozmo.ym.module.rights.dal.dataobject.rightsrules.RightsRulesDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import static com.fozmo.ym.module.rights.dal.redis.RightsRedisConstans.RIGHTS_KEY;
import static com.fozmo.ym.module.rights.dal.redis.RightsRedisConstans.RIGHTS_RULE_KEY;


@Repository
public class RightsRedisDao {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    public RightsDO getRightsInfo(Long rightsId) {
        String key = RIGHTS_KEY;
        String hk = rightsId.toString();
        return (RightsDO) redisTemplate.opsForHash().get(key, hk);

    }

    public void saveRightsInfo(RightsDO rightsInfo) {
        String key = RIGHTS_KEY;
        String hk = rightsInfo.getId().toString();
        if (!hasRightInfo(rightsInfo.getId())) {
            redisTemplate.opsForHash().put(key, hk, rightsInfo);
        }

    }

    public void delRightsInfo(Long rightsId) {
        String key = RIGHTS_KEY;
        String hk = rightsId.toString();
        redisTemplate.opsForHash().delete(key, hk);
    }

    public boolean hasRightInfo(Long rightsId) {
        String key = RIGHTS_KEY;
        String hk = rightsId.toString();
        return redisTemplate.opsForHash().hasKey(key, hk);
    }


    public RightsRulesDO getRightsRule(Long ruleId) {
        String key = RIGHTS_RULE_KEY;
        String hk = ruleId.toString();
        return (RightsRulesDO) redisTemplate.opsForHash().get(key, hk);

    }

    public void saveRightsRule(RightsRulesDO rightsRule) {
        String key = RIGHTS_RULE_KEY;
        String hk = rightsRule.getId().toString();
        redisTemplate.opsForHash().put(key, hk, rightsRule);
    }

    public void delRightsRule(Long ruleId) {
        String key = RIGHTS_RULE_KEY;
        String hk = ruleId.toString();
        redisTemplate.opsForHash().delete(key, hk);
    }

    public boolean hasRightRule(Long ruleId) {
        String key = RIGHTS_RULE_KEY;
        String hk = ruleId.toString();
        return redisTemplate.opsForHash().hasKey(key, hk);
    }
}

package com.fozmo.ym.module.rights.framework.web.config;

import com.fozmo.ym.framework.swagger.config.YmSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ai 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class RightsWebConfiguration {

    /**
     * ai 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi rightsGroupedOpenApi() {
        return YmSwaggerAutoConfiguration.buildGroupedOpenApi("rights");
    }

}

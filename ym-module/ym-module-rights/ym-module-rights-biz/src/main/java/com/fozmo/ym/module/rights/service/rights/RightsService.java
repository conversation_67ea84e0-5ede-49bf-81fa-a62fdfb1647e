package com.fozmo.ym.module.rights.service.rights;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsInfoRespVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsPageReqVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsSaveReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rights.RightsDO;
import jakarta.validation.Valid;

/**
 * 权益 Service 接口
 *
 * <AUTHOR>
 */
public interface RightsService {

    /**
     * 创建权益
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRights(@Valid RightsSaveReqVO createReqVO);

    /**
     * 更新权益
     *
     * @param updateReqVO 更新信息
     */
    void updateRights(@Valid RightsSaveReqVO updateReqVO);

    /**
     * 删除权益
     *
     * @param id 编号
     */
    void deleteRights(Long id);

    /**
     * 获得权益
     *
     * @param id 编号
     * @return 权益
     */
    RightsInfoRespVO getRights(Long id);


    RightsDO getRightsInfo(Long id);
    /**
     * 获得权益分页
     *
     * @param pageReqVO 分页查询
     * @return 权益分页
     */
    PageResult<RightsDO> getRightsPage(RightsPageReqVO pageReqVO);

}
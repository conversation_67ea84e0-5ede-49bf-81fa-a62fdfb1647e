package com.fozmo.ym.module.rights.service.rights;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsInfoRespVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsPageReqVO;
import com.fozmo.ym.module.rights.controller.admin.rights.vo.RightsSaveReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rights.RightsDO;
import com.fozmo.ym.module.rights.dal.dataobject.rightsrules.RightsRulesDO;
import com.fozmo.ym.module.rights.dal.mysql.rights.RightsMapper;
import com.fozmo.ym.module.rights.dal.redis.rigths.RightsRedisDao;
import com.fozmo.ym.module.rights.service.rightsrules.RightsRulesService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.rights.enums.ErrorCodeConstants.RIGHTS_NOT_EXISTS;

/**
 * 权益 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RightsServiceImpl implements RightsService {

    @Resource
    private RightsMapper rightsMapper;

    @Resource
    private RightsRulesService rightsRulesService;

    @Resource
    private RightsRedisDao rightsRedisDao;

    @Resource
    private IdService idService;

    @Override
    public Long createRights(RightsSaveReqVO createReqVO) {
        // 插入
        RightsDO rights = BeanUtils.toBean(createReqVO, RightsDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        rights.setCreateId(loginUser.getId());
        rights.setCreator(loginUser.getUsername());
        rights.setCreateData(LocalDate.now());
        rights.setId(idService.nextId("rights"));
        rights.setTenantId(loginUser.getTenantId());
        rightsMapper.insert(rights);
        rightsRedisDao.saveRightsInfo(rights);
        // 返回
        return rights.getId();
    }

    @Override
    public void updateRights(RightsSaveReqVO updateReqVO) {
        if (rightsRedisDao.hasRightInfo(updateReqVO.getId())) {
            rightsRedisDao.delRightsRule(updateReqVO.getId());
        }
        // 更新
        RightsDO updateObj = BeanUtils.toBean(updateReqVO, RightsDO.class);
        rightsMapper.updateById(updateObj);
        rightsRedisDao.saveRightsInfo(getRightsInfo(updateReqVO.getId()));
    }

    @Override
    public void deleteRights(Long id) {
        // 校验存在
        validateRightsExists(id);
        // 删除
        rightsMapper.deleteById(id);
    }

    private void validateRightsExists(Long id) {
        if (rightsMapper.selectById(id) == null) {
            throw exception(RIGHTS_NOT_EXISTS);
        }
    }

    @Override
    public RightsInfoRespVO getRights(Long id) {
        RightsInfoRespVO rightsInfo = new RightsInfoRespVO();
        RightsDO rightsDO = getRightsInfo(id);

        if (ObjectUtil.isNotEmpty(rightsDO)) {
            rightsInfo = BeanUtils.toBean(rightsDO, RightsInfoRespVO.class);

        }

        RightsRulesDO rightsRules = rightsRulesService.getRules(id);
        if (ObjectUtil.isNotEmpty(rightsRules)) {
            rightsInfo.setRuleId(rightsRules.getId());
            rightsInfo.setRuleName(rightsRules.getName());
            rightsInfo.setHumanRights(rightsRules.getHumanRights());
            rightsInfo.setSpaceRights(rightsRules.getSpaceRights());
            rightsInfo.setWorksRights(rightsRules.getWorksRights());
            rightsInfo.setRegisterPoint(rightsRules.getRegisterPoint());
            rightsInfo.setRightsPoint(rightsRules.getRightsPoint());
            rightsInfo.setTemplateRights(rightsRules.getTemplateRights());
            rightsInfo.setRuleStatus(rightsRules.getRuleStatus());
            rightsInfo.setMusicRights(rightsRules.getMusicRights());
            rightsInfo.setAiRights(rightsRules.getAiRights());
            rightsInfo.setWeight(rightsRules.getWeight());
            rightsInfo.setRightsId(rightsDO.getId());
        }
            return rightsInfo;
    }

    /**
     * @param id
     * @return
     */
    @Override
    public RightsDO getRightsInfo(Long id) {

        if (rightsRedisDao.hasRightInfo(id)) {
            return rightsRedisDao.getRightsInfo(id);
        } else {
            RightsDO rights = rightsMapper.selectById(id);
            if (ObjectUtil.isNotEmpty(rights)) {
                rightsRedisDao.saveRightsInfo(rights);
            }
            return rights;
        }
    }

    @Override
    public PageResult<RightsDO> getRightsPage(RightsPageReqVO pageReqVO) {
        return rightsMapper.selectPage(pageReqVO);
    }

}
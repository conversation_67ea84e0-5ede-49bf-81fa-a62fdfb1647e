package com.fozmo.ym.module.rights.service.rightsrules;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesPageReqVO;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesSaveReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rightsrules.RightsRulesDO;
import jakarta.validation.Valid;

/**
 * 权益规则 Service 接口
 *
 * <AUTHOR>
 */
public interface RightsRulesService {

    /**
     * 创建权益规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRules(@Valid RightsRulesSaveReqVO createReqVO);

    /**
     * 更新权益规则
     *
     * @param updateReqVO 更新信息
     */
    void updateRules(@Valid RightsRulesSaveReqVO updateReqVO);

    /**
     * 删除权益规则
     *
     * @param id 编号
     */
    void deleteRules(Long id);

    /**
     * 获得权益规则
     *
     * @param id 编号
     * @return 权益规则
     */
    RightsRulesDO getRules(Long id);

    /**
     * 获得权益规则分页
     *
     * @param pageReqVO 分页查询
     * @return 权益规则分页
     */
    PageResult<RightsRulesDO> getRulesPage(RightsRulesPageReqVO pageReqVO);

    /***
     * 查询权限规则
     */
    RightsRulesDO queryByRightsId(Long rightsId);
}
package com.fozmo.ym.module.rights.service.rightsrules;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesPageReqVO;
import com.fozmo.ym.module.rights.controller.admin.rightsrules.vo.RightsRulesSaveReqVO;
import com.fozmo.ym.module.rights.dal.dataobject.rightsrules.RightsRulesDO;
import com.fozmo.ym.module.rights.dal.mysql.rightsrules.RightsRulesMapper;
import com.fozmo.ym.module.rights.dal.redis.rigths.RightsRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.rights.enums.ErrorCodeConstants.RULES_NOT_EXISTS;

/**
 * 权益规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RightsRulesServiceImpl implements RightsRulesService {

    @Resource
    private RightsRulesMapper rulesMapper;

    @Resource
    private RightsRedisDao rightsRedisDao;

    @Resource
    private IdService idService;

    @Override
    public Long createRules(RightsRulesSaveReqVO createReqVO) {
        // 插入

        RightsRulesDO rights = BeanUtils.toBean(createReqVO, RightsRulesDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        rights.setCreateId(loginUser.getId());
        rights.setCreator(loginUser.getUsername());
        rights.setCreateData(LocalDate.now());
        rights.setId(createReqVO.getRightsId());
        rights.setTenantId(loginUser.getTenantId());
        rulesMapper.insert(rights);
        rightsRedisDao.saveRightsRule(rights);
        // 返回
        return rights.getId();
    }

    @Override
    public void updateRules(RightsRulesSaveReqVO updateReqVO) {
        if (rightsRedisDao.hasRightRule(updateReqVO.getId())) {
            rightsRedisDao.delRightsRule(updateReqVO.getId());
        }
        RightsRulesDO updateObj = BeanUtils.toBean(updateReqVO, RightsRulesDO.class);
        rulesMapper.updateById(updateObj);
        rightsRedisDao.saveRightsRule(getRules(updateReqVO.getId()));
    }

    @Override
    public void deleteRules(Long id) {
        // 校验存在
        if (rightsRedisDao.hasRightRule(id)) {
            rightsRedisDao.delRightsRule(id);
        } else {
            throw exception(RULES_NOT_EXISTS);
        }
        // 删除
        rulesMapper.deleteById(id);
    }

    private void validateRulesExists(Long id) {
        if (rulesMapper.selectById(id) == null) {
            throw exception(RULES_NOT_EXISTS);
        }
    }

    @Override
    public RightsRulesDO getRules(Long id) {
        if (rightsRedisDao.hasRightRule(id)) {
            return rightsRedisDao.getRightsRule(id);
        } else {
            RightsRulesDO rightsRulesDO = rulesMapper.selectById(id);
            if (ObjectUtil.isNotEmpty(rightsRulesDO)) {
                rightsRedisDao.saveRightsRule(rightsRulesDO);
            }
            return rightsRulesDO;
        }
    }

    @Override
    public PageResult<RightsRulesDO> getRulesPage(RightsRulesPageReqVO pageReqVO) {
        return rulesMapper.selectPage(pageReqVO);
    }

    /***
     * 查询权限规则
     */
    @Override
    public RightsRulesDO queryByRightsId(Long rightsId) {
        return rulesMapper.queryByRightsId(rightsId);
    }
}
package com.fozmo.ym.module.shop.dal.dataobject.goods;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

@TableName("shop_goods")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopGoodsDO extends TenantBaseDO {
    /**
     * 商品ID
     */
    private Long id;
    /**
     *
     * 商品名称
     */
    private String name;

    /**
     *
     * 商品编码
     */
    private String code;

    /**
     *
     * 商品关键字
     */
    private String keyword;
    /**
     *
     * 商品描述
     */
    private String description;
    /**
     *
     * 商品类型ID
     */
    private Integer typeId;
    /**
     *
     * 商品品牌ID
     */
    private Long brandId;
    /**
     *
     * 商家ID
     */
    private Long merchantId;



}

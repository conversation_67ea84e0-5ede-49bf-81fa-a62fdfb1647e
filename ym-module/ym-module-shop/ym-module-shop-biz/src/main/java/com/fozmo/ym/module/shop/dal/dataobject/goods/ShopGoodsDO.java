package com.fozmo.ym.module.shop.dal.dataobject.goods;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

@TableName("shop_goods")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopGoodsDO extends TenantBaseDO {
    /**
     * 商品ID
     */
    private Long id;
    /**
     *
     * 商品名称
     */
    private String name;

    /**
     *
     * 商品编码
     */
    private String code;

    /**
     *
     * 商品关键字
     */
    private String keyword;
    /**
     *
     * 商品描述
     */
    private String description;
    /**
     *
     * 商品类型ID
     */
    private Integer typeId;
    /**
     *
     * 商品品牌ID
     */
    private Long brandId;
    /**
     *
     * 商家ID
     */
    private Long merchantId;
    /**
     * 商品关联Id
     */
    private Long infoId;

    /**
     * 商品关联编码
     */
    private String infoCode;


    /**
     * 创建id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户码
     */
    private String tenantCode;



}

package com.fozmo.ym.module.shop.dal.dataobject.goods;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

@TableName("shop_goods_sku")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopGoodsSkuDO extends TenantBaseDO {
    @TableId
    private Long id;
    /**
     * spuid
     */
    private Long goodsSpuId;
    /**
     * 商品id
     * */
    private Long goodsId;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * sku编码
     * */
    private String skuCode;
    /**
     * sku图片
     * */
    private String skuImg;
    /**
     * sku规格
     */

    private String skuSpec;
    /**
     * skukey
     */
    private String skuKey;
    /**
     *  sku值
     */
    private String skuValue;
    /**
     * sku 数量
     */
    private Integer skuNum;
    /**
     * sku 排序
     */
    private Integer skuSort;

    /**
     * 创建id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户码
     */
    private String tenantCode;


}

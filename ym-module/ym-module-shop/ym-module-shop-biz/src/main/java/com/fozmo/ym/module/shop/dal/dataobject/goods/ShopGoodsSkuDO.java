package com.fozmo.ym.module.shop.dal.dataobject.goods;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

@TableName("shop_goods_sku")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopGoodsSkuDO extends TenantBaseDO {

    private Long id;

    private Long goodsSpuId;

    private Long goodsId;

    private String skuName;

    private String skuCode;

    private String skuImg;

    private String skuSpec;


}

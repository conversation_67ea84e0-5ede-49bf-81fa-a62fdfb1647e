package com.fozmo.ym.module.shop.dal.dataobject.goods;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

@TableName("shop_goods_spu")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopGoodsSpuDO extends TenantBaseDO {

   private Long goodsId;
    /**
     * 商品封面图
     */
    private String picUrl;
    /**
     * 支付方式
     */
    private String payWay;

    /**
     *  商家标志 系统默认值：YM 会员商家 MB 三方商家 MT
     */
    private String mtCode;
    /**
     *  商品价格
     */
    private Integer price;
    /**
     *  单位
     */
    private String unit;

    /**
     *  商品币种
     */
    private String currency;

    /**
     * 规格类型
     *
     * false - 单规格
     * true - 多规格
     */
    private Boolean specType;
}

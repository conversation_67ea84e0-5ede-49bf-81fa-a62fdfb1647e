package com.fozmo.ym.module.sms.api.sms;

import com.fozmo.ym.module.sms.SmsCodeApi;
import com.fozmo.ym.module.sms.dto.code.SmsCodeSendReqDTO;
import com.fozmo.ym.module.sms.dto.code.SmsCodeUseReqDTO;
import com.fozmo.ym.module.sms.dto.code.SmsCodeValidateReqDTO;
import com.fozmo.ym.module.sms.service.sms.SmsCodeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 短信验证码 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsCodeApiImpl implements SmsCodeApi {

    @Resource
    private SmsCodeService smsCodeService;

    @Override
    public void sendSmsCode(SmsCodeSendReqDTO reqDTO) {
        smsCodeService.sendSmsCode(reqDTO);
    }

    @Override
    public void useSmsCode(SmsCodeUseReqDTO reqDTO) {
        smsCodeService.useSmsCode(reqDTO);
    }

    @Override
    public void validateSmsCode(SmsCodeValidateReqDTO reqDTO) {
        smsCodeService.validateSmsCode(reqDTO);
    }

}

package com.fozmo.ym.module.sms.core.client.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.http.HttpUtil;
import com.fozmo.ym.framework.common.core.KeyValue;
import com.fozmo.ym.framework.common.util.collection.MapUtils;
import com.fozmo.ym.framework.common.util.json.JsonUtils;
import com.fozmo.ym.module.sms.core.client.dto.SmsReceiveRespDTO;
import com.fozmo.ym.module.sms.core.client.dto.SmsSendRespDTO;
import com.fozmo.ym.module.sms.core.client.dto.SmsTemplateRespDTO;
import com.fozmo.ym.module.sms.core.enums.SmsTemplateAuditStatusEnum;
import com.fozmo.ym.module.sms.core.property.SmsChannelProperties;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class FeiShuSmsClient extends AbstractSmsClient {
    public FeiShuSmsClient(SmsChannelProperties properties) {
        super(properties);
        Assert.notEmpty(properties.getApiSecret(), "apiSecret 不能为空");
    }

    /**
     * 发送消息
     *
     * @param logId          日志编号
     * @param mobile         手机号
     * @param apiTemplateId  短信 API 的模板编号
     * @param templateParams 短信模板参数。通过 List 数组，保证参数的顺序
     * @return 短信发送结果
     */
    @Override
    public SmsSendRespDTO sendSms(Long logId, String mobile, String apiTemplateId, List<KeyValue<String, Object>> templateParams) throws Throwable {
        // 构建请求
        Map<String,String> map = buildUrl();
        Map<String, Object> params = new HashMap<>();
        params.put("msg_type", "text");
//        params.put("sign", map.get("sign"));
//        params.put("timestamp", map.get("timestamp"));
        String content = String.format("【模拟短信】\n手机号：%s\n短信日志编号：%d\n模板参数：%s",
                mobile, logId, MapUtils.convertMap(templateParams));
        params.put("content", MapUtil.builder().put("text", content).build());
        // 执行请求
        String url = map.get("url");
        String responseText = HttpUtil.post(url, JsonUtils.toJsonString(params));
        // 解析结果
        Map<?, ?> responseObj = JsonUtils.parseObject(responseText, Map.class);
        String errorCode = MapUtil.getStr(responseObj, "errcode");
        return new SmsSendRespDTO().setSuccess(Objects.equals(errorCode, "0")).setSerialNo(StrUtil.uuid())
                .setApiCode(errorCode).setApiMsg(MapUtil.getStr(responseObj, "errorMsg"));
    }

    /**
     * 构建请求地址
     *
     * 参见 <a href="https://developers.dingtalk.com/document/app/custom-robot-access/title-nfv-794-g71">文档</a>
     *
     * @return 请求地址
     */
    @SuppressWarnings("SameParameterValue")
    private Map<String,String> buildUrl() {
        // 生成 timestamp
        long timestamp = System.currentTimeMillis();
        // 生成 sign
        String secret = properties.getApiSecret();
        String stringToSign = timestamp + "\n" + secret;
        byte[] signData = DigestUtil.hmac(HmacAlgorithm.HmacSHA256, StrUtil.bytes(secret)).digest(stringToSign);
        String sign = Base64.encode(signData);
        // 构建最终 URL
         String url = String.format("https://open.feishu.cn/open-apis/bot/v2/hook/%s",
                 properties.getApiKey());
         Map<String,String> map = new HashMap<>();
         map.put("sign", sign);
         map.put("timestamp", String.valueOf(timestamp));
         map.put("apiKey", properties.getApiKey());
         map.put("secret", secret);
         map.put("url", url);
         return map;
    }

    /**
     * 解析接收短信的接收结果
     *
     * @param text 结果
     * @return 结果内容
     * @throws Throwable 当解析 text 发生异常时，则会抛出异常
     */
    @Override
    public List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) throws Throwable {
        throw new UnsupportedOperationException("模拟短信客户端，暂时无需解析回调");
    }

    /**
     * 查询指定的短信模板
     * <p>
     * 如果查询失败，则返回 null 空
     *
     * @param apiTemplateId 短信 API 的模板编号
     * @return 短信模板
     */
    @Override
    public SmsTemplateRespDTO getSmsTemplate(String apiTemplateId) throws Throwable {
        return new SmsTemplateRespDTO().setId(apiTemplateId).setContent("")
                .setAuditStatus(SmsTemplateAuditStatusEnum.SUCCESS.getStatus()).setAuditReason("");
    }
}

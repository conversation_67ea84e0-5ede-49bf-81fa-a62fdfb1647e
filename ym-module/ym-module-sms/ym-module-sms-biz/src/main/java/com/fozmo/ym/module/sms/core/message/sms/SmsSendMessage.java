package com.fozmo.ym.module.sms.core.message.sms;

import com.fozmo.ym.framework.common.core.KeyValue;
import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 短信发送消息
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SmsSendMessage  extends AbstractRedisStreamMessage {

    /**
     * 短信日志编号
     */
    @NotNull(message = "短信日志编号不能为空")
    private Long logId;
    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String mobile;
    /**
     * 短信渠道编号
     */
    @NotNull(message = "短信渠道编号不能为空")
    private Long channelId;
    /**
     * 短信 API 的模板编号
     */
    @NotNull(message = "短信 API 的模板编号不能为空")
    private String apiTemplateId;
    /**
     * 短信模板参数
     */
    private List<KeyValue<String, Object>> templateParams;

}

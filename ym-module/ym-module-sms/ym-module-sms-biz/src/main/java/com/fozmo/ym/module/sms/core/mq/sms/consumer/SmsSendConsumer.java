package com.fozmo.ym.module.sms.core.mq.sms.consumer;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import com.fozmo.ym.module.sms.core.message.sms.SmsSendMessage;
import com.fozmo.ym.module.sms.service.sms.SmsSendService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 针对 {@link SmsSendMessage} 的消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SmsSendConsumer extends AbstractRedisStreamMessageListener<SmsSendMessage> {

    @Resource
    private SmsSendService smsSendService;
    @Override
    public void onMessage(SmsSendMessage message) {
        log.info("[onMessage][消息内容({})]", message);
        smsSendService.doSendSms(message);
    }



}

package com.fozmo.ym.module.social.api.dto;

import lombok.Data;

import java.time.LocalDate;
@Data
public class SocialPlatformDTO {
	private Long id;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 渠道码
	 */
	private String code;
	/**
	 * 客户端id
	 */
	private String clientId;
	/**
	 * 客户端密钥
	 */
	private String clientSecret;
	/**
	 * 状态 0正常
	 */
	private Integer status;
	/**
	 * 创建id
	 */
	private Long createId;
	/**
	 * 创建日期
	 */
	private LocalDate createData;
	/**
	 * 更新人id
	 */
	private Long updaterId;
	/**
	 * 更新日期
	 */
	private LocalDate updateData;
	/**
	 * 租户码
	 */
	private String tenantCode;
	
}

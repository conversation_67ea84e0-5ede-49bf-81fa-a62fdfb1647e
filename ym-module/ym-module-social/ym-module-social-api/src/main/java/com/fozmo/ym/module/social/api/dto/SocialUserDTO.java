package com.fozmo.ym.module.social.api.dto;

import lombok.Data;

@Data
public class SocialUserDTO {

    private Long id;
    /**
     * 认证类型（如0-微信,1-手机号）
     */
    private Long type;
    /**
     * 用户唯一标识
     */
    private String userCode;
    /**
     * 第三方开放平台ID（如微信openid）
     */
    private String openId;
    /**
     * 登录令牌
     */
    private String token;
    /**
     * 手机号（格式：+86 13812345678）
     */
    private String mobile;

    /**
     * 昵称
     */
    private String nickName;
    /**
     * 头像URL地址
     */
    private String avatar;

    private Long platformId;
    /**
     * 名称
     */
    private String  platformName;
    /**
     * 渠道码
     */
    private String  platformCode;
    
    private Long createId;
}

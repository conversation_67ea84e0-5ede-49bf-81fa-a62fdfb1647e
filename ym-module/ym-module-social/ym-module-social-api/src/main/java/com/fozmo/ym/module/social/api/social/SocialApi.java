package com.fozmo.ym.module.social.api.social;


import com.fozmo.ym.module.social.api.dto.SocialPlatformDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;

import java.util.List;

public interface SocialApi {
    List<SocialUserDTO> querySocialUsersByAccountId(Long accountId);

    SocialPlatformDTO getPlatformByCode(String code);
    
    
    void createUserBind(SocialUserMessageDTO SocialUserMessageDTO);

    SocialUserDTO querySocialUserByCode(String code);
}

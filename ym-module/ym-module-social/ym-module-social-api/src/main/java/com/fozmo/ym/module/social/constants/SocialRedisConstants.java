package com.fozmo.ym.module.social.constants;

public interface SocialRedisConstants {

    String SPACE_COMMENT_KEY_PREFIX = "ym:comment:space:";
    String SPACE_REPLY_KEY_PREFIX = "ym:comment:spaceReply:";
    String SYNC_SPACE_COMMENT_TIMESTAMP_KEY = "sync:space:comment:timestamp";
    String SYNC_SPACE_REPLY_TIMESTAMP_KEY = "sync:space:reply:timestamp";
    String SYNC_LOCK_KEY = "lock:sync:comment";
    
    String WORKS_COMMENT_KEY_PREFIX = "ym:comment:works:";
    String WORKS_REPLY_KEY_PREFIX = "ym:comment:worksReply:";
    String SYNC_WORKS_COMMENT_TIMESTAMP_KEY = "sync:works:comment:timestamp";
    String SYNC_WORKS_REPLY_TIMESTAMP_KEY = "sync:works:reply:timestamp";

    
    String FANS_ADD_KEY_PREFIX = "ym:fans:add:";

    String SOCIAL_PLATFORM = "ym:social:platform";
    
    
    String SOCIAL_USER = "ym:social:user:";
    String SOCIAL_CODE = "ym:social:code";
    
    String SOCIAL_BIND = "ym:social:bind:";
    
    String WORKS_LIKE_KEY = "ym:like:works:";
    
    String SPACE_LIKE_KEY = "ym:like:space:";
    
    String SPACE_SHARE_KEY = "ym:share:space:";

    String WORKS_SHARE_KEY = "ym:share:works:";
    
    
}

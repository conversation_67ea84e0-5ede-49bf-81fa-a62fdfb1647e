package com.fozmo.ym.module.social.enums.social;

import cn.hutool.core.util.ArrayUtil;
import com.fozmo.ym.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 社交平台的类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SocialPlatformEnum implements ArrayValuable<Integer> {
    H5(1,"h5"),
    YM_APP(2,"ym_miniapp"),
    APP_MOBILE(3,"app_mobile"),
    TIKTOK(4,"tiktok");
    public static final Integer[] ARRAYS = Arrays.stream(values()).map(SocialPlatformEnum::getType).toArray(Integer[]::new);

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 类型的标识
     */
    private final String code;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static SocialPlatformEnum valueOfType(Integer type) {
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }

}

package com.fozmo.ym.module.social.api.fans;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.api.dto.SocialFansDTO;
import com.fozmo.ym.module.social.dal.dataobject.fans.SocialFansDO;
import com.fozmo.ym.module.social.service.fans.SocialFansService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
@Slf4j
@Service
public class SocialFansApiImpl implements SocialFansApi {

	@Resource
	private SocialFansService socialFansService;
	/**
	 *
	 */
	@Override
	public List<SocialFansDTO> queryFans(long userId) {
		List<SocialFansDO> list = socialFansService.getFanList(userId);
		return BeanUtils.toBean(list, SocialFansDTO.class);
	}

	/**
	 * @param accountId
	 * @return
	 */
	@Override
	public boolean isFans(Long accountId, Long fansAccountId) {
		return socialFansService.isFans(accountId, fansAccountId);
	}

	/**
	 * @param accountId
	 * @return
	 */
	@Override
	public Long queryFollowNum(Long accountId) {
		return socialFansService.queryFollowNum(accountId);
	}

	/**
	 * @param accountId
	 * @return
	 */
	@Override
	public Long fansNum(Long accountId) {
		return socialFansService.queryFansNum(accountId);
	}
}

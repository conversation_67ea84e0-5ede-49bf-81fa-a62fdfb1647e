package com.fozmo.ym.module.social.api.social;

import cn.hutool.core.collection.CollectionUtil;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.api.dto.SocialPlatformDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserDTO;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;
import com.fozmo.ym.module.social.core.mq.social.SocialUserProduce;
import com.fozmo.ym.module.social.dal.dataobject.bind.SocialBindDO;
import com.fozmo.ym.module.social.dal.dataobject.platform.SocialPlatformDO;
import com.fozmo.ym.module.social.dal.dataobject.user.SocialUserDO;
import com.fozmo.ym.module.social.service.bind.SocialBindService;
import com.fozmo.ym.module.social.service.platform.SocialPlatformService;
import com.fozmo.ym.module.social.service.user.SocialUserService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SocialApiImpl implements SocialApi {

    @Resource
    @Lazy
    private SocialUserService socialUserService;

    @Resource
    @Lazy
    private SocialBindService socialBindService;

    @Resource
    @Lazy
    private SocialPlatformService socialPlatformService;
    
    @Resource
    private SocialUserProduce socialUserProduce;
    /**
     */
    @Override
    public List<SocialUserDTO> querySocialUsersByAccountId(Long accountId) {
        List<SocialUserDTO> userList =new ArrayList<>();
        // 先查询绑定关系
        List<SocialBindDO> socialBind = socialBindService.getBindListByAccountId(accountId);
        if(CollectionUtil.isEmpty(socialBind)){
            return userList;
        }
        for (SocialBindDO socialBindDO : socialBind) {
            SocialUserDTO socialUserDTO = new SocialUserDTO();
            SocialUserDO socialUserDO = socialUserService.getUser(socialBindDO.getUserId());
            if (socialUserDO != null) {
                socialUserDTO= BeanUtils.toBean(socialUserDO, SocialUserDTO.class);
                SocialPlatformDO platform = socialPlatformService.getPlatform(socialBindDO.getSocialType());
                socialUserDTO.setPlatformId(platform.getId());
                socialUserDTO.setPlatformName(platform.getName());
                socialUserDTO.setPlatformCode(platform.getCode());
                userList.add(socialUserDTO);
            }
        }

        return userList;
    }

        /**
         *
         */
        @Override
        public SocialPlatformDTO getPlatformByCode(String code) {
            SocialPlatformDO socialPlatformDO = socialPlatformService.getPlatformByCode(code);
            return BeanUtils.toBean(socialPlatformDO, SocialPlatformDTO.class);
        }

    /**
     */
    @Override
    public void createUserBind(SocialUserMessageDTO SocialUserMessageDTO) {
        socialUserProduce.sendSocialUser(SocialUserMessageDTO);
    }

    /**
     *
     */
    @Override
    public SocialUserDTO querySocialUserByCode(String code) {
        SocialUserDO socialUserDO = socialUserService.getUserByCode(code);
        return BeanUtils.toBean(socialUserDO, SocialUserDTO.class);
    }
}

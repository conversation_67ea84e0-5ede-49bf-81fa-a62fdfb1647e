package com.fozmo.ym.module.social.api.space;

import com.fozmo.ym.module.social.service.comment.SocialSpaceCommentService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SpaceCommentApiImpl implements SpaceCommentApi {

	@Resource
	private SocialSpaceCommentService spaceCommentService;
	/**
	 *
	 */
	@Override
	public Long countBySpaceId(Long id) {
		return spaceCommentService.countBySpaceId(id);
	}
	
	/**
	 *
	 */
	@Override
	public boolean isComment(Long spaceId, Long accountId) {
		return spaceCommentService.isComment(spaceId, accountId);
	}

    /**
     * @param spaceId
     */
    @Override
    public void deleteBySpaceId(Long spaceId) {
        spaceCommentService.deleteBySpaceId(spaceId);
    }


}

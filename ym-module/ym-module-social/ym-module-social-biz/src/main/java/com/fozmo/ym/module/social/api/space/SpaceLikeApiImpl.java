package com.fozmo.ym.module.social.api.space;

import com.fozmo.ym.module.social.service.like.SpaceLikeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SpaceLikeApiImpl implements SpaceLikeApi {

	@Resource
	private SpaceLikeService spaceLikeService;
/**
 *
 */
	@Override
	public Long countBySpaceId(Long id) {
		return spaceLikeService.selectSpaceLikeCountBySpaceId(id);
	}

/**
 *
 */
	@Override
	public boolean isLike(Long spaceId, Long accountId) {
		return spaceLikeService.isLike(spaceId, accountId);
	}

    /**
     * @param spaceId
     */
    @Override
    public void deleteBySpaceId(Long spaceId) {
        spaceLikeService.deleteBySpaceId(spaceId);
    }
}

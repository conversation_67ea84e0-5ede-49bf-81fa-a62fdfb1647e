package com.fozmo.ym.module.social.api.space;

import com.fozmo.ym.module.social.service.share.SpaceShareService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SpaceShareApiImpl implements SpaceShareApi {

	@Resource
	private SpaceShareService spaceShareService;
	/**
	 *
	 */
	@Override
	public Long countBySpaceId(Long spaceId) {
		return spaceShareService.countBySpaceId(spaceId);
	}

	/**
	 *
	 */
	@Override
	public boolean isShare(Long spaceId, Long accountId) {
		return spaceShareService.isShare(spaceId, accountId);
	}

    /**
     * @param spaceId
     */
    @Override
    public void deleteBySpaceId(Long spaceId) {
        spaceShareService.deleteBySpaceId(spaceId);
    }
}

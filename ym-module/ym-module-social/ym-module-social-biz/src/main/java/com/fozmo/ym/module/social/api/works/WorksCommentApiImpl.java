package com.fozmo.ym.module.social.api.works;

import com.fozmo.ym.module.social.service.comment.SocialWorksCommentService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorksCommentApiImpl implements WorksCommentApi {

    @Resource
    private SocialWorksCommentService worksCommentService;
    /**
     * 查询作品评论数
     *
     */
    @Override
    public Long countByWorksId(Long worksId) {
        return worksCommentService.countByWorksId(worksId);
    }

    /**
     * 是否存在评论
     *
     */
    @Override
    public Boolean isComment(Long worksId, Long accountId) {
        return false;
    }

    /**
     * 删除评论
     *
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        worksCommentService.deleteByWorksId(worksId);
    }
}

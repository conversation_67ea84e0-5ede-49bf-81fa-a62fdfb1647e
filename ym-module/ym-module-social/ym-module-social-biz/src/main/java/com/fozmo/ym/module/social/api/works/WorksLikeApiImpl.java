package com.fozmo.ym.module.social.api.works;

import com.fozmo.ym.module.social.service.like.WorksLikeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorksLikeApiImpl implements WorksLikeApi {

    @Resource
    private WorksLikeService worksLikeService;
    /**
     */
    @Override
    public Long countByWorksId(Long worksId) {
        return worksLikeService.countByWorksId(worksId);
    }

    /**
     */
    @Override
    public boolean isLike(Long worksId, Long accountId) {
        return worksLikeService.isLike(worksId, accountId);
    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        worksLikeService.deleteByWorksId(worksId);
    }
}

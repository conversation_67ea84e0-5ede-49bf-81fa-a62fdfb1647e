package com.fozmo.ym.module.social.api.works;

import com.fozmo.ym.module.social.service.share.WorksShareService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorksShareApiImpl implements WorksShareApi{

    @Resource
    private WorksShareService worksShareService;
    /**
     */
    @Override
    public Long countByWorksId(Long worksId) {

        return worksShareService.countByWorksId(worksId);
    }

    /**
     */
    @Override
    public Boolean isShare(Long worksId, Long accountId) {

        return worksShareService.isShare(worksId, accountId);
    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        worksShareService.deleteByWorksId(worksId);
    }
}

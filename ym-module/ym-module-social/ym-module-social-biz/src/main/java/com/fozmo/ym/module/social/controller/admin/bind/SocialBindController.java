package com.fozmo.ym.module.social.controller.admin.bind;//package com.fozmo.ym.module.auth.controller.admin.socail.bind;

import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindPageReqVO;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindRespVO;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.bind.SocialBindDO;
import com.fozmo.ym.module.social.service.bind.SocialBindService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 社交模块 - 用户第三方社交绑定信息")
@RestController
@RequestMapping("/social/bind")
@Validated
public class SocialBindController {

    @Resource
    private SocialBindService socialBindService;

    @PostMapping("/create")
    @Operation(summary = "创建用户第三方社交授权信息")
    @PreAuthorize("@ss.hasPermission('social:bind:create')")
    public CommonResult<Long> createBind(@Valid @RequestBody SocialBindSaveReqVO createReqVO) {
        return success(socialBindService.createBind(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户第三方社交授权信息")
    @PreAuthorize("@ss.hasPermission('social:bind:update')")
    public CommonResult<Boolean> updateBind(@Valid @RequestBody SocialBindSaveReqVO updateReqVO) {
        socialBindService.updateBind(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户第三方社交授权信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('social:bind:delete')")
    public CommonResult<Boolean> deleteBind(@RequestParam("id") Long id) {
        socialBindService.deleteBind(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户第三方社交授权信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('social:bind:query')")
    public CommonResult<SocialBindRespVO> getBind(@RequestParam("id") Long id) {
        SocialBindDO bind = socialBindService.getBind(id);
        return success(BeanUtils.toBean(bind, SocialBindRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户第三方社交授权信息分页")
    @PreAuthorize("@ss.hasPermission('social:bind:query')")
    public CommonResult<PageResult<SocialBindRespVO>> getBindPage(@Valid SocialBindPageReqVO pageReqVO) {
        PageResult<SocialBindDO> pageResult = socialBindService.getBindPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SocialBindRespVO.class));
    }
}
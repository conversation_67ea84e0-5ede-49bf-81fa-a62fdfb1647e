package com.fozmo.ym.module.social.controller.admin.bind.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户第三方社交授权信息分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SocialBindPageReqVO extends PageParam {

    @Schema(description = "平台用户ID", example = "3978")
    private Long userId;

    @Schema(description = "用户类型（如0-个人用户，1-企业用户）", example = "1")
    private Long userType;

    @Schema(description = "账户ID（关联账户表）", example = "22705")
    private Long accountId;

    @Schema(description = "社交平台类型（1-微信，2-支付宝，3-微博等）", example = "2")
    private Long socialType;

    @Schema(description = "创建人ID", example = "21609")
    private Long createId;

    @Schema(description = "创建日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] createDate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人ID", example = "15517")
    private Long updaterId;

    @Schema(description = "更新日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] updateDate;

    @Schema(description = "租户编码")
    private String tenantCode;

}
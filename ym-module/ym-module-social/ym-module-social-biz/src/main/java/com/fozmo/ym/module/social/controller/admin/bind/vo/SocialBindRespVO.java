package com.fozmo.ym.module.social.controller.admin.bind.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户第三方社交授权信息 Response VO")
@Accessors(chain = true)
@Data
public class SocialBindRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18440")
    private Long id;

    @Schema(description = "平台用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3978")
    private Long userId;

    @Schema(description = "用户类型（如0-个人用户，1-企业用户）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long userType;

    @Schema(description = "账户ID（关联账户表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "22705")
    private Long accountId;

    @Schema(description = "社交平台类型（1-微信，2-支付宝，3-微博等）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long socialType;

    @Schema(description = "创建人ID", example = "21609")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createDate;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID", example = "15517")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateDate;

    @Schema(description = "租户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tenantCode;

}
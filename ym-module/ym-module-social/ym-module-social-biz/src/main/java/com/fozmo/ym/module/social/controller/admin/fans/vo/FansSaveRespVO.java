package com.fozmo.ym.module.social.controller.admin.fans.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class FansSaveRespVO {

	private Long id;
	
	private Long accountId;
	
	@TableField(exist = false)
	private String accountName;
	
	@TableField(exist = false)
	private String nickname;
	
	@TableField(exist = false)
	private String avatar;
	
	@TableField(exist = false)
	private Long rightsId;
	
	private Long fansAccountId;
	
	@TableField(exist = false)
	private String fansAccountName;
	
	@TableField(exist = false)
	private String fansNickname;
	
	@TableField(exist = false)
	private String fansAvatar;
	
	@TableField(exist = false)
	private Long fansRightsId;
	
	private Integer fansStatus;
	
	private LocalDateTime fansTime;
}

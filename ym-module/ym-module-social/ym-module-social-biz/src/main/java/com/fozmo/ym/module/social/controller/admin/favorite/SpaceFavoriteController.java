package com.fozmo.ym.module.social.controller.admin.favorite;//package com.fozmo.ym.module.social.controller.admin.favorite;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.social.controller.admin.favorite.vo.*;
//import com.fozmo.ym.module.social.dal.dataobject.favorite.SpaceFavoriteDO;
//import com.fozmo.ym.module.social.service.favorite.SpaceFavoriteService;
//
//@Tag(name = "后管-空间模块 - 空间收藏")
//@RestController
//@RequestMapping("/space/favorite")
//@Validated
//public class SpaceFavoriteController {
//
//    @Resource
//    private SpaceFavoriteService favoriteService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间收藏")
//    @PreAuthorize("@ss.hasPermission('space:favorite:create')")
//    public CommonResult<Long> createFavorite(@Valid @RequestBody SpaceFavoriteSaveReqVO createReqVO) {
//        return success(favoriteService.createFavorite(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间收藏")
//    @PreAuthorize("@ss.hasPermission('space:favorite:update')")
//    public CommonResult<Boolean> updateFavorite(@Valid @RequestBody SpaceFavoriteSaveReqVO updateReqVO) {
//        favoriteService.updateFavorite(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间收藏")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:favorite:delete')")
//    public CommonResult<Boolean> deleteFavorite(@RequestParam("id") Long id) {
//        favoriteService.deleteFavorite(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间收藏")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:favorite:query')")
//    public CommonResult<SpaceFavoriteRespVO> getFavorite(@RequestParam("id") Long id) {
//        SpaceFavoriteDO favorite = favoriteService.getFavorite(id);
//        return success(BeanUtils.toBean(favorite, SpaceFavoriteRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间收藏分页")
//    @PreAuthorize("@ss.hasPermission('space:favorite:query')")
//    public CommonResult<PageResult<SpaceFavoriteRespVO>> getFavoritePage(@Valid SpaceFavoritePageReqVO pageReqVO) {
//        PageResult<SpaceFavoriteDO> pageResult = favoriteService.getFavoritePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceFavoriteRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间收藏 Excel")
//    @PreAuthorize("@ss.hasPermission('space:favorite:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportFavoriteExcel(@Valid SpaceFavoritePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceFavoriteDO> list = favoriteService.getFavoritePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间收藏.xls", "数据", SpaceFavoriteRespVO.class,
//                        BeanUtils.toBean(list, SpaceFavoriteRespVO.class));
//    }
//
//}
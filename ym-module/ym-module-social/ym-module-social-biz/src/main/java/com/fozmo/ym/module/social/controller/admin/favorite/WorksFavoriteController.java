package com.fozmo.ym.module.social.controller.admin.favorite;//package com.fozmo.ym.module.social.controller.admin.worksfavorite;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//import com.fozmo.ym.module.social.controller.admin.worksfavorite.vo.*;
//import com.fozmo.ym.module.social.dal.dataobject.worksfavorite.WorksFavoriteDO;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.Valid;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import com.fozmo.ym.module.social.service.worksfavorite.WorksFavoriteService;
//
//import java.io.IOException;
//import java.util.List;
//
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "管理后台 - 作品收藏")
//@RestController
//@RequestMapping("/space/works-favorite")
//@Validated
//public class WorksFavoriteController {
//
//    @Resource
//    private WorksFavoriteService worksFavoriteService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建作品收藏")
//    @PreAuthorize("@ss.hasPermission('space:works-favorite:create')")
//    public CommonResult<Long> createWorksFavorite(@Valid @RequestBody WorksFavoriteSaveReqVO createReqVO) {
//        return success(worksFavoriteService.createWorksFavorite(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新作品收藏")
//    @PreAuthorize("@ss.hasPermission('space:works-favorite:update')")
//    public CommonResult<Boolean> updateWorksFavorite(@Valid @RequestBody WorksFavoriteSaveReqVO updateReqVO) {
//        worksFavoriteService.updateWorksFavorite(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除作品收藏")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:works-favorite:delete')")
//    public CommonResult<Boolean> deleteWorksFavorite(@RequestParam("id") Long id) {
//        worksFavoriteService.deleteWorksFavorite(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得作品收藏")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:works-favorite:query')")
//    public CommonResult<WorksFavoriteRespVO> getWorksFavorite(@RequestParam("id") Long id) {
//        WorksFavoriteDO worksFavorite = worksFavoriteService.getWorksFavorite(id);
//        return success(BeanUtils.toBean(worksFavorite, WorksFavoriteRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得作品收藏分页")
//    @PreAuthorize("@ss.hasPermission('space:works-favorite:query')")
//    public CommonResult<PageResult<WorksFavoriteRespVO>> getWorksFavoritePage(@Valid WorksFavoritePageReqVO pageReqVO) {
//        PageResult<WorksFavoriteDO> pageResult = worksFavoriteService.getWorksFavoritePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, WorksFavoriteRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出作品收藏 Excel")
//    @PreAuthorize("@ss.hasPermission('space:works-favorite:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportWorksFavoriteExcel(@Valid WorksFavoritePageReqVO pageReqVO,
//                                         HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<WorksFavoriteDO> list = worksFavoriteService.getWorksFavoritePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "作品收藏.xls", "数据", WorksFavoriteRespVO.class,
//                BeanUtils.toBean(list, WorksFavoriteRespVO.class));
//    }
//
//}

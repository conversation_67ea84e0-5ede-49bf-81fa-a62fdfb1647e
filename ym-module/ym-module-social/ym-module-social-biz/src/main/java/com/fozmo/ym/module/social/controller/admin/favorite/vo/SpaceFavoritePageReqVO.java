package com.fozmo.ym.module.social.controller.admin.favorite.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 社交模块-空间收藏 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpaceFavoritePageReqVO extends PageParam {

    @Schema(description = "空间id", example = "31447")
    private Long spaceId;

    @Schema(description = "收藏人id", example = "13755")
    private Long accountId;

    @Schema(description = "收藏人", example = "芋艿")
    private String accountName;

    @Schema(description = "创建人id", example = "14642")
    private Long createId;

    @Schema(description = "favorite_time 收藏时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] favoriteTime;

    @Schema(description = "创建用户名称", example = "芋艿")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "28958")
    private Long updateId;

    @Schema(description = "更新人", example = "李四")
    private String updater;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
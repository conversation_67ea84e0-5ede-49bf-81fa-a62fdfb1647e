package com.fozmo.ym.module.social.controller.admin.favorite.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 作品收藏分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorksFavoritePageReqVO extends PageParam {

    @Schema(description = "空间id", example = "9795")
    private Long worksId;

    @Schema(description = "收藏人id", example = "1730")
    private Long accountId;

    @Schema(description = "收藏人", example = "张三")
    private String accountName;

    @Schema(description = "创建人id", example = "19839")
    private Long createId;

    @Schema(description = "favorite_time 收藏时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] favoriteTime;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "20464")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}

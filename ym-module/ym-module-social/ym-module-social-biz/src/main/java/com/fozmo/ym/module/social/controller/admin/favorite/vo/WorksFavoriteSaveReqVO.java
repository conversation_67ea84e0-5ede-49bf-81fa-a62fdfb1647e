package com.fozmo.ym.module.social.controller.admin.favorite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 作品收藏新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class WorksFavoriteSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16767")
    private Long id;

    @Schema(description = "空间id", example = "9795")
    private Long worksId;

    @Schema(description = "收藏人id", example = "1730")
    private Long accountId;

    @Schema(description = "收藏人", example = "张三")
    private String accountName;

    @Schema(description = "创建人id", example = "19839")
    private Long createId;

    @Schema(description = "favorite_time 收藏时间")
    private LocalDateTime favoriteTime;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "20464")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}

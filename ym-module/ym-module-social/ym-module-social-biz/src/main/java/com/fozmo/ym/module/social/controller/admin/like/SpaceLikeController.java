package com.fozmo.ym.module.social.controller.admin.like;//package com.fozmo.ym.module.social.controller.admin.like;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.social.controller.admin.like.vo.*;
//import com.fozmo.ym.module.social.dal.dataobject.like.SpaceLikeDO;
//import com.fozmo.ym.module.social.service.like.SpaceLikeService;
//
//@Tag(name = "后管-空间模块 - 空间点赞")
//@RestController
//@RequestMapping("/space/like")
//@Validated
//public class SpaceLikeController {
//
//    @Resource
//    private SpaceLikeService likeService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间点赞")
//    @PreAuthorize("@ss.hasPermission('space:like:create')")
//    public CommonResult<Long> createLike(@Valid @RequestBody SpaceLikeSaveReqVO createReqVO) {
//        return success(likeService.createLike(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间点赞")
//    @PreAuthorize("@ss.hasPermission('space:like:update')")
//    public CommonResult<Boolean> updateLike(@Valid @RequestBody SpaceLikeSaveReqVO updateReqVO) {
//        likeService.updateLike(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间点赞")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:like:delete')")
//    public CommonResult<Boolean> deleteLike(@RequestParam("id") Long id) {
//        likeService.deleteLike(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间点赞")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:like:query')")
//    public CommonResult<SpaceLikeRespVO> getLike(@RequestParam("id") Long id) {
//        SpaceLikeDO like = likeService.getLike(id);
//        return success(BeanUtils.toBean(like, SpaceLikeRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间点赞分页")
//    @PreAuthorize("@ss.hasPermission('space:like:query')")
//    public CommonResult<PageResult<SpaceLikeRespVO>> getLikePage(@Valid SpaceLikePageReqVO pageReqVO) {
//        PageResult<SpaceLikeDO> pageResult = likeService.getLikePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceLikeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间点赞 Excel")
//    @PreAuthorize("@ss.hasPermission('space:like:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportLikeExcel(@Valid SpaceLikePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceLikeDO> list = likeService.getLikePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间点赞.xls", "数据", SpaceLikeRespVO.class,
//                        BeanUtils.toBean(list, SpaceLikeRespVO.class));
//    }
//
//}
package com.fozmo.ym.module.social.controller.admin.like;//package com.fozmo.ym.module.social.controller.admin.workslike;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.social.controller.admin.workslike.vo.*;
//import com.fozmo.ym.module.social.dal.dataobject.workslike.WorksLikeDO;
//import com.fozmo.ym.module.social.service.like.WorksLikeService;
//
//@Tag(name = "管理后台 - 作品点赞")
//@RestController
//@RequestMapping("/space/works-like")
//@Validated
//public class WorksLikeController {
//
//    @Resource
//    private WorksLikeService worksLikeService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建作品点赞")
//    @PreAuthorize("@ss.hasPermission('space:works-like:create')")
//    public CommonResult<Long> createWorksLike(@Valid @RequestBody WorksLikeSaveReqVO createReqVO) {
//        return success(worksLikeService.createWorksLike(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新作品点赞")
//    @PreAuthorize("@ss.hasPermission('space:works-like:update')")
//    public CommonResult<Boolean> updateWorksLike(@Valid @RequestBody WorksLikeSaveReqVO updateReqVO) {
//        worksLikeService.updateWorksLike(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除作品点赞")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:works-like:delete')")
//    public CommonResult<Boolean> deleteWorksLike(@RequestParam("id") Long id) {
//        worksLikeService.deleteWorksLike(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得作品点赞")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:works-like:query')")
//    public CommonResult<WorksLikeRespVO> getWorksLike(@RequestParam("id") Long id) {
//        return success(worksLikeService.getWorksLike(id));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得作品点赞分页")
//    @PreAuthorize("@ss.hasPermission('space:works-like:query')")
//    public CommonResult<PageResult<WorksLikeRespVO>> getWorksLikePage(@Valid WorksLikePageReqVO pageReqVO) {
//        PageResult<WorksLikeRespVO> pageResult = worksLikeService.getWorksLikePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, WorksLikeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出作品点赞 Excel")
//    @PreAuthorize("@ss.hasPermission('space:works-like:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportWorksLikeExcel(@Valid WorksLikePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<WorksLikeRespVO> list = worksLikeService.getWorksLikePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "作品点赞.xls", "数据", WorksLikeRespVO.class,list);
//    }
//
//}
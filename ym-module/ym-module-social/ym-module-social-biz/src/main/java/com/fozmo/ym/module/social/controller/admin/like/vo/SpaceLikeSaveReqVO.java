package com.fozmo.ym.module.social.controller.admin.like.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间点赞新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class SpaceLikeSaveReqVO {

    @Schema(description = "id", example = "27776")
    private Long id;

    @Schema(description = "空间id", example = "10393")
    private Long spaceId;

    @Schema(description = "点赞人", example = "1927")
    private Long accountId;

    @Schema(description = "点赞人姓名", example = "张三")
    private String accountName;

    @Schema(description = "点赞时间")
    private LocalDateTime likeTime;

    @Schema(description = "创建人id", example = "15857")
    private Long createId;

    @Schema(description = "创建用户名称", example = "王五")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "8260")
    private Long updateId;

    @Schema(description = "更新人", example = "张三")
    private String updater;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
package com.fozmo.ym.module.social.controller.admin.like.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 作品点赞分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorksLikePageReqVO extends PageParam {

    @Schema(description = "作品id", example = "27966")
    private Long worksId;

    @Schema(description = "点赞人", example = "12585")
    private Long accountId;

    @Schema(description = "点赞人姓名", example = "芋艿")
    private String accountName;

    @Schema(description = "点赞时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] likeTime;

    @Schema(description = "创建人id", example = "16724")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "14503")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
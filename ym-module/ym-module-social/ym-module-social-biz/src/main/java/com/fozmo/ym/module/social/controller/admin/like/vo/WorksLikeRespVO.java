package com.fozmo.ym.module.social.controller.admin.like.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 作品点赞 Response VO")
@Accessors(chain = true)
@Data
public class WorksLikeRespVO {

    @Schema(description = "id", example = "9703")
    private Long id;

    @Schema(description = "作品id", example = "27966")
    private Long worksId;

    @Schema(description = "点赞人", example = "12585")
    private Long accountId;

    @Schema(description = "点赞人姓名", example = "芋艿")
    private String accountName;
/**
 * 用户头像
 */

    @Schema(description = "点赞时间")
    private LocalDateTime likeTime;

    @Schema(description = "创建人id", example = "16724")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "14503")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

    private String avatar;
    
    private Boolean likeStatus;

}
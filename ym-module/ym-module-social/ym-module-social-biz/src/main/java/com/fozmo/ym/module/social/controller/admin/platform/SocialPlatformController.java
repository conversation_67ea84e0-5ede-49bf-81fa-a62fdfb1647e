package com.fozmo.ym.module.social.controller.admin.platform;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformPageReqVO;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformRespVO;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.platform.SocialPlatformDO;
import com.fozmo.ym.module.social.service.platform.SocialPlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 社交模块 社交平台")
@RestController
@RequestMapping("/social/platform")
@Validated
public class SocialPlatformController {

    @Resource
    private SocialPlatformService platformService;

    @PostMapping("/create")
    @Operation(summary = "创建社交平台")
    @PreAuthorize("@ss.hasPermission('social:platform:create')")
    public CommonResult<Long> createPlatform(@Valid @RequestBody SocialPlatformSaveReqVO createReqVO) {
        return success(platformService.createPlatform(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新社交平台")
    @PreAuthorize("@ss.hasPermission('social:platform:update')")
    public CommonResult<Boolean> updatePlatform(@Valid @RequestBody SocialPlatformSaveReqVO updateReqVO) {
        platformService.updatePlatform(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除社交平台")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('social:platform:delete')")
    public CommonResult<Boolean> deletePlatform(@RequestParam("id") Long id) {
        platformService.deletePlatform(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得社交平台")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('social:platform:query')")
    public CommonResult<SocialPlatformRespVO> getPlatform(@RequestParam("id") Long id) {
        SocialPlatformDO platform = platformService.getPlatform(id);
        return success(BeanUtils.toBean(platform, SocialPlatformRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得社交平台分页")
    @PreAuthorize("@ss.hasPermission('social:platform:query')")
    public CommonResult<PageResult<SocialPlatformRespVO>> getPlatformPage(@Valid SocialPlatformPageReqVO pageReqVO) {
        PageResult<SocialPlatformDO> pageResult = platformService.getPlatformPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SocialPlatformRespVO.class));
    }
}
package com.fozmo.ym.module.social.controller.admin.platform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 社交平台 Response VO")
@Accessors(chain = true)
@Data
public class SocialPlatformRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6978")
    private Long id;

    @Schema(description = "名称", example = "李四")
    private String name;

    @Schema(description = "渠道码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "客户端id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21766")
    private String clientId;

    @Schema(description = "客户端密钥", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientSecret;

    @Schema(description = "状态 0正常", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    @Schema(description = "创建id", example = "31080")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人id", example = "22610")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tenantCode;

}
package com.fozmo.ym.module.social.controller.admin.platform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 社交平台新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class SocialPlatformSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6978")
    private Long id;

    @Schema(description = "名称", example = "李四")
    private String name;

    @Schema(description = "渠道码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "渠道码不能为空")
    private String code;

    @Schema(description = "客户端id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21766")
    @NotEmpty(message = "客户端id不能为空")
    private String clientId;

    @Schema(description = "客户端密钥", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "客户端密钥不能为空")
    private String clientSecret;

    @Schema(description = "状态 0正常", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态 0正常不能为空")
    private Integer status;

    @Schema(description = "创建id", example = "31080")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人id", example = "22610")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "租户码不能为空")
    private String tenantCode;

}
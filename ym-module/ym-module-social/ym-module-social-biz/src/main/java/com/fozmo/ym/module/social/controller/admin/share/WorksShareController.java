package com.fozmo.ym.module.social.controller.admin.share;//package com.fozmo.ym.module.social.controller.admin.worksshare;
//
//import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareRespVO;
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.social.controller.admin.worksshare.vo.*;
//import com.fozmo.ym.module.social.dal.dataobject.worksshare.WorksShareDO;
//import com.fozmo.ym.module.social.service.worksshare.WorksShareService;
//
//@Tag(name = "管理后台 - 作品分享")
//@RestController
//@RequestMapping("/space/works-share")
//@Validated
//public class WorksShareController {
//
//    @Resource
//    private WorksShareService worksShareService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建作品分享")
//    @PreAuthorize("@ss.hasPermission('space:works-share:create')")
//    public CommonResult<WorksShareRespVO> createWorksShare(@Valid @RequestBody WorksShareSaveReqVO createReqVO) {
//        return success(worksShareService.createWorksShare(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新作品分享")
//    @PreAuthorize("@ss.hasPermission('space:works-share:update')")
//    public CommonResult<Boolean> updateWorksShare(@Valid @RequestBody WorksShareSaveReqVO updateReqVO) {
//        worksShareService.updateWorksShare(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除作品分享")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:works-share:delete')")
//    public CommonResult<Boolean> deleteWorksShare(@RequestParam("id") Long id) {
//        worksShareService.deleteWorksShare(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得作品分享")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:works-share:query')")
//    public CommonResult<WorksShareRespVO> getWorksShare(@RequestParam("id") Long id) {
//        WorksShareDO worksShare = worksShareService.getWorksShare(id);
//        return success(BeanUtils.toBean(worksShare, WorksShareRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得作品分享分页")
//    @PreAuthorize("@ss.hasPermission('space:works-share:query')")
//    public CommonResult<PageResult<WorksShareRespVO>> getWorksSharePage(@Valid WorksSharePageReqVO pageReqVO) {
//        PageResult<WorksShareDO> pageResult = worksShareService.getWorksSharePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, WorksShareRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出作品分享 Excel")
//    @PreAuthorize("@ss.hasPermission('space:works-share:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportWorksShareExcel(@Valid WorksSharePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<WorksShareDO> list = worksShareService.getWorksSharePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "作品分享.xls", "数据", WorksShareRespVO.class,
//                        BeanUtils.toBean(list, WorksShareRespVO.class));
//    }
//
//}
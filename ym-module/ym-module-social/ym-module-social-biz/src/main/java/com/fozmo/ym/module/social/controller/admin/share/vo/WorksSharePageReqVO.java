package com.fozmo.ym.module.social.controller.admin.share.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 作品分享分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorksSharePageReqVO extends PageParam {

    @Schema(description = "作品id", example = "1558")
    private Long worksId;

    @Schema(description = "分享id", example = "14506")
    private Long accountId;

    @Schema(description = "分享人名称", example = "赵六")
    private String accountName;

    @Schema(description = "分享时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] shareTime;

    @Schema(description = "分享码值")
    private String shareCode;

    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;

    @Schema(description = "创建人id", example = "26439")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "13928")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
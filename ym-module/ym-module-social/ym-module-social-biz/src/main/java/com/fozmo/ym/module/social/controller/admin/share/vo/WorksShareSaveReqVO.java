package com.fozmo.ym.module.social.controller.admin.share.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 作品分享新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class WorksShareSaveReqVO {

    @Schema(description = "作品id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1558")
    private Long worksId;

    @Schema(description = "id", example = "7797")
    private Long id;

    @Schema(description = "分享id", example = "14506")
    private Long accountId;

    @Schema(description = "分享人名称", example = "赵六")
    private String accountName;

    @Schema(description = "分享时间")
    private LocalDateTime shareTime;

    @Schema(description = "分享码值")
    private String shareCode;

    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;

    @Schema(description = "创建人id", example = "26439")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "13928")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

    @NotNull(message = "分享页不可为空")
    private String sharePage;
    @NotNull(message = "版本不可为空")
    private String env;
    @NotNull(message = "场景码不可为空")
    private String scene;

}
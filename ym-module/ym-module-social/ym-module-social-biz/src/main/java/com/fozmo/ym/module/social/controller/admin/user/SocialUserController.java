package com.fozmo.ym.module.social.controller.admin.user;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserPageReqVO;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserRespVO;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.user.SocialUserDO;
import com.fozmo.ym.module.social.service.user.SocialUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 社交模块-用户授权信息")
@RestController
@RequestMapping("/social/user")
@Validated
public class SocialUserController {

    @Resource
    private SocialUserService socialUserService;

    @PostMapping("/create")
    @Operation(summary = "创建用户授权信息")
    @PreAuthorize("@ss.hasPermission('social:user:create')")
    public CommonResult<Long> createUser(@Valid @RequestBody SocialUserSaveReqVO createReqVO) {
        return success(socialUserService.createUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户授权信息")
    @PreAuthorize("@ss.hasPermission('social:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody SocialUserSaveReqVO updateReqVO) {
        socialUserService.updateUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户授权信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('social:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        socialUserService.deleteUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户授权信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('social:user:query')")
    public CommonResult<SocialUserRespVO> getUser(@RequestParam("id") Long id) {
        SocialUserDO user = socialUserService.getUser(id);
        return success(BeanUtils.toBean(user, SocialUserRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户授权信息分页")
    @PreAuthorize("@ss.hasPermission('social:user:query')")
    public CommonResult<PageResult<SocialUserRespVO>> getUserPage(@Valid SocialUserPageReqVO pageReqVO) {
        PageResult<SocialUserDO> pageResult = socialUserService.getUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SocialUserRespVO.class));
    }

}
package com.fozmo.ym.module.social.controller.admin.user.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户授权信息分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SocialUserPageReqVO extends PageParam {

    @Schema(description = "认证类型（如0-微信,1-手机号）", example = "2")
    private Long type;

    @Schema(description = "用户唯一标识")
    private String userCode;

    @Schema(description = "第三方开放平台ID（如微信openid）", example = "26667")
    private String openId;

    @Schema(description = "登录令牌")
    private String token;

    @Schema(description = "手机号（格式：+86 13812345678）")
    private String mobile;

    @Schema(description = "原始Token信息（JSON格式）")
    private String rawTokenInfo;

    @Schema(description = "昵称", example = "张三")
    private String nickName;

    @Schema(description = "头像URL地址")
    private String avatar;

    @Schema(description = "原始用户信息（JSON格式）")
    private String rawUserInfo;

    @Schema(description = "最后登录授权码")
    private String code;

    @Schema(description = "最后登录状态参数")
    private String state;

    @Schema(description = "创建人ID", example = "23443")
    private Long createId;

    @Schema(description = "创建日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] createDate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人ID", example = "5292")
    private Long updaterId;

    @Schema(description = "更新日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] updateDate;

    @Schema(description = "租户编码")
    private String tenantCode;

}
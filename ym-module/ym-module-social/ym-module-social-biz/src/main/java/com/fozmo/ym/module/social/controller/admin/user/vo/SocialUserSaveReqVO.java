package com.fozmo.ym.module.social.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 用户授权信息新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class SocialUserSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11250")
    private Long id;

    @Schema(description = "认证类型（如0-微信,1-手机号）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long type;

    @Schema(description = "用户唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userCode;

    @Schema(description = "第三方开放平台ID（如微信openid）", requiredMode = Schema.RequiredMode.REQUIRED, example = "26667")
    private String openId;

    @Schema(description = "登录令牌", requiredMode = Schema.RequiredMode.REQUIRED)
    private String token;

    @Schema(description = "手机号（格式：+86 13812345678）")
    private String mobile;

    @Schema(description = "原始Token信息（JSON格式）")
    private String rawTokenInfo;

    @Schema(description = "昵称", example = "张三")
    private String nickName;

    @Schema(description = "头像URL地址")
    private String avatar;

    @Schema(description = "原始用户信息（JSON格式）")
    private String rawUserInfo;

    @Schema(description = "最后登录授权码")
    private String code;

    @Schema(description = "最后登录状态参数")
    private String state;

    @Schema(description = "创建人ID", example = "23443")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createDate;

    @Schema(description = "更新人ID", example = "5292")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateDate;

    @Schema(description = "租户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tenantCode;

}
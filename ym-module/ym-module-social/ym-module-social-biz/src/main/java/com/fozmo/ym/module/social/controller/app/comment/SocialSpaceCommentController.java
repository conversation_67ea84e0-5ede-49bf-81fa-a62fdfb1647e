package com.fozmo.ym.module.social.controller.app.comment;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.api.dto.SpaceCommentDTO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentPageReqVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentRespVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentSaveReqVO;
import com.fozmo.ym.module.social.service.comment.SocialSpaceCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
@Tag(name = "APP-社交模块-空间评论")
@RestController
@RequestMapping("/social")
@Validated
public class SocialSpaceCommentController {

	@Resource
	private SocialSpaceCommentService socialSpaceCommentService;

	@PostMapping("/spaceCreate")
	@Operation(summary = "创建空间评论")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Long> createComment(@Valid @RequestBody SocialSpaceCommentSaveReqVO createReqVO) {
		SpaceCommentDTO spaceCommentDTO = BeanUtils.toBean(createReqVO, SpaceCommentDTO.class);
		
		return success(socialSpaceCommentService.createComment(spaceCommentDTO));
	}
	@GetMapping("/spacePage")
	@Operation(summary = "获得空间评论分页")
	@PermitAll
	@ApiAccessLog
	public CommonResult<PageResult<SocialSpaceCommentRespVO>> getCommentPage(@Valid SocialSpaceCommentPageReqVO pageReqVO) {
		// 1. 参数转换：将App端请求参数转换为Service层参数
		PageResult<SocialSpaceCommentRespVO> respVOList = socialSpaceCommentService.getCommentPage(pageReqVO);
		return success(respVOList);
	}
}

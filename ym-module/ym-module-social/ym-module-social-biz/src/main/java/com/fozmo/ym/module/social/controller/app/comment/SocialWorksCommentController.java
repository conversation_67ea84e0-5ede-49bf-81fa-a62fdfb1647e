package com.fozmo.ym.module.social.controller.app.comment;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.api.dto.WorksCommentDTO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentPageReqVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentRespVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentSaveReqVO;
import com.fozmo.ym.module.social.service.comment.SocialWorksCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-社交模块-作品评论")
@RestController
@RequestMapping("/social")
@Validated
public class SocialWorksCommentController {

	@Resource
	private SocialWorksCommentService socialWorksCommentService;

	@PostMapping("/worksCreate")
	@Operation(summary = "创建作品评论")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Long> createComment(@Valid @RequestBody SocialWorksCommentSaveReqVO createReqVO) {
		WorksCommentDTO spaceCommentDTO = BeanUtils.toBean(createReqVO, WorksCommentDTO.class);
		return success(socialWorksCommentService.createComment(spaceCommentDTO));
	}
	@GetMapping("/worksPage")
	@Operation(summary = "获得作品评论分页")
	@PermitAll
	@ApiAccessLog
	public CommonResult<PageResult<SocialWorksCommentRespVO>> getCommentPage(@Valid SocialWorksCommentPageReqVO pageReqVO) {
		PageResult<SocialWorksCommentRespVO> respVOList = socialWorksCommentService.getCommentPage(pageReqVO);
		return success(respVOList);
	}
}

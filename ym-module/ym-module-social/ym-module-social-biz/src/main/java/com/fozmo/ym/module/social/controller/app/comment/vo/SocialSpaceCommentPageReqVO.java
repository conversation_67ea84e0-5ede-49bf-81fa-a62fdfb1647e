package com.fozmo.ym.module.social.controller.app.comment.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间评论分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SocialSpaceCommentPageReqVO extends PageParam {

    @Schema(description = "空间Id", example = "31073")
    @NotNull(message = "空间Id不可为null")
    private Long spaceId;

    @Schema(description = "回答id", example = "20025")
    private Long replyId;

    @Schema(description = "评论类型 0评论 1回复")
    private Integer commentType;

}
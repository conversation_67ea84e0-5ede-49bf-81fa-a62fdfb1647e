package com.fozmo.ym.module.social.controller.app.comment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "APP - 作品评论 Response VO")
@Accessors(chain = true)
@Data
public class SocialWorksCommentRespVO {

    @Schema(description = "id", example = "28932")
    private Long id;
    @Schema(description = "作品Id", example = "31073")
    private Long worksId;
    
    private String worksName;
    @Schema(description = "回答id", example = "20025")
    private Long replyId;
    @Schema(description = "评论内容")
    private String commentContent;
    @Schema(description = "评论时间")
    private Long commentTime;
    @Schema(description = "评论类型 0评论 1回复")
    private Integer commentType;
    @Schema(description = "回复列表")
    private List<SocialWorksCommentRespVO> respVOList;
    @Schema(description = "回复总数")
    private Long total;


    @Schema(description = "账号", example = "32700")
    private Long accountId;
    @Schema(description = "评论人", example = "李四")
    private String accountName;
    @Schema(description = "评论/回复头像")
    private  String avatar;
    @Schema(description = "评论、回复人昵称")
    private String nickname;
    @Schema(description = "评论、回复人权益")
    private Long rightsId;
    @Schema(description = "艾特人账户Id")
    private String inviteIds;
    @Schema(description = "艾特人账户名称")
    private String inviteAccountName;
    @Schema(description = "艾特邀请观看")
    private String inviteAvatar;
    @Schema(description = "艾特人权益")
    private Long  inviteRightsId;
    @Schema(description = "艾特人昵称")
    private String inviteNickName;

}
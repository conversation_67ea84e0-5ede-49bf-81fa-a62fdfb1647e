package com.fozmo.ym.module.social.controller.app.comment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 作品评论新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class SocialWorksCommentSaveReqVO {

	@Schema(description = "id", example = "28932")
	private Long id;
	
	@Schema(description = "作品Id", example = "31073")
	private Long worksId;
	
	@Schema(description = "回答id", example = "20025")
	private Long replyId;
	
	@Schema(description = "艾特邀请观看")
	private String inviteIds;
	
	@Schema(description = "评论内容")
	private String commentContent;
	
	@Schema(description = "评论类型 0评论 1回复")
	private Integer commentType;
}

package com.fozmo.ym.module.social.controller.app.fans;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.fans.vo.FansSaveRespVO;
import com.fozmo.ym.module.social.controller.app.fans.vo.AppFansPageReqVO;
import com.fozmo.ym.module.social.controller.app.fans.vo.AppFansPageRespVO;
import com.fozmo.ym.module.social.controller.app.fans.vo.AppFansSaveRespVO;
import com.fozmo.ym.module.social.dal.dataobject.fans.SocialFansDO;
import com.fozmo.ym.module.social.service.fans.SocialFansService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-社交模块-粉丝")
@RestController
@RequestMapping("/social/fans")
@Validated
public class AppFansController {
	
	@Resource
	private SocialFansService socialFansService;
	
	
	/**
	* 关注用户
	*/
	@PostMapping("/create")
	@Operation(summary = "关注")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Boolean> addFans(@Valid @RequestBody AppFansSaveRespVO respVO) {

		FansSaveRespVO saveRespVO = BeanUtils.toBean(respVO, FansSaveRespVO.class);

		Long fansAccountId = SecurityFrameworkUtils.getLoginUserId();
		if (ObjectUtil.isEmpty(fansAccountId)) {
			throw exception(UNAUTHORIZED);
		}
		saveRespVO.setFansAccountId(fansAccountId);
		socialFansService.addFans(saveRespVO);
		return success(Boolean.TRUE);
	}
	
	/**
	 * 取消关注
	 */
	
	@DeleteMapping("/delete")
	@Operation(summary = "取消关注")
	@PermitAll
	@ApiAccessLog
	public CommonResult<Boolean> deleteFans(@RequestParam("accountId") Long accountId){
		socialFansService.deleteFans(accountId, SecurityFrameworkUtils.getLoginUserId());
		return success(Boolean.TRUE);
	}
	
	@GetMapping("/page")
	@Operation(summary = "查询粉丝列表-不指定查自己")
	@PermitAll
	@ApiAccessLog
	public CommonResult<PageResult<AppFansPageRespVO>> fansPage(@Valid AppFansPageReqVO reqVO){
		if (ObjectUtil.isEmpty(reqVO.getAccountId())){
			reqVO.setAccountId(WebFrameworkUtils.getLoginUserId());
		}
		PageResult<SocialFansDO> fansPage = socialFansService.getFans(reqVO);
		return success(BeanUtils.toBean(fansPage, AppFansPageRespVO.class));
	}

	@GetMapping("/myPage")
	@Operation(summary = "我的关注列表")
	@PermitAll
	@ApiAccessLog
	public CommonResult<PageResult<AppFansPageRespVO>> myFansPage(@Valid AppFansPageReqVO reqVO) {
		PageResult<SocialFansDO> fansPage = socialFansService.myFansPage(reqVO);
		return success(BeanUtils.toBean(fansPage, AppFansPageRespVO.class));
	}
}

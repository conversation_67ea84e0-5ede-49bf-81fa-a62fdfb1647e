package com.fozmo.ym.module.social.controller.app.fans.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "APP - 社交模块-粉丝 response VO")
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
public class AppFansPageRespVO {

private Long id;

private Long accountId;

@TableField(exist = false)
private String accountName;

@TableField(exist = false)
private String nickname;

@TableField(exist = false)
private String avatar;

@TableField(exist = false)
private Long rightsId;

private Long fansAccountId;

@TableField(exist = false)
private String fansAccountName;

@TableField(exist = false)
private String fansNickname;

@TableField(exist = false)
private String fansAvatar;

@TableField(exist = false)
private Long fansRightsId;

private Boolean fansStatus;

private LocalDateTime fansTime;

}

package com.fozmo.ym.module.social.controller.app.favorite;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoritePageReqVO;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoriteSaveReqVO;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppWorksFavoritePageReqVO;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppWorksFavoriteRespVO;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppWorksFavoriteSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.WorksFavoriteDO;
import com.fozmo.ym.module.social.service.favorite.WorksFavoriteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-社交模块 - 作品收藏")
@RestController
@RequestMapping("/social/worksFavorite")
@Validated
public class AppWorksFavoriteController {

    @Resource
    private WorksFavoriteService worksFavoriteService;

    @PostMapping("/create")
    @Operation(summary = "创建作品收藏")
    public CommonResult<Long> createWorksFavorite(@Valid @RequestBody AppWorksFavoriteSaveReqVO createReqVO) {
        WorksFavoriteSaveReqVO reqVO = BeanUtils.toBean(createReqVO, WorksFavoriteSaveReqVO.class);
        return success(worksFavoriteService.createWorksFavorite(reqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新作品收藏")
    public CommonResult<Boolean> updateWorksFavorite(@Valid @RequestBody WorksFavoriteSaveReqVO updateReqVO) {
        worksFavoriteService.updateWorksFavorite(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除作品收藏")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteWorksFavorite(@RequestParam("id") Long id) {
        worksFavoriteService.deleteWorksFavorite(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得作品收藏")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppWorksFavoriteRespVO> getWorksFavorite(@RequestParam("id") Long id) {
        WorksFavoriteDO worksFavorite = worksFavoriteService.getWorksFavorite(id);
        return success(BeanUtils.toBean(worksFavorite, AppWorksFavoriteRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得作品收藏分页")
    public CommonResult<PageResult<AppWorksFavoriteRespVO>> getWorksFavoritePage(@Valid AppWorksFavoritePageReqVO pageReqVO) {
        WorksFavoritePageReqVO reqVO = BeanUtils.toBean(pageReqVO, WorksFavoritePageReqVO.class);
        reqVO.setAccountId(WebFrameworkUtils.getLoginUserId());
        PageResult<WorksFavoriteDO> pageResult = worksFavoriteService.getWorksFavoritePage(reqVO);
        return success(BeanUtils.toBean(pageResult, AppWorksFavoriteRespVO.class));
    }

}

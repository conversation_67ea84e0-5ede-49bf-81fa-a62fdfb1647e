package com.fozmo.ym.module.social.controller.app.favorite.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间收藏分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSpaceFavoritePageReqVO extends PageParam {

    @Schema(description = "空间id", example = "31447")
    private Long spaceId;

    @Schema(description = "收藏人id", example = "13755")
    private Long accountId;

    @Schema(description = "收藏人", example = "芋艿")
    private String accountName;
}

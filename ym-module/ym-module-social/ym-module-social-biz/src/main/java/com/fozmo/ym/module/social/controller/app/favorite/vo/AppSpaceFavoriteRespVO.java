package com.fozmo.ym.module.social.controller.app.favorite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间收藏 Response VO")
@Accessors(chain = true)
@Data
public class AppSpaceFavoriteRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24814")
    private Long id;

    @Schema(description = "空间id", example = "31447")
    private Long spaceId;

    @Schema(description = "收藏人id", example = "13755")
    private Long accountId;

    @Schema(description = "收藏人", example = "芋艿")
    private String accountName;
}

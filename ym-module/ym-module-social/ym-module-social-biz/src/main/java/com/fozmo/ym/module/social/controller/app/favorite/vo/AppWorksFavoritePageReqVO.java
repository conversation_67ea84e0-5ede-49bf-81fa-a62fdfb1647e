package com.fozmo.ym.module.social.controller.app.favorite.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 作品收藏分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWorksFavoritePageReqVO extends PageParam {

    @Schema(description = "空间id", example = "9795")
    private Long worksId;

}


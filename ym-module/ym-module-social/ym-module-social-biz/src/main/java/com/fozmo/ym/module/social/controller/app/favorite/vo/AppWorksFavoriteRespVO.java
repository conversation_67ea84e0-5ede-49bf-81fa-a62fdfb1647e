package com.fozmo.ym.module.social.controller.app.favorite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 作品收藏 Response VO")
@Accessors(chain = true)
@Data
public class AppWorksFavoriteRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16767")
    private Long id;

    @Schema(description = "空间id", example = "9795")
    private Long worksId;

    @Schema(description = "收藏人id", example = "1730")
    private Long accountId;

    @Schema(description = "收藏人", example = "张三")
    private String accountName;

    @Schema(description = "favorite_time 收藏时间")
    private LocalDateTime favoriteTime;

}


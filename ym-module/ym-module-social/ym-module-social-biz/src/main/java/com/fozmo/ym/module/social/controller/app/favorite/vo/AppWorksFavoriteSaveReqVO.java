package com.fozmo.ym.module.social.controller.app.favorite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 作品收藏新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppWorksFavoriteSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16767")
    private Long id;

    @Schema(description = "空间id", example = "9795")
    private Long worksId;
}

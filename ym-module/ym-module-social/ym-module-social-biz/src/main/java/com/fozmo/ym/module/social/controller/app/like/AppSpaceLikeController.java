package com.fozmo.ym.module.social.controller.app.like;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.like.vo.SpaceLikePageReqVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.SpaceLikeRespVO;
import com.fozmo.ym.module.social.controller.app.like.vo.AppSpaceLikePageReqVO;
import com.fozmo.ym.module.social.controller.app.like.vo.AppSpaceLikeSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.like.SpaceLikeDO;
import com.fozmo.ym.module.social.service.like.SpaceLikeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-社交模块 - 空间点赞")
@RestController
@RequestMapping("/social/like")
@Validated
public class AppSpaceLikeController {

    @Resource
    private SpaceLikeService likeService;

    @PostMapping("/create")
    @Operation(summary = "创建空间点赞", description = "空间点赞")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> createLike(@Valid @RequestBody AppSpaceLikeSaveReqVO createReqVO) {
        return success(likeService.createLikeApp(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "取消空间点赞", description = "取消空间点赞")
//    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> deleteLike(@RequestParam("spaceId") Long spaceId) {

        likeService.deleteLike(spaceId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得空间点赞")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<SpaceLikeRespVO> getLike(@RequestParam("id") Long id) {
        SpaceLikeDO like = likeService.getLike(id);
        return success(BeanUtils.toBean(like, SpaceLikeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间点赞分页")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<SpaceLikeRespVO>> getLikePage(@Valid AppSpaceLikePageReqVO pageReqVO) {
        SpaceLikePageReqVO pageReq = BeanUtils.toBean(pageReqVO, SpaceLikePageReqVO.class);
        pageReq.setSpaceId(pageReqVO.getSpaceId());
        pageReq.setAccountId(WebFrameworkUtils.getLoginUserId());
        PageResult<SpaceLikeDO> pageResult = likeService.getLikePage(pageReq);
        return success(BeanUtils.toBean(pageResult, SpaceLikeRespVO.class));
    }

}
package com.fozmo.ym.module.social.controller.app.like;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikePageReqVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikeSaveReqVO;
import com.fozmo.ym.module.social.controller.app.like.vo.AppWorksLikePageReqVO;
import com.fozmo.ym.module.social.controller.app.like.vo.AppWorksLikeRespVO;
import com.fozmo.ym.module.social.dal.dataobject.like.WorksLikeDO;
import com.fozmo.ym.module.social.service.like.WorksLikeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-社交模块 - 作品点赞")
@RestController
@RequestMapping("/social/worksLike")
@Validated
public class AppWorksLikeController {

    @Resource
    private WorksLikeService worksLikeService;

    @PostMapping("/create")
    @Operation(summary = "创建作品点赞")
    public CommonResult<Long> createWorksLike(@Valid @RequestBody WorksLikeSaveReqVO createReqVO) {
        return success(worksLikeService.createWorksLike(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新作品点赞")
    public CommonResult<Boolean> updateWorksLike(@Valid @RequestBody WorksLikeSaveReqVO updateReqVO) {
        worksLikeService.updateWorksLike(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除作品点赞")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteWorksLike(@RequestParam("worksId") Long worksId) {
        worksLikeService.deleteWorksLike(worksId);
        return success(true);
    }
    
    @GetMapping("/page")
    @Operation(summary = "获得作品点赞分页")
    public CommonResult<PageResult<AppWorksLikeRespVO>> getWorksLikePage(@Valid AppWorksLikePageReqVO pageReqVO) {
        WorksLikePageReqVO reqVO = BeanUtils.toBean(pageReqVO, WorksLikePageReqVO.class);
        reqVO.setAccountId(WebFrameworkUtils.getLoginUserId());
        PageResult<WorksLikeDO> pageResult = worksLikeService.getWorksLikePage(reqVO);
        return success(BeanUtils.toBean(pageResult, AppWorksLikeRespVO.class));
    }

}
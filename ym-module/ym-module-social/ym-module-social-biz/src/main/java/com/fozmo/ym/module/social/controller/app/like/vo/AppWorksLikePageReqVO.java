package com.fozmo.ym.module.social.controller.app.like.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 作品点赞分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWorksLikePageReqVO extends PageParam {

    @Schema(description = "作品id", example = "27966")
    private Long worksId;
}

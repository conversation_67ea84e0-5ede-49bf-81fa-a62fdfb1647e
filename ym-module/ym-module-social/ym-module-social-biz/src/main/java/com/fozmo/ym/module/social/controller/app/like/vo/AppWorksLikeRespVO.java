package com.fozmo.ym.module.social.controller.app.like.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 作品点赞 Response VO")
@Accessors(chain = true)
@Data
public class AppWorksLikeRespVO {

    @Schema(description = "id", example = "9703")
    private Long id;

    @Schema(description = "作品id", example = "27966")
    private Long worksId;

    @Schema(description = "点赞人", example = "12585")
    private Long accountId;

    @Schema(description = "点赞人姓名", example = "芋艿")
    private String accountName;

    @Schema(description = "点赞时间")
    private LocalDateTime likeTime;

    private String avatar;
    
    private Boolean likeStatus;

}

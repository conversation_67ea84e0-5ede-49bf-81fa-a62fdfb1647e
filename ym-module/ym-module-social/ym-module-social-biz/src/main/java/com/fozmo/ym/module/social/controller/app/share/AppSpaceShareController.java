package com.fozmo.ym.module.social.controller.app.share;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareSaveReqVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppSpaceSharePageReqVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppSpaceShareRespVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppSpaceShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
import com.fozmo.ym.module.social.service.share.SpaceShareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-社交模块 - 空间分享")
@RestController
@RequestMapping("/social/share")
@Validated
public class AppSpaceShareController {

    @Resource
    private SpaceShareService shareService;

    @PostMapping("/create")
    @Operation(summary = "创建空间分享")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppSpaceShareRespVO> createShare(@Valid @RequestBody AppSpaceShareSaveReqVO createReqVO) {
        SpaceShareSaveReqVO reqVO =BeanUtils.toBean(createReqVO, SpaceShareSaveReqVO.class);
        SpaceShareRespVO info = shareService.createShare(reqVO);
        return success(BeanUtils.toBean(info, AppSpaceShareRespVO.class));
    }

    @PutMapping("/update")
    @Operation(summary = "更新空间分享")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> updateShare(@Valid @RequestBody AppSpaceShareSaveReqVO updateReqVO) {
        SpaceShareSaveReqVO reqVO =BeanUtils.toBean(updateReqVO, SpaceShareSaveReqVO.class);
        shareService.updateShare(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除空间分享")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> deleteShare(@RequestParam("id") Long id) {
        shareService.deleteShare(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得空间分享")
    @Parameter(name = "scene", description = "编号", required = true, example = "scene")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppSpaceShareRespVO> getShare(@RequestParam("scene") String scene) {
        SpaceShareDO share = shareService.getShareByScene(scene);
        return success(BeanUtils.toBean(share, AppSpaceShareRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间分享分页")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<SpaceShareRespVO>> getSharePage(@Valid AppSpaceSharePageReqVO pageReqVO) {
        SpaceSharePageReqVO reqVO = BeanUtils.toBean(pageReqVO, SpaceSharePageReqVO.class);
        reqVO.setAccountId(WebFrameworkUtils.getLoginUserId());
        PageResult<SpaceShareDO> pageResult = shareService.getSharePage(reqVO);
        return success(BeanUtils.toBean(pageResult, SpaceShareRespVO.class));
    }
    
}
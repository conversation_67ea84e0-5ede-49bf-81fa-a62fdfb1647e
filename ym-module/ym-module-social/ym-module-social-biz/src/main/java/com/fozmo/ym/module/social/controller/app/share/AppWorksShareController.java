package com.fozmo.ym.module.social.controller.app.share;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksShareSaveReqVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppWorksSharePageReqVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppWorksShareRespVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppWorksShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.WorksShareDO;
import com.fozmo.ym.module.social.service.share.WorksShareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP—社交模块 - 作品分享")
@RestController
@RequestMapping("/social/worksshare")
@Validated
public class AppWorksShareController {

    @Resource
    private WorksShareService worksShareService;

    @PostMapping("/create")
    @Operation(summary = "创建作品分享")
    public CommonResult<AppWorksShareRespVO> createWorksShare(@Valid @RequestBody AppWorksShareSaveReqVO createReqVO) {
        WorksShareSaveReqVO reqVO = BeanUtils.toBean(createReqVO, WorksShareSaveReqVO.class);
        WorksShareRespVO worksShareRespVO = worksShareService.createWorksShare(reqVO);
        return success(BeanUtils.toBean(worksShareRespVO, AppWorksShareRespVO.class));
    }

    @PutMapping("/update")
    @Operation(summary = "更新作品分享")
    public CommonResult<Boolean> updateWorksShare(@Valid @RequestBody AppWorksShareSaveReqVO updateReqVO) {

        WorksShareSaveReqVO reqVO = BeanUtils.toBean(updateReqVO, WorksShareSaveReqVO.class);
        worksShareService.updateWorksShare(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除作品分享")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteWorksShare(@RequestParam("id") Long id) {
        worksShareService.deleteWorksShare(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得作品分享")
    @Parameter(name = "scene", description = "编号", required = true, example = "scene")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppWorksShareRespVO> getWorksShare(@RequestParam("scene") String scene) {
        WorksShareDO worksShare = worksShareService.getWorksShareByScene(scene);
        return success(BeanUtils.toBean(worksShare, AppWorksShareRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得作品分享分页")
    public CommonResult<PageResult<AppWorksShareRespVO>> getWorksSharePage(@Valid AppWorksSharePageReqVO pageReqVO) {
        WorksSharePageReqVO reqVO = BeanUtils.toBean(pageReqVO, WorksSharePageReqVO.class);
        reqVO.setAccountId(WebFrameworkUtils.getLoginUserId());
        PageResult<WorksShareDO> pageResult = worksShareService.getWorksSharePage(reqVO);
        return success(BeanUtils.toBean(pageResult, AppWorksShareRespVO.class));
    }

}
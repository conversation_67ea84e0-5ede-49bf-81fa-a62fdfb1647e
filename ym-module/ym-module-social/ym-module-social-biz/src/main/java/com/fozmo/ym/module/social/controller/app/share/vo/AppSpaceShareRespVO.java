package com.fozmo.ym.module.social.controller.app.share.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间分享 Response VO")
@Accessors(chain = true)
@Data
public class AppSpaceShareRespVO {

    @Schema(description = "id", example = "10513")
    private Long id;

    @Schema(description = "空间id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25451")
    private Long spaceId;

    @Schema(description = "分享id", example = "18333")
    private Long accountId;

    @Schema(description = "分享人名称", example = "芋艿")
    private String accountName;

    @Schema(description = "分享时间")
    private LocalDateTime shareTime;

    @Schema(description = "分享码值")
    private String shareCode;

    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;
    
    private String shareQrcode;
    
    private String sharePage;
    
    private String env;
    
    private String scene;
    
    private Long spaceAccountId;

    private String spaceCover;
    
    private Long spaceRightsId;
    
    private String spaceAccountName;
    
    private String spaceAccountCover;

    private String spaceName;
    
}

package com.fozmo.ym.module.social.controller.app.share.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


@Schema(description = "管理后台 - 作品分享分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWorksSharePageReqVO extends PageParam {

    @Schema(description = "作品id", example = "1558")
    private Long worksId;

    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;

}

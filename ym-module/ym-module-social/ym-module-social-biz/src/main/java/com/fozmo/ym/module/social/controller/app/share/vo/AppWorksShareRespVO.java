package com.fozmo.ym.module.social.controller.app.share.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "App - 作品分享 Response VO")
@Accessors(chain = true)
@Data
public class AppWorksShareRespVO {

    @Schema(description = "作品id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1558")
    private Long worksId;

    @Schema(description = "id", example = "7797")
    private Long id;

    @Schema(description = "分享id", example = "14506")
    private Long accountId;

    @Schema(description = "分享人名称", example = "赵六")
    private String accountName;

    @Schema(description = "分享时间")
    private LocalDateTime shareTime;

    @Schema(description = "分享码值")
    private String shareCode;

    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;

    private String shareQrcode;
    
    private String sharePage;
    
    private String env;
    
    private String scene;
    
    private Long worksAccountId;
    
    private String worksCover;
    
    private Long worksRightsId;
    
    private String worksAccountName;
    
    private String worksAccountCover;
    
    private String worksName;

}

package com.fozmo.ym.module.social.controller.app.share.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 作品分享新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppWorksShareSaveReqVO {

    @Schema(description = "作品id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1558")
    private Long worksId;

    @NotNull(message = "分享页渠道不可为空")
    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;
    @NotNull(message = "分享页不可为空")
    private String sharePage;
    @NotNull(message = "版本不可为空")
    private String env;
    @NotNull(message = "场景码不可为空")
    private String scene;

}

package com.fozmo.ym.module.social.core.job.comment;

import cn.hutool.json.JSONUtil;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.social.core.mq.comment.message.WorksCommentMessage;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialWorksCommentDO;
import com.fozmo.ym.module.social.service.comment.SocialWorksCommentService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.*;

/**
 *  作品评论回复消息同步任务
 */
@Component
@Slf4j
public class SyncWorksCommentTask {

	@Resource
	private RedissonClient redissonClient; // 分布式锁依赖

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private SocialWorksCommentService socialWorksCommentService;


@XxlJob("worksCommentSyncToDB")
//@TenantJob // 多租户
public void worksCommentSyncToDB() {
	
	// 1. 获取分布式锁（锁定15分钟）
	RLock lock = redissonClient.getLock(SYNC_LOCK_KEY);
	try {

		if (!lock.tryLock(0, 15, TimeUnit.MINUTES)) {
			return;
		}
		
		// 2 分页扫描Keys 避免阻塞
		Set<String> keys = scanKeys(WORKS_COMMENT_KEY_PREFIX + "*");
		
		// 3. 并行处理每个spaceId
		keys.parallelStream().forEach(this::processSpace);
		
		
	} catch (InterruptedException e) {
		
		log.error("<UNK>", e);
		throw new RuntimeException(e);
	}finally {
		lock.unlock();
	}
}

	private void processSpace(String redisKey) {
		try {
			// 解析spaceId（示例：ym:space:comment:1001 -> 1001）
			long worksId = Long.parseLong(redisKey.split(":")[3]);
			
			
			// 获取最后同步时间（从Hash获取）
			long commentLastSync = Optional.ofNullable(
							redisTemplate.opsForHash().get(SYNC_WORKS_COMMENT_TIMESTAMP_KEY, Long.toString(worksId))
			                                        )
					                     .map(value -> {
						                     if (value instanceof Number) {
							                     return ((Number) value).longValue(); // 处理 Integer/Long 等数值类型
						                     } else if (value instanceof String) {
							                     try {
								                     return Long.parseLong((String) value); // 处理字符串数值
							                     } catch (NumberFormatException e) {
								                     return 0L; // 格式错误时降级为默认值
							                     }
						                     }
						                     return 0L; // 非预期类型降级
					                     })
					                     .orElse(0L);
			
			// 增量拉取数据（按时间范围）
			Set<ZSetOperations.TypedTuple<Object>> commentData = redisTemplate.opsForZSet()
					                                                 .rangeByScoreWithScores(redisKey, commentLastSync, Double.MAX_VALUE);

			assert commentData != null;
			if (!commentData.isEmpty()) {
				// 解析并分批写入DB
				List<WorksCommentMessage> batchList = commentData.stream()
						                                      .map(tuple -> parseComment(tuple.getValue(), tuple.getScore()))
						                                      .collect(Collectors.toList());
				
				batchInsert(batchList); // 分批插入
				
				// 更新同步时间（取最大时间戳）
				double maxTime = commentData.stream()
						                 .mapToDouble(ZSetOperations.TypedTuple::getScore)
						                 .max().orElse(commentLastSync);
				
				redisTemplate.opsForHash().put(
						SYNC_WORKS_COMMENT_TIMESTAMP_KEY,
						Long.toString(worksId),
						maxTime);
				
				// 主评论完成后 处理 回复消息
				// 扫描这个空间下的 所有的回复key
				List<WorksCommentMessage> replyList = new ArrayList<>();
				
				Set<String> keys = scanKeys(WORKS_REPLY_KEY_PREFIX+worksId + ":*");
				keys.parallelStream().forEach(key -> {
					long replyId = Long.parseLong(key.split(":")[3]);
					// 获取最后同步时间（从Hash获取）
					long replyLastSync = Optional.ofNullable(
									redisTemplate.opsForHash().get(SYNC_WORKS_REPLY_TIMESTAMP_KEY, Long.toString(replyId))
					                                        )
							                     .map(value -> {
								                     if (value instanceof Number) {
									                     return ((Number) value).longValue(); // 处理 Integer/Long 等数值类型
								                     } else if (value instanceof String) {
									                     try {
										                     return Long.parseLong((String) value); // 处理字符串数值
									                     } catch (NumberFormatException e) {
										                     return 0L; // 格式错误时降级为默认值
									                     }
								                     }
								                     return 0L; // 非预期类型降级
							                     })
							                     .orElse(0L);
					// 增量拉取数据
					Set<ZSetOperations.TypedTuple<Object>> replyData = redisTemplate.opsForZSet()
							                                                     .rangeByScoreWithScores(key, replyLastSync, Double.MAX_VALUE);
					assert replyData != null;
					if (!replyData.isEmpty()) {
						// 解析
						List<WorksCommentMessage> batchReplyList = replyData.stream()
								                                      .map(tuple -> parseComment(tuple.getValue(), tuple.getScore()))
								                                      .collect(Collectors.toList());
						
						replyList.addAll(batchReplyList);
						
						// 更新同步时间（取最大时间戳）
						double replyMaxTime = replyData.stream()
								                 .mapToDouble(ZSetOperations.TypedTuple::getScore)
								                 .max().orElse(replyLastSync);
						
						redisTemplate.opsForHash().put(
								SYNC_WORKS_REPLY_TIMESTAMP_KEY,
								Long.toString(replyId),
								replyMaxTime);
					}
				});
				
				batchInsert(replyList);
			}
		} catch (Exception e) {
			log.error("Sync failed for key: {}", redisKey, e);
			// 此处可加入重试队列逻辑
		}
	}

	private WorksCommentMessage parseComment(Object value, Double score) {
		WorksCommentMessage message = new WorksCommentMessage();
		message = JSONUtil.parse(value).toBean(WorksCommentMessage.class);
		return message;
		
	}

// 分页扫描Keys（避免阻塞）
	private Set<String> scanKeys(String pattern) {
		Set<String> keys = new HashSet<>();
		keys = redisTemplate.keys(pattern);
		return keys;
	}

	// 批量插入（分批次提交）
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void batchInsert(List<WorksCommentMessage> list) {
		int batchSize = 500;
		for (int i = 0; i < list.size(); i += batchSize) {
			int end = Math.min(i + batchSize, list.size());
			List<SocialWorksCommentDO> subList = BeanUtils.toBean(list.subList(i, end), SocialWorksCommentDO.class);
			log.info("spaceComment插入条数：{}", subList.size());
			log.info("spaceComment 数据：{}",subList);
			socialWorksCommentService.saveBatch(subList);
		}
	}
}

package com.fozmo.ym.module.social.core.mq.comment;

import com.fozmo.ym.framework.common.util.json.JsonUtils;
import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import com.fozmo.ym.module.social.core.mq.comment.message.WorksCommentMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_COMMENT_KEY_PREFIX;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_REPLY_KEY_PREFIX;

/**
 *  空间评论消费者
 *
 */
@Component
@Slf4j
public class WorksCommentConsumer extends AbstractRedisStreamMessageListener<WorksCommentMessage> {


	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	/**
	 * 处理消息
	 *
	 * @param message 消息
	 */
	@Override
	public void onMessage(WorksCommentMessage message) {
		processMsg(message);
	}
	

	private void processMsg(WorksCommentMessage msg) {
		String commentKey = WORKS_COMMENT_KEY_PREFIX + msg.getWorksId();
		// 处理主评论
		if (msg.getCommentType() == 0) {
			// 写入Redis ZSet（按时间排序）
			redisTemplate.opsForZSet().add(
					commentKey,
					JsonUtils.toJsonString(msg),
					msg.getCommentTime()
			                              );
		}
		// 处理回复
		else {
			String replyKey = WORKS_REPLY_KEY_PREFIX+msg.getWorksId()+":"+msg.getReplyId();
			// Zset（key=消息ID）
			// 写入Redis ZSet（按时间排序）
			redisTemplate.opsForZSet().add(
					replyKey,
					JsonUtils.toJsonString(msg),
					msg.getCommentTime()
			                              );
		}
		
		// 3. 双删策略
		deletePageCache(msg.getReplyId());
	}


	private void deletePageCache(Long commentKey) {
		String pattern = "page:" + commentKey + ":*";
		Set<String> keys = redisTemplate.keys(pattern);
		redisTemplate.delete(keys); // 立即删除
		
		// 延迟二次删除（500ms）
		new Thread(() -> {
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				log.error("延迟删除异常", e);
			}
			redisTemplate.delete(keys);
		}).start();
	}
}

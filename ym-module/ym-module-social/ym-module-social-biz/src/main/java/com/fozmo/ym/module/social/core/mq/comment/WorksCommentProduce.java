package com.fozmo.ym.module.social.core.mq.comment;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.social.api.dto.WorksCommentDTO;
import com.fozmo.ym.module.social.core.mq.comment.message.WorksCommentMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/***
 * <p>
 *  空间评论的生产者
 */

@Slf4j
@Component
public class WorksCommentProduce {

	@Resource
	private RedisMQTemplate redisMQTemplate; // 重点：注入 RedisMQTemplate 对象

	public void sendComment(WorksCommentDTO worksCommentDTO) {
		// 生成 唯一Id
		WorksCommentMessage worksCommentMessage = BeanUtils.toBean(worksCommentDTO, WorksCommentMessage.class);
		redisMQTemplate.send(worksCommentMessage);
	}
}

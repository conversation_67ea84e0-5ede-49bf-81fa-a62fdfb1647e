package com.fozmo.ym.module.social.core.mq.comment.message;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data

public class SpaceCommentMessage extends AbstractRedisStreamMessage {

	private Long id;
	
	private Long spaceId;
	
	private String spaceName;
	
	private Long replyId;
	
	private Long accountId;
	private String accountName;
	
	private String nickname;
	
	private Long rightsId;
	
	private  String avatar;
	
	private String inviteIds;
	
	private String inviteAccountName;
	
	private String inviteAvatar;
	
	private Long  inviteRightsId;
	
	private String inviteNickName;
	
	private Integer commentType;
	
	private String commentContent;
	
	private Long commentTime;
	
	private String tenantCode;
	
	private Long createId;
	
	private String creator;
}

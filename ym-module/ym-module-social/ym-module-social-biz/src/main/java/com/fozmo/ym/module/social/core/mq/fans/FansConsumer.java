package com.fozmo.ym.module.social.core.mq.fans;

//import cn.hutool.core.util.ObjectUtil;
//import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
//import com.fozmo.ym.module.social.controller.admin.fans.vo.FansSaveRespVO;
//import com.fozmo.ym.module.social.core.mq.fans.message.FansMessage;
//import com.fozmo.ym.module.social.service.fans.SocialFansService;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;


/// **
// * 粉丝消费者
// */
//@Component
//@Slf4j
//public class FansConsumer extends AbstractRedisStreamMessageListener<FansMessage> {
//
//	@Resource
//	private SocialFansService socialFansService;
//	/**
//	 * 处理消息
//	 *
//	 * @param message 消息
//	 */
//	@Override
//	public void onMessage(FansMessage message) {
//		Integer operType = message.getOperType();
//		if (ObjectUtil.isEmpty(operType)) {
//			return;
//		}
//		if (operType.equals(0)) {
//			FansSaveRespVO respVO = new FansSaveRespVO();
//			respVO.setAccountId(message.getAccountId());
//			respVO.setFansAccountId(message.getFansAccountId());
//			socialFansService.addFans(respVO);
//		}else {
//			// s删除
//			socialFansService.deleteFans(message.getFansAccountId(), message.getFansAccountId());
//		}
//	}
//}

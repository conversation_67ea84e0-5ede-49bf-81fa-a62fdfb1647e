package com.fozmo.ym.module.social.core.mq.fans;

//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
//import com.fozmo.ym.module.social.api.dto.FansOperDTO;
//import com.fozmo.ym.module.social.core.mq.fans.message.FansMessage;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//
/// **
// * 关注生产者
// */
//@Slf4j
//@Component
//public class FansProduce {
//
//	@Resource
//	private RedisMQTemplate redisMQTemplate;
//
//	public void sendComment(FansOperDTO fansOperDTO) {
//		// 生成 唯一Id
//		FansMessage fansMessage = BeanUtils.toBean(fansOperDTO, FansMessage.class);
//		redisMQTemplate.send(fansMessage);
//	}
//}

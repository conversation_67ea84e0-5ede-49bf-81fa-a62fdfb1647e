package com.fozmo.ym.module.social.core.mq.social;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindSaveReqVO;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserSaveReqVO;
import com.fozmo.ym.module.social.core.mq.social.message.SocialUserMessage;
import com.fozmo.ym.module.social.service.bind.SocialBindService;
import com.fozmo.ym.module.social.service.user.SocialUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
/**
 *  社交用户消费者
 */
@Component
@Slf4j
public class SocialUserConsumer extends AbstractRedisStreamMessageListener<SocialUserMessage> {


	@Resource
	private SocialUserService socialUserService;
	
	@Resource
	private SocialBindService socialBindService;


	/**
	 * 处理消息
	 *
	 * @param message 消息
	 */
	@Override
	public void onMessage(SocialUserMessage message) {
		Integer operType = message.getOperType();
		if (ObjectUtil.isEmpty(operType)) {
			return;
		}
		
		if (operType == 0) {
			// 组装新增用户数据
			SocialUserSaveReqVO socialUserSaveReqVO = new SocialUserSaveReqVO();
			socialUserSaveReqVO = BeanUtils.toBean(message, SocialUserSaveReqVO.class);
			socialUserSaveReqVO.setCreateId(message.getAccountId());
			socialUserSaveReqVO.setType(message.getSocialType());
			socialUserSaveReqVO.setMobile(message.getMobile());
			socialUserSaveReqVO.setCode(message.getUserCode());
			socialUserSaveReqVO.setUserCode(message.getUserCode());
			Long socialUserId = socialUserService.createUser(socialUserSaveReqVO);
			
			if (ObjectUtil.isNotEmpty(socialUserId)) {
				// 组装绑定数据
				SocialBindSaveReqVO socialBindSaveReqVO = new SocialBindSaveReqVO();
				socialBindSaveReqVO = BeanUtils.toBean(message, SocialBindSaveReqVO.class);
				socialBindSaveReqVO.setCreateId(message.getAccountId());
				socialBindSaveReqVO.setSocialType(message.getSocialType());
				socialBindSaveReqVO.setUserId(socialUserId);
				Long socialBindId = socialBindService.createBind(socialBindSaveReqVO);
			}
			
		}
		if (operType == 2) {
			// 更新社交用户
			SocialUserSaveReqVO socialUserSaveReqVO = new SocialUserSaveReqVO();
			socialUserSaveReqVO = BeanUtils.toBean(message, SocialUserSaveReqVO.class);
			socialUserSaveReqVO.setUpdaterId(message.getAccountId());
			socialUserSaveReqVO.setId(message.getId());
			socialUserSaveReqVO.setType(message.getSocialType());
			socialUserService.updateUser(socialUserSaveReqVO);
		}
	}
}

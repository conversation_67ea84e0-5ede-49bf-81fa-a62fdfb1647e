package com.fozmo.ym.module.social.core.mq.social;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.social.api.dto.SocialUserMessageDTO;
import com.fozmo.ym.module.social.core.mq.social.message.SocialUserMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SocialUserProduce {

	@Resource
	private RedisMQTemplate redisMQTemplate; // 重点：注入 RedisMQTemplate 对象
	
	public void sendSocialUser( SocialUserMessageDTO socialUser) {
		
		SocialUserMessage socialUserMessage = BeanUtils.toBean( socialUser, SocialUserMessage.class);
		redisMQTemplate.send(socialUserMessage);
	}
}

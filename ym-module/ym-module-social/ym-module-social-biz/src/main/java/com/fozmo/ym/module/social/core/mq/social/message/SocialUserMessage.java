package com.fozmo.ym.module.social.core.mq.social.message;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class SocialUserMessage extends AbstractRedisStreamMessage {
	

/**
 * 0 新增 1 删除
 */
	private Integer operType;
	/**平台用户id*/
	private Long id;
	/** 平台用户 token*/
	private String token;
	/** 平台用户唯一标识 */
	private String userCode;
	/** 平台用户 openId*/
	private String openId;
	/** 平台用户手机号*/
	private String mobile;
	/** 账户Id*/
	private Long accountId;
	/** 平台Id */
	private Long socialType;
}

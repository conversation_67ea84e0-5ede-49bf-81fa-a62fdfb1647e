package com.fozmo.ym.module.social.dal.dataobject.bind;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 用户第三方社交授权信息 DO
 *
 * <AUTHOR>
 */
@TableName("social_bind")
@KeySequence("social_bind_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialBindDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 平台用户ID
     */
    private Long userId;
    /**
     * 用户类型（如0-个人用户，1-企业用户）
     */
    private Long userType;
    /**
     * 账户ID（关联账户表）
     */
    private Long accountId;
    /**
     * 社交平台类型（1-微信，2-支付宝，3-微博等）
     */
    private Long socialType;
    /**
     * 创建人ID
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createDate;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateDate;
    /**
     * 租户编码
     */
    private String tenantCode;

}
package com.fozmo.ym.module.social.dal.dataobject.comment;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 空间评论 DO
 *
 * <AUTHOR>
 */
@TableName("space_comment")
@KeySequence("space_comment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialSpaceCommentDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 空间Id
     */
    private Long spaceId;
    @TableField(exist = false)
    private String spaceName;
    /**
     * 回答id
     */
    private Long replyId;

    /**
     * 账号
     */
    private Long accountId;
    /**
     * 评论人
     */
    private String accountName;
    @TableField(exist = false)
    private String nickname;
    @TableField(exist = false)
    private Long rightsId;

    @TableField(exist = false)
    private String avatar;
    
    /**
     * 艾特邀请观看
     */
    private String inviteIds;

    @TableField(exist = false)
    private String inviteAccountName;
    @TableField(exist = false)
    private String inviteAvatar;
    @TableField(exist = false)
    private Long inviteRightsId;
    @TableField(exist = false)
    private String inviteNickName;
    /**
     * 评论内容
     */
    private String commentContent;
    /**
     * 回复类型 0评论 1回复
     */
    private Integer commentType;

    /**
     * 评论时间
     */
    private LocalDateTime commentTime;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建用户名称
     */
    private String creator;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    // 回复列表
    @TableField(exist = false)
    private List<SocialSpaceCommentDO> replyList;
    @TableField(exist = false)
    private Long total;
}
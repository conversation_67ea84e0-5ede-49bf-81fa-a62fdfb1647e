package com.fozmo.ym.module.social.dal.dataobject.fans;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("social_fans")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialFansDO extends TenantBaseDO {

	@TableId
	private Long id;
	
	private Long accountId;
	
	@TableField(exist = false)
	private String accountName;

	@TableField(exist = false)
	private String nickname;

	@TableField(exist = false)
	private String avatar;

	@TableField(exist = false)
	private Long rightsId;
	
	private Long fansAccountId;
	
	@TableField(exist = false)
	private String fansAccountName;
	
	@TableField(exist = false)
	private String fansNickname;
	
	@TableField(exist = false)
	private String fansAvatar;
	
	@TableField(exist = false)
	private Long fansRightsId;
	@TableField(exist = false)
	private boolean fansStatus;
	
	private LocalDateTime fansTime;

	/**
	 * 创建人ID
	 */
	private Long createId;
	/**
	 * 创建日期
	 */
	private LocalDate createDate;
	/**
	 * 更新人ID
	 */
	private Long updaterId;
	/**
	 * 更新日期
	 */
	private LocalDate updateDate;
	/**
	 * 租户编码
	 */
	private String tenantCode;
	
}

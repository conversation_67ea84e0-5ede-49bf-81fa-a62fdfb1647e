package com.fozmo.ym.module.social.dal.dataobject.favorite;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 空间收藏 DO
 *
 * <AUTHOR>
 */
@TableName("space_favorite")
@KeySequence("space_favorite_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceFavoriteDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 空间id
     */
    private Long spaceId;
    /**
     * 收藏人id
     */
    private Long accountId;
    /**
     * 收藏人
     */
    private String accountName;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * favorite_time 收藏时间
     */
    private LocalDateTime favoriteTime;
    /**
     * 创建用户名称
     */
    private String creator;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

}
package com.fozmo.ym.module.social.dal.dataobject.like;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 作品点赞 DO
 *
 * <AUTHOR>
 */
@TableName("space_works_like")
@KeySequence("space_works_like_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorksLikeDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 作品id
     */
    private Long worksId;
    /**
     * 点赞人
     */
    private Long accountId;
    /**
     * 点赞人姓名
     */
    private String accountName;
    /**
     * 点赞时间
     */
    private LocalDateTime likeTime;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;
    @TableField(exist = false)
    private String avatar;
    @TableField(exist = false)
    private Boolean likeStatus;

}
package com.fozmo.ym.module.social.dal.dataobject.share;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 作品分享 DO
 *
 * <AUTHOR>
 */
@TableName("space_works_share")
@KeySequence("space_works_share_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorksShareDO extends TenantBaseDO {

    /**
     * 作品id
     */
    @TableId
    private Long worksId;
    /**
     * id
     */
    private Long id;
    /**
     * 分享id
     */
    private Long accountId;
    /**
     * 分享人名称
     */
    private String accountName;
    /**
     * 分享时间
     */
    private LocalDateTime shareTime;
    /**
     * 分享码值
     */
    private String shareCode;
    /**
     * 分享渠道 0 微信 1 抖音 2 其他
     */
    private Integer shareChannel;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    private String tenantCode;
    @TableField(value = "share_qrcode")
    private String shareQrcode;
    
    private String sharePage;
    
    private String env;
    
    private String scene;
    
    private Long worksAccountId;
    
    private String worksCover;
    
    private Long worksRightsId;
    
    private String worksAccountName;
    
    private String worksAccountCover;
    
    private String worksName;


}
package com.fozmo.ym.module.social.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 用户授权信息 DO
 *
 * <AUTHOR>
 */
@TableName("social_user")
@KeySequence("social_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialUserDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 认证类型（如0-微信,1-手机号）
     */
    private Long type;
    /**
     * 用户唯一标识
     */
    private String userCode;
    /**
     * 第三方开放平台ID（如微信openid）
     */
    private String openId;
    /**
     * 登录令牌
     */
    private String token;
    /**
     * 手机号（格式：+86 13812345678）
     */
    private String mobile;
    /**
     * 原始Token信息（JSON格式）
     */
    private String rawTokenInfo;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 头像URL地址
     */
    private String avatar;
    /**
     * 原始用户信息（JSON格式）
     */
    private String rawUserInfo;
    /**
     * 最后登录授权码
     */
    private String code;
    /**
     * 最后登录状态参数
     */
    private String state;
    /**
     * 创建人ID
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createDate;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateDate;
    /**
     * 租户编码
     */
    private String tenantCode;

}
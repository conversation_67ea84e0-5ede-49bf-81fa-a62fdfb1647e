package com.fozmo.ym.module.social.dal.mysql.bind;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindPageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.bind.SocialBindDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 用户第三方社交授权信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SocialBindMapper extends BaseMapperX<SocialBindDO> {

    default PageResult<SocialBindDO> selectPage(SocialBindPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SocialBindDO>()
                .eqIfPresent(SocialBindDO::getUserId, reqVO.getUserId())
                .eqIfPresent(SocialBindDO::getUserType, reqVO.getUserType())
                .eqIfPresent(SocialBindDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(SocialBindDO::getSocialType, reqVO.getSocialType())
                .eqIfPresent(SocialBindDO::getCreateId, reqVO.getCreateId())
                .betweenIfPresent(SocialBindDO::getCreateDate, reqVO.getCreateDate())
                .betweenIfPresent(SocialBindDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SocialBindDO::getUpdaterId, reqVO.getUpdaterId())
                .betweenIfPresent(SocialBindDO::getUpdateDate, reqVO.getUpdateDate())
                .eqIfPresent(SocialBindDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SocialBindDO::getId));
    }

    default SocialBindDO getBindByUserId(Long socialUserId){
        return selectOne(SocialBindDO::getUserId, socialUserId);
    }

    default List<SocialBindDO> selectByAccountId(Long accountId){
        return selectList(SocialBindDO::getAccountId, accountId,SocialBindDO::getDeleted,false);
    }
}
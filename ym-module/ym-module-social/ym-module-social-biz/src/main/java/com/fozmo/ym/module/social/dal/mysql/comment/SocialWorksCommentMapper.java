package com.fozmo.ym.module.social.dal.mysql.comment;

import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialWorksCommentDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 作品评论 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SocialWorksCommentMapper extends BaseMapperX<SocialWorksCommentDO> {

//    default PageResult<WorksCommentDO> selectPage(WorksCommentPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<WorksCommentDO>()
//                .eqIfPresent(WorksCommentDO::getWorksId, reqVO.getWorksId())
//                .eqIfPresent(WorksCommentDO::getReplyId, reqVO.getReplyId())
//                .eqIfPresent(WorksCommentDO::getInviteIds, reqVO.getInviteIds())
//                .eqIfPresent(WorksCommentDO::getCommentContent, reqVO.getCommentContent())
//                .eqIfPresent(WorksCommentDO::getAccountId, reqVO.getAccountId())
//                .likeIfPresent(WorksCommentDO::getAccountName, reqVO.getAccountName())
//                .betweenIfPresent(WorksCommentDO::getCommentTime, reqVO.getCommentTime())
//                .eqIfPresent(WorksCommentDO::getCreateId, reqVO.getCreateId())
//                .eqIfPresent(WorksCommentDO::getCreateData, reqVO.getCreateData())
//                .betweenIfPresent(WorksCommentDO::getCreateTime, reqVO.getCreateTime())
//                .eqIfPresent(WorksCommentDO::getUpdateId, reqVO.getUpdateId())
//                .eqIfPresent(WorksCommentDO::getUpdateData, reqVO.getUpdateData())
//                .eqIfPresent(WorksCommentDO::getTenantCode, reqVO.getTenantCode())
//                .orderByDesc(WorksCommentDO::getId));
//    }

}

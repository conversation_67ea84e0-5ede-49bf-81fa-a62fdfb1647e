package com.fozmo.ym.module.social.dal.mysql.fans;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.app.fans.vo.AppFansPageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.fans.SocialFansDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SocialFansMapper extends BaseMapperX<SocialFansDO> {
default PageResult<SocialFansDO> selectPage(AppFansPageReqVO reqVO) {
	return selectPage(reqVO, new LambdaQueryWrapperX<SocialFansDO>()
			                         .eqIfPresent(SocialFansDO::getAccountId, reqVO.getAccountId())
			.eqIfPresent(SocialFansDO::getFansAccountId, reqVO.getFansAccountId())
			                         .orderByDesc(SocialFansDO::getId));
}

	default SocialFansDO selectByAccountId(Long accountId, Long loginId){
		return selectFirstOne(SocialFansDO::getAccountId, accountId,SocialFansDO::getFansAccountId, loginId,SocialFansDO::getDeleted,false);
	}
}

package com.fozmo.ym.module.social.dal.mysql.favorite;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.SpaceFavoritePageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.SpaceFavoriteDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间收藏 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceFavoriteMapper extends BaseMapperX<SpaceFavoriteDO> {

    default PageResult<SpaceFavoriteDO> selectPage(SpaceFavoritePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceFavoriteDO>()
                .eqIfPresent(SpaceFavoriteDO::getSpaceId, reqVO.getSpaceId())
                .eqIfPresent(SpaceFavoriteDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(SpaceFavoriteDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(SpaceFavoriteDO::getCreateId, reqVO.getCreateId())
                .betweenIfPresent(SpaceFavoriteDO::getFavoriteTime, reqVO.getFavoriteTime())
                .likeIfPresent(SpaceFavoriteDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceFavoriteDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceFavoriteDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceFavoriteDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceFavoriteDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceFavoriteDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceFavoriteDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SpaceFavoriteDO::getId));
    }

}
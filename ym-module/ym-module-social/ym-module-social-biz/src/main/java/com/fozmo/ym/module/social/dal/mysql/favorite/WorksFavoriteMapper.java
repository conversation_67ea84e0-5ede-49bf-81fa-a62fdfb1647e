package com.fozmo.ym.module.social.dal.mysql.favorite;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoritePageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.WorksFavoriteDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface WorksFavoriteMapper extends BaseMapperX<WorksFavoriteDO> {

default PageResult<WorksFavoriteDO> selectPage(WorksFavoritePageReqVO reqVO) {
	return selectPage(reqVO, new LambdaQueryWrapperX<WorksFavoriteDO>()
			                         .eqIfPresent(WorksFavoriteDO::getWorksId, reqVO.getWorksId())
			                         .eqIfPresent(WorksFavoriteDO::getAccountId, reqVO.getAccountId())
			                         .likeIfPresent(WorksFavoriteDO::getAccountName, reqVO.getAccountName())
			                         .eqIfPresent(WorksFavoriteDO::getCreateId, reqVO.getCreateId())
			                         .betweenIfPresent(WorksFavoriteDO::getFavoriteTime, reqVO.getFavoriteTime())
			                         .eqIfPresent(WorksFavoriteDO::getCreateData, reqVO.getCreateData())
			                         .betweenIfPresent(WorksFavoriteDO::getCreateTime, reqVO.getCreateTime())
			                         .eqIfPresent(WorksFavoriteDO::getUpdateId, reqVO.getUpdateId())
			                         .eqIfPresent(WorksFavoriteDO::getUpdateData, reqVO.getUpdateData())
			                         .eqIfPresent(WorksFavoriteDO::getTenantCode, reqVO.getTenantCode())
			                         .orderByDesc(WorksFavoriteDO::getId));
}

}
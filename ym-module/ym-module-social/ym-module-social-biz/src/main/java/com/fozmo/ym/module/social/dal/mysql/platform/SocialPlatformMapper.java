package com.fozmo.ym.module.social.dal.mysql.platform;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformPageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.platform.SocialPlatformDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 社交平台 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SocialPlatformMapper extends BaseMapperX<SocialPlatformDO> {

    default PageResult<SocialPlatformDO> selectPage(SocialPlatformPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SocialPlatformDO>()
                .likeIfPresent(SocialPlatformDO::getName, reqVO.getName())
                .eqIfPresent(SocialPlatformDO::getCode, reqVO.getCode())
                .eqIfPresent(SocialPlatformDO::getClientId, reqVO.getClientId())
                .eqIfPresent(SocialPlatformDO::getClientSecret, reqVO.getClientSecret())
                .eqIfPresent(SocialPlatformDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SocialPlatformDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(SocialPlatformDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SocialPlatformDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SocialPlatformDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(SocialPlatformDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SocialPlatformDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SocialPlatformDO::getId));
    }

    default SocialPlatformDO getPlatformByCode(String code){
        return selectOne(SocialPlatformDO::getCode, code,SocialPlatformDO::getDeleted,false);
    }
}
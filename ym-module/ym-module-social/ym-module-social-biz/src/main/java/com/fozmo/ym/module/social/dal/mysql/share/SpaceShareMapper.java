package com.fozmo.ym.module.social.dal.mysql.share;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceSharePageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间分享 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceShareMapper extends BaseMapperX<SpaceShareDO> {

    default PageResult<SpaceShareDO> selectPage(SpaceSharePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceShareDO>()
                .eqIfPresent(SpaceShareDO::getSpaceId, reqVO.getSpaceId())
                .eqIfPresent(SpaceShareDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(SpaceShareDO::getAccountName, reqVO.getAccountName())
                .betweenIfPresent(SpaceShareDO::getShareTime, reqVO.getShareTime())
                .eqIfPresent(SpaceShareDO::getShareCode, reqVO.getShareCode())
                .eqIfPresent(SpaceShareDO::getShareChannel, reqVO.getShareChannel())
                .eqIfPresent(SpaceShareDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceShareDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceShareDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceShareDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceShareDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceShareDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceShareDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceShareDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SpaceShareDO::getId));
    }

    default SpaceShareDO getShareBySpaceId(Long spaceId, Long accountId){
        return selectOne(SpaceShareDO::getSpaceId, spaceId,SpaceShareDO::getAccountId, accountId,SpaceShareDO::getDeleted,false );
    }

    default SpaceShareDO getShareByScene(String scene){
        return selectOne(SpaceShareDO::getScene, scene,SpaceShareDO::getDeleted,false );
    }
}
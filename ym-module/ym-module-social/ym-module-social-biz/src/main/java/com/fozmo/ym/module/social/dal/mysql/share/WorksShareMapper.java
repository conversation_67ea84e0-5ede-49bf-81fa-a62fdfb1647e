package com.fozmo.ym.module.social.dal.mysql.share;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksSharePageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.WorksShareDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 作品分享 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorksShareMapper extends BaseMapperX<WorksShareDO> {

    default PageResult<WorksShareDO> selectPage(WorksSharePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WorksShareDO>()
                .eqIfPresent(WorksShareDO::getWorksId, reqVO.getWorksId())
                .eqIfPresent(WorksShareDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(WorksShareDO::getAccountName, reqVO.getAccountName())
                .betweenIfPresent(WorksShareDO::getShareTime, reqVO.getShareTime())
                .eqIfPresent(WorksShareDO::getShareCode, reqVO.getShareCode())
                .eqIfPresent(WorksShareDO::getShareChannel, reqVO.getShareChannel())
                .eqIfPresent(WorksShareDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(WorksShareDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(WorksShareDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(WorksShareDO::getUpdateId, reqVO.getUpdateId())
                .eqIfPresent(WorksShareDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(WorksShareDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(WorksShareDO::getId));
    }

    default WorksShareDO getShareByWorksId(Long worksId, Long accountId){
        return selectOne(WorksShareDO::getWorksId, worksId,WorksShareDO::getAccountId, accountId,WorksShareDO::getDeleted,false);
    }

    default WorksShareDO getWorksShareByScene(String scene){
        return selectOne(WorksShareDO::getScene, scene,WorksShareDO::getDeleted,false);
    }
}
package com.fozmo.ym.module.social.dal.mysql.user;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserPageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.user.SocialUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户授权信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SocialUserMapper extends BaseMapperX<SocialUserDO> {

    default PageResult<SocialUserDO> selectPage(SocialUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SocialUserDO>()
                .eqIfPresent(SocialUserDO::getType, reqVO.getType())
                .eqIfPresent(SocialUserDO::getUserCode, reqVO.getUserCode())
                .eqIfPresent(SocialUserDO::getOpenId, reqVO.getOpenId())
                .eqIfPresent(SocialUserDO::getToken, reqVO.getToken())
                .eqIfPresent(SocialUserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(SocialUserDO::getRawTokenInfo, reqVO.getRawTokenInfo())
                .likeIfPresent(SocialUserDO::getNickName, reqVO.getNickName())
                .eqIfPresent(SocialUserDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(SocialUserDO::getRawUserInfo, reqVO.getRawUserInfo())
                .eqIfPresent(SocialUserDO::getCode, reqVO.getCode())
                .eqIfPresent(SocialUserDO::getState, reqVO.getState())
                .eqIfPresent(SocialUserDO::getCreateId, reqVO.getCreateId())
                .betweenIfPresent(SocialUserDO::getCreateDate, reqVO.getCreateDate())
                .betweenIfPresent(SocialUserDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SocialUserDO::getUpdaterId, reqVO.getUpdaterId())
                .betweenIfPresent(SocialUserDO::getUpdateDate, reqVO.getUpdateDate())
                .eqIfPresent(SocialUserDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SocialUserDO::getId));
    }

    default SocialUserDO getUserByCode(String unionid){
        return selectOne(SocialUserDO::getUserCode, unionid,SocialUserDO::getDeleted,false);
    }

    default SocialUserDO getUserByOpenId(String openId){
        return selectOne(SocialUserDO::getOpenId, openId,SocialUserDO::getDeleted,false);
    }

    default SocialUserDO getUserByMobile(Long typeCode, String mobile){
        return selectOne(SocialUserDO::getType,typeCode,SocialUserDO::getMobile, mobile,SocialUserDO::getDeleted,false);
    }
}
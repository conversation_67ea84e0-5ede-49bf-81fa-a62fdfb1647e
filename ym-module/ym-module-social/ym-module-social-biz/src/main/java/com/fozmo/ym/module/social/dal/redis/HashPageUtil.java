package com.fozmo.ym.module.social.dal.redis;

import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class HashPageUtil {

@Resource
private RedisTemplate<String, Object> redisTemplate;

	/**
	 *  存放 hash 缓存
	 */
	public boolean hput(String key, String hkey, Object value) {
		try {
			redisTemplate.opsForHash().put(key, hkey, value);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	/**
	 * 分页存取数据
	 * @param key  hash存取的key
	 * @param hkey hash存取的hkey
	 * @param score 指定字段排序
	 */
	public boolean setPage(String key, String hkey, double score, String value){
		boolean result = false;
		try {
			result = Boolean.TRUE.equals(redisTemplate.opsForZSet().add(key + ":page", hkey, score));
		} catch (Exception e) {
			e.printStackTrace();
		}
		//设置辅助分页的过期时间
		redisTemplate.expire(key+":page",1800000 , TimeUnit.MILLISECONDS);
		//redisTemplate.expire(key,60000 , TimeUnit.MILLISECONDS);
		return result;
	}

	/**
	 * 分页取出 hash中hkey值
	 */
	public Set<Object> getPage(String key, int pageNo, int pageSize){
		Set<Object> result = null;
		try {
			result = redisTemplate.opsForZSet().rangeByScore(key + ":page", 1, 100000, (long) (pageNo - 1) * pageSize, pageSize);//1 100000代表score的排序氛围值，即从1-100000的范围
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 计算key值对应的数量
	 */
	public Long getSize(String key){
		long num = 0L;
		try {
			Long size = redisTemplate.opsForZSet().zCard(key+":page");
			assert size != null;
			return size.intValue() + num;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return num;
	}
}

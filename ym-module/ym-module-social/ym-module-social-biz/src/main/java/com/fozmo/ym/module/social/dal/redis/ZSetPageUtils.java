package com.fozmo.ym.module.social.dal.redis;

import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class ZSetPageUtils {

	@Resource
	private RedisTemplate<String, String> redisTemplate;
	
	// 滚动分页查询
	public RedisZsetResult scrollQuery(String key, double maxScore, int offset, int size) {
		Set<ZSetOperations.TypedTuple<String>> tuples = redisTemplate.opsForZSet()
				                                                .reverseRangeByScoreWithScores(key, 0, maxScore, offset, size);

		if (tuples == null || tuples.isEmpty()) {
			return null;
		}
		
		List<String> data = new ArrayList<>();
		double minScore = Double.MAX_VALUE;
		int newOffset = 0;
		for (ZSetOperations.TypedTuple<String> tuple : tuples) {
			data.add(tuple.getValue());
			double score = tuple.getScore();
			if (score < minScore) {
				minScore = score;
				newOffset = 1; // 重置计数器
			} else if (score == minScore) {
				newOffset++;   // 相同分数计数
			}
		}
		return new RedisZsetResult(data, minScore, newOffset);
	}
}

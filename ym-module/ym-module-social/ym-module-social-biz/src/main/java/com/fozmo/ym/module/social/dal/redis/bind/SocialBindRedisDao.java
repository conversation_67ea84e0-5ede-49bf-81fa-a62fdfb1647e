package com.fozmo.ym.module.social.dal.redis.bind;

import com.fozmo.ym.module.social.dal.dataobject.bind.SocialBindDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SOCIAL_BIND;

@Repository
public class SocialBindRedisDao {

	@Resource
	private RedisTemplate<String, String> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	/**
	 *  新增绑定数据
	 */
	
	public void addBind(SocialBindDO socialBindDO) {
		String bindKey = SOCIAL_BIND+socialBindDO.getCreateId();
		redisTemplate.opsForHash().put(bindKey,socialBindDO.getId().toString(),socialBindDO);
	}
	
	public void deleteBind(Long accountId,Long bindId) {
		String bindKey = SOCIAL_BIND+accountId;
		redisTemplate.opsForHash().delete(bindKey,bindId.toString());
	}
}

package com.fozmo.ym.module.social.dal.redis.comment;

import com.fozmo.ym.module.social.constants.SocialRedisConstants;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialSpaceCommentDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


@Repository
public class SpaceCommentRedisDao {

	@Resource
	private RedisTemplate<String, String> redisTemplate;
	
	public Set<String> getStaticCommentPage(String key, int pageNum, int pageSize) {
		long start = (long) (pageNum - 1) * pageSize; // 计算起始索引
		long end = start + pageSize - 1;       // 计算结束索引
		// 按分数倒序获取分页数据（最新数据在前）
		return redisTemplate.opsForZSet().reverseRange(key, start, end);
	}
	
	
	public Long getCommentTotal(String key) {
		return redisTemplate.opsForZSet().size(key);
	}

    public void deleteCommentBySpaceId(Long spaceId) {
        redisTemplate.delete(SocialRedisConstants.SPACE_COMMENT_KEY_PREFIX + spaceId);
    }

    public void deleteReplyBySpaceId(Long spaceId, Long replyId) {
        redisTemplate.delete(SocialRedisConstants.SPACE_REPLY_KEY_PREFIX + spaceId + ":" + replyId);
    }

    public boolean hasComment(Long spaceId) {
        return redisTemplate.hasKey(SocialRedisConstants.SPACE_COMMENT_KEY_PREFIX + spaceId);
    }

    public boolean hasReply(Long spaceId, Long replyId) {
        return redisTemplate.hasKey(SocialRedisConstants.SPACE_REPLY_KEY_PREFIX + spaceId + ":" + replyId);
    }

    public List<SocialSpaceCommentDO> getCommentList(Long spaceId) {

        Set<String> result = redisTemplate.opsForZSet().range(SocialRedisConstants.SPACE_COMMENT_KEY_PREFIX + spaceId, 0, -1);
        result.stream().map(Long::parseLong).map(id -> {
            SocialSpaceCommentDO commentDO = new SocialSpaceCommentDO();
            commentDO.setId(id);
            return commentDO;
        }).toList();
        return null;
    }


	
}

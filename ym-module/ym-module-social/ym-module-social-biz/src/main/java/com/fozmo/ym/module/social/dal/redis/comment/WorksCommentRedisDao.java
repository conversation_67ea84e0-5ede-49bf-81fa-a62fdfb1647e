package com.fozmo.ym.module.social.dal.redis.comment;

import com.fozmo.ym.module.social.dal.dataobject.comment.SocialWorksCommentDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_COMMENT_KEY_PREFIX;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_REPLY_KEY_PREFIX;


@Repository
public class WorksCommentRedisDao {

	@Resource
	private RedisTemplate<String, String> redisTemplate;
	
	public Set<String> getStaticCommentPage(String key, int pageNum, int pageSize) {
		long start = (long) (pageNum - 1) * pageSize; // 计算起始索引
		long end = start + pageSize - 1;       // 计算结束索引
		// 按分数倒序获取分页数据（最新数据在前）
		return redisTemplate.opsForZSet().reverseRange(key, start, end);
	}
	
	
	public Long getCommentTotal(String key) {
		return redisTemplate.opsForZSet().size(key);
	}


	public boolean hasWorksComment(Long worksId) {
		String key = WORKS_COMMENT_KEY_PREFIX + worksId;
		return redisTemplate.hasKey(key);
	}

	public boolean hasReply(Long worksId, Long replyId) {
		String key = WORKS_REPLY_KEY_PREFIX + worksId + ":" + replyId;
		return redisTemplate.hasKey(key);
	}

	public void deleteCommentByWorksId(Long worksId) {
		String key = WORKS_COMMENT_KEY_PREFIX + worksId;
		redisTemplate.delete(key);
	}

	public void deleteReplyByWorksId(Long worksId, Long replyId) {
		String key = WORKS_REPLY_KEY_PREFIX + worksId + ":" + replyId;
		redisTemplate.delete(key);
	}

	public List<SocialWorksCommentDO> getCommentList(Long worksId) {
		Set<String> result = redisTemplate.opsForZSet().range(WORKS_COMMENT_KEY_PREFIX + worksId, 0, -1);
		return result.stream().map(Long::parseLong).map(id -> {
			SocialWorksCommentDO commentDO = new SocialWorksCommentDO();
			commentDO.setId(id);
			return commentDO;
		}).toList();
	}
}

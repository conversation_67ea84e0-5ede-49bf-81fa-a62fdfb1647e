package com.fozmo.ym.module.social.dal.redis.fans;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.dal.dataobject.fans.SocialFansDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.FANS_ADD_KEY_PREFIX;

@Slf4j
@Repository
public class FansRedisDao {

	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@Resource
	private RedissonClient redissonClient;

	// 加锁
	public void lock(Long id, Long timeoutMillis) {
		String lockKey = FANS_ADD_KEY_PREFIX+id;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			lock.lock(timeoutMillis, TimeUnit.MILLISECONDS);
			// 执行逻辑
		} finally {
			lock.unlock();
		}
	}
	
	// 解锁
	public void unlock(Long id, Long timeoutMillis) {
		String lockKey = FANS_ADD_KEY_PREFIX+id;
		try {
			RLock lock = redissonClient.getLock(lockKey);
		}catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}
		
	}
	/***
	 * 新增粉丝
	 
	 */
	public void addFans(SocialFansDO socialFansDO) {
		
		String fansKey = FANS_ADD_KEY_PREFIX+socialFansDO.getAccountId();
		redisTemplate.opsForHash().put(
				fansKey,
				socialFansDO.getFansAccountId().toString(),
				socialFansDO
		                             );
	}
	
	/**
	 * 删除粉丝
	 */
	public void deleteFans(Long accountId,Long fansAccountId) {
		String fansKey = FANS_ADD_KEY_PREFIX+accountId;
		HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
		hashOperations.delete(fansKey,fansAccountId.toString());
	}
	/**
	 *  为True 说明 存在 不能新增，为fase 说明不存在
	 */
	public Boolean checkFans(Long accountId,Long fansAccountId){
		String fansKey = FANS_ADD_KEY_PREFIX+accountId;
		
		if (redisTemplate.hasKey(fansKey)) {
			if (redisTemplate.opsForHash().hasKey(fansKey,fansAccountId.toString())){
				return true;
			}
		}
		return Boolean.FALSE;
	}
	/**
	 *  获取粉丝详情
	 */
	public SocialFansDO getFansInfo(Long accountId,Long fansAccountI){
		String fansKey = FANS_ADD_KEY_PREFIX+accountId;
		return (SocialFansDO) redisTemplate.opsForHash().get(fansKey,fansAccountI.toString());
	}
	
	
	public PageResult<SocialFansDO> getFansPage(Long accountId, Integer pageNo, Integer pageSize){
		PageResult<SocialFansDO> pageResult = new PageResult<>();
		String fansKey = FANS_ADD_KEY_PREFIX+accountId;
		Long total = redisTemplate.opsForHash().size(fansKey);
		// 构建Redis 查询 条件
		HashOperations<String, String, SocialFansDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				                          .count(1000)      // 每次扫描数量（优化性能）
				                          .build();
		List<Map.Entry<String, SocialFansDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SocialFansDO>> cursor = hashOps.scan(fansKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<SocialFansDO> socialFansDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, SocialFansDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			socialFansDOS= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal(total);
		pageResult.setList(socialFansDOS);
		return  pageResult;
	}

public List<SocialFansDO> getFansList(Long accountId){
	String fansKey = FANS_ADD_KEY_PREFIX+accountId;
	List<SocialFansDO> socialFansDOS = new ArrayList<>();
	// 构建Redis 查询 条件
	HashOperations<String, String, SocialFansDO> hashOps = redisTemplate.opsForHash();
	ScanOptions scanOptions = ScanOptions.scanOptions()
			                          .count(10000)      // 每次扫描数量（优化性能）
			                          .build();
	List<Map.Entry<String, SocialFansDO>> allResult = new ArrayList<>();
	try (Cursor<Map.Entry<String, SocialFansDO>> cursor = hashOps.scan(fansKey, scanOptions)) {
		while (cursor.hasNext()) {
			allResult.add(cursor.next());
		}
	}
	

	if (CollUtil.isNotEmpty(allResult)) {
		socialFansDOS= allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
	}
	return socialFansDOS;
}

	public Long queryFollowNum(Long accountId) {
		String fansALlKey = FANS_ADD_KEY_PREFIX + "*";

		HashOperations<String, String, SocialFansDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				.count(50000)      // 每次扫描数量（优化性能）
				.build();
		List<Map.Entry<String, SocialFansDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SocialFansDO>> cursor = hashOps.scan(fansALlKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}

		if (CollUtil.isNotEmpty(allResult)) {
			List<SocialFansDO> socialFansDOS = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
			if (CollUtil.isNotEmpty(socialFansDOS)) {
				socialFansDOS.stream().filter(item -> item.getFansAccountId().equals(accountId)).collect(Collectors.toList());
				return (long) socialFansDOS.size();
			} else {
				return 0L;
			}
		} else {
			return 0L;
		}

	}

	public Long queryFansNum(Long accountId) {
		String fansALlKey = FANS_ADD_KEY_PREFIX + accountId;
		HashOperations<String, String, SocialFansDO> hashOps = redisTemplate.opsForHash();
		return hashOps.size(fansALlKey);
	}
}

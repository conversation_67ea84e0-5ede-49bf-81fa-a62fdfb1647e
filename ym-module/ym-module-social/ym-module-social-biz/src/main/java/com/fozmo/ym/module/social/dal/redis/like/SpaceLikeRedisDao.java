package com.fozmo.ym.module.social.dal.redis.like;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.dal.dataobject.like.SpaceLikeDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SPACE_LIKE_KEY;

@Slf4j
@Repository
public class SpaceLikeRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	/**
	 * redis 存储空间点赞信息
	 */
	public void saveSpaceLike(SpaceLikeDO spaceLikeDO) {
		
		String spaceKey = SPACE_LIKE_KEY + spaceLikeDO.getSpaceId();
		String hk = spaceLikeDO.getAccountId().toString();
		redisTemplate.opsForHash().put(spaceKey, hk, spaceLikeDO);
	}
	
	/**
	 *  校验点赞 key 是否存在
	 */
	public boolean hasSpaceLike(Long spaceId,Long accountId) {
		String spaceKey = SPACE_LIKE_KEY + spaceId;
		String hk = accountId.toString();
		return redisTemplate.opsForHash().hasKey(spaceKey, hk);
	}
	
	
	/**
	 *  获取一个空间下的所有评论
	 */
	public List<SpaceLikeDO> getSpaceLike(Long spaceId) {
		String spaceKey = SPACE_LIKE_KEY + spaceId;
		List<Object> list = redisTemplate.opsForHash().values(spaceKey);
		return list.stream()
				.map(obj -> {
					try {
						return new ObjectMapper().readValue((String) obj, SpaceLikeDO.class);
					} catch (JsonProcessingException e) {
						throw new RuntimeException(e);
					}
				})
				.collect(Collectors.toList());
	}
	
	/***
	 *  删除某个空间单个评论
	 */
	public void deleteSpaceLike(Long spaceId,Long accountId) {
		String spaceKey = SPACE_LIKE_KEY + spaceId;
		String hk = accountId.toString();
		redisTemplate.opsForHash().delete(spaceKey,hk);
	}
	/**
	 * 删除 某个空间的 所有评论 
	 */
	public void deleteSpaceAllLike(Long spaceId) {
		redisTemplate.delete(SPACE_LIKE_KEY + spaceId);
	}
	
	/**
	 * 获取点赞详情
	 */

	public SpaceLikeDO getSpaceLikeInfo(Long spaceId, Long accountId) {
		String spaceKey = SPACE_LIKE_KEY + spaceId;
		String hk = accountId.toString();
		return (SpaceLikeDO)redisTemplate.opsForHash().get(spaceKey, hk);
	}
	
	/**
	 * 获取点赞分页
	 */
	public PageResult<SpaceLikeDO> getSpaceLikePage(Long spaceId,Integer pageNo,Integer pageSize){
		PageResult<SpaceLikeDO> pageResult = new PageResult<>();
		
		String spaceKey = SPACE_LIKE_KEY + spaceId;
		Long total = redisTemplate.opsForHash().size(spaceKey);
		
		HashOperations<String, String, SpaceLikeDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				                          .count(10000)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, SpaceLikeDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SpaceLikeDO>> cursor = hashOps.scan(spaceKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		List<SpaceLikeDO> spaceLikeList = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, SpaceLikeDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			spaceLikeList= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal(total);
		pageResult.setList(spaceLikeList);
		return  pageResult;
	}

	public Long getSpaceCount(Long spaceId) {
		
		String spaceKey = SPACE_LIKE_KEY + spaceId;
		return redisTemplate.opsForHash().size(spaceKey);
	}

    public void deleteSpaceLikeBySpaceId(Long spaceId) {
        redisTemplate.delete(SPACE_LIKE_KEY + spaceId);
    }

    public boolean hasSpaceLikeBySpaceId(Long spaceId) {
        return redisTemplate.hasKey(SPACE_LIKE_KEY + spaceId);
    }
}

package com.fozmo.ym.module.social.dal.redis.like;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.dal.dataobject.like.WorksLikeDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_LIKE_KEY;

@Slf4j
@Repository
public class WorksLikeRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	/**
	 * redis 存储空间点赞信息
	 */
	public void saveWorksLike(WorksLikeDO worksLikeDO) {
		
		String worksKey = WORKS_LIKE_KEY + worksLikeDO.getWorksId();
		String hk = worksLikeDO.getAccountId().toString();
		redisTemplate.opsForHash().put(worksKey, hk, worksLikeDO);
	}
	
	/**
	 *  校验点赞 key 是否存在
	 */
	public boolean hasWorksLike(Long worksId,Long accountId) {
		String worksKey = WORKS_LIKE_KEY + worksId;
		String hk = accountId.toString();
		return redisTemplate.opsForHash().hasKey(worksKey, hk);
	}

	/**
	 * 校验点赞 key 是否存在
	 */
	public boolean hasWorksList(Long worksId) {
		String worksKey = WORKS_LIKE_KEY + worksId;
		return redisTemplate.hasKey(worksKey);
	}

	/**
	 *  获取一个作品下的所有评论
	 */
	public List<WorksLikeDO> getWorksLike(Long worksId) {
		String worksKey = WORKS_LIKE_KEY + worksId;
		List<Object> list = redisTemplate.opsForHash().values(worksKey);
		return list.stream()
				.map(obj -> {
					try {
						return new ObjectMapper().readValue((String) obj, WorksLikeDO.class);
					} catch (JsonProcessingException e) {
						throw new RuntimeException(e);
					}
				})
				.collect(Collectors.toList());
	}
	
	/***
	 *  删除某个作品单个评论
	 */
	public void deleteWorksLike(Long worksId,Long accountId) {
		String worksKey = WORKS_LIKE_KEY + worksId;
		String hk = accountId.toString();
		redisTemplate.opsForHash().delete(worksKey,hk);
	}
	/**
	 * 删除 某个作品的 所有评论
	 */
	public void deleteWorksAllLike(Long worksId) {
		redisTemplate.delete(WORKS_LIKE_KEY + worksId);
	}
	
	/**
	 * 获取评论详情
	 */
	
	public WorksLikeDO getWorksLikeInfo(Long worksId,Long accountId){
		String worksKey = WORKS_LIKE_KEY + worksId;
		String hk = accountId.toString();
		return (WorksLikeDO)redisTemplate.opsForHash().get(worksKey, hk);
	}
	
	/**
	 * 获取评论分页
	 */
	public PageResult<WorksLikeDO> getWorksLikePage(Long worksId,Integer pageNo,Integer pageSize){
		PageResult<WorksLikeDO> pageResult = new PageResult<>();
		
		String worksKey = WORKS_LIKE_KEY + worksId;
		Long total = redisTemplate.opsForHash().size(worksKey);
		
		HashOperations<String, String, WorksLikeDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				                          .count(10000)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, WorksLikeDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, WorksLikeDO>> cursor = hashOps.scan(worksKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		List<WorksLikeDO> worksList = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, WorksLikeDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			worksList= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal(total);
		pageResult.setList(worksList);
		return pageResult;
	}

		public Long selectCountByWorksId(Long worksId) {
			String worksKey = WORKS_LIKE_KEY + worksId;
			return redisTemplate.opsForHash().size(worksKey);
		}
}

package com.fozmo.ym.module.social.dal.redis.platform;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.dal.dataobject.platform.SocialPlatformDO;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SOCIAL_PLATFORM;

@Repository
public class SocialPlatformRedisDao {

	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@Resource
	private RedissonClient redissonClient;


public SocialPlatformDO getSocialPlatform(String code){
		return (SocialPlatformDO)redisTemplate.opsForHash().get(SOCIAL_PLATFORM,code);
	}
	
	public PageResult<SocialPlatformDO> getSocialPlatformPage(Integer pageNo, Integer pageSize){
		PageResult<SocialPlatformDO> pageResult = new PageResult<>();
		
		Long total = redisTemplate.opsForSet().size(SOCIAL_PLATFORM);
		pageResult.setTotal(total);
		
		// 查询条件
		HashOperations<String, String, SocialPlatformDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				                          .count(100)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, SocialPlatformDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SocialPlatformDO>> cursor = hashOps.scan(SOCIAL_PLATFORM, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<SocialPlatformDO> socialPlatformDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, SocialPlatformDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			socialPlatformDOS= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal(total);
		pageResult.setList(socialPlatformDOS);
		return  pageResult;
	}

	public void deleteSocialPlatform(String code) {
		HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
		hashOperations.delete(SOCIAL_PLATFORM,code);
	}
	
	public void deleteAll(){
		redisTemplate.delete(SOCIAL_PLATFORM);
	}

	public void set(SocialPlatformDO socialPlatformDO) {
		redisTemplate.opsForHash().put(SOCIAL_PLATFORM,socialPlatformDO.getCode(),socialPlatformDO);
		
	}
	/**
	 *  为True 说明 存在 不能新增，为fase 说明不存在
	 */
	public boolean hasKey(String code){
	    if (redisTemplate.hasKey(SOCIAL_PLATFORM)){
			return redisTemplate.opsForHash().hasKey(SOCIAL_PLATFORM, code);
	    }
		return false;
	}


	// 加锁
	public void lock(Long timeoutMillis) {
		RLock lock = redissonClient.getLock(SOCIAL_PLATFORM);
		try {
			lock.lock(timeoutMillis, TimeUnit.MILLISECONDS);
			// 执行逻辑
		} finally {
			lock.unlock();
		}
	}
	
	// 解锁
	public void unlock(Long timeoutMillis) {
		try {
			RLock lock = redissonClient.getLock(SOCIAL_PLATFORM);
		}catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
}

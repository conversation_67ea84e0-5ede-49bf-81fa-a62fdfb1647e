package com.fozmo.ym.module.social.dal.redis.share;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SPACE_SHARE_KEY;

@Repository
public class SpaceShareRedisDao {
	
	// 注入RedisTemplate
	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	// 注入RedissonClient
	@Resource
	private RedissonClient redissonClient;
		
		public void saveShareInfo(SpaceShareDO spaceShareDO) {
			// 生成共享信息的key
			String shareKey = SPACE_SHARE_KEY + spaceShareDO.getSpaceId();
			// 获取共享信息的账户id
			String hk = spaceShareDO.getAccountId().toString();
			// 将共享信息存入redis的hash表中，如果key不存在则存入
			redisTemplate.opsForHash().put(shareKey, hk, spaceShareDO);
		}
		
		public void deleteShareInfo(Long spaceId, Long accountId) {
			// 生成共享信息的key
			String shareKey = SPACE_SHARE_KEY + spaceId;
			// 获取共享信息的账户id
			String hk = accountId.toString();
			// 删除共享信息
			redisTemplate.opsForHash().delete(shareKey, hk);
		}
		
		public Boolean hasShareInfo(Long spaceId, Long accountId) {
			// 生成共享信息的key
			String shareKey = SPACE_SHARE_KEY + spaceId;
			// 获取共享信息的账户id
			String hk = accountId.toString();
			// 判断共享信息是否存在
			return redisTemplate.opsForHash().hasKey(shareKey, hk);
		}

    public boolean hasShareInfoBySpaceId(Long spaceId) {
        // 获取共享信息列表
        String shareKey = SPACE_SHARE_KEY + spaceId;
        return redisTemplate.hasKey(shareKey);
    }
		
		public SpaceShareDO getShareInfo(Long spaceId, Long accountId) {
			// 生成共享信息的key
			String shareKey = SPACE_SHARE_KEY + spaceId;
			// 获取共享信息的账户id
			String hk = accountId.toString();
			// 获取共享信息
			return (SpaceShareDO) redisTemplate.opsForHash().get(shareKey, hk);
		}
		
		public List<SpaceShareDO> getShareInfoList(Long spaceId) {
			// 生成共享信息的key
            String shareKey = SPACE_SHARE_KEY + spaceId;
            // 获取共享信息列表
            Map<Object, Object> shareInfoMap = redisTemplate.opsForHash().entries(shareKey);
            // 将共享信息列表转换为SpaceShareDO列表
            return shareInfoMap.values().stream().map(shareInfo -> (SpaceShareDO) shareInfo).collect(Collectors.toList());
        }
        
        public PageResult<SpaceShareDO> getSharePage(Long spaceId,int pageNum, int pageSize) {
			// 生成共享信息的key
            String shareKey = SPACE_SHARE_KEY + spaceId;
	        PageResult<SpaceShareDO> pageResult = new PageResult<>();
	        Long total = redisTemplate.opsForHash().size(shareKey);
			
			pageResult.setTotal(total);
            // 获取共享信息列表
            Map<Object, Object> shareInfoMap = redisTemplate.opsForHash().entries(shareKey);
            // 将共享信息列表转换为SpaceShareDO列表
            List<SpaceShareDO> shareInfoList = shareInfoMap.values().stream().map(shareInfo -> (SpaceShareDO) shareInfo).collect(Collectors.toList());
            // 分页
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, shareInfoList.size());
			List<SpaceShareDO> pageList = shareInfoList.subList(fromIndex, toIndex);
			pageResult.setList(pageList);
			
			return pageResult;
        }

	public Long selectCountBySpaceId(Long spaceId) {
		
		String shareKey = SPACE_SHARE_KEY + spaceId;
		return redisTemplate.opsForHash().size(shareKey);
	}

    public void deleteBySpaceId(Long spaceId) {
        redisTemplate.delete(SPACE_SHARE_KEY + spaceId);
    }
}

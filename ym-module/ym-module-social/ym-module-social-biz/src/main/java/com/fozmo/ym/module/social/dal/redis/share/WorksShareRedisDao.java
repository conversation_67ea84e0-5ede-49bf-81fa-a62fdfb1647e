package com.fozmo.ym.module.social.dal.redis.share;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.dal.dataobject.share.WorksShareDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_SHARE_KEY;

@Repository
public class WorksShareRedisDao {


	// 注入RedisTemplate
	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	// 注入RedissonClient
	@Resource
	private RedissonClient redissonClient;


	public void saveShareInfo(WorksShareDO woksShareDO) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + woksShareDO.getWorksId();
		// 获取共享信息的账户id
		String hk = woksShareDO.getAccountId().toString();
		// 将共享信息存入redis的hash表中，如果key不存在则存入
		redisTemplate.opsForHash().put(shareKey, hk, woksShareDO);
	}
	
	public void deleteShareInfo(Long worksId, Long accountId) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + worksId;
		// 获取共享信息的账户id
		String hk = accountId.toString();
		// 删除共享信息
		redisTemplate.opsForHash().delete(shareKey, hk);
	}

	public void deleteShareList(Long worksId) {
		// 删除共享信息
		redisTemplate.delete(WORKS_SHARE_KEY + worksId);
	}
	
	public Boolean hasShareInfo(Long worksId, Long accountId) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + worksId;
		// 获取共享信息的账户id
		String hk = accountId.toString();
		// 判断共享信息是否存在
		return redisTemplate.opsForHash().hasKey(shareKey, hk);
	}

	public Boolean hasShareList(Long worksId) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + worksId;
		// 获取共享信息的账户id
		return redisTemplate.hasKey(shareKey);
	}
	
	public WorksShareDO getShareInfo(Long worksId, Long accountId) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + worksId;
		// 获取共享信息的账户id
		String hk = accountId.toString();
		// 获取共享信息
		return (WorksShareDO) redisTemplate.opsForHash().get(shareKey, hk);
	}
	
	public List<WorksShareDO> getShareInfoList(Long worksId) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + worksId;
		// 获取共享信息列表
		Map<Object, Object> shareInfoMap = redisTemplate.opsForHash().entries(shareKey);
		// 将共享信息列表转换为SpaceShareDO列表
		return shareInfoMap.values().stream().map(shareInfo -> (WorksShareDO) shareInfo).collect(Collectors.toList());
	}
	
	public PageResult<WorksShareDO> getSharePage(Long spaceId, int pageNum, int pageSize) {
		// 生成共享信息的key
		String shareKey = WORKS_SHARE_KEY + spaceId;
		PageResult<WorksShareDO> pageResult = new PageResult<>();
		Long total = redisTemplate.opsForHash().size(shareKey);
		
		pageResult.setTotal(total);
		// 获取共享信息列表
		Map<Object, Object> shareInfoMap = redisTemplate.opsForHash().entries(shareKey);
		// 将共享信息列表转换为SpaceShareDO列表
		List<WorksShareDO> shareInfoList = shareInfoMap.values().stream().map(shareInfo -> (WorksShareDO) shareInfo).collect(Collectors.toList());
		// 分页
		int fromIndex = (pageNum - 1) * pageSize;
		int toIndex = Math.min(fromIndex + pageSize, shareInfoList.size());
		List<WorksShareDO> pageList = shareInfoList.subList(fromIndex, toIndex);
		pageResult.setList(pageList);
		
		return pageResult;
	}

		public Long countByWorksId(Long worksId) {
			String shareKey = WORKS_SHARE_KEY + worksId;
			return redisTemplate.opsForHash().size(shareKey);
		}
}

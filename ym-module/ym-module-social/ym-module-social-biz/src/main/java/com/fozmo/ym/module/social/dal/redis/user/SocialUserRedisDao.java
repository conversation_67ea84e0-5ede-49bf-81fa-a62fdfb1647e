package com.fozmo.ym.module.social.dal.redis.user;

import com.fozmo.ym.module.social.dal.dataobject.user.SocialUserDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SOCIAL_CODE;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SOCIAL_USER;

@Repository
public class SocialUserRedisDao {

	@Resource
    private RedisTemplate<String, Object> redisTemplate;
	
	public void addSocialUser(SocialUserDO socialUser) {
		String key =SOCIAL_USER + socialUser.getCreateId();
		String codeKey = SOCIAL_CODE;
		redisTemplate.opsForHash().put(key, socialUser.getId().toString(), socialUser);
//		if (socialUser.getType()==1L) {
//			redisTemplate.opsForHash().put(key, socialUser.getType().toString(), socialUser);
//		}
		if (socialUser.getType()==2L){
			redisTemplate.opsForHash().put(codeKey,socialUser.getUserCode(),socialUser);
		}
		if (socialUser.getType()==3L){
			redisTemplate.opsForHash().put(codeKey,socialUser.getMobile(),socialUser);
		}
		
	}
	
	public void removeSocialUser(Long accountId,Long socialUserId) {
		String key =SOCIAL_USER + accountId;
		redisTemplate.opsForHash().delete(key, socialUserId.toString());
	}
	
	public void removeSocialUserByAccountId(Long accountId) {
		String key =SOCIAL_USER + accountId;
		redisTemplate.delete(key);
	}
	
	public SocialUserDO getInfoByCode(String code) {
		return (SocialUserDO) redisTemplate.opsForHash().get(SOCIAL_CODE, code);
	}

	public boolean checkSocialUser(String code) {
		return redisTemplate.opsForHash().hasKey(SOCIAL_CODE, code);
	}
}

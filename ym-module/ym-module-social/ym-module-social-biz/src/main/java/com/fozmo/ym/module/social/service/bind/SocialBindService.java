package com.fozmo.ym.module.social.service.bind;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindPageReqVO;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.bind.SocialBindDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 用户第三方社交授权信息 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialBindService {

    /**
     * 创建用户第三方社交授权信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBind(@Valid SocialBindSaveReqVO createReqVO);

    /**
     * 更新用户第三方社交授权信息
     *
     * @param updateReqVO 更新信息
     */
    void updateBind(@Valid SocialBindSaveReqVO updateReqVO);

    /**
     * 删除用户第三方社交授权信息
     *
     * @param id 编号
     */
    void deleteBind(Long id);

    /**
     * 获得用户第三方社交授权信息
     *
     * @param id 编号
     * @return 用户第三方社交授权信息
     */
    SocialBindDO getBind(Long id);

    /**
     * 获得用户第三方社交授权信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户第三方社交授权信息分页
     */
    PageResult<SocialBindDO> getBindPage(SocialBindPageReqVO pageReqVO);

    /**
     *
     * 根据社交用户id查询绑定 关系
     *
     */
    SocialBindDO getBindByUserId(Long socialUserId);

    /**
     *
     * 根据账号Id 查询绑定关系
     */
    List<SocialBindDO> getBindListByAccountId(Long accountId);
}
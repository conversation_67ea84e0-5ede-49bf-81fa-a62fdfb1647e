package com.fozmo.ym.module.social.service.bind;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindPageReqVO;
import com.fozmo.ym.module.social.controller.admin.bind.vo.SocialBindSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.bind.SocialBindDO;
import com.fozmo.ym.module.social.dal.mysql.bind.SocialBindMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 用户第三方社交授权信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialBindServiceImpl implements SocialBindService {

    @Resource
    private SocialBindMapper socialBindMapper;

    @Resource
    private IdService idService;

    @Override
    public Long createBind(SocialBindSaveReqVO createReqVO) {
        // 插入
        SocialBindDO bind = BeanUtils.toBean(createReqVO, SocialBindDO.class);
        bind.setId(idService.nextId("social_bind"));
        bind.setTenantId(1L);
        socialBindMapper.insert(bind);
        // 返回
        return bind.getId();
    }

    @Override
    public void updateBind(SocialBindSaveReqVO updateReqVO) {
        // 校验存在
        validateBindExists(updateReqVO.getId());
        // 更新
        SocialBindDO updateObj = BeanUtils.toBean(updateReqVO, SocialBindDO.class);
        socialBindMapper.updateById(updateObj);
    }

    @Override
    public void deleteBind(Long id) {
        // 校验存在
        validateBindExists(id);
        // 删除
        socialBindMapper.deleteById(id);
    }

    private void validateBindExists(Long id) {
        if (socialBindMapper.selectById(id) == null) {

        }
    }

    @Override
    public SocialBindDO getBind(Long id) {
        return socialBindMapper.selectById(id);
    }

    @Override
    public PageResult<SocialBindDO> getBindPage(SocialBindPageReqVO pageReqVO) {
        return socialBindMapper.selectPage(pageReqVO);
    }

    /**
     */
    @Override
    public SocialBindDO getBindByUserId(Long socialUserId) {
        return socialBindMapper.getBindByUserId(socialUserId);
    }

    /**
     * 根据账号Id 查询绑定关系
     *
     */
    @Override
    public List<SocialBindDO> getBindListByAccountId(Long accountId) {
       return socialBindMapper.selectByAccountId(accountId);
    }

}
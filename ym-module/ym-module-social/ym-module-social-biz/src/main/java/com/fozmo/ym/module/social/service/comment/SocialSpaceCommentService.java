package com.fozmo.ym.module.social.service.comment;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.api.dto.SpaceCommentDTO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentPageReqVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentRespVO;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialSpaceCommentDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间评论 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialSpaceCommentService {

    /**
     * 创建空间评论
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createComment(@Valid SpaceCommentDTO createReqVO);

    void saveBatch(List<SocialSpaceCommentDO> subList);

    PageResult<SocialSpaceCommentRespVO> getCommentPage(@Valid SocialSpaceCommentPageReqVO pageReqVO);

    Long countBySpaceId(Long id);

    boolean isComment(Long spaceId, Long accountId);

    void deleteBySpaceId(Long spaceId);
}
package com.fozmo.ym.module.social.service.comment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.social.api.dto.SpaceCommentDTO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentPageReqVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialSpaceCommentRespVO;
import com.fozmo.ym.module.social.core.mq.comment.SpaceCommentProduce;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialSpaceCommentDO;
import com.fozmo.ym.module.social.dal.mysql.comment.SocialSpaceCommentMapper;
import com.fozmo.ym.module.social.dal.redis.comment.SpaceCommentRedisDao;
import com.fozmo.ym.module.space.api.SpaceApi;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SPACE_COMMENT_KEY_PREFIX;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.SPACE_REPLY_KEY_PREFIX;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.*;

/**
 * 空间评论 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialSpaceCommentServiceImpl implements SocialSpaceCommentService {

    @Resource
    SpaceCommentProduce spaceCommentProduce;
    @Resource
    private IdService idService;
    @Resource
    private AccountApi accountApi;
    @Resource
    private SpaceCommentRedisDao spaceCommentRedisDao;
    
    @Resource
    private SocialSpaceCommentMapper socialSpaceCommentMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    @Lazy
    private SpaceApi spaceApi;

    @Override
    public Long createComment(SpaceCommentDTO createReqVO) {

        Long spaceId = createReqVO.getSpaceId();
        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }

        // 校验账户是否存在
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(ACCOUNT_NOT);
        }
        if (!spaceApi.hasSpace(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        Integer commentType = createReqVO.getCommentType();
        if (ObjectUtil.isEmpty(commentType)) {
            throw exception(COMMENT_TYPE_NOT_EXISTS);
        }
        if(commentType==1){
            // 处理回复
            // 1 校验回复的评论是否存在
            Long replyId = createReqVO.getReplyId();
            if (ObjectUtil.isEmpty(replyId)) {
                throw exception(COMMENT_NOT_EXISTS);
            }
            // 校验是否存在该评论信息
        }
        createReqVO.setId(idService.nextId("spaceComment"));
        SpaceCommentDTO spaceCommentDTO = addValue(createReqVO);
        spaceCommentProduce.sendComment(spaceCommentDTO);
        return spaceCommentDTO.getId();
    }

    /**
     */
    @Override
    public void saveBatch(List<SocialSpaceCommentDO> subList) {
        if (ObjectUtil.isEmpty(subList)) {
            return;
        }
        LocalDateTime currenTime = LocalDateTime.now();
        
        LocalDate currentDate = LocalDate.now();
        for (SocialSpaceCommentDO sub : subList) {
            sub.setTenantCode("1");
            sub.setDeleted(false);
            sub.setCommentTime(currenTime);
            sub.setTenantId(1L);
            sub.setCreateData(currentDate);
        }
        socialSpaceCommentMapper.insertBatch(subList,subList.size());
    }

/**
 *
 */
@Override
public PageResult<SocialSpaceCommentRespVO> getCommentPage(SocialSpaceCommentPageReqVO pageReqVO) {
    
    
    Integer commentType = pageReqVO.getCommentType();
    if (commentType == 0) {
        // 先查询评论 再查询回复
        return getSpaceCommentPage(pageReqVO);
    } else {
        if (ObjectUtil.isEmpty(pageReqVO.getReplyId())) {
            throw exception(COMMENT_NOT_EXISTS);
        }
        return getSpaceReplyPage(pageReqVO);
    }
}

/**
 *
 */
@Override
public Long countBySpaceId(Long id) {
    String commentKey = SPACE_COMMENT_KEY_PREFIX+id;
    return spaceCommentRedisDao.getCommentTotal(commentKey);
}

    /**
     *
     */
    @Override
    public boolean isComment(Long spaceId, Long accountId) {
        
        if (ObjectUtil.isEmpty(spaceId) || ObjectUtil.isEmpty(accountId)) {
            return false;
        }
        return false;
    }

    /**
     * @param spaceId
     */
    @Override
    public void deleteBySpaceId(Long spaceId) {
        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        // 判断评论是否存在
        if (spaceCommentRedisDao.hasComment(spaceId)) {
            // 遍历所有 数据
            List<SocialSpaceCommentDO> commentList = spaceCommentRedisDao.getCommentList(spaceId);
            if (CollUtil.isNotEmpty(commentList)) {
                for (SocialSpaceCommentDO comment : commentList) {
                    if (spaceCommentRedisDao.hasReply(comment.getSpaceId(), comment.getId())) {
                        spaceCommentRedisDao.deleteReplyBySpaceId(comment.getSpaceId(), comment.getId());
                    }

                }
            }
            spaceCommentRedisDao.deleteCommentBySpaceId(spaceId);
        }


    }

    private SpaceCommentDTO addValue(SpaceCommentDTO spaceCommentDTO) {
        if (ObjectUtil.isEmpty(spaceCommentDTO)) {
            throw exception(COMMENT_NOT_CREATE);
        }
        
        // 获取当前用户
        AccountBaseInfoDTO loginAccount = accountApi.queryAccountBaseInfoById(SecurityFrameworkUtils.getLoginUserId());
       
        // 处理创建人信息
        if (ObjectUtil.isNotEmpty(loginAccount)) {
            spaceCommentDTO.setAccountId(loginAccount.getId());
            spaceCommentDTO.setAvatar(loginAccount.getAvatar());
            spaceCommentDTO.setNickname(loginAccount.getNickname());
            spaceCommentDTO.setAccountName(loginAccount.getName());
            spaceCommentDTO.setRightsId(loginAccount.getRightsId());
            // 创建信息
            spaceCommentDTO.setCreateId(loginAccount.getId());
            spaceCommentDTO.setCreator(loginAccount.getName());
            spaceCommentDTO.setTenantCode("1");
        }
        
        if (ObjectUtil.isEmpty(spaceCommentDTO.getCommentTime())) {
            spaceCommentDTO.setCommentTime(System.currentTimeMillis());
        }
        
        if (ObjectUtil.isNotEmpty(spaceCommentDTO.getInviteIds())) {
            AccountBaseInfoDTO inviteAccount = accountApi.queryAccountBaseInfoById(Long.valueOf(spaceCommentDTO.getInviteIds()));
            if (ObjectUtil.isEmpty(inviteAccount)) {
                spaceCommentDTO.setInviteAccountName(inviteAccount.getName());
                spaceCommentDTO.setInviteNickName(inviteAccount.getNickname());
                spaceCommentDTO.setInviteAvatar(inviteAccount.getAvatar());
                spaceCommentDTO.setInviteRightsId(inviteAccount.getRightsId());
            }
        }
        return spaceCommentDTO;
    }

    
    private PageResult<SocialSpaceCommentRespVO> getSpaceReplyPage(SocialSpaceCommentPageReqVO pageReqVO){
        
        PageResult<SocialSpaceCommentRespVO> pageResult = new PageResult<>();
        Long spaceId = pageReqVO.getSpaceId();
        
        Long replyId = pageReqVO.getReplyId();
        
        String replyKey = SPACE_REPLY_KEY_PREFIX+spaceId+":"+replyId;
        Long replyTotal = spaceCommentRedisDao.getCommentTotal(replyKey);
        pageResult.setTotal(replyTotal);
        if (ObjectUtil.isEmpty(replyTotal)) {
            return pageResult;
        }
        List<SocialSpaceCommentRespVO> replyList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(replyTotal) && replyTotal > 0) {
            Set<String> replyResult = spaceCommentRedisDao.getStaticCommentPage(replyKey, pageReqVO.getPageNo(), pageReqVO.getPageSize());
            if (CollectionUtil.isNotEmpty(replyResult)) {
                for (String reply : replyResult) {
                    SocialSpaceCommentRespVO respRespVO = JSON.parseObject(reply, SocialSpaceCommentRespVO.class);
                    replyList.add(respRespVO);
                }
            }
        }
        pageResult.setList(replyList);
        return pageResult;
    }

    private PageResult<SocialSpaceCommentRespVO> getSpaceCommentPage(SocialSpaceCommentPageReqVO pageReqVO){
        Long allNum = 0L;
        PageResult<SocialSpaceCommentRespVO> pageResult = new PageResult();
        Long spaceId = pageReqVO.getSpaceId();
        String commentKey = SPACE_COMMENT_KEY_PREFIX+spaceId;
        Long commentTotal = spaceCommentRedisDao.getCommentTotal(commentKey);
        allNum+=commentTotal;
        pageResult.setTotal(commentTotal);
        if (ObjectUtil.isEmpty(commentTotal)) {
            return pageResult;
        }
  

    List<SocialSpaceCommentRespVO> commentList = new ArrayList<>();
    // 处理分页数据
    Set<String> setResult = spaceCommentRedisDao.getStaticCommentPage(commentKey, pageReqVO.getPageNo(), pageReqVO.getPageSize());
    
    if (CollectionUtil.isNotEmpty(setResult)) {
        for (String comment : setResult) {
            SocialSpaceCommentRespVO commentRespVO = JSON.parseObject(comment, SocialSpaceCommentRespVO.class);
            commentList.add(commentRespVO);
        }
    }
    if (ObjectUtil.isEmpty(commentList)) {
        return pageResult;
    }
    
    for (SocialSpaceCommentRespVO commentRespVO : commentList) {
        if (ObjectUtil.isNotEmpty(commentRespVO)) {
            // 处理回复消息
            Long replyId = commentRespVO.getId();
            String replyKey = SPACE_REPLY_KEY_PREFIX+spaceId+":"+replyId;
            Long replyTotal = spaceCommentRedisDao.getCommentTotal(replyKey);
            allNum+=replyTotal;
            commentRespVO.setTotal(replyTotal);
            
            List<SocialSpaceCommentRespVO> replyList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(replyTotal) && replyTotal > 0) {
                Set<String> replyResult = spaceCommentRedisDao.getStaticCommentPage(replyKey, pageReqVO.getPageNo(), 3);
                if (CollectionUtil.isNotEmpty(replyResult)) {
                    for (String reply : replyResult) {
                        SocialSpaceCommentRespVO respRespVO = JSON.parseObject(reply, SocialSpaceCommentRespVO.class);
                        replyList.add(respRespVO);
                    }
                }
            }
            commentRespVO.setRespVOList(replyList);
        }
    }
    pageResult.setList(commentList);
    pageResult.setOtherTotal(allNum.toString());
    return pageResult;
}


//    @Override
//    public void updateComment(SpaceCommentSaveReqVO updateReqVO) {
//        // 校验存在
//        validateCommentExists(updateReqVO.getId());
//        // 更新
//        SpaceCommentDO updateObj = BeanUtils.toBean(updateReqVO, SpaceCommentDO.class);
//        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
//        if (ObjectUtil.isNull(loginUser)) {
//            throw new RuntimeException("用户未登录");
//        }
//        updateObj.setUpdateId(loginUser.getId());
//        updateObj.setUpdater(loginUser.getUsername());
//        updateObj.setUpdateData(LocalDate.now());
//        commentMapper.updateById(updateObj);
//    }
//
//    @Override
//    public void deleteComment(Long id) {
//        // 校验存在
//        validateCommentExists(id);
//        // 删除
//        commentMapper.deleteById(id);
//    }
//
//    private void validateCommentExists(Long id) {
//        if (commentMapper.selectById(id) == null) {
//            throw exception(COMMENT_NOT_EXISTS);
//        }
//    }
//
//    @Override
//    public SpaceCommentDO getComment(Long id) {
//        return commentMapper.selectById(id);
//    }
//
//    @Override
//    public PageResult<SpaceCommentDO> getCommentPage(SpaceCommentPageReqVO pageReqVO) {
//        StopWatch stopWatch = new StopWatch("getCommentPage");
//        // 设置查询类型为主评论(0表示主评论)
//        Integer commonType = pageReqVO.getCommentType();
//
//        if (commonType ==1){
//            if (pageReqVO.getReplyId() == null || pageReqVO.getReplyId() == 0) {
//                throw exception(COMMENT_NOT_EXISTS);
//            }
//            // 查询主评论分页数据
//            PageResult<SpaceCommentDO> pageResult = commentMapper.selectPage(pageReqVO);
//            return pageResult;
//        }
//
//        // 查询主评论分页数据
//        stopWatch.start("主评论分页数据");
//        PageResult<SpaceCommentDO> pageResult = commentMapper.selectPage(pageReqVO);
//        if (ObjectUtil.isNotEmpty(pageResult) && ObjectUtil.isNotEmpty(pageResult.getList())) {
//            for (SpaceCommentDO spaceCommentDO : pageResult.getList()) {
//                Long accountId = spaceCommentDO.getAccountId();
//                AppAccountInfoRespDTO accountInfoDTO = accountApi.getAccountInfoById(accountId);
//
//                if (ObjectUtil.isNotNull(accountInfoDTO)) {
//                    spaceCommentDO.setAccountName(accountInfoDTO.getName());
//                    spaceCommentDO.setAccountId(accountId);
//                    spaceCommentDO.setNickname(accountInfoDTO.getNickname());
//                    spaceCommentDO.setAvatar(accountInfoDTO.getAvatar());
//                    spaceCommentDO.setRightsId(accountInfoDTO.getRightsId());
//                }
//
//
//                if (StrUtil.isNotEmpty(spaceCommentDO.getInviteIds())){
//                    Long inviteId = Long.parseLong(spaceCommentDO.getInviteIds());
//                    AppAccountInfoRespDTO inviteInfoDTO = accountApi.getAccountInfoById(inviteId);
//                    if (ObjectUtil.isNotNull(inviteInfoDTO)) {
//                        spaceCommentDO.setInviteAvatar(inviteInfoDTO.getAvatar());
//                        spaceCommentDO.setInviteRightsId(inviteInfoDTO.getRightsId());
//                        spaceCommentDO.setInviteNickName(inviteInfoDTO.getNickname());
//                        spaceCommentDO.setInviteAccountName(inviteInfoDTO.getName());
//                    }
//
//                }
//            }
//        }
//        stopWatch.stop();
//        // 如果查询结果为空或没有数据，直接返回
//        if (ObjectUtil.isEmpty(pageResult) || CollectionUtil.isEmpty(pageResult.getList())) {
//            return pageResult;
//        }
////        spaceCommentDOList.addAll(pageResult.getList());
//        // 收集主评论ID集合
//
//        stopWatch.start("回复处理");
//        List<Long> commentIds = pageResult.getList().stream()
//                .map(SpaceCommentDO::getId)
//                .collect(Collectors.toList());
//
//        // 批量查询这些主评论下的所有回复(1表示回复)
//        LambdaQueryWrapperX<SpaceCommentDO> queryWrapper = new LambdaQueryWrapperX<>();
//        queryWrapper.in(SpaceCommentDO::getReplyId, commentIds)  // 回复ID在主评论ID集合中
//                .eq(SpaceCommentDO::getCommentType, 1)           // 评论类型为回复
//                .eq(SpaceCommentDO::getDeleted, false);          // 未删除的评论
//
//        // 查询所有符合条件的回复
//        PageParam pageParam = new PageParam().setPageNo(1).setPageSize(9999);
//        PageResult<SpaceCommentDO> allReplies = commentMapper.selectPage(pageParam,queryWrapper);
//        if (ObjectUtil.isNotEmpty(allReplies) && CollectionUtil.isNotEmpty(pageResult.getList())) {
//            for (SpaceCommentDO spaceCommentDO : allReplies.getList()) {
//                Long accountId = spaceCommentDO.getAccountId();
//                AppAccountInfoRespDTO accountInfoDTO = accountApi.getAccountInfoById(accountId);
//                spaceCommentDO.setAccountName(accountInfoDTO.getName());
//                spaceCommentDO.setAccountId(accountId);
//                spaceCommentDO.setNickname(accountInfoDTO.getNickname());
//                spaceCommentDO.setAvatar(accountInfoDTO.getAvatar());
//                spaceCommentDO.setRightsId(accountInfoDTO.getRightsId());
//
//                if (StrUtil.isNotEmpty(spaceCommentDO.getInviteIds())){
//                    Long inviteId = Long.parseLong(spaceCommentDO.getInviteIds());
//                    AppAccountInfoRespDTO inviteInfoDTO = accountApi.getAccountInfoById(inviteId);
//                    spaceCommentDO.setInviteAvatar(inviteInfoDTO.getAvatar());
//                    spaceCommentDO.setInviteRightsId(inviteInfoDTO.getRightsId());
//                    spaceCommentDO.setInviteNickName(inviteInfoDTO.getNickname());
//                    spaceCommentDO.setInviteAccountName(inviteInfoDTO.getName());
//                }
//            }
//        }
//        stopWatch.stop();
////        spaceCommentDOList.addAll(allReplies.getList());
//        // 如果查询到回复，按回复ID分组
//        stopWatch.start("拼接内容");
//        if (ObjectUtil.isNotEmpty(allReplies) && CollectionUtil.isNotEmpty(allReplies.getList())) {
//            // 按replyId分组，建立回复ID到回复列表的映射
//            Map<Long, List<SpaceCommentDO>> repliesGroupByCommentId = allReplies.getList().stream()
//                    .collect(Collectors.groupingBy(SpaceCommentDO::getReplyId));
//
//            // 为每个主评论设置对应的回复列表
//            for (SpaceCommentDO mainComment : pageResult.getList()) {
//                // 从分组映射中获取该主评论的回复列表，如果没有则返回空列表
//                List<SpaceCommentDO> replies = repliesGroupByCommentId.getOrDefault(
//                        mainComment.getId(), Collections.emptyList());
//                if (repliesGroupByCommentId.containsKey(mainComment.getId())) {
//                    mainComment.setTotal((long) repliesGroupByCommentId.get(mainComment.getId()).size());
//                }else {
//                    mainComment.setTotal(0l);
//                }
//                mainComment.setReplyList(replies);
//            }
//        }
//        stopWatch.stop();
//
//        for (StopWatch.TaskInfo taskInfo : stopWatch.getTaskInfo()){
//            System.out.println(taskInfo.getTaskName());
//
//            System.out.println(taskInfo.getTimeMillis());
//        }
//
//        System.out.println("总时间："+stopWatch.getTotalTimeMillis());
//        return pageResult;
//    }
//
//    /**
//     * @param spaceId
//     * @return
//     */
//    @Override
//    public List<SpaceCommentDO> queryCommentListBySpaceId(Long spaceId) {
//        // 查询主评论分页数据
//
//        SpaceCommentPageReqVO pageReqVO = new SpaceCommentPageReqVO();
//        pageReqVO.setSpaceId(spaceId);
//        pageReqVO.setCommentType(0);
//        pageReqVO.setPageNo(1);
//        pageReqVO.setPageSize(10);
//
//
//        // 查询主评论分页数据
//        PageResult<SpaceCommentDO> pageResult = commentMapper.selectPage(pageReqVO);
//
//        // 如果查询结果为空或没有数据，直接返回
//        if (ObjectUtil.isEmpty(pageResult) || CollectionUtil.isEmpty(pageResult.getList())) {
//            return pageResult.getList();
//        }
//
//        // 收集主评论ID集合
//        List<Long> commentIds = pageResult.getList().stream()
//                .map(SpaceCommentDO::getId)
//                .collect(Collectors.toList());
//
//        // 批量查询这些主评论下的所有回复(1表示回复)
//        LambdaQueryWrapperX<SpaceCommentDO> queryWrapper = new LambdaQueryWrapperX<>();
//        queryWrapper.in(SpaceCommentDO::getReplyId, commentIds)  // 回复ID在主评论ID集合中
//                .eq(SpaceCommentDO::getCommentType, 1)           // 评论类型为回复
//                .eq(SpaceCommentDO::getDeleted, false);          // 未删除的评论
//
//        // 查询所有符合条件的回复
//        PageParam pageParam = new PageParam().setPageNo(1).setPageSize(10);
//        PageResult<SpaceCommentDO> allReplies = commentMapper.selectPage(pageParam,queryWrapper);
//
//        // 如果查询到回复，按回复ID分组
//        if (ObjectUtil.isNotEmpty(allReplies) && CollectionUtil.isNotEmpty(allReplies.getList())) {
//            // 按replyId分组，建立回复ID到回复列表的映射
//            Map<Long, List<SpaceCommentDO>> repliesGroupByCommentId = allReplies.getList().stream()
//                    .collect(Collectors.groupingBy(SpaceCommentDO::getReplyId));
//
//            // 为每个主评论设置对应的回复列表
//            for (SpaceCommentDO mainComment : pageResult.getList()) {
//                // 从分组映射中获取该主评论的回复列表，如果没有则返回空列表
//                List<SpaceCommentDO> replies = repliesGroupByCommentId.getOrDefault(
//                        mainComment.getId(), Collections.emptyList());
//                if (repliesGroupByCommentId.containsKey(mainComment.getId())) {
//                    mainComment.setTotal((long) repliesGroupByCommentId.get(mainComment.getId()).size());
//                }else {
//                    mainComment.setTotal(0l);
//                }
//                mainComment.setReplyList(replies);
//            }
//        }
//        return pageResult.getList();
//    }
//
//    /**
//     * @param spaceIds
//     * @return
//     */
//    @Override
//    public List<SpaceCommentDO> getCommentBySpaceId(List<Long> spaceIds) {
//
//        LambdaQueryWrapperX<SpaceCommentDO> queryWrapper = new LambdaQueryWrapperX<>();
//        queryWrapper.in(SpaceCommentDO::getSpaceId, spaceIds);
//        queryWrapper.eq(SpaceCommentDO::getDeleted, false);
//        queryWrapper.in(SpaceCommentDO::getCommentType, 0);
//        queryWrapper.orderByAsc(SpaceCommentDO::getId);
//        return commentMapper.selectList(queryWrapper);
//    }

}
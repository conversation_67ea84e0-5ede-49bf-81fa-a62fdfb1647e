package com.fozmo.ym.module.social.service.comment;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.api.dto.WorksCommentDTO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentPageReqVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentRespVO;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialWorksCommentDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 作品评论 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialWorksCommentService {
    
    void saveBatch(List<SocialWorksCommentDO> subList);

    Long createComment(@Valid WorksCommentDTO createReqVO);

    PageResult<SocialWorksCommentRespVO> getCommentPage(@Valid SocialWorksCommentPageReqVO pageReqVO);

    Long countByWorksId(Long worksId);

    void deleteByWorksId(Long worksId);
}

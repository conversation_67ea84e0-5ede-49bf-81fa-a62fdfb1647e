package com.fozmo.ym.module.social.service.comment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.social.api.dto.WorksCommentDTO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentPageReqVO;
import com.fozmo.ym.module.social.controller.app.comment.vo.SocialWorksCommentRespVO;
import com.fozmo.ym.module.social.core.mq.comment.WorksCommentProduce;
import com.fozmo.ym.module.social.dal.dataobject.comment.SocialWorksCommentDO;
import com.fozmo.ym.module.social.dal.mysql.comment.SocialWorksCommentMapper;
import com.fozmo.ym.module.social.dal.redis.comment.WorksCommentRedisDao;
import com.fozmo.ym.module.space.api.WorksApi;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_COMMENT_KEY_PREFIX;
import static com.fozmo.ym.module.social.constants.SocialRedisConstants.WORKS_REPLY_KEY_PREFIX;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.*;


/**
 * 作品评论 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialWorksCommentServiceImpl implements SocialWorksCommentService {
  
    @Resource
    private IdService idService;
    
    @Resource
    private AccountApi accountApi;

    @Resource
    private SocialWorksCommentMapper socialWorksCommentMapper;
    
    @Resource
    private WorksCommentProduce worksCommentProduce;
    
    @Resource
    private WorksCommentRedisDao worksCommentRedisDao;

    @Resource
    @Lazy
    private WorksApi worksApi;
    /**
     */
    @Override
    public void saveBatch(List<SocialWorksCommentDO> subList) {
        
        if (ObjectUtil.isEmpty(subList)) {
            return;
        }
        LocalDateTime currenTime = LocalDateTime.now();
        
        LocalDate currentDate = LocalDate.now();
        for (SocialWorksCommentDO sub : subList) {
            sub.setTenantCode("1");
            sub.setDeleted(false);
            sub.setCommentTime(currenTime);
            sub.setTenantId(1L);
            sub.setCreateData(currentDate);
        }
        socialWorksCommentMapper.insertBatch(subList,subList.size());
    }

    @Override
    public Long createComment(WorksCommentDTO createReqVO) {

        Long worksId = createReqVO.getWorksId();

        if (ObjectUtil.isEmpty(worksId)) {
            throw exception(WORKS_NOT_EXISTS);
        }
        if (worksApi.hasWorks(worksId)) {

        } else {
            throw exception(WORKS_NOT_EXISTS);
        }

        Integer commentType = createReqVO.getCommentType();
        if (ObjectUtil.isEmpty(commentType)) {
            throw exception(COMMENT_TYPE_NOT_EXISTS);
        }
        if(commentType==1){
            // 处理回复
            // 1 校验回复的评论是否存在
            Long replyId = createReqVO.getReplyId();
            if (ObjectUtil.isEmpty(replyId)) {
                throw exception(COMMENT_NOT_EXISTS);
            }
            // 校验是否存在该评论信息
            //  validateCommentExists(replyId);
        }
        createReqVO.setId(idService.nextId("spaceWorksComment"));
        WorksCommentDTO spaceCommentDTO = addValue(createReqVO);
        worksCommentProduce.sendComment(spaceCommentDTO);
        return spaceCommentDTO.getId();
    }

    private WorksCommentDTO addValue(WorksCommentDTO spaceCommentDTO){
        if (ObjectUtil.isEmpty(spaceCommentDTO)) {
            throw exception(COMMENT_NOT_CREATE);
        }
        
        // 获取当前用户
        AccountBaseInfoDTO loginAccount = accountApi.queryAccountBaseInfoById(SecurityFrameworkUtils.getLoginUserId());
        
        // 处理创建人信息
        if (ObjectUtil.isNotEmpty(loginAccount)) {
            spaceCommentDTO.setAccountId(loginAccount.getId());
            spaceCommentDTO.setAvatar(loginAccount.getAvatar());
            spaceCommentDTO.setNickname(loginAccount.getNickname());
            spaceCommentDTO.setAccountName(loginAccount.getName());
            spaceCommentDTO.setRightsId(loginAccount.getRightsId());
            // 创建信息
            spaceCommentDTO.setCreateId(loginAccount.getId());
            spaceCommentDTO.setCreator(loginAccount.getName());
            spaceCommentDTO.setTenantCode("1");
        }
        
        if (ObjectUtil.isEmpty(spaceCommentDTO.getCommentTime())) {
            spaceCommentDTO.setCommentTime(System.currentTimeMillis());
        }
        
        if (ObjectUtil.isNotEmpty(spaceCommentDTO.getInviteIds())) {
            AccountBaseInfoDTO inviteAccount = accountApi.queryAccountBaseInfoById(Long.valueOf(spaceCommentDTO.getInviteIds()));
            if (ObjectUtil.isEmpty(inviteAccount)) {
                spaceCommentDTO.setInviteAccountName(inviteAccount.getName());
                spaceCommentDTO.setInviteNickName(inviteAccount.getNickname());
                spaceCommentDTO.setInviteAvatar(inviteAccount.getAvatar());
                spaceCommentDTO.setInviteRightsId(inviteAccount.getRightsId());
            }
        }
        return spaceCommentDTO;
    }
/**
 *
 */
@Override
public PageResult<SocialWorksCommentRespVO> getCommentPage(SocialWorksCommentPageReqVO pageReqVO) {
    
    
    Integer commentType = pageReqVO.getCommentType();
    if (commentType == 0) {
        // 先查询评论 再查询回复
        return getWorksCommentPage(pageReqVO);
    } else {
        if (ObjectUtil.isEmpty(pageReqVO.getReplyId())) {
            throw exception(COMMENT_NOT_EXISTS);
        }
        return getWorksReplyPage(pageReqVO);
    }
}

    /**
     */
    @Override
    public Long countByWorksId(Long worksId) {
        return worksCommentRedisDao.getCommentTotal(WORKS_COMMENT_KEY_PREFIX+worksId);
    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        if (worksCommentRedisDao.hasWorksComment(worksId)) {
            List<SocialWorksCommentDO> socialWorksCommentDOS = worksCommentRedisDao.getCommentList(worksId);
            if (CollUtil.isNotEmpty(socialWorksCommentDOS)) {
                for (SocialWorksCommentDO socialWorksCommentDO : socialWorksCommentDOS) {
                    if (worksCommentRedisDao.hasReply(worksId, socialWorksCommentDO.getId())) {
                        worksCommentRedisDao.deleteReplyByWorksId(worksId, socialWorksCommentDO.getId());
                    }
                }
            }
            worksCommentRedisDao.deleteCommentByWorksId(worksId);
        }
        socialWorksCommentMapper.delete(new LambdaQueryWrapperX<SocialWorksCommentDO>()
                .eq(SocialWorksCommentDO::getWorksId, worksId)
                .eq(SocialWorksCommentDO::getDeleted, false));
    }


    private PageResult<SocialWorksCommentRespVO> getWorksReplyPage(SocialWorksCommentPageReqVO pageReqVO){
    
    PageResult<SocialWorksCommentRespVO> pageResult = new PageResult<>();
    Long worksId = pageReqVO.getWorksId();
    
    Long replyId = pageReqVO.getReplyId();

        String replyKey = WORKS_REPLY_KEY_PREFIX + worksId + ":" + replyId;
    Long replyTotal = worksCommentRedisDao.getCommentTotal(replyKey);
    pageResult.setTotal(replyTotal);
    if (ObjectUtil.isEmpty(replyTotal)) {
        return pageResult;
    }
    List<SocialWorksCommentRespVO> replyList = new ArrayList<>();
    if (ObjectUtil.isNotEmpty(replyTotal) && replyTotal > 0) {
        Set<String> replyResult = worksCommentRedisDao.getStaticCommentPage(replyKey, pageReqVO.getPageNo(), pageReqVO.getPageSize());
        if (CollectionUtil.isNotEmpty(replyResult)) {
            for (String reply : replyResult) {
                SocialWorksCommentRespVO respRespVO = JSON.parseObject(reply, SocialWorksCommentRespVO.class);
                replyList.add(respRespVO);
            }
        }
    }
    pageResult.setList(replyList);
    return pageResult;
}

private PageResult<SocialWorksCommentRespVO> getWorksCommentPage(SocialWorksCommentPageReqVO pageReqVO){
    Long allNum = 0L;
    PageResult<SocialWorksCommentRespVO> pageResult = new PageResult();
    Long worksId = pageReqVO.getWorksId();
    String commentKey = WORKS_COMMENT_KEY_PREFIX+worksId;
    
    Long commentTotal = worksCommentRedisDao.getCommentTotal(commentKey);
    pageResult.setTotal(commentTotal);
    allNum += commentTotal;
    if (ObjectUtil.isEmpty(commentTotal)) {
        return pageResult;
    }
    
    
    List<SocialWorksCommentRespVO> commentList = new ArrayList<>();
    // 处理分页数据
    Set<String> setResult = worksCommentRedisDao.getStaticCommentPage(commentKey, pageReqVO.getPageNo(), pageReqVO.getPageSize());
    
    if (CollectionUtil.isNotEmpty(setResult)) {
        for (String comment : setResult) {
            SocialWorksCommentRespVO commentRespVO = JSON.parseObject(comment, SocialWorksCommentRespVO.class);
            commentList.add(commentRespVO);
        }
    }
    if (ObjectUtil.isEmpty(commentList)) {
        return pageResult;
    }
    
    for (SocialWorksCommentRespVO commentRespVO : commentList) {
        if (ObjectUtil.isNotEmpty(commentRespVO)) {
            // 处理回复消息
            Long replyId = commentRespVO.getId();
            String replyKey = WORKS_REPLY_KEY_PREFIX+worksId+":"+replyId;
            Long replyTotal = worksCommentRedisDao.getCommentTotal(replyKey);
            allNum +=replyTotal;
            commentRespVO.setTotal(replyTotal);
            
            List<SocialWorksCommentRespVO> replyList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(replyTotal) && replyTotal > 0) {
                Set<String> replyResult = worksCommentRedisDao.getStaticCommentPage(replyKey, pageReqVO.getPageNo(), 3);
                if (CollectionUtil.isNotEmpty(replyResult)) {
                    for (String reply : replyResult) {
                        SocialWorksCommentRespVO respRespVO = JSON.parseObject(reply, SocialWorksCommentRespVO.class);
                        replyList.add(respRespVO);
                    }
                }
            }
            commentRespVO.setRespVOList(replyList);
        }
    }
    pageResult.setList(commentList);
    pageResult.setOtherTotal(allNum.toString());
    return pageResult;
}
}
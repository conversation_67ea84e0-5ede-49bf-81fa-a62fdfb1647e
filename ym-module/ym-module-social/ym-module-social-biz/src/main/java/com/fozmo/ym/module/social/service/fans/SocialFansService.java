package com.fozmo.ym.module.social.service.fans;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.fans.vo.FansSaveRespVO;
import com.fozmo.ym.module.social.controller.app.fans.vo.AppFansPageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.fans.SocialFansDO;
import jakarta.validation.Valid;

import java.util.List;

public interface SocialFansService {
	
	/**
	 * 关注
	 */
	Long addFans(FansSaveRespVO saveRespVO);
	/**
	 *  取消关注
	 */

	Boolean deleteFans(Long accountId,Long fansAccountId);
	/**
	 *
	 * 关注列表
	 */
	PageResult<SocialFansDO> getFans(AppFansPageReqVO reqVO);
	// 更新粉丝信息
	Long updFans(FansSaveRespVO saveRespVO);


    List<SocialFansDO> getFanList(long userId);

    boolean isFans(Long accountId, Long fansAccountId);

	Long queryFollowNum(Long accountId);

	Long queryFansNum(Long accountId);

	PageResult<SocialFansDO> myFansPage(@Valid AppFansPageReqVO reqVO);
}

package com.fozmo.ym.module.social.service.fans;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.social.controller.admin.fans.vo.FansSaveRespVO;
import com.fozmo.ym.module.social.controller.app.fans.vo.AppFansPageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.fans.SocialFansDO;
import com.fozmo.ym.module.social.dal.mysql.fans.SocialFansMapper;
import com.fozmo.ym.module.social.dal.redis.fans.FansRedisDao;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.*;

@Service
public class SocialFansServiceImpl implements SocialFansService {

	@Resource
	private SocialFansMapper socialFansMapper;
	
	@Resource
	private AccountApi accountAuthApi;
	
	@Resource
	private IdService idService;
	
	@Resource
	@Lazy
	private FansRedisDao fansRedisDao;
	/**
	 * 关注
	 *
	 */
	@Override
	public Long addFans(FansSaveRespVO saveRespVO) {
		// 获取 当前 登录人Id
		Long accountId = saveRespVO.getAccountId();

		Long fansAccountId = SecurityFrameworkUtils.getLoginUserId();
		if (ObjectUtil.isEmpty(fansAccountId)) {
			throw exception(UNAUTHORIZED);
		}
		if (ObjectUtil.isEmpty(accountId)) {
			throw exception(FANS_ACCOUNT_NOT);
		}


		if (fansRedisDao.checkFans(accountId, fansAccountId)) {
			throw exception(FANS_HAVE);
		}
		AccountBaseInfoDTO account = accountAuthApi.queryAccountBaseInfoById(accountId);
		if (ObjectUtil.isEmpty(account)) {
			throw exception(FANS_ACCOUNT_NOT);
		}
		
		// 获取 当前 登录人账户信息
		AccountBaseInfoDTO loginAccount = accountAuthApi.queryAccountBaseInfoById(fansAccountId);

		SocialFansDO createObj = BeanUtils.toBean(loginAccount, SocialFansDO.class);
		
		// 组装 被关注人数据
		createObj.setRightsId(account.getRightsId());
		createObj.setNickname(account.getNickname());
		createObj.setAvatar(account.getAvatar());
		createObj.setAccountName(account.getName());
		createObj.setAccountId(accountId);
		
		// 组装关注人数据数据
		createObj.setFansAccountId(loginAccount.getId());
		createObj.setFansAccountName(loginAccount.getName());
		createObj.setFansAvatar(loginAccount.getAvatar());
		createObj.setFansNickname(loginAccount.getNickname());
		createObj.setFansRightsId(saveRespVO.getRightsId());
		createObj.setFansTime(LocalDateTime.now());
		
	
		createObj.setCreateId(loginAccount.getId());
		createObj.setCreator(loginAccount.getName());
		createObj.setCreateDate(LocalDate.now());
		createObj.setId(idService.nextId("socialfans"));
		createObj.setTenantId(1L);
		createObj.setTenantCode("1");
		
		fansRedisDao.addFans(createObj);
		socialFansMapper.insert(createObj);
		return createObj.getId();
	}

/**
 * 取消关注
 *
 */
@Override
public Boolean deleteFans(Long accountId,Long fansAccountId) {
	AccountBaseInfoDTO account = accountAuthApi.queryAccountBaseInfoById(accountId);
	if (ObjectUtil.isEmpty(account)) {
		throw exception(FANS_ACCOUNT_NOT);
	}
	if (fansRedisDao.checkFans(accountId, fansAccountId)) {
		fansRedisDao.deleteFans(accountId, fansAccountId);
		SocialFansDO fansDO = socialFansMapper.selectByAccountId(accountId,fansAccountId);
		if (ObjectUtil.isEmpty(fansDO)) {
			throw exception(FANS_NOT_EXISTS);
		}
		int result = socialFansMapper.deleteById(fansDO.getId());
		return result > 0;
	} else {
		throw exception(FANS_NOT_EXISTS);
	}
}

/**
 * 关注列表
 */
	@Override
	public PageResult<SocialFansDO> getFans(AppFansPageReqVO pageReqVO) {
		PageResult<SocialFansDO> pageResult = new PageResult<>();
		
		// 先查询缓存
		pageResult = fansRedisDao.getFansPage(pageReqVO.getAccountId(), pageReqVO.getPageNo(), pageReqVO.getPageSize());
		if (ObjectUtil.isNotEmpty(pageResult)) {
			return pageResult;
		}
		// 加锁 查询数据库
		fansRedisDao.lock(pageReqVO.getAccountId(),6000L);
		pageResult = queryDbPage(pageReqVO);
		fansRedisDao.unlock(pageReqVO.getAccountId(),6000L);
		return pageResult;
	}

/**
 *
 */
	@Override
	public Long updFans(FansSaveRespVO saveRespVO) {
		return 0L;
	}

	/**
	 *
	 */
	@Override
	public List<SocialFansDO> getFanList(long userId) {
		List<SocialFansDO> fans = new ArrayList<>();
		
		fans= fansRedisDao.getFansList(userId);
		if (ObjectUtil.isEmpty(fans)) {
			LambdaQueryWrapperX<SocialFansDO> queryWrapper = new LambdaQueryWrapperX<>();
			queryWrapper.eq(SocialFansDO::getAccountId, userId);
			fans=socialFansMapper.selectList(queryWrapper);
		}
		return fans;
	}

    /**
     * @param accountId
     * @return
     */
    @Override
    public boolean isFans(Long accountId, Long fansAccountId) {
        return fansRedisDao.checkFans(accountId, fansAccountId);
    }

	/**
	 * @param accountId
	 * @return
	 */
	@Override
	public Long queryFollowNum(Long accountId) {
		return fansRedisDao.queryFollowNum(accountId);
	}

	/**
	 * @param accountId
	 * @return
	 */
	@Override
	public Long queryFansNum(Long accountId) {
		return fansRedisDao.queryFansNum(accountId);
	}

	/**
	 * @param reqVO
	 * @return
	 */
	@Override
	public PageResult<SocialFansDO> myFansPage(AppFansPageReqVO reqVO) {

		PageResult<SocialFansDO> pageResult = queryFlowPage(reqVO);
		return pageResult;
	}

	private PageResult<SocialFansDO> queryDbPage(AppFansPageReqVO pageReqVO) {
		PageResult<SocialFansDO> pageResult = socialFansMapper.selectPage(pageReqVO);
		if (ObjectUtil.isNotEmpty(pageResult) && CollUtil.isNotEmpty(pageResult.getList())) {
			AccountBaseInfoDTO account = accountAuthApi.queryAccountBaseInfoById(pageReqVO.getAccountId());
			pageResult.getList().forEach(socialFansDO -> {

				AccountBaseInfoDTO fansAccount = accountAuthApi.queryAccountBaseInfoById(socialFansDO.getFansAccountId());
				if (ObjectUtil.isNotEmpty(account)) {
					socialFansDO.setAccountId(account.getId());
					socialFansDO.setAccountName(account.getName());
					socialFansDO.setAvatar(account.getAvatar());
					socialFansDO.setNickname(account.getNickname());
					socialFansDO.setRightsId(account.getRightsId());
					socialFansDO.setFansStatus(isFans(socialFansDO.getAccountId(), socialFansDO.getFansAccountId()));
				} else {
					socialFansDO.setFansStatus(false);
				}

				if (ObjectUtil.isNotEmpty(fansAccount)) {
					socialFansDO.setFansAccountName(fansAccount.getName());
					socialFansDO.setFansAvatar(fansAccount.getAvatar());
					socialFansDO.setFansNickname(fansAccount.getNickname());
					socialFansDO.setFansRightsId(fansAccount.getRightsId());
				}
			});
		}
		return pageResult;
	}


	private PageResult<SocialFansDO> queryFlowPage(AppFansPageReqVO pageReqVO) {
		PageResult<SocialFansDO> pageResult = socialFansMapper.selectPage(pageReqVO);
		if (ObjectUtil.isNotEmpty(pageResult) && CollUtil.isNotEmpty(pageResult.getList())) {
			AccountBaseInfoDTO fansAccount = accountAuthApi.queryAccountBaseInfoById(pageReqVO.getFansAccountId());

			pageResult.getList().forEach(socialFansDO -> {
				AccountBaseInfoDTO account = accountAuthApi.queryAccountBaseInfoById(socialFansDO.getAccountId());
				if (ObjectUtil.isNotEmpty(account)) {
					socialFansDO.setAccountId(account.getId());
					socialFansDO.setAccountName(account.getName());
					socialFansDO.setAvatar(account.getAvatar());
					socialFansDO.setNickname(account.getNickname());
					socialFansDO.setRightsId(account.getRightsId());
					socialFansDO.setFansStatus(isFans(socialFansDO.getAccountId(), socialFansDO.getFansAccountId()));
				} else {
					socialFansDO.setFansStatus(false);
				}

				if (ObjectUtil.isNotEmpty(fansAccount)) {
					socialFansDO.setFansAccountName(fansAccount.getName());
					socialFansDO.setFansAvatar(fansAccount.getAvatar());
					socialFansDO.setFansNickname(fansAccount.getNickname());
					socialFansDO.setFansRightsId(fansAccount.getRightsId());
				}


			});
		}
		return pageResult;
	}
}

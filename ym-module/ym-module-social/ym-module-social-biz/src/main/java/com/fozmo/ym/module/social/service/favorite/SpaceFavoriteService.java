package com.fozmo.ym.module.social.service.favorite;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.SpaceFavoritePageReqVO;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.SpaceFavoriteSaveReqVO;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppFavoriteSaveReqVo;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppSpaceFavoritePageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.SpaceFavoriteDO;
import jakarta.validation.Valid;

/**
 * 空间收藏 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceFavoriteService {

    /**
     * 创建空间收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFavorite(@Valid SpaceFavoriteSaveReqVO createReqVO);

    /**
     * 更新空间收藏
     *
     * @param updateReqVO 更新信息
     */
    void updateFavorite(@Valid SpaceFavoriteSaveReqVO updateReqVO);

    /**
     * 删除空间收藏
     *
     * @param id 编号
     */
    void deleteFavorite(Long id);

    /**
     * 获得空间收藏
     *
     * @param id 编号
     * @return 空间收藏
     */
    SpaceFavoriteDO getFavorite(Long id);

    /**
     * 获得空间收藏分页
     *
     * @param pageReqVO 分页查询
     * @return 空间收藏分页
     */
    PageResult<SpaceFavoriteDO> getFavoritePage(SpaceFavoritePageReqVO pageReqVO);
    /**
     * 用户收藏空间
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAppFavorite(@Valid AppFavoriteSaveReqVo createReqVO);

    PageResult<SpaceFavoriteDO> getFavoritePageApp(@Valid AppSpaceFavoritePageReqVO pageReqVO);
}
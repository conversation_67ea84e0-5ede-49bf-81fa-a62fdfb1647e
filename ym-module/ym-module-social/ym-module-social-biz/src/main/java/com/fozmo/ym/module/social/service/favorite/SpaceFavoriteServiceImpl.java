package com.fozmo.ym.module.social.service.favorite;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.SpaceFavoritePageReqVO;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.SpaceFavoriteSaveReqVO;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppFavoriteSaveReqVo;
import com.fozmo.ym.module.social.controller.app.favorite.vo.AppSpaceFavoritePageReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.SpaceFavoriteDO;
import com.fozmo.ym.module.social.dal.mysql.favorite.SpaceFavoriteMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.*;

/**
 * 空间收藏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceFavoriteServiceImpl implements SpaceFavoriteService {

    @Resource
    private SpaceFavoriteMapper favoriteMapper;

    @Resource
    private IdService idService;

    @Override
    public Long createFavorite(SpaceFavoriteSaveReqVO createReqVO) {
        // 插入
        SpaceFavoriteDO createObj = BeanUtils.toBean(createReqVO, SpaceFavoriteDO.class);

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("spaceFavorite"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        createObj.setAccountId(loginUser.getId());
        createObj.setAccountName(loginUser.getUsername());


        favoriteMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateFavorite(SpaceFavoriteSaveReqVO updateReqVO) {
        // 校验存在
        validateFavoriteExists(updateReqVO.getId());
        // 更新
        SpaceFavoriteDO updateObj = BeanUtils.toBean(updateReqVO, SpaceFavoriteDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        favoriteMapper.updateById(updateObj);
    }

    @Override
    public void deleteFavorite(Long id) {
        // 校验存在
        validateFavoriteExists(id);
        // 删除
        favoriteMapper.deleteById(id);
    }

    private void validateFavoriteExists(Long id) {
        if (favoriteMapper.selectById(id) == null) {
            throw exception(FAVORITE_NOT_EXISTS);
        }
    }

    @Override
    public SpaceFavoriteDO getFavorite(Long id) {
        return favoriteMapper.selectById(id);
    }

    @Override
    public PageResult<SpaceFavoriteDO> getFavoritePage(SpaceFavoritePageReqVO pageReqVO) {
        return favoriteMapper.selectPage(pageReqVO);
    }

    /**
     */
    @Override
    public Long createAppFavorite(AppFavoriteSaveReqVo createReqVO) {

        // 校验空间是否已收藏
        Long spaceId = createReqVO.getSpaceId();
        if (spaceId == null) {
            throw exception(SPACE_NOT_EXISTS);
        }

        // 校验用户是否已收藏
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        LambdaQueryWrapperX<SpaceFavoriteDO> queryWrapper = new LambdaQueryWrapperX<>();
        assert loginUser != null;
        queryWrapper.eq(SpaceFavoriteDO::getSpaceId, spaceId).eq(SpaceFavoriteDO::getCreateId, loginUser.getId());
        queryWrapper.eq(SpaceFavoriteDO::getDeleted, false);
        List<SpaceFavoriteDO> list = favoriteMapper.selectList(queryWrapper);
        if (!list.isEmpty()) {
            throw exception(FAVORITE_NOT_ONE);
        }
        SpaceFavoriteDO favorite = new SpaceFavoriteDO();
        favorite.setSpaceId(spaceId); // 设置空间ID
        LocalDate current = LocalDate.now();
        favorite.setCreator(loginUser.getUsername());// 设置创建者
        favorite.setCreateId(loginUser.getId());// 设置创建者ID
        favorite.setTenantCode(loginUser.getTenantId()+"");// 设置租户代码
        favorite.setTenantId(loginUser.getTenantId());// 设置租户ID
        favorite.setCreateData(current);// 设置创建日期
        favorite.setUpdateData(current);// 设置更新日期
        favorite.setUpdateId(loginUser.getId());// 设置更新者ID
        favorite.setAccountId(loginUser.getId());
        favorite.setAccountName(loginUser.getUsername());
        favorite.setId(idService.nextId("spaceFavorite"));
        favoriteMapper.insert(favorite);
        return 0L;
    }

    /**
     */
    @Override
    public PageResult<SpaceFavoriteDO> getFavoritePageApp(AppSpaceFavoritePageReqVO pageReqVO) {
        SpaceFavoritePageReqVO reqVO = BeanUtils.toBean(pageReqVO, SpaceFavoritePageReqVO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        reqVO.setAccountId(loginUser.getId());
        return favoriteMapper.selectPage(reqVO);
    }

}
package com.fozmo.ym.module.social.service.favorite;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoritePageReqVO;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoriteSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.WorksFavoriteDO;
import jakarta.validation.Valid;

/**
 * 作品收藏 Service 接口
 *
 * <AUTHOR>
 */
public interface WorksFavoriteService {

    /**
     * 创建作品收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWorksFavorite(@Valid WorksFavoriteSaveReqVO createReqVO);

    /**
     * 更新作品收藏
     *
     * @param updateReqVO 更新信息
     */
    void updateWorksFavorite(@Valid WorksFavoriteSaveReqVO updateReqVO);

    /**
     * 删除作品收藏
     *
     * @param id 编号
     */
    void deleteWorksFavorite(Long id);

    /**
     * 获得作品收藏
     *
     * @param id 编号
     * @return 作品收藏
     */
    WorksFavoriteDO getWorksFavorite(Long id);

    /**
     * 获得作品收藏分页
     *
     * @param pageReqVO 分页查询
     * @return 作品收藏分页
     */
    PageResult<WorksFavoriteDO> getWorksFavoritePage(WorksFavoritePageReqVO pageReqVO);

}

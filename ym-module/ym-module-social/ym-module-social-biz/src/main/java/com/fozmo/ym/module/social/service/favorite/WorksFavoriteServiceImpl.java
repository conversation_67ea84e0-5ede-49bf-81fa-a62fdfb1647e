package com.fozmo.ym.module.social.service.favorite;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoritePageReqVO;
import com.fozmo.ym.module.social.controller.admin.favorite.vo.WorksFavoriteSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.favorite.WorksFavoriteDO;
import com.fozmo.ym.module.social.dal.mysql.favorite.WorksFavoriteMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.*;

/**
 * 作品收藏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WorksFavoriteServiceImpl implements WorksFavoriteService {

    @Resource
    private WorksFavoriteMapper worksFavoriteMapper;

    @Resource
    private IdService idService;

    @Override
    public Long createWorksFavorite(WorksFavoriteSaveReqVO createReqVO) {
        // 插入
        WorksFavoriteDO createObj = BeanUtils.toBean(createReqVO, WorksFavoriteDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("worksFavorite"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        createObj.setAccountId(loginUser.getId());
        createObj.setAccountName(loginUser.getUsername());
        worksFavoriteMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateWorksFavorite(WorksFavoriteSaveReqVO updateReqVO) {
        // 校验存在
        validateWorksFavoriteExists(updateReqVO.getId());
        // 更新
        WorksFavoriteDO updateObj = BeanUtils.toBean(updateReqVO, WorksFavoriteDO.class);

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setAccountId(loginUser.getId());
        updateObj.setAccountName(loginUser.getUsername());
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        worksFavoriteMapper.updateById(updateObj);
    }

    @Override
    public void deleteWorksFavorite(Long id) {
        // 校验存在
        validateWorksFavoriteExists(id);
        // 删除
        worksFavoriteMapper.deleteById(id);
    }

    private void validateWorksFavoriteExists(Long id) {
        if (worksFavoriteMapper.selectById(id) == null) {
            throw exception(WORKS_FAVORITE_NOT_EXISTS);
        }
    }

    @Override
    public WorksFavoriteDO getWorksFavorite(Long id) {
        return worksFavoriteMapper.selectById(id);
    }

    @Override
    public PageResult<WorksFavoriteDO> getWorksFavoritePage(WorksFavoritePageReqVO pageReqVO) {
        return worksFavoriteMapper.selectPage(pageReqVO);
    }

}

package com.fozmo.ym.module.social.service.like;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.like.vo.SpaceLikePageReqVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.SpaceLikeSaveReqVO;
import com.fozmo.ym.module.social.controller.app.like.vo.AppSpaceLikeSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.like.SpaceLikeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间点赞 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceLikeService {

    /**
     * 创建空间点赞
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLike(@Valid SpaceLikeSaveReqVO createReqVO);

    /**
     * 更新空间点赞
     *
     * @param updateReqVO 更新信息
     */
    void updateLike(@Valid SpaceLikeSaveReqVO updateReqVO);

    /**
     * 删除空间点赞
     *
     */
    void deleteLike(Long spaceId);

    /**
     * 获得空间点赞
     *
     * @param id 编号
     * @return 空间点赞
     */
    SpaceLikeDO getLike(Long id);

    /**
     * 获得空间点赞分页
     *
     * @param pageReqVO 分页查询
     * @return 空间点赞分页
     */
    PageResult<SpaceLikeDO> getLikePage(SpaceLikePageReqVO pageReqVO);

    Long createLikeApp(@Valid AppSpaceLikeSaveReqVO createReqVO);

    Long selectSpaceLikeCountBySpaceId(Long spaceId);

    List<SpaceLikeDO> getLikeBySpaceId(List<Long> spaceIds);

    boolean isLike(Long spaceId, Long accountId);

    void deleteBySpaceId(Long spaceId);
}
package com.fozmo.ym.module.social.service.like;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.notice.api.NoticeMessageApi;
import com.fozmo.ym.module.notice.api.dto.NoticeMessageDTO;
import com.fozmo.ym.module.social.controller.admin.like.vo.SpaceLikePageReqVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.SpaceLikeSaveReqVO;
import com.fozmo.ym.module.social.controller.app.like.vo.AppSpaceLikeSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.like.SpaceLikeDO;
import com.fozmo.ym.module.social.dal.mysql.like.SpaceLikeMapper;
import com.fozmo.ym.module.social.dal.redis.like.SpaceLikeRedisDao;
import com.fozmo.ym.module.space.api.SpaceApi;
import com.fozmo.ym.module.space.api.dto.SpaceInfoDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.*;

/**
 * 空间点赞 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceLikeServiceImpl implements SpaceLikeService {

    @Resource
    private SpaceLikeMapper likeMapper;
    
    @Resource
    private IdService idService;
    
    @Resource
    private SpaceLikeRedisDao likeRedisDao;
    
    @Resource
    private AccountApi accountApi;

    @Resource
    @Lazy
    private SpaceApi spaceApi;

    @Resource
    private NoticeMessageApi noticeMessageApi;

    @Override
    public Long createLike(SpaceLikeSaveReqVO createReqVO) {
        // 插入
        SpaceLikeDO createObj = BeanUtils.toBean(createReqVO, SpaceLikeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("spaceLike"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        createObj.setAccountId(loginUser.getId());
        createObj.setAccountName(loginUser.getUsername());
        likeMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateLike(SpaceLikeSaveReqVO updateReqVO) {
        // 校验存在
        validateLikeExists(updateReqVO.getId());
        // 更新
        SpaceLikeDO updateObj = BeanUtils.toBean(updateReqVO, SpaceLikeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdateTime(LocalDateTimeUtil.now());
        likeMapper.updateById(updateObj);
    }

    @Override
    public void deleteLike(Long spaceId) {
        // 校验存在
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(ACCOUNT_NOT);
        }
        
        AccountBaseInfoDTO accountBaseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
        if (ObjectUtil.isEmpty(accountBaseInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }
        // 校验空间是否存在
        if (spaceApi.hasSpace(spaceId)){
            if (likeRedisDao.hasSpaceLike(spaceId, accountId)) {
                // 删除
                SpaceLikeDO spaceLikeDO = likeRedisDao.getSpaceLikeInfo(spaceId, accountId);
                likeRedisDao.deleteSpaceLike(spaceId, accountId);
                if (ObjectUtil.isNotEmpty(spaceLikeDO)) {
                    likeMapper.deleteById(spaceLikeDO.getId());
                }

            } else {
                throw exception(LIKE_NOT_EXISTS);
            }
        } else {
            throw exception(SPACE_NOT_EXISTS);
        }
    }

    private void validateLikeExists(Long id) {
        if (likeMapper.selectById(id) == null) {
            throw exception(LIKE_NOT_EXISTS);
        }
    }

    @Override
    public SpaceLikeDO getLike(Long id) {
        return likeMapper.selectById(id);
    }

    @Override
    public PageResult<SpaceLikeDO> getLikePage(SpaceLikePageReqVO pageReqVO) {
        PageResult<SpaceLikeDO> pageResult = likeRedisDao.getSpaceLikePage(pageReqVO.getSpaceId(), pageReqVO.getPageNo(), pageReqVO.getPageSize());
        return pageResult;
    }

    /**
     */
    @Override
    public Long createLikeApp(AppSpaceLikeSaveReqVO createReqVO) {
        
        //校验 是否 已经点赞 且点赞是否是个用户
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(ACCOUNT_NOT);
        }
        
        AccountBaseInfoDTO accountBaseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
        if (ObjectUtil.isEmpty(accountBaseInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }
        // 校验空间是否存在
        if (spaceApi.hasSpace(createReqVO.getSpaceId())){
        
        }else {
            throw exception(SPACE_NOT_EXISTS);
        }
        
        // 校验是否已点赞
        if (likeRedisDao.hasSpaceLike(createReqVO.getSpaceId(), accountId)){
            throw exception(LIKE_NOT_CREATE);
        }
        
        SpaceLikeDO like = BeanUtils.toBean(createReqVO, SpaceLikeDO.class);
        like.setCreator(accountBaseInfoDTO.getName());
        like.setCreateId(accountBaseInfoDTO.getId());
        like.setTenantId(1L);
        LocalDateTime now = LocalDateTime.now();
        like.setLikeTime(now);
        like.setId(idService.nextId("spaceLike"));
        like.setCreateData(now.toLocalDate());
        like.setAccountId(accountId);
        like.setAccountName(accountBaseInfoDTO.getName());
        
        // 写入 缓存
        likeRedisDao.saveSpaceLike(like);
        likeMapper.insert(like);

        // 创建点赞通知
        sendNotify(like.getSpaceId(), accountId);
        return like.getId();
    }

    /**
     */
    @Override
    public Long selectSpaceLikeCountBySpaceId(Long spaceId) {

        return likeRedisDao.getSpaceCount(spaceId);
    }

    /**
     */
    @Override
    public List<SpaceLikeDO> getLikeBySpaceId(List<Long> spaceIds) {


        if (CollUtil.isEmpty(spaceIds)) {
            return null;
        }
        List<SpaceLikeDO> likeList = new ArrayList<>();
        for (Long spaceId : spaceIds) {
            List <SpaceLikeDO> list = likeRedisDao.getSpaceLike(spaceId);
            if (CollUtil.isEmpty(list)) {
                LambdaQueryWrapperX<SpaceLikeDO> queryWrapperX = new LambdaQueryWrapperX<>();
                queryWrapperX.eq(SpaceLikeDO::getSpaceId, spaceId);
                queryWrapperX.eq(SpaceLikeDO::getDeleted,false);
                list= likeMapper.selectList(queryWrapperX);
                
                if (CollUtil.isNotEmpty(list)) {
                    for (SpaceLikeDO likeDO : list) {
                        likeRedisDao.saveSpaceLike(likeDO);
                    }
                }
            }
            
            likeList.addAll(list);
        }
        return likeList;
    }

    /**
     *
     */
    @Override
    public boolean isLike(Long spaceId, Long accountId) {
        
        return likeRedisDao.hasSpaceLike(spaceId, accountId);
    }

    /**
     * @param spaceId
     */
    @Override
    public void deleteBySpaceId(Long spaceId) {
        if (likeRedisDao.hasSpaceLikeBySpaceId(spaceId)) {
            likeRedisDao.deleteSpaceAllLike(spaceId);
        }
        likeMapper.delete(new LambdaQueryWrapperX<SpaceLikeDO>().eq(SpaceLikeDO::getSpaceId, spaceId).eq(SpaceLikeDO::getDeleted, false));
    }

    private void sendNotify(Long spaceId, Long accountId) {

        // 创建点赞通知
        NoticeMessageDTO noticeMessageDTO = new NoticeMessageDTO();
        noticeMessageDTO.setChannelId(1L);
        noticeMessageDTO.setTemplateId(1L);
        // 查询space
        SpaceInfoDTO spaceDTO = spaceApi.getSpaceInfo(spaceId);

        AccountBaseInfoDTO accountBaseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);

        noticeMessageDTO.setSendUserInfo(Map.of("accountId",accountBaseInfoDTO.getId(),"nickname", accountBaseInfoDTO.getNickname(), "avatar", accountBaseInfoDTO.getAvatar()));
        noticeMessageDTO.setParams(Arrays.asList(accountId.toString(),spaceDTO.getSpaceCnName()));
        noticeMessageDTO.setSendObjectInfo(Map.of("spaceId", spaceDTO.getId(), "spaceName", spaceDTO.getSpaceCnName(),"type","space","spaceCover",spaceDTO.getSpaceCover(),"jumpType","inner"));
        noticeMessageDTO.setSendResource("spaceLike");
        noticeMessageDTO.setSendApp("ym");
        noticeMessageDTO.setToUser(Collections.singletonList(spaceDTO.getAccountId()));

        noticeMessageApi.createMessage(noticeMessageDTO);

    }

}
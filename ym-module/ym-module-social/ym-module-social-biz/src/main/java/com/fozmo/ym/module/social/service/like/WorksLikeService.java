package com.fozmo.ym.module.social.service.like;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikePageReqVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikeRespVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikeSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.like.WorksLikeDO;
import jakarta.validation.Valid;

/**
 * 作品点赞 Service 接口
 *
 * <AUTHOR>
 */
public interface WorksLikeService {

    /**
     * 创建作品点赞
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWorksLike(@Valid WorksLikeSaveReqVO createReqVO);

    /**
     * 更新作品点赞
     *
     * @param updateReqVO 更新信息
     */
    void updateWorksLike(@Valid WorksLikeSaveReqVO updateReqVO);

    /**
     * 删除作品点赞
     *
     */
    void deleteWorksLike(Long worksId);

    /**
     * 获得作品点赞
     *
     * @param id 编号
     * @return 作品点赞
     */
    WorksLikeRespVO getWorksLike(Long id);

    /**
     * 获得作品点赞分页
     *
     * @param pageReqVO 分页查询
     * @return 作品点赞分页
     */
    PageResult<WorksLikeDO> getWorksLikePage(WorksLikePageReqVO pageReqVO);

    Long countByWorksId(Long worksId);

    boolean isLike(Long worksId, Long accountId);

    void deleteByWorksId(Long worksId);
}
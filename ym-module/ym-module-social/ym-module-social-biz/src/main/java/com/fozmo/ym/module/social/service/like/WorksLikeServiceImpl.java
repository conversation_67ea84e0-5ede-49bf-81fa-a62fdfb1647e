package com.fozmo.ym.module.social.service.like;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.account.api.account.dto.AccountRightsInfoDTO;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikePageReqVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikeRespVO;
import com.fozmo.ym.module.social.controller.admin.like.vo.WorksLikeSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.like.WorksLikeDO;
import com.fozmo.ym.module.social.dal.mysql.like.WorksLikeMapper;
import com.fozmo.ym.module.social.dal.redis.like.WorksLikeRedisDao;
import com.fozmo.ym.module.social.enums.ErrorCodeConstants;
import com.fozmo.ym.module.space.api.WorksApi;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.LIKE_NOT_CREATE;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.WORKS_NOT_EXISTS;

/**
 * 作品点赞 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WorksLikeServiceImpl implements WorksLikeService {

    @Resource
    private WorksLikeMapper worksLikeMapper;
    
    @Resource
    @Lazy
    private AccountApi accountApi;

    @Resource
    private IdService idService;
    
    @Resource
    private WorksLikeRedisDao worksLikeRedisDao;
    
   @Resource
   @Lazy
   private WorksApi worksApi;

    @Override
    public Long createWorksLike(WorksLikeSaveReqVO createReqVO) {
        
        // 1 校验作品 是否存在
        Long worksId = createReqVO.getWorksId();
        if (ObjectUtil.isEmpty(worksId)) {
            throw exception(WORKS_NOT_EXISTS);
        }
        if (worksApi.hasWorks(worksId)){
        
        }else {
            throw exception(WORKS_NOT_EXISTS);
        }
        //校验 是否 已经点赞 且点赞是否是个用户
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(ACCOUNT_NOT);
        }
        
        AccountBaseInfoDTO accountBaseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
        if (ObjectUtil.isEmpty(accountBaseInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }
        // 校验是否 点赞
        if (worksLikeRedisDao.hasWorksLike(worksId,accountId)){
            throw exception(LIKE_NOT_CREATE);
        }
        // 插入
        WorksLikeDO createObj = BeanUtils.toBean(createReqVO, WorksLikeDO.class);

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("workslike"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        createObj.setAccountId(loginUser.getId());
        createObj.setAccountName(loginUser.getUsername());
        worksLikeMapper.insert(createObj);
        worksLikeRedisDao.saveWorksLike(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateWorksLike(WorksLikeSaveReqVO updateReqVO) {
        // 校验存在
        validateWorksLikeExists(updateReqVO.getId());
        // 更新
        WorksLikeDO updateObj = BeanUtils.toBean(updateReqVO, WorksLikeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setAccountId(loginUser.getId());
        updateObj.setAccountName(loginUser.getUsername());
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        worksLikeMapper.updateById(updateObj);
    }

    @Override
    public void deleteWorksLike(Long worksId) {
        // 校验存在
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(UNAUTHORIZED);
        }
        
        AccountBaseInfoDTO accountBaseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
        if (ObjectUtil.isEmpty(accountBaseInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }
        // 校验空间是否存在
        if (worksApi.hasWorks(worksId)){

            if (worksLikeRedisDao.hasWorksLike(worksId, accountId)) {
                WorksLikeDO worksLikeDO = worksLikeRedisDao.getWorksLikeInfo(worksId, accountId);
                if (ObjectUtil.isNotEmpty(worksLikeDO)) {
                    Long id = worksLikeDO.getId();
                    worksLikeRedisDao.deleteWorksLike(worksId, accountId);
                    worksLikeMapper.deleteById(id);
                } else {
                    throw exception(ErrorCodeConstants.WORKS_LIKE_NOT_EXISTS);
                }

            } else {
                throw exception(ErrorCodeConstants.WORKS_LIKE_NOT_EXISTS);
            }
        } else {
            throw exception(WORKS_NOT_EXISTS);
        }
        

        

    }

    private void validateWorksLikeExists(Long id) {
        if (worksLikeMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.WORKS_LIKE_NOT_EXISTS);
        }
    }

    @Override
    public WorksLikeRespVO getWorksLike(Long id) {
        WorksLikeDO likeDO = worksLikeMapper.selectById(id);
        
        if (ObjectUtil.isNull(likeDO)) {
            throw exception(ErrorCodeConstants.WORKS_LIKE_NOT_EXISTS);
        }

        return addWorksLikeInfo(likeDO);
    }

    @Override
    public PageResult<WorksLikeDO> getWorksLikePage(WorksLikePageReqVO pageReqVO) {
        Long worksId = pageReqVO.getWorksId();
        if (ObjectUtil.isEmpty(worksId)) {
            throw exception(WORKS_NOT_EXISTS);
        }
        if (worksApi.hasWorks(worksId)) {
        
        }else {
            throw exception(WORKS_NOT_EXISTS);
        }
        PageResult<WorksLikeDO> result = worksLikeRedisDao.getWorksLikePage(worksId,pageReqVO.getPageNo(), pageReqVO.getPageSize());
        
        if (ObjectUtil.isNotEmpty(pageReqVO) && result.getTotal()>0) {
            return result;
        }
        LambdaQueryWrapperX<WorksLikeDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(WorksLikeDO::getWorksId,worksId);
        wrapperX.orderByDesc(WorksLikeDO::getLikeTime);
        
        result = worksLikeMapper.selectPage(pageReqVO, wrapperX);
        if (ObjectUtil.isNotEmpty(result) && result.getTotal()>0) {
           result.getList().forEach(worksLikeDO -> {
               worksLikeRedisDao.saveWorksLike(worksLikeDO);
           });
        }
        return result;
    }

    /**
     */
    @Override
    public Long countByWorksId(Long worksId) {

        return worksLikeRedisDao.selectCountByWorksId(worksId);
    }

    /**
     * @param worksId
     * @param accountId
     * @return
     */
    @Override
    public boolean isLike(Long worksId, Long accountId) {
        return worksLikeRedisDao.hasWorksLike(worksId, accountId);
    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        if (worksLikeRedisDao.hasWorksList(worksId)) {
            worksLikeRedisDao.deleteWorksAllLike(worksId);
        }

        worksLikeMapper.delete(new LambdaQueryWrapperX<WorksLikeDO>()
                .eq(WorksLikeDO::getWorksId, worksId)
                .eq(WorksLikeDO::getDeleted, false));

    }

    private WorksLikeRespVO addWorksLikeInfo(WorksLikeDO worksLikeDO) {
        
        // 获取当前用户Id
        Long loginAccountId = worksLikeDO.getAccountId();
        WorksLikeRespVO worksLikeRespVO = new WorksLikeRespVO();
        Long LikeAccountId = worksLikeDO.getAccountId();
        
        AccountRightsInfoDTO likeAccount= accountApi.queryAccountRightsInfoById(LikeAccountId);
        worksLikeRespVO=BeanUtils.toBean(worksLikeDO, WorksLikeRespVO.class);
        worksLikeRespVO.setAccountName(likeAccount.getName());
        worksLikeRespVO.setAvatar(likeAccount.getAvatar());
        worksLikeRespVO.setLikeStatus(ObjectUtil.isNotEmpty(loginAccountId) && loginAccountId.equals(LikeAccountId));
        
        return worksLikeRespVO;
    }

}
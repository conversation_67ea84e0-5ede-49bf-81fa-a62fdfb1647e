package com.fozmo.ym.module.social.service.platform;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformPageReqVO;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.platform.SocialPlatformDO;
import jakarta.validation.Valid;

/**
 * 社交平台 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialPlatformService {

    /**
     * 创建社交平台
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlatform(@Valid SocialPlatformSaveReqVO createReqVO);

    /**
     * 更新社交平台
     *
     * @param updateReqVO 更新信息
     */
    void updatePlatform(@Valid SocialPlatformSaveReqVO updateReqVO);

    /**
     * 删除社交平台
     *
     * @param id 编号
     */
    void deletePlatform(Long id);

    /**
     * 获得社交平台
     *
     * @param id 编号
     * @return 社交平台
     */
    SocialPlatformDO getPlatform(Long id);

    /**
     * 获得社交平台分页
     *
     * @param pageReqVO 分页查询
     * @return 社交平台分页
     */
    PageResult<SocialPlatformDO> getPlatformPage(SocialPlatformPageReqVO pageReqVO);

    SocialPlatformDO getPlatformByCode(String code);
}
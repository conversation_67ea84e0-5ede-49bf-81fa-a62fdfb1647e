package com.fozmo.ym.module.social.service.platform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformPageReqVO;
import com.fozmo.ym.module.social.controller.admin.platform.vo.SocialPlatformSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.platform.SocialPlatformDO;
import com.fozmo.ym.module.social.dal.mysql.platform.SocialPlatformMapper;
import com.fozmo.ym.module.social.dal.redis.platform.SocialPlatformRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.SOCIAL_PLATFORM_EXISTS;
import static com.fozmo.ym.module.social.enums.ErrorCodeConstants.SOCIAL_PLATFORM_NOT_EXISTS;

/**
 * 社交平台 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialPlatformServiceImpl implements SocialPlatformService {

    @Resource
    private SocialPlatformMapper platformMapper;
    
    @Resource
    private SocialPlatformRedisDao socialPlatformRedisDao;
    
    @Resource
    private IdService idService;
    @Resource
    private AccountApi accountAuthApi;

    @Override
    public Long createPlatform(SocialPlatformSaveReqVO createReqVO) {
        // 插入
        SocialPlatformDO platform = BeanUtils.toBean(createReqVO, SocialPlatformDO.class);
        
        if (socialPlatformRedisDao.hasKey(createReqVO.getCode())) {
            throw exception(SOCIAL_PLATFORM_EXISTS);
        }
        
        // 组装数据
        platform.setId(idService.nextId("social_platform"));
        AccountBaseInfoDTO accountInfoDTO = accountAuthApi.queryAccountBaseInfoById(WebFrameworkUtils.getLoginUserId());
        platform.setCreateId(accountInfoDTO.getId());
        platform.setCreator(accountInfoDTO.getName());
        platform.setCreateData(LocalDate.now());
        platform.setTenantId(1L);
        platform.setTenantCode("1");
        
        
        socialPlatformRedisDao.set(platform);
        platformMapper.insert(platform);
        // 返回
        return platform.getId();
    }

    @Override
    public void updatePlatform(SocialPlatformSaveReqVO updateReqVO) {
        // 校验存在
        if (socialPlatformRedisDao.hasKey(updateReqVO.getCode()) ||validatePlatformExists(updateReqVO.getId())){
            throw exception(SOCIAL_PLATFORM_NOT_EXISTS);
        }
        
        // 更新
        SocialPlatformDO updateObj = BeanUtils.toBean(updateReqVO, SocialPlatformDO.class);
        AccountBaseInfoDTO accountInfoDTO = accountAuthApi.queryAccountBaseInfoById(WebFrameworkUtils.getLoginUserId());
        updateObj.setUpdaterId(accountInfoDTO.getId());
        updateObj.setUpdater(accountInfoDTO.getName());
        updateObj.setUpdateData(LocalDate.now());
        socialPlatformRedisDao.deleteSocialPlatform(updateObj.getCode());
        socialPlatformRedisDao.set(updateObj);
        platformMapper.updateById(updateObj);
        
        
    }

    @Override
    public void deletePlatform(Long id) {
        // 校验存在
        SocialPlatformDO socialPlatformDO = platformMapper.selectById(id);
        
        if (ObjectUtil.isEmpty(socialPlatformDO)) {
            throw exception(SOCIAL_PLATFORM_NOT_EXISTS);
        }
        if (socialPlatformRedisDao.hasKey(socialPlatformDO.getCode())) {
            socialPlatformRedisDao.deleteSocialPlatform(socialPlatformDO.getCode());
        }
        platformMapper.deleteById(id);
    }

    private boolean validatePlatformExists(Long id) {
        return platformMapper.selectById(id) != null;
    }

    @Override
    public SocialPlatformDO getPlatform(Long id) {
        SocialPlatformDO platform = platformMapper.selectById(id);
        if (socialPlatformRedisDao.hasKey(platform.getCode())) {
            return platform;
        }else {
            socialPlatformRedisDao.set(platform);
            return platform;
        }
      
    }

    @Override
    public PageResult<SocialPlatformDO> getPlatformPage(SocialPlatformPageReqVO pageReqVO) {
        
        PageResult<SocialPlatformDO> pageResult =  socialPlatformRedisDao.getSocialPlatformPage(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        if (ObjectUtil.isNotEmpty(pageResult)&& pageResult.getTotal()==0L && CollUtil.isEmpty(pageResult.getList())) {
            socialPlatformRedisDao.lock(6000L);
            pageResult = platformMapper.selectPage(pageReqVO);
            socialPlatformRedisDao.unlock(6000L);
        }
       
        return pageResult;
    }

    /**
     */
    @Override
    public SocialPlatformDO getPlatformByCode(String code) {
        SocialPlatformDO socialPlatformDO = new SocialPlatformDO();
        if (socialPlatformRedisDao.hasKey(code)){
            socialPlatformDO= socialPlatformRedisDao.getSocialPlatform(code);
        }else {
            socialPlatformDO=platformMapper.getPlatformByCode(code);
            if (ObjectUtil.isNotEmpty(socialPlatformDO)){
                socialPlatformRedisDao.set(socialPlatformDO);
            }
        }
        return socialPlatformDO ;
    }

}
package com.fozmo.ym.module.social.service.share;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
import jakarta.validation.Valid;

/**
 * 空间分享 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceShareService {

    /**
     * 创建空间分享
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    SpaceShareRespVO createShare(@Valid SpaceShareSaveReqVO createReqVO);

    /**
     * 更新空间分享
     *
     * @param updateReqVO 更新信息
     */
    void updateShare(@Valid SpaceShareSaveReqVO updateReqVO);

    /**
     * 删除空间分享
     *
     * @param id 编号
     */
    void deleteShare(Long id);

    /**
     * 获得空间分享
     *
     * @param id 编号
     * @return 空间分享
     */
    SpaceShareDO getShare(Long id);

    SpaceShareDO getShareBySpaceId(Long spaceId, Long accountId);

    SpaceShareDO getShareByScene(String scene);

    /**
     * 获得空间分享分页
     *
     * @param pageReqVO 分页查询
     * @return 空间分享分页
     */
    PageResult<SpaceShareDO> getSharePage(SpaceSharePageReqVO pageReqVO);

    Long countBySpaceId(Long spaceId);

    boolean isShare(Long spaceId, Long accountId);

    void deleteBySpaceId(Long spaceId);
}
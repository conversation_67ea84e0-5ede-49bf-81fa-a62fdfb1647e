package com.fozmo.ym.module.social.service.share;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.auth.api.WechatApi;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.SpaceShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
import com.fozmo.ym.module.social.dal.mysql.share.SpaceShareMapper;
import com.fozmo.ym.module.social.dal.redis.share.SpaceShareRedisDao;
import com.fozmo.ym.module.social.enums.ErrorCodeConstants;
import com.fozmo.ym.module.space.api.SpaceApi;
import com.fozmo.ym.module.space.api.dto.SpaceInfoDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.SPACE_NOT_EXISTS;


/**
 * 空间分享 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceShareServiceImpl implements SpaceShareService {

    @Resource
    private SpaceShareMapper shareMapper;
    
    @Resource
    private AccountApi accountApi;
    
    @Resource
    private IdService idService;
    
    @Resource
    private WechatApi wechatApi;
    
    @Resource
    private SpaceShareRedisDao shareRedisDao;

    @Resource
    @Lazy
    private SpaceApi spaceApi;
    

    @Override
    public SpaceShareRespVO createShare(SpaceShareSaveReqVO createReqVO) {
        // 获取当前用户权益
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)){
            throw exception(ACCOUNT_NOT);
        }
        // 校验当前用户是否拥有空间分享权限
        AccountBaseInfoDTO baseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
        if (ObjectUtil.isEmpty(baseInfoDTO)){
            throw exception(ACCOUNT_NOT);
        }
        // 判断空间是否存在
        Long spaceId = createReqVO.getSpaceId();
        if (ObjectUtil.isEmpty(spaceId)){
            throw exception(SPACE_NOT_EXISTS);
        }
        // 判断空间是否允许分享
        SpaceInfoDTO spaceInfoDTO = spaceApi.getSpaceInfo(spaceId);
        if (ObjectUtil.isEmpty(spaceInfoDTO)){
            throw exception(SPACE_NOT_EXISTS);
        }
        if (ObjectUtil.isNotEmpty(spaceInfoDTO.getPrivacyFlag())) {
            if (!spaceInfoDTO.getPrivacyFlag().equals(0)) {
                throw exception(SPACE_NOT_EXISTS);
            }
        }
        SpaceShareDO spaceShareDO = new SpaceShareDO();
        assert accountId != null;
        if (shareRedisDao.hasShareInfo(spaceId,accountId)){
            spaceShareDO = shareRedisDao.getShareInfo(spaceId,accountId);
        }else {
            spaceShareDO = shareMapper.selectById(spaceId);
            if (ObjectUtil.isNotEmpty(spaceShareDO)) {
                shareRedisDao.saveShareInfo(spaceShareDO);
            }

        }
        if (ObjectUtil.isEmpty(spaceShareDO)){
            AccountBaseInfoDTO spaceAccount = accountApi.queryAccountBaseInfoById(spaceInfoDTO.getAccountId());
//            // 插入
            SpaceShareDO share = BeanUtils.toBean(createReqVO, SpaceShareDO.class);

            String sharCode = IdUtil.simpleUUID();
            String qrcode = wechatApi.createQRCode(createReqVO.getSharePage(),createReqVO.getScene(),createReqVO.getEnv());

            share.setId(idService.nextId("spaceShare"));
            share.setShareCode(sharCode);
            share.setShareQrcode(qrcode);
            share.setSpaceAccountId(spaceAccount.getId());
            share.setCreator(baseInfoDTO.getName());
            share.setCreateId(baseInfoDTO.getId());
            share.setCreateTime(LocalDateTimeUtil.now());
            share.setCreateData(LocalDate.now());
            share.setAccountId(baseInfoDTO.getId());
            share.setAccountName(baseInfoDTO.getName());
            share.setTenantId(1L);
            share.setSpaceCover(spaceInfoDTO.getSpaceCover());
            share.setSpaceAccountId(spaceAccount.getId());
            share.setSpaceAccountName(spaceAccount.getNickname());
            share.setSpaceAccountCover(spaceAccount.getAvatar());
            share.setSpaceName(spaceInfoDTO.getSpaceCnName());
            share.setSpaceRightsId(spaceAccount.getRightsId());
            shareMapper.insert(share);
            shareRedisDao.saveShareInfo(share);
            return BeanUtils.toBean(share, SpaceShareRespVO.class);
        }else {
            return BeanUtils.toBean(spaceShareDO, SpaceShareRespVO.class);
        }
    }

    @Override
    public void updateShare(SpaceShareSaveReqVO updateReqVO) {
        // 校验存在
        validateShareExists(updateReqVO.getId());
        // 更新
        SpaceShareDO updateObj = BeanUtils.toBean(updateReqVO, SpaceShareDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdateTime(LocalDateTimeUtil.now());
        shareMapper.updateById(updateObj);
    }

    @Override
    public void deleteShare(Long id) {
        // 校验存在
        validateShareExists(id);
        // 删除
        shareMapper.deleteById(id);
    }
    
        private void validateShareExists(Long id) {
            if (shareMapper.selectById(id) == null) {
                throw exception(ErrorCodeConstants.SHARE_NOT_EXISTS);
            }
        }
    
        @Override
        public SpaceShareDO getShare(Long id) {
            // 查看分享 是否存在
            
            return shareMapper.selectById(id);
        }
    
    /**
     *
     */
    @Override
    public SpaceShareDO getShareBySpaceId(Long spaceId, Long accountId) {
        return shareMapper.getShareBySpaceId(spaceId,accountId);
    }
    
    /**
     *
     */
    @Override
    public SpaceShareDO getShareByScene(String scene) {
        
        return shareMapper.getShareByScene(scene);
    }
    
        @Override
        public PageResult<SpaceShareDO> getSharePage(SpaceSharePageReqVO pageReqVO) {
        PageResult<SpaceShareDO> pageResult = shareRedisDao.getSharePage(pageReqVO.getSpaceId(), pageReqVO.getPageNo(), pageReqVO.getPageSize());
        if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0) {
            pageResult = shareMapper.selectPage(pageReqVO);
            if (!ObjectUtil.isEmpty(pageResult) && pageResult.getTotal() > 0 && CollUtil.isNotEmpty(pageResult.getList())) {
                pageResult.getList().forEach(share -> {
                    shareRedisDao.saveShareInfo(share);
                });
            }
        }
        return pageResult;
    }

    /**
     *
     */
    @Override
    public Long countBySpaceId(Long spaceId) {
        return shareRedisDao.selectCountBySpaceId(spaceId);
    }

    /**
     *
     */
    @Override
    public boolean isShare(Long spaceId, Long accountId) {
        return shareRedisDao.hasShareInfo(spaceId, accountId);
    }

    /**
     * @param spaceId
     */
    @Override
    public void deleteBySpaceId(Long spaceId) {
        if (shareRedisDao.hasShareInfoBySpaceId(spaceId)) {
            shareRedisDao.deleteBySpaceId(spaceId);
        }

        shareMapper.delete(new LambdaQueryWrapperX<SpaceShareDO>().eq(SpaceShareDO::getSpaceId, spaceId).eq(SpaceShareDO::getDeleted, false));


    }
}
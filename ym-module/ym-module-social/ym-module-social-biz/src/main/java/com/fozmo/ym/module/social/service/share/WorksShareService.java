package com.fozmo.ym.module.social.service.share;


import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.WorksShareDO;
import jakarta.validation.Valid;

/**
 * 作品分享 Service 接口
 *
 * <AUTHOR>
 */
public interface WorksShareService {

    /**
     * 创建作品分享
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    WorksShareRespVO createWorksShare(@Valid WorksShareSaveReqVO createReqVO);

    /**
     * 更新作品分享
     *
     * @param updateReqVO 更新信息
     */
    void updateWorksShare(@Valid WorksShareSaveReqVO updateReqVO);

    /**
     * 删除作品分享
     *
     * @param id 编号
     */
    void deleteWorksShare(Long id);

    /**
     * 获得作品分享
     *
     * @param id 编号
     * @return 作品分享
     */
    WorksShareDO getWorksShare(Long id);

    WorksShareDO getShareByWorksId(Long worksId, Long accountId);


    /**
     * 获得作品分享分页
     *
     * @param pageReqVO 分页查询
     * @return 作品分享分页
     */
    PageResult<WorksShareDO> getWorksSharePage(WorksSharePageReqVO pageReqVO);

    WorksShareDO getWorksShareByScene(String scene);

    Long countByWorksId(Long worksId);

    Boolean isShare(Long worksId, Long accountId);

    void deleteByWorksId(Long worksId);
}
package com.fozmo.ym.module.social.service.share;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.auth.api.WechatApi;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.WorksShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.WorksShareDO;
import com.fozmo.ym.module.social.dal.mysql.share.WorksShareMapper;
import com.fozmo.ym.module.social.dal.redis.share.WorksShareRedisDao;
import com.fozmo.ym.module.social.enums.ErrorCodeConstants;
import com.fozmo.ym.module.space.api.WorksApi;
import com.fozmo.ym.module.space.api.dto.WorksInfoDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.WORKS_NOT_EXISTS;


/**
 * 作品分享 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WorksShareServiceImpl implements WorksShareService {

    @Resource
    private WorksShareMapper worksShareMapper;

    @Resource
    private AccountApi accountAuthApi;
    
    @Resource
    private IdService idService;
    
    @Resource
    private WechatApi wechatApi;
    
    @Resource
    private WorksShareRedisDao worksShareRedisDao;
    
   @Resource
   @Lazy
   private WorksApi worksApi;

    @Override
    public WorksShareRespVO createWorksShare(WorksShareSaveReqVO createReqVO) {
        
        // 获取当前用户权益
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)){
            accountId = -1L;
        }

        Long worksId = createReqVO.getWorksId();
        WorksInfoDTO worksInfoDTO = new WorksInfoDTO();
        if (worksApi.hasWorks(worksId)) {
            worksInfoDTO = worksApi.queryWorksInfoById(worksId);
        } else {
            throw exception(WORKS_NOT_EXISTS);
        }

        //        // 查询分享作品是否存在
        WorksShareDO worksShareDO = new WorksShareDO();
        if (worksShareRedisDao.hasShareInfo(worksId,accountId)) {
            worksShareDO = worksShareRedisDao.getShareInfo(worksId,accountId);
            return BeanUtils.toBean(worksShareDO, WorksShareRespVO.class);
        }else {
            WorksShareDO createObj = BeanUtils.toBean(createReqVO, WorksShareDO.class);
            AccountBaseInfoDTO baseInfoDTO = accountAuthApi.queryAccountBaseInfoById(accountId);
            AccountBaseInfoDTO worksAccount = accountAuthApi.queryAccountBaseInfoById(worksInfoDTO.getAccountId());
            createObj.setCreateTime(LocalDateTimeUtil.now());
            createObj.setCreateData(LocalDate.now());
            createObj.setTenantId(1L);
            createObj.setWorksCover(worksInfoDTO.getWorksCover());
            createObj.setWorksAccountId(worksAccount.getId());
            createObj.setWorksAccountName(worksAccount.getNickname());
            createObj.setWorksName(worksInfoDTO.getWorksCnName());
            createObj.setWorksRightsId(worksAccount.getRightsId());
            createObj.setWorksAccountCover(worksAccount.getAvatar());

            String sharCode = IdUtil.simpleUUID();
            String qrcode = wechatApi.createQRCode(createReqVO.getSharePage(), createReqVO.getScene(), createReqVO.getEnv());

            createObj.setId(idService.nextId("worksShare"));
            createObj.setShareCode(sharCode);
            createObj.setShareQrcode(qrcode);
            createObj.setWorksAccountId(worksAccount.getId());

            if (ObjectUtil.isEmpty(baseInfoDTO)) {
                createObj.setAccountId(accountId);
                createObj.setAccountName("游客");
                createObj.setCreator("游客");
                createObj.setCreateId(accountId);
            } else {
                createObj.setAccountId(baseInfoDTO.getId());
                createObj.setAccountName(baseInfoDTO.getName());
                createObj.setCreator(baseInfoDTO.getName());
                createObj.setCreateId(baseInfoDTO.getId());
            }
            worksShareMapper.insert(createObj);
            worksShareRedisDao.saveShareInfo(createObj);
            // 返回
            return BeanUtils.toBean(createObj, WorksShareRespVO.class);
        }


    }

    @Override
    public void updateWorksShare(WorksShareSaveReqVO updateReqVO) {
        // 校验存在
        validateWorksShareExists(updateReqVO.getId());
        // 更新
        WorksShareDO updateObj = BeanUtils.toBean(updateReqVO, WorksShareDO.class);


        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        worksShareMapper.updateById(updateObj);
    }

    @Override
    public void deleteWorksShare(Long id) {
        // 校验存在
        validateWorksShareExists(id);
        // 删除
        worksShareMapper.deleteById(id);
    }

    private void validateWorksShareExists(Long id) {
        if (worksShareMapper.selectById(id) == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.WORKS_SHARE_NOT_EXISTS);
        }
    }

    @Override
    public WorksShareDO getWorksShare(Long id) {
        return worksShareMapper.selectById(id);
    }

/**
 *
 */
@Override
public WorksShareDO getShareByWorksId(Long worksId, Long accountId) {
    return worksShareMapper.getShareByWorksId(worksId,accountId);
}

@Override
    public PageResult<WorksShareDO> getWorksSharePage(WorksSharePageReqVO pageReqVO) {
        return worksShareMapper.selectPage(pageReqVO);
    }

/**
 *
 */
@Override
public WorksShareDO getWorksShareByScene(String scene) {
    return worksShareMapper.getWorksShareByScene(scene);
}

    /**
     */
    @Override
    public Long countByWorksId(Long worksId) {
        return worksShareRedisDao.countByWorksId(worksId);
    }

    /**
     */
    @Override
    public Boolean isShare(Long worksId, Long accountId) {
        return worksShareRedisDao.hasShareInfo(worksId, accountId);
    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        if (worksShareRedisDao.hasShareList(worksId)) {
            worksShareRedisDao.deleteShareList(worksId);
        }
        worksShareMapper.delete(new LambdaQueryWrapperX<WorksShareDO>()
                .eq(WorksShareDO::getWorksId, worksId)
                .eq(WorksShareDO::getDeleted, false));
    }

}
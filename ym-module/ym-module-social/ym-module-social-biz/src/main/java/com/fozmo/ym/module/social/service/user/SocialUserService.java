package com.fozmo.ym.module.social.service.user;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserPageReqVO;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.user.SocialUserDO;
import jakarta.validation.Valid;

/**
 * 用户授权信息 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialUserService {

    /**
     * 创建用户授权信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUser(@Valid SocialUserSaveReqVO createReqVO);

    /**
     * 更新用户授权信息
     *
     * @param updateReqVO 更新信息
     */
    void updateUser(@Valid SocialUserSaveReqVO updateReqVO);

    /**
     * 删除用户授权信息
     *
     * @param id 编号
     */
    void deleteUser(Long id);

    /**
     * 获得用户授权信息
     *
     * @param id 编号
     * @return 用户授权信息
     */
    SocialUserDO getUser(Long id);

    /**
     * 获得用户授权信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户授权信息分页
     */
    PageResult<SocialUserDO> getUserPage(SocialUserPageReqVO pageReqVO);

    /**
     * 通过唯一标识 获取社交用户
     */
    SocialUserDO getUserByCode(String code);

    /**
     * 通过唯一标识 获取社交用户
     */
    SocialUserDO getUserByMobile(Long type, String mobile);


    /**
     *通过openId 获取
     */
    SocialUserDO getUserByOpenId(String openId);
}
package com.fozmo.ym.module.social.service.user;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserPageReqVO;
import com.fozmo.ym.module.social.controller.admin.user.vo.SocialUserSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.user.SocialUserDO;
import com.fozmo.ym.module.social.dal.mysql.user.SocialUserMapper;
import com.fozmo.ym.module.social.dal.redis.user.SocialUserRedisDao;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

/**
 * 用户授权信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialUserServiceImpl implements SocialUserService {

    @Resource
    private SocialUserMapper socialUserMapper;
    
    @Resource
    private IdService idService;
    
    @Resource
    private SocialUserRedisDao socialUserRedisDao;
    
    @Resource
    private AccountApi accountAuthApi;

    @Override
    public Long createUser(SocialUserSaveReqVO createReqVO) {
        // 插入
        SocialUserDO createObj = BeanUtils.toBean(createReqVO, SocialUserDO.class);
        
        String code = "";
        if (createObj.getType()==2L){
            code= createObj.getOpenId();
        }
        if (createObj.getType()==3L){
            code=createObj.getMobile();
        }
       if (StringUtils.isNotEmpty(code)){
           if (socialUserRedisDao.checkSocialUser(code)){
               createObj.setCreateId(createReqVO.getCreateId());
               createObj.setCreator("");
               createObj.setCreateDate(LocalDate.now());
               createObj.setId(idService.nextId("social_user"));
               createObj.setTenantId(1L);
               socialUserMapper.insert(createObj);
               socialUserRedisDao.addSocialUser(createObj);
               // 返回
               return createObj.getId();
           }
       }
        return null;

    }

    @Override
    public void updateUser(SocialUserSaveReqVO updateReqVO) {

        // 校验存在
        validateUserExists(updateReqVO.getId());
        // 更新
        SocialUserDO updateObj = BeanUtils.toBean(updateReqVO, SocialUserDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdaterId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateDate(LocalDate.now());

        socialUserMapper.updateById(updateObj);
    }

    @Override
    public void deleteUser(Long id) {
        // 校验存在
        validateUserExists(id);
        // 删除
        socialUserMapper.deleteById(id);
    }

    private void validateUserExists(Long id) {
        if (socialUserMapper.selectById(id) == null) {
//            throw exception(SOCIAL_USER_NOT_EXISTS);
        }
    }

    @Override
    public SocialUserDO getUser(Long id) {
        return socialUserMapper.selectById(id);
    }

    @Override
    public PageResult<SocialUserDO> getUserPage(SocialUserPageReqVO pageReqVO) {
        return socialUserMapper.selectPage(pageReqVO);
    }

    /**
     * 通过唯一标识 获取社交用户
     *
     */
    @Override
    public SocialUserDO getUserByCode(String unionid) {
        
        SocialUserDO socialUserDO = socialUserRedisDao.getInfoByCode(unionid);
        if (ObjectUtil.isNull(socialUserDO)) {
            socialUserDO = socialUserMapper.getUserByCode(unionid);
        }
        return socialUserDO;
    }

    /**
     * 通过唯一标识 获取社交用户
     *
     */
    @Override
    public SocialUserDO getUserByMobile(Long type, String mobile) {
        return socialUserMapper.getUserByMobile(type,mobile);
    }

    /**
     * 通过openId 获取
     *
     */
    @Override
    public SocialUserDO getUserByOpenId(String openId) {
        return socialUserMapper.getUserByOpenId(openId);
    }

}
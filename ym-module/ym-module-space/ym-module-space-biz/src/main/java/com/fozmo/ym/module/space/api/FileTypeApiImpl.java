package com.fozmo.ym.module.space.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.api.dto.FileTypeInfoDTO;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import com.fozmo.ym.module.space.service.file.FileTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class FileTypeApiImpl implements FileTypeApi{

    @Resource
    private FileTypeService fileTypeService;
    /**
     * @param id
     * @return
     */
    @Override
    public FileTypeInfoDTO getFileTypeInfo(Long id) {
        FileTypeDO fileTypeDO = fileTypeService.getFileType(id);
        return BeanUtils.toBean(fileTypeDO, FileTypeInfoDTO.class);
    }
}

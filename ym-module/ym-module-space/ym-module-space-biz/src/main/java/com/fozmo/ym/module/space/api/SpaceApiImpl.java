package com.fozmo.ym.module.space.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.api.dto.SpaceDTO;
import com.fozmo.ym.module.space.api.dto.SpaceInfoDTO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceVO;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.service.space.SpaceService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class SpaceApiImpl implements SpaceApi {

    @Resource
    private SpaceService spaceService;

    /**
     * 查询空间
     *
     * @param pageNo
     * @param pageSize
     * @param accountId
     */
    @Override
    public SpaceDTO getSpaceInfo(Integer pageNo, Integer pageSize, Long accountId) {

        SpacePageReqVO spacePageReqVO = new SpacePageReqVO();
        spacePageReqVO.setPageNo(pageNo);
        spacePageReqVO.setPageSize(pageSize);
        spacePageReqVO.setAccountId(accountId);
        AppSpaceVO spaceVO = spaceService.getMySpacePage(spacePageReqVO);
        return BeanUtils.toBean(spaceVO,SpaceDTO.class);
    }

/**
 * @param spaceId
 *
 * @return
 */
@Override
public SpaceInfoDTO getSpaceInfo(Long spaceId) {
    SpaceDO spaceDO = spaceService.getSpaceInfo(spaceId);
    return BeanUtils.toBean(spaceDO,SpaceInfoDTO.class);
}

/**
     * @param spaceId
     *
     * @return
     */
    @Override
    public Boolean hasSpace(Long spaceId) {
        return spaceService.hasSpace(spaceId);
    }
}

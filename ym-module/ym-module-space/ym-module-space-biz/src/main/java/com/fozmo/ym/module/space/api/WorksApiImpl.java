package com.fozmo.ym.module.space.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.api.dto.WorksDTO;
import com.fozmo.ym.module.space.api.dto.WorksInfoDTO;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
import com.fozmo.ym.module.space.controller.app.works.vo.WorksVO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.service.works.WorksService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class WorksApiImpl implements WorksApi {
    @Resource
    private WorksService worksService;
    /**
     * 查询空间
     *
     * @param pageNo
     * @param pageSize
     * @param accountId
     */
    @Override
    public WorksDTO getWorks(Integer pageNo, Integer pageSize, Long accountId,Integer pointType) {
        WorksPageReqVO reqVO = new WorksPageReqVO();
        reqVO.setPageNo(pageNo);
        reqVO.setPageSize(pageSize);
        reqVO.setAccountId(accountId);
        reqVO.setPointType(pointType);
        WorksVO worksVO = worksService.queryMyWorksPage(reqVO);
        return BeanUtils.toBean(worksVO, WorksDTO.class);
    }

    /**
     * @param worksId
     *
     * @return
     */
    @Override
    public Boolean hasWorks(Long worksId) {
        return worksService.hasWork(worksId);
    }

    /**
     * @param worksId
     *
     * @return
     */
    @Override
    public WorksInfoDTO queryWorksInfoById(Long worksId) {
        
        WorksDO worksDO = worksService.getWorksInfo(worksId);
        return BeanUtils.toBean(worksDO, WorksInfoDTO.class);
    }
}

//package com.fozmo.ym.module.space.controller.admin.editing;
//
//import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingInfoRespVO;
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.editing.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.editing.EditingDO;
//import com.fozmo.ym.module.space.service.editing.EditingService;
//
//@Tag(name = "管理后台 - 空间编辑")
//@RestController
//@RequestMapping("/space/editing")
//@Validated
//public class EditingController {
//
//    @Resource
//    private EditingService editingService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间编辑")
//    @PreAuthorize("@ss.hasPermission('space:editing:create')")
//    public CommonResult<Long> createEditing(@Valid @RequestBody EditingSaveReqVO createReqVO) {
//        return success(0L);
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间编辑")
//    @PreAuthorize("@ss.hasPermission('space:editing:update')")
//    public CommonResult<Boolean> updateEditing(@Valid @RequestBody AppEditingInfoRespVO updateReqVO) {
//        editingService.updateEditing(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间编辑")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:editing:delete')")
//    public CommonResult<Boolean> deleteEditing(@RequestParam("id") Long id) {
//        editingService.deleteEditing(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "我的空间编辑详情")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:editing:query')")
//    public CommonResult<EditingRespVO> getEditing(@RequestParam("id") Long id) {
////        EditingDO editing = editingService.getEditing(id);
////        return success(BeanUtils.toBean(editing, EditingRespVO.class));
//        return null;
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "我的空间编辑列表")
//    @PreAuthorize("@ss.hasPermission('space:editing:query')")
//    public CommonResult<PageResult<EditingRespVO>> getEditingPage(@Valid EditingPageReqVO pageReqVO) {
//        PageResult<EditingDO> pageResult = editingService.getEditingPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, EditingRespVO.class));
//    }
//
//}
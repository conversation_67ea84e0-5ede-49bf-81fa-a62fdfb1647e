package com.fozmo.ym.module.space.controller.admin.editing.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 空间编辑分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EditingPageReqVO extends PageParam {

    @Schema(description = "空间id", example = "31828")
    private Long spaceId;

    @Schema(description = "作品")
    private String spaceImage;



    @Schema(description = "文字")
    private String spaceWord;

    @Schema(description = "资源")
    private String spaceFile;

    @Schema(description = "创建人id", example = "25571")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;
    @Schema(description = "更新人", example = "32021")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;
}
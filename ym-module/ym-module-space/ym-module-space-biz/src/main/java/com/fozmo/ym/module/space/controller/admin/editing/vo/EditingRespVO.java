package com.fozmo.ym.module.space.controller.admin.editing.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间编辑 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class EditingRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15798")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "空间id", example = "31828")

    private Long spaceId;

    private String spaceName;

    @Schema(description = "作品")
    @ExcelProperty("作品")
    private String spaceWorks;

    @Schema(description = "文字")
    @ExcelProperty("文字")
    private String spaceWord;

    @Schema(description = "资源")
    @ExcelProperty("资源")
    private String spaceFile;

}
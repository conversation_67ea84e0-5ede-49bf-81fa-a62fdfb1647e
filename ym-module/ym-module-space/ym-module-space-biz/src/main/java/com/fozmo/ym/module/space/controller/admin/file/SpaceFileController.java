//package com.fozmo.ym.module.space.controller.admin.file;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//import com.fozmo.ym.module.space.controller.admin.file.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
//import com.fozmo.ym.module.space.service.file.SpaceFileService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.Valid;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.io.IOException;
//import java.util.List;
//
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "后管-空间模块- 空间资源")
//@RestController
//@RequestMapping("/space/file")
//@Validated
//public class SpaceFileController {
//
//    @Resource
//    private SpaceFileService spaceFileService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建资源")
//    @PreAuthorize("@ss.hasPermission('space:file:create')")
//    public CommonResult<List<FileSaveRespVO>> createFile(@Valid @RequestBody List<FileSaveReqVO> createReqVO) {
//        return success(spaceFileService.createFile(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新资源")
//    @PreAuthorize("@ss.hasPermission('space:file:update')")
//    public CommonResult< List<FileUploadRespVO>> updateFile(@Valid @RequestBody List<FileSaveReqVO> updateReqVO) {
//        List<FileUploadRespVO> resultList = spaceFileService.updateFile(updateReqVO);
//        return success(resultList);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除资源")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:file:delete')")
//    public CommonResult<Boolean> deleteFile(@RequestParam("id") Long id) {
//        spaceFileService.deleteFile(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得资源")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:file:query')")
//    public CommonResult<FileRespVO> getFile(@RequestParam("id") Long id) {
//        SpaceFileDO file = spaceFileService.getFile(id);
//        return success(BeanUtils.toBean(file, FileRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得资源分页")
//    @PreAuthorize("@ss.hasPermission('space:file:query')")
//    public CommonResult<PageResult<FileRespVO>> getFilePage(@Valid FilePageReqVO pageReqVO) {
//        PageResult<SpaceFileDO> pageResult = spaceFileService.getFilePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, FileRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出资源 Excel")
//    @PreAuthorize("@ss.hasPermission('space:file:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportFileExcel(@Valid FilePageReqVO pageReqVO,
//                                HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceFileDO> list = spaceFileService.getFilePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "资源.xls", "数据", FileRespVO.class,
//                BeanUtils.toBean(list, FileRespVO.class));
//    }
//
//}

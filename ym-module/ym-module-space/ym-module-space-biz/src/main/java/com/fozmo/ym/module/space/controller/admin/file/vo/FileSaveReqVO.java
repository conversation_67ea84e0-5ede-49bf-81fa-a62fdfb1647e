package com.fozmo.ym.module.space.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资源新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class FileSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9917")
    private Long fileId;

    @Schema(description = "文件名称", example = "张三")
    @NotNull(message = "文件名不为空")
    private String fileName;

    @Schema(description = "文件大小")
    private Integer fileSize;

    @Schema(description = "文件类型id", example = "4729")
    @NotNull(message = "类型id")
    private Long fileTypeId;

    @Schema(description = "说明", example = "你说的对")
    private String description;

    @Schema(description = "类型编码")
    private String fileTypeCode;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件后缀")
    private String fileSuffix;

    @Schema(description = "文件状态", example = "2")
    private Integer fileStatus;

    @Schema(description = "文件属性")
    private String fileAttribute;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "文件桶")
    private String fileBucket;

    @Schema(description = "文件url", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema
    private Boolean uploadStatus;

    private String fileMd5;

}

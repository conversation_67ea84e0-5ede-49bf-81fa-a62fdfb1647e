package com.fozmo.ym.module.space.controller.admin.file.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 资源新增/修改返回 VO")
@Accessors(chain = true)
@Data
public class FileSaveRespVO {
    @TableId
    private Long fileId;

    private String fileName;

    private String uploadFileUrl;

    private Boolean uploadStatus;

    private String fileMd5;

    @Schema(description = "说明", example = "你说的对")
    private String description;

}

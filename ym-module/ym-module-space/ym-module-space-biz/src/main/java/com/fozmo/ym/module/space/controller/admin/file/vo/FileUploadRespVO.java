package com.fozmo.ym.module.space.controller.admin.file.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资源新增/修改返回 VO")
@Accessors(chain = true)
@Data
public class FileUploadRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9917")
    @ExcelProperty("id")
    private Long fileId;

    @Schema(description = "文件名称", example = "张三")
    @ExcelProperty("文件名称")
    private String fileName;

    @Schema(description = "文件大小")
    @ExcelProperty("文件大小")
    private Integer fileSize;

    @Schema(description = "文件类型id", example = "4729")
    @ExcelProperty("文件类型id")
    private Long fileTypeId;

    @Schema(description = "说明", example = "你说的对")
    @ExcelProperty("说明")
    private String description;

    @Schema(description = "类型编码")
    @ExcelProperty("类型编码")
    private String fileTypeCode;

    @Schema(description = "文件路径")
    @ExcelProperty("文件路径")
    private String filePath;

    @Schema(description = "文件后缀")
    @ExcelProperty("文件后缀")
    private String fileSuffix;

    @Schema(description = "文件状态", example = "2")
    @ExcelProperty("文件状态")
    private Integer fileStatus;

    @Schema(description = "文件属性")
    @ExcelProperty("文件属性")
    private String fileAttribute;

    @Schema(description = "上传时间")
    @ExcelProperty("上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "文件桶")
    @ExcelProperty("文件桶")
    private String fileBucket;

    @Schema(description = "文件url", example = "https://www.iocoder.cn")
    @ExcelProperty("文件url")
    private String fileUrl;
}

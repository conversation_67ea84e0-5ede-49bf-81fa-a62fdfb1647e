//package com.fozmo.ym.module.space.controller.admin.filetype;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.filetype.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
//import com.fozmo.ym.module.space.service.file.FileTypeService;
//
//@Tag(name = "管理后台 - 空间资源类型")
//@RestController
//@RequestMapping("/space/file-type")
//@Validated
//public class FileTypeController {
//
//    @Resource
//    private FileTypeService fileTypeService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间资源类型")
//    @PreAuthorize("@ss.hasPermission('space:file-type:create')")
//    public CommonResult<Long> createFileType(@Valid @RequestBody FileTypeSaveReqVO createReqVO) {
//        return success(fileTypeService.createFileType(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间资源类型")
//    @PreAuthorize("@ss.hasPermission('space:file-type:update')")
//    public CommonResult<Boolean> updateFileType(@Valid @RequestBody FileTypeSaveReqVO updateReqVO) {
//        fileTypeService.updateFileType(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间资源类型")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:file-type:delete')")
//    public CommonResult<Boolean> deleteFileType(@RequestParam("id") Long id) {
//        fileTypeService.deleteFileType(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间资源类型")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:file-type:query')")
//    public CommonResult<FileTypeRespVO> getFileType(@RequestParam("id") Long id) {
//        FileTypeDO fileType = fileTypeService.getFileType(id);
//        return success(BeanUtils.toBean(fileType, FileTypeRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间资源类型分页")
//    @PreAuthorize("@ss.hasPermission('space:file-type:query')")
//    public CommonResult<PageResult<FileTypeRespVO>> getFileTypePage(@Valid FileTypePageReqVO pageReqVO) {
//        PageResult<FileTypeDO> pageResult = fileTypeService.getFileTypePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, FileTypeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间资源类型 Excel")
//    @PreAuthorize("@ss.hasPermission('space:file-type:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportFileTypeExcel(@Valid FileTypePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<FileTypeDO> list = fileTypeService.getFileTypePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间资源类型.xls", "数据", FileTypeRespVO.class,
//                        BeanUtils.toBean(list, FileTypeRespVO.class));
//    }
//
//}
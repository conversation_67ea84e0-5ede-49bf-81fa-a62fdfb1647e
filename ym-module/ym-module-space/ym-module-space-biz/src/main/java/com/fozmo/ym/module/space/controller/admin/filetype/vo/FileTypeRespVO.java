package com.fozmo.ym.module.space.controller.admin.filetype.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间资源类型 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class FileTypeRespVO {

    @Schema(description = "主键", example = "30743")
    @ExcelProperty("主键")
    private Long typeId;

    @Schema(description = "资源类别中文名称", example = "赵六")
    @ExcelProperty("资源类别中文名称")
    private String typeCnName;

    @Schema(description = "资源类别英文名称", example = "赵六")
    @ExcelProperty("资源类别英文名称")
    private String typeEnName;

    @Schema(description = "空间编码")
    @ExcelProperty("空间编码")
    private String typeCode;

    @Schema(description = "中文说明", example = "你说的对")
    @ExcelProperty("中文说明")
    private String cnDescription;

    @Schema(description = "英文说明", example = "你猜")
    @ExcelProperty("英文说明")
    private String enDescription;

    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "2")
    @ExcelProperty("状态 0 初始化 1启用 2 停用 3 删除")
    private Integer typeStatus;

    @Schema(description = "创建标识 0系统初始化 1 用户自定义 2 其他")
    @ExcelProperty("创建标识 0系统初始化 1 用户自定义 2 其他")
    private Integer createDefault;

    @Schema(description = "创建人id", example = "735")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "30945")
    @ExcelProperty("更新人")
    private Long updateId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
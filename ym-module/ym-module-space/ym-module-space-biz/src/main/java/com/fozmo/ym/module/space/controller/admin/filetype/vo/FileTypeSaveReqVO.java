package com.fozmo.ym.module.space.controller.admin.filetype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 空间资源类型新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class FileTypeSaveReqVO {

    @Schema(description = "主键", example = "30743")
    private Long typeId;

    @Schema(description = "资源类别中文名称", example = "赵六")
    private String typeCnName;

    @Schema(description = "资源类别英文名称", example = "赵六")
    private String typeEnName;

    @Schema(description = "空间编码")
    private String typeCode;

    @Schema(description = "中文说明", example = "你说的对")
    private String cnDescription;

    @Schema(description = "英文说明", example = "你猜")
    private String enDescription;

    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "2")
    private Integer typeStatus;

    @Schema(description = "创建标识 0系统初始化 1 用户自定义 2 其他")
    private Integer createDefault;

    @Schema(description = "创建人id", example = "735")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "30945")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
//package com.fozmo.ym.module.space.controller.admin.space;
//
//import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
//import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceRespVO;
//import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceSaveReqVO;
//import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
//import com.fozmo.ym.module.space.service.space.SpaceService;
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//
//@Tag(name = "后管-空间模块- 空间")
//@RestController
//@RequestMapping("/space/space")
//@Validated
//public class SpaceController {
//
//    @Resource
//    private SpaceService spaceService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间")
//    @PreAuthorize("@ss.hasPermission('space:space:create')")
//    public CommonResult<Long> createSpace(@Valid @RequestBody SpaceSaveReqVO createReqVO) {
//        return success(spaceService.createSpace(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间")
//    @PreAuthorize("@ss.hasPermission('space:space:update')")
//    public CommonResult<Boolean> updateSpace(@Valid @RequestBody SpaceSaveReqVO updateReqVO) {
//        spaceService.updateSpace(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:space:delete')")
//    public CommonResult<Boolean> deleteSpace(@RequestParam("id") Long id) {
//        spaceService.deleteSpace(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:space:query')")
//    public CommonResult<SpaceRespVO> getSpace(@RequestParam("id") Long id) {
//        SpaceDO space = spaceService.getSpace(id);
//        return success(BeanUtils.toBean(space, SpaceRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间分页")
//    @PreAuthorize("@ss.hasPermission('space:space:query')")
//    public CommonResult<PageResult<SpaceRespVO>> getSpacePage(@Valid SpacePageReqVO pageReqVO) {
//       return success(spaceService.getSpacePage(pageReqVO));
//    }
//
//
//}
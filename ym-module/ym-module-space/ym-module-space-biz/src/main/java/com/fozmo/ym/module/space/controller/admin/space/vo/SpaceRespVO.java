package com.fozmo.ym.module.space.controller.admin.space.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class SpaceRespVO {

    @Schema(description = "id", example = "24030")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "空间模板id", example = "29611")
    @ExcelProperty("空间模板id")
    private Long spaceTemplateId;

    @Schema(description = "空间中文名称", example = "芋艿")
    @ExcelProperty("空间中文名称")
    private String spaceCnName;

    @Schema(description = "空间英文名称", example = "张三")
    @ExcelProperty("空间英文名称")
    private String spaceEnName;

    @Schema(description = "空间码值")
    @ExcelProperty("空间码值")
    private String spaceCode;

    @Schema(description = "空间类型", example = "24414")
    @ExcelProperty("空间类型")
    private Long spaceTypeId;

    @Schema(description = "2d挂载点")
    @ExcelProperty("2d挂载点")
    private Integer spaceNum2;

    @Schema(description = "3d挂载点")
    @ExcelProperty("3d挂载点")
    private Integer spaceNum3;

    @Schema(description = "账户id", example = "13935")
    @ExcelProperty("账户id")
    private Long accountId;

    @Schema(description = "账户编码")
    @ExcelProperty("账户编码")
    private String accountCode;
    @Schema(description = "中文说明", example = "你说的对")
    @ExcelProperty("中文说明")
    private String cnDescription;
    @Schema(description = "英文说明", example = "你说的对")
    @ExcelProperty("英文说明")
    private String enDescription;
    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "1")
    @ExcelProperty("状态 0 初始化 1启用 2 停用 3 删除")
    private Integer spaceStatus;
    @Schema(description = "空间封面")
    @ExcelProperty("空间封面")
    private String spaceCover;
    @Schema(description = "一键复制 0 不允许 1运行")
    @ExcelProperty("一键复制 0 不允许 1运行")
    private Integer copyFlag;
    @Schema(description = "0 全部可见 ")
    @ExcelProperty("0 全部可见 ")
    private Integer privacyFlag;
    @Schema(description = "评论数")
    private Long commentCount;
    @Schema(description = "评论状态")
    private Boolean commentStatus;
    @Schema(description = "点赞数")
    private Long likeCount;
    @Schema(description = "点赞状态")
    private Boolean likeStatus;
    @Schema(description = "分享数")
    private Long shareCount;
    @Schema(description = "分享状态")
    private Boolean shareStatus;
    @Schema(description = "收藏数")
    private Long favoriteCount;
    @Schema(description = "收藏状态")
    private Boolean favoriteStatus;
    @Schema(description = "账户名称")
    private String accountName;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "用户头像")
    private String avatar;
    @Schema(description = "权益ID")
    private Long rightsId;
    @Schema(description = "权益名称")
    private String rightsName;
    @Schema(description = "空间下作品数量")
    private Long worksCount;
    @Schema(description = "空间下资源数量")
    private Long filesCount;
    
    private String sponsor;
    
    private LocalDateTime exhibitionStartTime;
    
    private LocalDateTime exhibitionEndTime;

    private Integer exhibitionTimeStatus;

}
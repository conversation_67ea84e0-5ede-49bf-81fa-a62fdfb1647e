//package com.fozmo.ym.module.space.controller.admin.spacetemplate;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
//import com.fozmo.ym.module.space.service.spacetemplate.SpaceTemplateService;
//
//@Tag(name = "后管-空间模块 - 空间模板")
//@RestController
//@RequestMapping("/space/template")
//@Validated
//public class SpaceTemplateController {
//
//    @Resource
//    private SpaceTemplateService templateService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间模板")
////    @PreAuthorize("@ss.hasPermission('space:template:create')")
//    public CommonResult<Long> createTemplate(@Valid @RequestBody SpaceTemplateSaveReqVO createReqVO) {
//        return success(templateService.createTemplate(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间模板")
////    @PreAuthorize("@ss.hasPermission('space:template:update')")
//    public CommonResult<Boolean> updateTemplate(@Valid @RequestBody SpaceTemplateSaveReqVO updateReqVO) {
//        templateService.updateTemplate(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间模板")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:template:delete')")
//    public CommonResult<Boolean> deleteTemplate(@RequestParam("id") Long id) {
//        templateService.deleteTemplate(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间模板")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:template:query')")
//    public CommonResult<SpaceTemplateInfoRespVO> getTemplate(@RequestParam("id") Long id) {
//        SpaceTemplateInfoRespVO template = templateService.getTemplate(id);
//        return success(BeanUtils.toBean(template, SpaceTemplateInfoRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间模板分页")
//    @PreAuthorize("@ss.hasPermission('space:template:query')")
//    public CommonResult<PageResult<SpaceTemplateRespVO>> getTemplatePage(@Valid SpaceTemplatePageReqVO pageReqVO) {
//        PageResult<SpaceTemplateDO> pageResult = templateService.getTemplatePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceTemplateRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间模板 Excel")
//    @PreAuthorize("@ss.hasPermission('space:template:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportTemplateExcel(@Valid SpaceTemplatePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceTemplateDO> list = templateService.getTemplatePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间模板.xls", "数据", SpaceTemplateRespVO.class,
//                        BeanUtils.toBean(list, SpaceTemplateRespVO.class));
//    }
//
//}
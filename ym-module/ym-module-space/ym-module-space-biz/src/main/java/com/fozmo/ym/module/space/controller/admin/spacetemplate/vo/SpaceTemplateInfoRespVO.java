package com.fozmo.ym.module.space.controller.admin.spacetemplate.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class SpaceTemplateInfoRespVO {

    @Schema(description = "模板id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17449")
    @ExcelProperty("模板id")
    private Long templateId;
    
    @Schema(description = "模板英文名称", example = "芋艿")
    @ExcelProperty("模板英文名称")
    private String templateEnName;
    
    @Schema(description = "模板中文名称", example = "赵六")
    @ExcelProperty("模板中文名称")
    private String templateCnName;
    
    @Schema(description = "模板码值")
    @ExcelProperty("模板码值")
    private String templateCode;
    
    @Schema(description = "模板类型id", example = "5241")
    @ExcelProperty("模板类型id")
    private Long templateTypeId;
    
    @Schema(description = "模板类型码值")
    @ExcelProperty("模板类型码值")
    private String templateTypeCode;
    
    @Schema(description = "2d挂载点数")
    @ExcelProperty("2d挂载点数")
    private Integer pointNum2;
    
    @Schema(description = "3d挂载点数")
    @ExcelProperty("3d挂载点数")
    private Integer pointNum3;
    
    @Schema(description = "中文说明", example = "你猜")
    @ExcelProperty("中文说明")
    private String cnDescription;
    
    @Schema(description = "英文说明", example = "随便")
    @ExcelProperty("英文说明")
    private String enDescription;
    
    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "1")
    @ExcelProperty("状态 0 初始化 1启用 2 停用 3 删除")
    private Integer templateStatus;
    
    @Schema(description = "空间封面")
    @ExcelProperty("空间封面")
    private String templateCover;
    
    @Schema(description = "模板地址")
    @ExcelProperty("模板地址")
    private String templateRes;
    
    @Schema(description = "文本内容")
    @ExcelProperty("文本内容")
    private String templateConfig;
    
    @Schema(description = "权益id", example = "10646")
    @ExcelProperty("权益id")
    private Long rightsId;
    
    @Schema(description = "0系统初始化 1 用户自定义 2 其他")
    @ExcelProperty("0系统初始化 1 用户自定义 2 其他")
    private Integer createDefault;
    
    @Schema(description = "template_tags")
    @ExcelProperty("template_tags")
    private String templateTags;
    
    private List<SpaceTemplateTypeDO> typeList;

    private List<SpaceTemplateTagDO> tagList;
}

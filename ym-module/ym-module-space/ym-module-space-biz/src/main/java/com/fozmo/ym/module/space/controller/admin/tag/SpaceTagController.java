//package com.fozmo.ym.module.space.controller.admin.tag;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.tag.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.tag.SpaceTagDO;
//import com.fozmo.ym.module.space.service.tag.SpaceTagService;
//
//@Tag(name = "后管-空间模块 - 空间标签")
//@RestController
//@RequestMapping("/space/spaceTag")
//@Validated
//public class SpaceTagController {
//
//    @Resource
//    private SpaceTagService spaceTagService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间标签")
//    @PreAuthorize("@ss.hasPermission('space:spaceTag:create')")
//    public CommonResult<Long> createSpaceTag(@Valid @RequestBody SpaceTagSaveReqVO createReqVO) {
//        return success(spaceTagService.createSpaceTag(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间标签")
//    @PreAuthorize("@ss.hasPermission('space:spaceTag:update')")
//    public CommonResult<Boolean> updateSpaceTag(@Valid @RequestBody SpaceTagSaveReqVO updateReqVO) {
//        spaceTagService.updateSpaceTag(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间标签")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:spaceTag:delete')")
//    public CommonResult<Boolean> deleteSpaceTag(@RequestParam("id") Long id) {
//        spaceTagService.deleteSpaceTag(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间标签")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:spaceTag:query')")
//    public CommonResult<SpaceTagRespVO> getSpaceTag(@RequestParam("id") Long id) {
//        SpaceTagDO spaceTag = spaceTagService.getSpaceTag(id);
//        return success(BeanUtils.toBean(spaceTag, SpaceTagRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间标签分页")
//    @PreAuthorize("@ss.hasPermission('space:spaceTag:query')")
//    public CommonResult<PageResult<SpaceTagRespVO>> getSpaceTagPage(@Valid SpaceTagPageReqVO pageReqVO) {
//        PageResult<SpaceTagDO> pageResult = spaceTagService.getSpaceTagPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceTagRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间标签 Excel")
//    @PreAuthorize("@ss.hasPermission('space:spaceTag:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportSpaceTagExcel(@Valid SpaceTagPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceTagDO> list = spaceTagService.getSpaceTagPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间标签.xls", "数据", SpaceTagRespVO.class,
//                        BeanUtils.toBean(list, SpaceTagRespVO.class));
//    }
//
//}
package com.fozmo.ym.module.space.controller.admin.tag.vo;

import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fozmo.ym.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 空间标签分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpaceTagPageReqVO extends PageParam {

    @Schema(description = "空间标签名称", example = "王五")
    private String name;

    @Schema(description = "创建人id", example = "10065")
    private Long createId;

    @Schema(description = "创建用户名称", example = "王五")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "4715")
    private Long updateId;

    @Schema(description = "更新人", example = "张三")
    private String updater;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
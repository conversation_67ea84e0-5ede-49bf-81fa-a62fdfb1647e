package com.fozmo.ym.module.space.controller.admin.tag.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 空间标签 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class SpaceTagRespVO {

    @Schema(description = "id", example = "10839")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "空间标签名称", example = "王五")
    @ExcelProperty("空间标签名称")
    private String name;

    @Schema(description = "创建人id", example = "10065")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建用户名称", example = "王五")
    @ExcelProperty("创建用户名称")
    private String creator;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "4715")
    @ExcelProperty("更新人")
    private Long updateId;

    @Schema(description = "更新人", example = "张三")
    @ExcelProperty("更新人")
    private String updater;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
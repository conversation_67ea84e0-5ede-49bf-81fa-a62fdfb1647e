//package com.fozmo.ym.module.space.controller.admin.templateaccount;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.templateaccount.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.templateaccount.TemplateAccountDO;
//import com.fozmo.ym.module.space.service.templateaccount.TemplateAccountService;
//
//@Tag(name = "管理后台 - 指定账户模板")
//@RestController
//@RequestMapping("/space/template-account")
//@Validated
//public class TemplateAccountController {
//
//    @Resource
//    private TemplateAccountService templateAccountService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建指定账户模板")
//    @PreAuthorize("@ss.hasPermission('space:template-account:create')")
//    public CommonResult<Long> createTemplateAccount(@Valid @RequestBody TemplateAccountSaveReqVO createReqVO) {
//        return success(templateAccountService.createTemplateAccount(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新指定账户模板")
//    @PreAuthorize("@ss.hasPermission('space:template-account:update')")
//    public CommonResult<Boolean> updateTemplateAccount(@Valid @RequestBody TemplateAccountSaveReqVO updateReqVO) {
//        templateAccountService.updateTemplateAccount(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除指定账户模板")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:template-account:delete')")
//    public CommonResult<Boolean> deleteTemplateAccount(@RequestParam("id") Long id) {
//        templateAccountService.deleteTemplateAccount(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得指定账户模板")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:template-account:query')")
//    public CommonResult<TemplateAccountRespVO> getTemplateAccount(@RequestParam("id") Long id) {
//        TemplateAccountDO templateAccount = templateAccountService.getTemplateAccount(id);
//        return success(BeanUtils.toBean(templateAccount, TemplateAccountRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得指定账户模板分页")
//    @PreAuthorize("@ss.hasPermission('space:template-account:query')")
//    public CommonResult<PageResult<TemplateAccountRespVO>> getTemplateAccountPage(@Valid TemplateAccountPageReqVO pageReqVO) {
//        PageResult<TemplateAccountDO> pageResult = templateAccountService.getTemplateAccountPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, TemplateAccountRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出指定账户模板 Excel")
//    @PreAuthorize("@ss.hasPermission('space:template-account:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportTemplateAccountExcel(@Valid TemplateAccountPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<TemplateAccountDO> list = templateAccountService.getTemplateAccountPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "指定账户模板.xls", "数据", TemplateAccountRespVO.class,
//                        BeanUtils.toBean(list, TemplateAccountRespVO.class));
//    }
//
//}
package com.fozmo.ym.module.space.controller.admin.templateaccount.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 指定账户模板 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class TemplateAccountRespVO {

    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24186")
    @ExcelProperty("账户id")
    private Long accountId;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11152")
    @ExcelProperty("id")
    private Long id;


    @Schema(description = "模板", example = "32379")
    private Long templateId;
    @Schema(description = "创建人id", example = "9216")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "25718")
    @ExcelProperty("更新人")
    private Long updateId;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
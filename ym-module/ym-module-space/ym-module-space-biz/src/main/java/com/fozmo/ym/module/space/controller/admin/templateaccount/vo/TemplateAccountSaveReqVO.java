package com.fozmo.ym.module.space.controller.admin.templateaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 指定账户模板新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class TemplateAccountSaveReqVO {

    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24186")
    @NotNull(message = "账户id不能为空")
    private Long accountId;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11152")
    private Long id;


    @Schema(description = "模板", example = "32379")
    private Long templateId;

    @Schema(description = "创建人id", example = "9216")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "25718")
    private Long updateId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
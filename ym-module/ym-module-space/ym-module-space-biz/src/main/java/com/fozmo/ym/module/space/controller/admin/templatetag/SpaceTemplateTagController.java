//package com.fozmo.ym.module.space.controller.admin.templatetag;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.templatetag.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
//import com.fozmo.ym.module.space.service.templatetag.SpaceTemplateTagService;
//
//@Tag(name = "后管-空间模块- 空间模板标签")
//@RestController
//@RequestMapping("/space/template-tag")
//@Validated
//public class SpaceTemplateTagController {
//
//    @Resource
//    private SpaceTemplateTagService templateTagService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间模板标签")
//    @PreAuthorize("@ss.hasPermission('space:template-tag:create')")
//    public CommonResult<Long> createTemplateTag(@Valid @RequestBody SpaceTemplateTagSaveReqVO createReqVO) {
//        return success(templateTagService.createTemplateTag(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间模板标签")
//    @PreAuthorize("@ss.hasPermission('space:template-tag:update')")
//    public CommonResult<Boolean> updateTemplateTag(@Valid @RequestBody SpaceTemplateTagSaveReqVO updateReqVO) {
//        templateTagService.updateTemplateTag(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间模板标签")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:template-tag:delete')")
//    public CommonResult<Boolean> deleteTemplateTag(@RequestParam("id") Long id) {
//        templateTagService.deleteTemplateTag(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间模板标签")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:template-tag:query')")
//    public CommonResult<SpaceTemplateTagRespVO> getTemplateTag(@RequestParam("id") Long id) {
//        SpaceTemplateTagDO templateTag = templateTagService.getTemplateTag(id);
//        return success(BeanUtils.toBean(templateTag, SpaceTemplateTagRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间模板标签分页")
//    @PreAuthorize("@ss.hasPermission('space:template-tag:query')")
//    public CommonResult<PageResult<SpaceTemplateTagRespVO>> getTemplateTagPage(@Valid SpaceTemplateTagPageReqVO pageReqVO) {
//        PageResult<SpaceTemplateTagDO> pageResult = templateTagService.getTemplateTagPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceTemplateTagRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间模板标签 Excel")
//    @PreAuthorize("@ss.hasPermission('space:template-tag:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportTemplateTagExcel(@Valid SpaceTemplateTagPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceTemplateTagDO> list = templateTagService.getTemplateTagPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间模板标签.xls", "数据", SpaceTemplateTagRespVO.class,
//                        BeanUtils.toBean(list, SpaceTemplateTagRespVO.class));
//    }
//
//}
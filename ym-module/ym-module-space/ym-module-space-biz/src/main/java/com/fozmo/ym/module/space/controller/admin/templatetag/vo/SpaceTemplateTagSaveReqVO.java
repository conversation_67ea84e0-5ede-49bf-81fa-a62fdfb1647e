package com.fozmo.ym.module.space.controller.admin.templatetag.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 空间模板标签新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class SpaceTemplateTagSaveReqVO {

    @Schema(description = "id", example = "20845")
    private Long id;

    @Schema(description = "名称", example = "张三")
    private String name;

    @Schema(description = "创建人id", example = "2321")
    private Long createId;

    @Schema(description = "创建用户名称", example = "芋艿")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "22998")
    private Long updateId;

    @Schema(description = "更新人", example = "张三")
    private String updater;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
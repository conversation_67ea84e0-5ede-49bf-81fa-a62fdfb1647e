//package com.fozmo.ym.module.space.controller.admin.templatetype;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.templatetype.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
//import com.fozmo.ym.module.space.service.templatetype.SpaceTemplateTypeService;
//
//@Tag(name = "后管-空间模块- 空间模板类型")
//@RestController
//@RequestMapping("/space/template-type")
//@Validated
//public class SpaceTemplateTypeController {
//
//    @Resource
//    private SpaceTemplateTypeService templateTypeService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间模板类型")
////    @PreAuthorize("@ss.hasPermission('space:template-type:create')")
//    public CommonResult<Long> createTemplateType(@Valid @RequestBody SpaceTemplateTypeSaveReqVO createReqVO) {
//        return success(templateTypeService.createTemplateType(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间模板类型")
//    @PreAuthorize("@ss.hasPermission('space:template-type:update')")
//    public CommonResult<Boolean> updateTemplateType(@Valid @RequestBody SpaceTemplateTypeSaveReqVO updateReqVO) {
//        templateTypeService.updateTemplateType(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间模板类型")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:template-type:delete')")
//    public CommonResult<Boolean> deleteTemplateType(@RequestParam("id") Long id) {
//        templateTypeService.deleteTemplateType(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间模板类型")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:template-type:query')")
//    public CommonResult<SpaceTemplateTypeRespVO> getTemplateType(@RequestParam("id") Long id) {
//        SpaceTemplateTypeDO templateType = templateTypeService.getTemplateType(id);
//        return success(BeanUtils.toBean(templateType, SpaceTemplateTypeRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间模板类型分页")
//    @PreAuthorize("@ss.hasPermission('space:template-type:query')")
//    public CommonResult<PageResult<SpaceTemplateTypeRespVO>> getTemplateTypePage(@Valid SpaceTemplateTypePageReqVO pageReqVO) {
//        PageResult<SpaceTemplateTypeDO> pageResult = templateTypeService.getTemplateTypePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceTemplateTypeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间模板类型 Excel")
//    @PreAuthorize("@ss.hasPermission('space:template-type:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportTemplateTypeExcel(@Valid SpaceTemplateTypePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceTemplateTypeDO> list = templateTypeService.getTemplateTypePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间模板类型.xls", "数据", SpaceTemplateTypeRespVO.class,
//                        BeanUtils.toBean(list, SpaceTemplateTypeRespVO.class));
//    }
//
//}
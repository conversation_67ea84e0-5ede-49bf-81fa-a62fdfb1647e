package com.fozmo.ym.module.space.controller.admin.templatetype.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间模板类型 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class SpaceTemplateTypeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19813")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "空间模板名称", example = "王五")
    @ExcelProperty("空间模板名称")
    private String name;

    @Schema(description = "创建人id", example = "4211")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建用户名称", example = "王五")
    @ExcelProperty("创建用户名称")
    private String creator;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "9811")
    @ExcelProperty("更新人")
    private Long updateId;

    @Schema(description = "更新人", example = "王五")
    @ExcelProperty("更新人")
    private String updater;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
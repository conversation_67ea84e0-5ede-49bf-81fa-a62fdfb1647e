package com.fozmo.ym.module.space.controller.admin.templatetype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "管理后台 - 空间模板类型新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class SpaceTemplateTypeSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19813")
    private Long id;

    @Schema(description = "空间模板名称", example = "王五")
    private String name;

    @Schema(description = "创建人id", example = "4211")
    private Long createId;

    @Schema(description = "创建用户名称", example = "王五")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "9811")
    private Long updateId;

    @Schema(description = "更新人", example = "王五")
    private String updater;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
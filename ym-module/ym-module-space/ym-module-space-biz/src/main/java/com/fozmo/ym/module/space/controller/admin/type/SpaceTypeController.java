//package com.fozmo.ym.module.space.controller.admin.type;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.space.controller.admin.type.vo.*;
//import com.fozmo.ym.module.space.dal.dataobject.type.SpaceTypeDO;
//import com.fozmo.ym.module.space.service.type.SpaceTypeService;
//
//@Tag(name = "后管-空间模块- 空间类型")
//@RestController
//@RequestMapping("/space/type")
//@Validated
//public class SpaceTypeController {
//
//    @Resource
//    private SpaceTypeService typeService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间类型")
////    @PreAuthorize("@ss.hasPermission('space:type:create')")
//    public CommonResult<Long> createType(@Valid @RequestBody SpaceTypeSaveReqVO createReqVO) {
//        return success(typeService.createType(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间类型")
//    @PreAuthorize("@ss.hasPermission('space:type:update')")
//    public CommonResult<Boolean> updateType(@Valid @RequestBody SpaceTypeSaveReqVO updateReqVO) {
//        typeService.updateType(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间类型")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:type:delete')")
//    public CommonResult<Boolean> deleteType(@RequestParam("id") Long id) {
//        typeService.deleteType(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间类型")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:type:query')")
//    public CommonResult<SpaceTypeRespVO> getType(@RequestParam("id") Long id) {
//        SpaceTypeDO type = typeService.getType(id);
//        return success(BeanUtils.toBean(type, SpaceTypeRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间类型分页")
//    @PreAuthorize("@ss.hasPermission('space:type:query')")
//    public CommonResult<PageResult<SpaceTypeRespVO>> getTypePage(@Valid SpaceTypePageReqVO pageReqVO) {
//        PageResult<SpaceTypeDO> pageResult = typeService.getTypePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceTypeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间类型 Excel")
//    @PreAuthorize("@ss.hasPermission('space:type:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportTypeExcel(@Valid SpaceTypePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceTypeDO> list = typeService.getTypePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间类型.xls", "数据", SpaceTypeRespVO.class,
//                        BeanUtils.toBean(list, SpaceTypeRespVO.class));
//    }
//
//}
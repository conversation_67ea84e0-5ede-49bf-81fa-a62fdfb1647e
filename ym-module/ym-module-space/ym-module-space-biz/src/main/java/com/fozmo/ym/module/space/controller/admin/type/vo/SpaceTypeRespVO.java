package com.fozmo.ym.module.space.controller.admin.type.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间类型 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class SpaceTypeRespVO {

    @Schema(description = "id", example = "9172")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "类型名称", example = "芋艿")
    @ExcelProperty("类型名称")
    private String name;

    @Schema(description = "创建人id", example = "15032")
    @ExcelProperty("创建人id")
    private Long createId;

    @Schema(description = "创建用户名称", example = "赵六")
    @ExcelProperty("创建用户名称")
    private String creator;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "10145")
    @ExcelProperty("更新人")
    private Long updateId;

    @Schema(description = "更新人", example = "赵六")
    @ExcelProperty("更新人")
    private String updater;

    @Schema(description = "更新日期")
    @ExcelProperty("更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    @ExcelProperty("租户Code")
    private String tenantCode;

}
//package com.fozmo.ym.module.space.controller.admin.works;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
//import com.fozmo.ym.module.space.controller.admin.works.vo.WorksRespVO;
//import com.fozmo.ym.module.space.controller.admin.works.vo.WorksSaveReqVO;
//import com.fozmo.ym.module.space.controller.app.works.vo.AppWorksPageRespVO;
//import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
//import com.fozmo.ym.module.space.service.works.WorksService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.Valid;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.io.IOException;
//import java.util.List;
//
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "后管-空间模块-作品")
//@RestController
//@RequestMapping("/space/works")
//@Validated
//public class WorksController {
//
//    @Resource
//    private WorksService worksService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建作品")
//    @PreAuthorize("@ss.hasPermission('space:works:create')")
//    public CommonResult<Long> createWorks(@Valid @RequestBody WorksSaveReqVO createReqVO) {
//        return success(worksService.createWorks(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新作品")
//    @PreAuthorize("@ss.hasPermission('space:works:update')")
//    public CommonResult<Boolean> updateWorks(@Valid @RequestBody WorksSaveReqVO updateReqVO) {
//        worksService.updateWorks(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除作品")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:works:delete')")
//    public CommonResult<Boolean> deleteWorks(@RequestParam("id") Long id) {
//        worksService.deleteWorks(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得作品")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:works:query')")
//    public CommonResult<WorksRespVO> getWorks(@RequestParam("id") Long id) {
//        AppWorksPageRespVO works = worksService.getWorks(id);
//        return success(BeanUtils.toBean(works, WorksRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得作品分页")
//    @PreAuthorize("@ss.hasPermission('space:works:query')")
//    public CommonResult<PageResult<WorksRespVO>> getWorksPage(@Valid WorksPageReqVO pageReqVO) {
//        PageResult<WorksRespVO> pageResult = worksService.getWorksPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, WorksRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出作品 Excel")
//    @PreAuthorize("@ss.hasPermission('space:works:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportWorksExcel(@Valid WorksPageReqVO pageReqVO,
//                                 HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<WorksRespVO> list = worksService.getWorksPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "作品.xls", "数据", WorksRespVO.class, list);
//    }
//
//}
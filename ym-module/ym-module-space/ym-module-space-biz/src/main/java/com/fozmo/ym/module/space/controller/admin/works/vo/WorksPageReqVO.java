package com.fozmo.ym.module.space.controller.admin.works.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 空间作品分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorksPageReqVO extends PageParam {

    @Schema(description = "中文名称", example = "30195")
    private String worksCnName;
    
    @Schema(description = "英文名称", example = "30195")
    private String worksEnName;
    
    @Schema(description = "作品封面", example = "30195")
    private String worksCover;

    @Schema(description = "我的空间id", example = "27233")
    private Long spaceId;

    @Schema(description = "资源id", example = "30195")
    private Long fileId;

    @Schema(description = "作品状态： 0初始化 1布置  9占用状态", example = "2")
    private Integer worksStatus;

    @Schema(description = "高精度路径", example = "https://www.iocoder.cn")
    private String hignUrl;

    @Schema(description = "标精路径", example = "https://www.iocoder.cn")
    private String lowUrl;

    @Schema(description = "创建人", example = "31674")
    private Long accountId;

    @Schema(description = "挂载点")
    private Integer point;

    @Schema(description = "挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字", example = "2")
    private Integer pointType;

    @Schema(description = "文本内容")
    private String textContent;

    @Schema(description = "创建人id", example = "10939")
    private Long createId;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新人", example = "15956")
    private Long updaterId;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

    private List<Long> worksIds;

}

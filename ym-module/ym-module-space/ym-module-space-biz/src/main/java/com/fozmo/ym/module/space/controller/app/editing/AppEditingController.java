package com.fozmo.ym.module.space.controller.app.editing;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.editing.vo.EditingSaveReqVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingInfoRespVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingRespVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingSaveReqVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingUpdateReqVO;
import com.fozmo.ym.module.space.service.editing.EditingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP -空间模块- 空间内编辑")
@RestController
@RequestMapping("/space/edit")
@Validated
public class AppEditingController {

    @Resource
    private EditingService editingService;

    @PostMapping("/create")
    @Operation(summary = "新增一条草稿数据", description = "注意这里的新增只能是非挂点数据和资源，挂点点位已经初始化替换即可")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> createEditing(@Valid @RequestBody AppEditingSaveReqVO createReqVO) {
        EditingSaveReqVO reqVO = BeanUtils.toBean(createReqVO, EditingSaveReqVO.class);
        return success(editingService.createEditing(reqVO));
    }
    @PutMapping("/update")
    @Operation(summary = "编辑单条草稿数据", description = "单条编辑时使用,只限于编辑已存在数据")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> updateEditing(@Valid @RequestBody AppEditingUpdateReqVO updateReqVO) {
        editingService.updateEditing(updateReqVO);
        return success(true);
    }
    @DeleteMapping("/delete")
    @Operation(summary = "删除单条草稿信息", description = "删除单条草稿信息不可删除挂点数据，挂点数据只能将editId 置为point值,editType 置为0")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> deleteEditing(@RequestParam("editId") Long editId, @RequestParam("spaceId") Long spaceId) {
        editingService.deleteEditing(editId, spaceId);
        return success(true);
    }
    @GetMapping("/info")
    @Operation(summary = "草稿箱详情", description = "返回当前详情")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppEditingInfoRespVO> getEditing(@RequestParam("editId") Long editId, @RequestParam("spaceId") Long spaceId) {
        return success(editingService.getEditingInfo(editId, spaceId));
    }
    @GetMapping("/list")
    @Operation(summary = "草稿列表", description = "pc端空间编辑列表接口用于进入编辑数据前渲染使用，当空间新建时只有初始化挂点")
    @Parameter(name = "spaceId", description = "空间ID", required = true, example = "1024")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppEditingRespVO> getList(@RequestParam("spaceId") Long spaceId) {
        return success(editingService.getList(spaceId));
    }
    @PostMapping("/push")
    @Operation(summary = "编辑发布接口", description = "自定义布置、pc端编辑发布")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> pushEditing(@RequestParam("spaceId") Long spaceId) {
        editingService.pushEditing(spaceId);
        return success(true);
    }
}
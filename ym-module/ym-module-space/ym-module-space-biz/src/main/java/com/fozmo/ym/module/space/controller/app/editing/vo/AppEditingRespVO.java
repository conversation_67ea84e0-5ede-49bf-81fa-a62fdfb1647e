package com.fozmo.ym.module.space.controller.app.editing.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "管理后台 - 空间编辑 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppEditingRespVO {


    @Schema(description = "spaceId", requiredMode = Schema.RequiredMode.REQUIRED, example = "15798")
    private Long spaceId;
    @Schema(description = "空间中文名称", example = "芋艿")
    private String spaceCnName;
    @Schema(description = "简介", example = "张三")
    private String description;

    @Schema(description = "空间封面", example = "张三")
    private String spaceCover;
    @Schema(description = "一键复制 ", example = "24414")
    private Integer copyFlag;
    @Schema(description = "0 全部可见 1 不允许", example = "1")
    private Integer privacyFlag;
    private String sponsor;

    @Schema(description = "空间模板id", example = "29611")
    private Long spaceTemplateId;
    @Schema(description = "挂点作品")
    private List<EditInfoDTO> ponitList;

    @Schema(description = "无挂点作品")
    private List<EditInfoDTO> worksList;

    @Schema(description = "挂载资源")
    private List<EditInfoDTO> fileList;

    @Schema(description = "音频资源", example = "29611")
    private List<EditInfoDTO> metaList;

}
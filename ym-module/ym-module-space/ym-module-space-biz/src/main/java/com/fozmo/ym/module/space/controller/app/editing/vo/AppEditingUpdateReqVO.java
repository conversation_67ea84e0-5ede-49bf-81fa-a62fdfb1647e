package com.fozmo.ym.module.space.controller.app.editing.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间编辑新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppEditingUpdateReqVO {
    private Long id;
    @Schema(description = "spaceId", requiredMode = Schema.RequiredMode.REQUIRED, example = "15798")
    private Long spaceId;
    @Schema(description = "空间模板id", example = "29611")
    @ExcelProperty("空间模板id")
    private Long spaceTemplateId;
    @Schema(description = "编辑对象id", example = "29611")
    private Long editId;
    @Schema(description = "编辑对象类型", example = "29611")
    private Integer editType;
    @Schema(description = "挂点编号，挂点上位置，不再挂点默认为null", example = "")
    private Integer point;
    @Schema(description = "排序，这里的序号包含挂点和非挂点-前端自己维护", example = "1")
    private Integer sort;
    private Integer editSort;
    @Schema(description = "扩展数据：空间点位配置数据", example = "13935")
    private String extraData;

}
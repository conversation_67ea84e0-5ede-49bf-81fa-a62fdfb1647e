package com.fozmo.ym.module.space.controller.app.editing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EditInfoDTO {
    private Long id;
    @Schema(description = "编辑对象id", example = "29611")
    private Long editId;
    @Schema(description = "编辑对象类型", example = "29611")
    private Integer editType;

    private String editName;

    private String editDescription;

    @Schema(description = "扩展数据", example = "13935")
    private String extraData;
    @Schema(description = "权益Id", example = "13935")
    private Long rightsId;
    @Schema(description = "账户id", example = "13935")
    private Long accountId;
    @Schema(description = "账户名称", example = "13935")
    private String accountName;
    @Schema(description = "昵称", example = "13935")
    private String nickname;
    private String hignUrl;
    private Integer point;

    private Integer editSort;

    private String lowUrl;

    private String editCover;

    private String author;

    private Integer editWidth;

    private Integer editHeight;


}

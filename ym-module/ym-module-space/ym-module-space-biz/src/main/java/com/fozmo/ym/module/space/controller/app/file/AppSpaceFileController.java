package com.fozmo.ym.module.space.controller.app.file;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.file.vo.FilePageReqVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileRespVO;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import com.fozmo.ym.module.space.service.file.SpaceFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块- 空间资源")
@RestController
@RequestMapping("/space/file")
@Validated
public class AppSpaceFileController {

    @Resource
    private SpaceFileService spaceFileService;


    @GetMapping("/page")
    @Operation(summary = "获得资源分页", description = "获得资源分页")
    public CommonResult<PageResult<FileRespVO>> getFilePage(@Valid FilePageReqVO pageReqVO) {
        PageResult<SpaceFileDO> pageResult = spaceFileService.getFilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FileRespVO.class));
    }

}

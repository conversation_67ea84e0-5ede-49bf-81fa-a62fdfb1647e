package com.fozmo.ym.module.space.controller.app.file;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.space.controller.app.file.vo.SpaceFramePageReqVO;
import com.fozmo.ym.module.space.controller.app.file.vo.SpaceFrameRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "APP-空间模块- 空间资源")
@RestController
@RequestMapping("/space/frame")
@Validated
public class AppSpaceFrameController {

    // 画框列表
    @RequestMapping("/list")
    @Operation(summary = "获得画框列表", description = "获得画框列表")
    @PermitAll
    public CommonResult<List<SpaceFrameRespVO>> getFrameList( SpaceFramePageReqVO spaceFramePageReqVO){
        return CommonResult.success(null);
    }

}

package com.fozmo.ym.module.space.controller.app.file.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 画框类型分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpaceFramePageReqVO  extends PageParam {
    @Schema(description = "类别编号")
    private Integer typeId;
}

package com.fozmo.ym.module.space.controller.app.file.vo;

import com.fozmo.ym.module.space.api.dto.SpaceCardDTO;
import com.fozmo.ym.module.space.api.dto.SpaceFrameSizeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "APP-空间模块- 画框列表资源")
public class SpaceFrameRespVO {

    @Schema(description = "画框编号")
    private Long id;
    @Schema(description = "画框名称")
    private String name;

    @Schema(description = "画框编码")
    private String code;

    @Schema(description = "画框样式类型")
    private SpaceFrameSizeDTO type;

    @Schema(description = "画框粗细")
    private Integer width;

    @Schema(description = "边框图片")
    private String borderImage;

    @Schema(description = "夹角图片")
    private String cornerImage;

    @Schema(description = "使用类型 1 普通 2 付费 3 权益")
    private Integer useType;

    @Schema(description = "使用类型Id 普通为空 付费商品Id 权益为权益Id")
    private Long useTypeId;

    @Schema(description = "尺寸")
    private List<SpaceFrameSizeDTO> size;

    @Schema(description = "卡纸属性")
    private SpaceCardDTO card;







}

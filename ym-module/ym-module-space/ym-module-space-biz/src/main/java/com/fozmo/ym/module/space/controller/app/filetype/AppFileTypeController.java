package com.fozmo.ym.module.space.controller.app.filetype;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.filetype.vo.FileTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.filetype.vo.AppFileTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.filetype.vo.AppFileTypeRespVO;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import com.fozmo.ym.module.space.service.file.FileTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块- 资源类型")
@RestController
@RequestMapping("/space/fileType")
@Validated
public class AppFileTypeController {

    @Resource
    private FileTypeService fileTypeService;


    @GetMapping("/get")
    @Operation(summary = "获得资源类型详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppFileTypeRespVO> getFileType(@RequestParam("id") Long id) {
        FileTypeDO fileType = fileTypeService.getFileType(id);
        return success(BeanUtils.toBean(fileType, AppFileTypeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资源类型分页")
    public CommonResult<PageResult<AppFileTypeRespVO>> getFileTypePage(@Valid AppFileTypePageReqVO pageReqVO) {

        FileTypePageReqVO fileTypePageReqVO = BeanUtils.toBean(pageReqVO, FileTypePageReqVO.class);
        PageResult<FileTypeDO> pageResult = fileTypeService.getFileTypePage(fileTypePageReqVO);
        return success(BeanUtils.toBean(pageResult, AppFileTypeRespVO.class));
    }

}
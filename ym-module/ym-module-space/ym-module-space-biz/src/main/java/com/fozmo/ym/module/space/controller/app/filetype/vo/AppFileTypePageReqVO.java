package com.fozmo.ym.module.space.controller.app.filetype.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间资源类型分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppFileTypePageReqVO extends PageParam {

    @Schema(description = "资源类别中文名称", example = "赵六")
    private String typeCnName;


    @Schema(description = "空间资源类型编码")
    private String typeCode;

    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "2")
    private Integer typeStatus;

    @Schema(description = "创建标识 0系统初始化 1 用户自定义 2 其他")
    private Integer createDefault;
}

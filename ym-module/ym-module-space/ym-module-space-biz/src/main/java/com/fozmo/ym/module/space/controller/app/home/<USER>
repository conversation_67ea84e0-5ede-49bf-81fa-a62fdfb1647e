package com.fozmo.ym.module.space.controller.app.home;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountInfoDTO;
import com.fozmo.ym.module.social.api.fans.SocialFansApi;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceRespVO;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
import com.fozmo.ym.module.space.controller.app.home.vo.*;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateRespVO;
import com.fozmo.ym.module.space.controller.app.works.vo.AppWorksPageRespVO;
import com.fozmo.ym.module.space.dal.dataobject.hot.SpaceHotDO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.service.hot.SpaceHotService;
import com.fozmo.ym.module.space.service.space.SpaceService;
import com.fozmo.ym.module.space.service.spacetemplate.SpaceTemplateService;
import com.fozmo.ym.module.space.service.works.WorksService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块 -首页查询")
@RestController
@RequestMapping("/space/home")
@Validated
public class HomeController {

    @Resource
    private SpaceService spaceService;

    @Resource
    private WorksService worksService;

    @Resource
    private SpaceTemplateService spaceTemplateService;

    @Resource
    private SpaceHotService spaceHotService;

    @Autowired
    private AccountApi accountApi;

    @Resource
    @Lazy
    private SocialFansApi socialFansApi;




    @GetMapping("/space")
    @Operation(summary = "首页-空间", description = "不输入查询条件为热门空间数据，输入查询条件则分页查询全部空间数据")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<SpaceRespVO>> getHomeSpace(@Valid SpaceHomePageReqVO pageReqVO) {
        PageResult<SpaceRespVO> result = new PageResult<>();
        SpacePageReqVO spacePageReqVO =BeanUtils.toBean(pageReqVO, SpacePageReqVO.class);
        if (StringUtils.isEmpty(pageReqVO.getSpaceCnName())){
            // 查询条件为空时 默认查询热值空间 2
            List<SpaceHotDO> spaceHotDOS = spaceHotService.findByType(2);
            List<Long> spaceIds = spaceHotDOS.stream().map(SpaceHotDO::getTypeId).collect(Collectors.toList());
            spacePageReqVO.setSpaceIds(spaceIds);
        }
        result= BeanUtils.toBean(spaceService.getSpacePage(spacePageReqVO), SpaceRespVO.class);
        return success(result);
    }

    @GetMapping("/works")
    @Operation(summary = "首页-作品", description = "不输入查询条件为热门作品数据，输入查询条件则分页查询全部作品数据")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppWorksPageRespVO>> getHomeWorks(@Valid WorksHomePageReqVO pageReqVO) {
        WorksPageReqVO reqVO = BeanUtils.toBean(pageReqVO, WorksPageReqVO.class);
        PageResult<WorksDO> pageResult = new PageResult<>();
        if (StringUtils.isEmpty(pageReqVO.getWorksName())){
            List<SpaceHotDO> spaceHotDOS = spaceHotService.findByType(3);
            List<Long> worksIds = spaceHotDOS.stream().map(SpaceHotDO::getTypeId).collect(Collectors.toList());
            reqVO.setWorksIds(worksIds);
            pageResult = worksService.getWorksPage(reqVO);
        }else {
            reqVO.setWorksCnName(pageReqVO.getWorksName());
            reqVO.setPointType(pageReqVO.getPointType());
            pageResult = worksService.getWorksPage(reqVO);
        }

        return success(BeanUtils.toBean(pageResult, AppWorksPageRespVO.class));
    }

    @GetMapping("/template")
    @Operation(summary = "首页-模版", description = "不输入查询条件为热门模版数据，输入查询条件则分页查询全部模版数据")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppSpaceTemplateRespVO>> getHomeTemplate(@Valid SpaceHomeTemplatePageReqVO appPageReqVO) {
        SpaceTemplatePageReqVO pageReqVO = BeanUtils.toBean(appPageReqVO, SpaceTemplatePageReqVO.class);
        if (StringUtils.isEmpty(appPageReqVO.getTemplateCnName())){
            List<SpaceHotDO> spaceHotDOS = spaceHotService.findByType(1);
            List<Long> tps = spaceHotDOS.stream().map(SpaceHotDO::getTypeId).collect(Collectors.toList());
            pageReqVO.setIds(tps);
        }
        PageResult<SpaceTemplateDO> pageResult = spaceTemplateService.getTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTemplateRespVO.class));
    }

    @GetMapping("/account")
    @Operation(summary = "首页-账户", description = "不输入查询条件为热门账户数据，输入查询条件则分页查询全部账户数据")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AccountHomePageRespVO>> getHomeAccount(@Valid AccountHomePageReqVO accountHomePageReqVO) {
        PageResult<AccountHomePageRespVO>  result = new PageResult<>();
        if (StringUtils.isEmpty(accountHomePageReqVO.getNickname())) {
            List<SpaceHotDO> spaceHotDOS = spaceHotService.findByType(4);
            List<Long> userIds = spaceHotDOS.stream().map(SpaceHotDO::getTypeId).collect(Collectors.toList());
            accountHomePageReqVO.setAccountIds(userIds);
        }

        PageResult<AccountInfoDTO> dtoList = accountApi.queryAccountInfoList(accountHomePageReqVO.getPageNo(), accountHomePageReqVO.getPageSize(), accountHomePageReqVO.getNickname());
        if (ObjectUtil.isNotEmpty(dtoList) && dtoList.getTotal() > 0 && CollUtil.isNotEmpty(dtoList.getList())) {

            Long accountId = SecurityFrameworkUtils.getLoginUserId();
            List<AccountHomePageRespVO> list = dtoList.getList().stream().map(dto -> {
                AccountHomePageRespVO vo = new AccountHomePageRespVO();
                BeanUtils.copyProperties(dto, vo);
                if (accountId != null) {
                    vo.setFans(socialFansApi.isFans(dto.getId(), accountId));
                    vo.setFansNum(Math.toIntExact(socialFansApi.fansNum(accountId)));
                } else {
                    vo.setFans(false);
                    vo.setFansNum(0);
                }
                vo.setFollowNum(Math.toIntExact(socialFansApi.queryFollowNum(dto.getId())));
                vo.setSpaceNum(Math.toIntExact(spaceService.pageCount(dto.getId())));
                vo.setWorksNum(Math.toIntExact(worksService.worksCount(dto.getId())));
                return vo;
            }).collect(Collectors.toList());
            result.setList(list);
            result.setTotal(dtoList.getTotal());
        }
        return success(result);
    }

}

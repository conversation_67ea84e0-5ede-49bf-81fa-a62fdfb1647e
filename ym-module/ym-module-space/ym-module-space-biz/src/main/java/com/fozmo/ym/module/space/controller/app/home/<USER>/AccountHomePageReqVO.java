package com.fozmo.ym.module.space.controller.app.home.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "APP - 首页空间分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountHomePageReqVO extends PageParam {
    @Schema(description = "空间中文名称", example = "芋艿")
    private String nickname;
    @Schema(hidden = true)
    private List<Long> accountIds;
}

package com.fozmo.ym.module.space.controller.app.home.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP-首页账户信息 Response VO")
@Accessors(chain = true)
@Data
public class AccountHomePageRespVO {


    @Schema(description = "主键", example = "6035")
    private Long id;
    @Schema(description = "账户名称", example = "王五")
    private String name;
    @Schema(description = "权益id", example = "7736")
    private Long rightsId;
    @Schema(description = "0 男 1 女  3未知")
    private Integer sex;
    @Schema(description = "昵称", example = "张三")
    private String nickname;
    @Schema(description = "用户头像")
    private String avatar;
    @Schema(description = "空间数", example = "6")
    private Integer spaceNum;
    @Schema(description = "作品数")
    private Integer worksNum;
    @Schema(description = "是否关注",defaultValue = "false")
    private Boolean fans;
    @Schema(description = "粉丝数", defaultValue = "关注此用户的数量")
    private Integer fansNum;
    @Schema(description = "关注数", defaultValue = "指用户关注的数量")
    private Integer followNum;

    @Schema(description = "描述", example = "这是一个描述")
    private String description;
}

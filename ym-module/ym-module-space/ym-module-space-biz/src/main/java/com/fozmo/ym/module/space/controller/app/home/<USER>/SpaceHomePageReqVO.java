package com.fozmo.ym.module.space.controller.app.home.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 首页空间分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpaceHomePageReqVO extends PageParam {
    @Schema(description = "空间中文名称", example = "哈哈")
    private String spaceCnName;

    /**
     * 空间类型
     */
    @Schema(description = "空间分类Id", example = "1")
    private Long spaceTypeId;

}

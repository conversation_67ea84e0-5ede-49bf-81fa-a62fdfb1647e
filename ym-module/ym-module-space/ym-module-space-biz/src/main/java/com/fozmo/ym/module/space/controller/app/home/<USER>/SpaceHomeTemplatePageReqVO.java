package com.fozmo.ym.module.space.controller.app.home.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "App - 首页模版 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpaceHomeTemplatePageReqVO extends PageParam {

    @Schema(description = "模板中文名称", example = "赵六")
    private String templateCnName;
}

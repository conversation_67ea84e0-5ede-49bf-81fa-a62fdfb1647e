package com.fozmo.ym.module.space.controller.app.home.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP -空间热值-请求参数")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpaceHotPageReqVO extends PageParam {

    private Integer type;
}

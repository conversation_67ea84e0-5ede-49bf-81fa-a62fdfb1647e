package com.fozmo.ym.module.space.controller.app.home.vo;


import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 广场 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorksHomePageReqVO extends PageParam {

    private String worksName;

    @Schema(description = "我的空间id", example = "27233")
    private Long spaceId;

    private Integer pointType;

}


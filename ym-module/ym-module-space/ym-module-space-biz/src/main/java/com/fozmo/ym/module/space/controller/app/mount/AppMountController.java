package com.fozmo.ym.module.space.controller.app.mount;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksInfoRespVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksRespVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksSaveReqVO;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP -空间模块-挂载关系")
@RestController
@RequestMapping("/space/mount")
@Validated
public class AppMountController {

    @Resource
    private MountWorksService mountWorksService;

    @PostMapping("/create")
    @Operation(summary = "上架单条作品、资源、音乐", description = "作品、资源 音乐共用此接口，注意挂点不允许新增")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> create(@Valid @RequestBody MountWorksSaveReqVO createReqVO) {
        return success(mountWorksService.createMountWorks(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "编辑挂载（空间拖动、移到非挂点、修改音乐等）", description = "作品、资源 音乐共用此接口,资源类关系编辑时会校验权益")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> update(@Valid @RequestBody MountWorksSaveReqVO updateReqVO) {
        mountWorksService.updateMountWorks(updateReqVO);
        return success(true);
    }
//
    @DeleteMapping("/delete")
    @Operation(summary = "下架单条（空间、作品、音频）", description = "删除挂载关系-单一操作某一条数据，注意这里挂载点不允许删除")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        mountWorksService.deleteMountWorks(id);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "空间挂载关系列表", description = "返回空间下的作品、资源、音乐等，正式空间游览使用、查看挂载信息使用")
    @Parameter(name = "spaceId", description = "编号", required = true, example = "1024")
    @PermitAll
    @ApiAccessLog
    public CommonResult<MountWorksRespVO> getMountList(@RequestParam("spaceId") Long spaceId) {
        return success(mountWorksService.getMountList(spaceId));
    }

    @GetMapping("/info")
    @Operation(summary = "展示挂载详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    @ApiAccessLog
    public CommonResult<MountWorksInfoRespVO> getMountInfo(@RequestParam("id") Long id) {
        return success(mountWorksService.getMountInfo(id));
    }
    @PostMapping("/push")
    @Operation(summary = "挂载发布接口", description = "一键布置接口，这里只针对挂载点作品")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> pushMount(@RequestBody MountWorksRespVO reqVO) {
        return success(mountWorksService.pushMount(reqVO));
    }

    @PostMapping("/batchMount")
    @Operation(summary = "批量挂载接口", description = "（一键布置-当前需求只处理作品）批量挂载接口作品和资源，如果是点位数据会执行删除再插入")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> batchMount(@RequestBody MountWorksRespVO reqVO) {
        return success(mountWorksService.batchMount(reqVO));
    }

    @PostMapping("/batchUnMount")
    @Operation(summary = "批量下架接口", description = "（当前需求只处理作品）批量下架接口，非点位数据直接删除，点位数据会置为初始状态")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> batchUnMount(@RequestBody List<Long> ids) {
        return success(mountWorksService.batchUnMount(ids));
    }

    @PostMapping("/unMountALl")
    @Operation(summary = "一键下架接口", description = "一键下架接口，通过空间Id和类型 type 0 作品 1 资源 2 音乐")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> unMountType(@RequestParam("spaceId") Long spaceId, @RequestParam("type") Integer type) {
        return success(mountWorksService.unMountType(spaceId, type));
    }


}
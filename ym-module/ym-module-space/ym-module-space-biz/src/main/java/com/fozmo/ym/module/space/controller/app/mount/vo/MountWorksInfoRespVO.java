package com.fozmo.ym.module.space.controller.app.mount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
@Data
@Schema(description = "空间挂载详情")
public class MountWorksInfoRespVO {

    @Schema(description = "spaceId", requiredMode = Schema.RequiredMode.REQUIRED, example = "15798")
    private Long spaceId;
    @Schema(description = "空间中文名称", example = "芋艿")
    private String spaceCnName;
    @Schema(description = "简介", example = "张三")
    private String description;

    @Schema(description = "空间封面", example = "张三")
    private String spaceCover;
    @Schema(description = "一键复制 ", example = "24414")
    private Integer copyFlag;
    @Schema(description = "0 全部可见 1 不允许", example = "1")
    private Integer privacyFlag;
    private String sponsor;

    @Schema(description = "空间模板id", example = "29611")
    private Long spaceTemplateId;

    private MountInfoDTO mountInfo;
}

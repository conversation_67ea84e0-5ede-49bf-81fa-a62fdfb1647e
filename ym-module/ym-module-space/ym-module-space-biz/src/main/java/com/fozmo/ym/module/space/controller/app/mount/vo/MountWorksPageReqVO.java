package com.fozmo.ym.module.space.controller.app.mount.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "管理后台 - 空间挂在作品分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MountWorksPageReqVO extends PageParam {

    @Schema(description = "作品id", example = "32604")
    private Long worksId;

    @Schema(description = "空间id", example = "7244")
    private Long spaceId;
    
    @Schema(description = "挂载类型", example = "7244")
    private List<Integer> mountTypes;
}
package com.fozmo.ym.module.space.controller.app.mount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间挂在作品新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class MountWorksSaveReqVO {

    private Long id;
    @Schema(description = "spaceId", requiredMode = Schema.RequiredMode.REQUIRED, example = "15798")
    private Long spaceId;
    @Schema(description = "挂载对象对象id,当对象不存在时 存point数值", example = "29611")
    private Long mountId;
    @Schema(description = "挂载对象类型", example = "mountType = 0 说明为系统预制挂载点，默认挂载点 不可删除")
    private Integer mountType;
    @Schema(description = "挂点编号，挂点上位置，不再挂点默认为null", example = "")
    private Integer point;
    @Schema(description = "排序", example = "")
    private Integer mountSort;
    @Schema(description = "扩展数据：空间点位配置数据", example = "13935")
    private String extraData;

}
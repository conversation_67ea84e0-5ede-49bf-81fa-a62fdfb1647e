//package com.fozmo.ym.module.space.controller.app.mount.vo;
//
//import com.fozmo.ym.module.space.controller.admin.file.vo.FileRespVO;
//import com.fozmo.ym.module.space.controller.app.works.vo.AppWorksPageRespVO;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//
//import java.util.List;
//
//@Data
//@Schema(description = "空间挂载详情")
//public class SpaceMountWorksInfoRespVO {
//    @Schema(description = "空间名称")
//    private String spaceName;
//
//    @Schema(description = "空间id")
//    private Long spaceId;
//    @Schema(description = "空间作品-图片")
//    private List<AppWorksPageRespVO> spaceImage;
//
//    @Schema(description = "空间作品-视频")
//    private List<AppWorksPageRespVO> spaceVideo;
//
//    @Schema(description = "空间作品-模型")
//    private List<AppWorksPageRespVO> spaceModel;
//    @Schema(description = "空间资源-文字")
//    private List<FileRespVO> spaceFileWord;
//
//    @Schema(description = "空间资源-3D模型")
//    private List<FileRespVO> spaceFileModel;
//
//    @Schema(description = "空间资源-图片")
//    private List<FileRespVO> spaceFileImage;
//
//    @Schema(description = "空间资源-音乐")
//    private List<FileRespVO> spaceFileMusic;
//
//}

package com.fozmo.ym.module.space.controller.app.space;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceInfoRespVO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceRespVO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceSaveReqVO;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplateInfoRespVO;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.admin.tag.vo.SpaceTagPageReqVO;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpacePageReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceSaveReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceUpdateTempReqVO;
import com.fozmo.ym.module.space.controller.app.tag.vo.AppSpaceTagPageReqVO;
import com.fozmo.ym.module.space.controller.app.tag.vo.AppSpaceTagRespVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateInfoRespVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTypeRespVO;
import com.fozmo.ym.module.space.controller.app.works.vo.AppWorksPageRespVO;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import com.fozmo.ym.module.space.dal.dataobject.tag.SpaceTagDO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import com.fozmo.ym.module.space.service.space.SpaceService;
import com.fozmo.ym.module.space.service.spacetemplate.SpaceTemplateService;
import com.fozmo.ym.module.space.service.tag.SpaceTagService;
import com.fozmo.ym.module.space.service.templatetype.SpaceTemplateTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块-空间")
@RestController
@RequestMapping("/space/space")
@Validated
public class AppSpaceController {

    @Resource
    private SpaceService spaceService;

    @Resource
    private SpaceTemplateService templateService;
    @Resource
    private SpaceTagService spaceTagService;
    @Resource
    @Lazy
    private MountWorksService mountWorksService;
    @Resource
    private SpaceTemplateTypeService templateTypeService;

    @GetMapping("/get")
    @Operation(summary = "空间详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    @ApiAccessLog
    public CommonResult<SpaceInfoRespVO> getSpace(@RequestParam("id") Long id) {
        SpaceDO infoRespVO = spaceService.getSpaceInfo(id);
        return success(BeanUtils.toBean(infoRespVO, SpaceInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "空间列表", description = "查询自己或他人的空间列表")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<SpaceRespVO>> getSpacePage(@Valid AppSpacePageReqVO pageReqVO) {
        SpacePageReqVO reqVO = BeanUtils.toBean(pageReqVO, SpacePageReqVO.class);
        if (ObjectUtil.isEmpty(reqVO.getAccountId())) {
            reqVO.setAccountId( WebFrameworkUtils.getLoginUserId());
            return success(BeanUtils.toBean(spaceService.getMyAccountSpacePage(reqVO), SpaceRespVO.class));
        } else {
            return success(BeanUtils.toBean(spaceService.getAccountSpacePage(reqVO), SpaceRespVO.class));
        }


    }
    @PostMapping("/create")
    @Operation(summary = "创建空间")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> createSpace(@RequestBody @Valid AppSpaceSaveReqVO createReqVO) {
        SpaceSaveReqVO reqVO = BeanUtils.toBean(createReqVO, SpaceSaveReqVO.class);
        return success(spaceService.createSpace(reqVO));
    }

    @PostMapping("/updateTemp")
    @Operation(summary = "空间编辑-更换空间模板", description = "下架所有作品-立马生效")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> updateTemp(@RequestBody @Valid AppSpaceUpdateTempReqVO reqVO) {
        return success(spaceService.updateTemp(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "实时生效-空间编辑属性")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> updateSpace(@RequestBody @Valid AppSpaceSaveReqVO createReqVO) {
        SpaceSaveReqVO reqVO = BeanUtils.toBean(createReqVO, SpaceSaveReqVO.class);
        spaceService.updateSpace(reqVO);
        return success(true);
    }

        @DeleteMapping("/delete")
        @Operation(summary = "删除空间", description = "删除空间立马删除，关联数据队列执行删除")
        @Parameter(name = "id", description = "编号", required = true)
        @PermitAll
        @ApiAccessLog
        public CommonResult<Boolean> deleteSpace(@RequestParam("id") Long id) {
            spaceService.deleteSpace(id);
            return success(true);
        }
    
    @GetMapping("/getSpaceTag")
    @Operation(summary = "获得空间标签")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppSpaceTagRespVO> getSpaceTag(@RequestParam("id") Long id) {
        SpaceTagDO spaceTag = spaceTagService.getSpaceTag(id);
        return success(BeanUtils.toBean(spaceTag, AppSpaceTagRespVO.class));
    }

    @GetMapping("/getSpaceTagPage")
    @Operation(summary = "获得空间标签分页")
    public CommonResult<PageResult<AppSpaceTagRespVO>> getSpaceTagPage(@Valid AppSpaceTagPageReqVO appPageVO) {

        SpaceTagPageReqVO pageReqVO = BeanUtils.toBean(appPageVO, SpaceTagPageReqVO.class);
        PageResult<SpaceTagDO> pageResult = spaceTagService.getSpaceTagPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTagRespVO.class));
    }

    @GetMapping("/getTemplate")
    @Operation(summary = "获得空间模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppSpaceTemplateInfoRespVO> getTemplate(@RequestParam("id") Long id) {
        SpaceTemplateDO template = templateService.getTemplate(id);
        AppSpaceTemplateInfoRespVO vo = BeanUtils.toBean(template, AppSpaceTemplateInfoRespVO.class);
        return success(vo);
    }

    @GetMapping("/templatePage")
    @Operation(summary = "获得空间模板分页")
    public CommonResult<PageResult<SpaceTemplateInfoRespVO>> getTemplatePage(@Valid AppSpaceTemplatePageReqVO appPageReqVO) {

        SpaceTemplatePageReqVO pageReqVO = BeanUtils.toBean(appPageReqVO, SpaceTemplatePageReqVO.class);
        pageReqVO.setTemplateStatus(1);
        PageResult<SpaceTemplateDO> pageResult = templateService.getTemplateInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SpaceTemplateInfoRespVO.class));
    }

    @GetMapping("/templateTypeInfo")
    @Operation(summary = "获得空间模板类型")
    @PermitAll
    @ApiAccessLog
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppSpaceTemplateTypeRespVO> getTemplateType(@RequestParam("id") Long id) {
        SpaceTemplateTypeDO templateType = templateTypeService.getTemplateType(id);
        return success(BeanUtils.toBean(templateType, AppSpaceTemplateTypeRespVO.class));
    }
    
    @GetMapping("/templateTypePage")
    @Operation(summary = "获得空间模板类型分页")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppSpaceTemplateTypeRespVO>> getTemplateTypePage(@Valid AppSpaceTemplateTypePageReqVO pageReqVO) {
        SpaceTemplateTypePageReqVO reqVO = BeanUtils.toBean(pageReqVO, SpaceTemplateTypePageReqVO.class);
        PageResult<SpaceTemplateTypeDO> pageResult = templateTypeService.getTemplateTypePage(reqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTemplateTypeRespVO.class));
    }

    @GetMapping("/mountWoks")
    @Operation(summary = "当前空间挂载作品", description = "对应已展示作品")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppWorksPageRespVO>> getMountWoks(@RequestParam("spaceId") Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {
        PageResult<WorksDO> mountWorksRespVO = spaceService.getMountWoks(spaceId, pointType, pageNo, pageSize);
        return success(BeanUtils.toBean(mountWorksRespVO, AppWorksPageRespVO.class));
    }

    @GetMapping("/unMountWorks")
    @Operation(summary = "当前空间未挂载作品", description = "对应未展示作品")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppWorksPageRespVO>> getUnMountWorks(@RequestParam("spaceId") Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {
        PageResult<WorksDO> mountWorksRespVO = spaceService.getUnMountWorks(spaceId, pointType, pageNo, pageSize);
        return success(BeanUtils.toBean(mountWorksRespVO, AppWorksPageRespVO.class));
    }

    @GetMapping("/spaceWorks")
    @Operation(summary = "查询在当前空间下的作品-包含已挂载、未挂载的个人所有作品", description = "对应未展示作品")
    public CommonResult<PageResult<AppWorksPageRespVO>> spaceWorks(@RequestParam("spaceId") Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {
        PageResult<WorksDO> spaceWorksVO = spaceService.spaceWorks(spaceId, pointType, pageNo, pageSize);
        return success(BeanUtils.toBean(spaceWorksVO, AppWorksPageRespVO.class));
    }
    
}

package com.fozmo.ym.module.space.controller.app.space.vo;


import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "APP - 空间分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSpacePageReqVO extends PageParam {
    @Schema(description = "spaceIds 批量查询使用", example = "芋艿")
    private List<Long> spaceIds;
    @Schema(description = "spaceId", example = "芋艿")
    private Long spaceId;

    @Schema(description = "空间中文名称", example = "芋艿")
    private String spaceCnName;

    @Schema(description = "空间类型", example = "24414")
    private Long spaceTypeId;

    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "1")
    private Integer spaceStatus;

    @Schema(description = "一键复制 0 不允许 1运行")
    private Integer copyFlag;

    @Schema(description = "0 全部可见 ")
    private Integer privacyFlag;
    
    @Schema(description = "账户Id 如果不传 默认会获取自身 账户Id")
    private Long accountId;

}

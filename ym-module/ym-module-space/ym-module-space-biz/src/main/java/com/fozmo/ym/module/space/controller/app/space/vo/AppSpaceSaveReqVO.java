package com.fozmo.ym.module.space.controller.app.space.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.fozmo.ym.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 空间作品新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppSpaceSaveReqVO {

    @Schema(description = "id", example = "24030")
    private Long id;

    @Schema(description = "空间模板id", example = "29611")
    private Long spaceTemplateId;

    @Schema(description = "空间中文名称", example = "芋艿")
    private String spaceCnName;

    @Schema(description = "空间英文名称", example = "张三")
    private String spaceEnName;

    @Schema(description = "空间码值")
    private String spaceCode;

    @Schema(description = "空间类型", example = "24414")
    private Long spaceTypeId;

    @Schema(description = "2d挂载点")
    private Integer spaceNum2;

    @Schema(description = "3d挂载点")
    private Integer spaceNum3;

    @Schema(description = "账户id", example = "13935")
    private Long accountId;

    @Schema(description = "账户编码")
    private String accountCode;

    @Schema(description = "中文说明", example = "你说的对")
    private String cnDescription;

    @Schema(description = "英文说明", example = "你说的对")
    private String enDescription;

    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "1")
    private Integer spaceStatus;

    @Schema(description = "空间封面")
    private String spaceCover;

    @Schema(description = "一键复制 0 不允许 1运行")
    private Integer copyFlag;

    @Schema(description = "0 全部可见 ")
    private Integer privacyFlag;

    private String sponsor;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime exhibitionStartTime;
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime exhibitionEndTime;
    private Integer exhibitionTimeStatus;

}


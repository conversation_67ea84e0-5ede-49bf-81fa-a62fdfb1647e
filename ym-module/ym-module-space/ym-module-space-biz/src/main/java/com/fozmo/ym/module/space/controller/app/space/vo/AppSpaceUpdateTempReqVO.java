package com.fozmo.ym.module.space.controller.app.space.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppSpaceUpdateTempReqVO {

    @Schema(description = "空间id", example = "13935")
    @NotNull(message = "空间id不能为空")
    private Long spaceId;
    @Schema(description = "空间模板id", example = "29611")
    @NotNull(message = "空间模板id不能为空")
    private Long templateId;
}

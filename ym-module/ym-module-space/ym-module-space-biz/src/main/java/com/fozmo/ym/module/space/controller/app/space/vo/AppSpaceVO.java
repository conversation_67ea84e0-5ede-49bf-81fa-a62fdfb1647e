package com.fozmo.ym.module.space.controller.app.space.vo;

import com.fozmo.ym.module.space.api.dto.SpaceInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
@Data
@Schema(description = "空间信息")
public class AppSpaceVO {
    @Schema(description = "空间总数")
    private Integer totalNum;
    @Schema(description = "当前空间数")
    private Integer currentNum;
    @Schema(description = "空间剩余数")
    private Integer availableNum;
    @Schema(description = "分页总数")
    private Integer total;
    @Schema(description = "空间信息")
    private List<SpaceInfoDTO> spaceInfoList;
}

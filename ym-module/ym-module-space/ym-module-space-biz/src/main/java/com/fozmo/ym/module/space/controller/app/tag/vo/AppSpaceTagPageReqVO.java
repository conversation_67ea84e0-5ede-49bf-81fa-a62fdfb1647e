package com.fozmo.ym.module.space.controller.app.tag.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间标签分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSpaceTagPageReqVO extends PageParam {

    @Schema(description = "空间标签名称", example = "王五")
    private String name;
}
package com.fozmo.ym.module.space.controller.app.tag.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间标签 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppSpaceTagRespVO {

    @Schema(description = "id", example = "10839")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "空间标签名称", example = "王五")
    @ExcelProperty("空间标签名称")
    private String name;

}
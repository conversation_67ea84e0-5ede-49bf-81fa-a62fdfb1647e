package com.fozmo.ym.module.space.controller.app.tag.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间标签新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppSpaceTagSaveReqVO {

    @Schema(description = "id", example = "10839")
    private Long id;

    @Schema(description = "空间标签名称", example = "王五")
    private String name;

}
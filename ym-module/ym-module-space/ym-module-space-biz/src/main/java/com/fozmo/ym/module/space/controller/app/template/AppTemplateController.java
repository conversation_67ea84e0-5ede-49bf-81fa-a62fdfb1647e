package com.fozmo.ym.module.space.controller.app.template;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateInfoRespVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateRespVO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import com.fozmo.ym.module.space.service.spacetemplate.SpaceTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块-空间模板")
@RestController
@RequestMapping("/space/template")
@Validated
public class AppTemplateController {

    @Resource
    private SpaceTemplateService templateService;

    @GetMapping("/get")
    @Operation(summary = "获得空间模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppSpaceTemplateInfoRespVO> getTemplate(@RequestParam("id") Long id) {
        SpaceTemplateDO template = templateService.getTemplate(id);
        AppSpaceTemplateInfoRespVO vo = BeanUtils.toBean(template, AppSpaceTemplateInfoRespVO.class);
        return success(vo);
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间模板分页")
    public CommonResult<PageResult<AppSpaceTemplateRespVO>> getTemplatePage(@Valid AppSpaceTemplatePageReqVO appPageReqVO) {

        SpaceTemplatePageReqVO pageReqVO = BeanUtils.toBean(appPageReqVO, SpaceTemplatePageReqVO.class);
        pageReqVO.setTemplateStatus(1);
        PageResult<SpaceTemplateDO> pageResult = templateService.getTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTemplateRespVO.class));
    }

}
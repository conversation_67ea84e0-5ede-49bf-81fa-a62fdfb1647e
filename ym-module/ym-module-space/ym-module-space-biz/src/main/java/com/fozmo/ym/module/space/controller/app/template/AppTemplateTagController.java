package com.fozmo.ym.module.space.controller.app.template;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.templatetag.vo.SpaceTemplateTagPageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTagListReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTagPageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTagRespVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import com.fozmo.ym.module.space.dal.redis.template.SpaceTemplateTagRedisDao;
import com.fozmo.ym.module.space.service.templatetag.SpaceTemplateTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块 - 空间模板标签")
@RestController
@RequestMapping("/space/templateTag")
@Validated
public class AppTemplateTagController {

    @Resource
    private SpaceTemplateTagService templateTagService;
    @Autowired
    private SpaceTemplateTagRedisDao spaceTemplateTagRedisDao;

    @GetMapping("/get")
    @Operation(summary = "获得空间模板标签")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppSpaceTemplateTagRespVO> getTemplateTag(@RequestParam("id") Long id) {
        SpaceTemplateTagDO templateTag = templateTagService.getTemplateTag(id);
        return success(BeanUtils.toBean(templateTag, AppSpaceTemplateTagRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间模板标签分页")
    public CommonResult<PageResult<AppSpaceTemplateTagRespVO>> getTemplateTagPage(@Valid AppSpaceTemplateTagPageReqVO appPageReqVO) {
        SpaceTemplateTagPageReqVO pageReqVO = BeanUtils.toBean(appPageReqVO, SpaceTemplateTagPageReqVO.class);
        PageResult<SpaceTemplateTagDO> pageResult = templateTagService.getTemplateTagPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTemplateTagRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得空间模板标签")
    public CommonResult<List<AppSpaceTemplateTagRespVO>> getTemplateTagList(@Valid AppSpaceTemplateTagListReqVO appPageReqVO) {
        return success(BeanUtils.toBean(templateTagService.getTemplateTagList(appPageReqVO), AppSpaceTemplateTagRespVO.class));
    }
}
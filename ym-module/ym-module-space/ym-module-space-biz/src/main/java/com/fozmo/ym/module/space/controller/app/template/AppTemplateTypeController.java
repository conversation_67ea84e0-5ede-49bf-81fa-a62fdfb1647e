package com.fozmo.ym.module.space.controller.app.template;

import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTypeRespVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import com.fozmo.ym.module.space.service.templatetype.SpaceTemplateTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块 - 空间模板类型")
@RestController
@RequestMapping("/space/templatetype")
@Validated
public class AppTemplateTypeController {

    @Resource
    private SpaceTemplateTypeService templateTypeService;
    @GetMapping("/get")
    @Operation(summary = "获得空间模板类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppSpaceTemplateTypeRespVO> getTemplateType(@RequestParam("id") Long id) {
        SpaceTemplateTypeDO templateType = templateTypeService.getTemplateType(id);
        return success(BeanUtils.toBean(templateType, AppSpaceTemplateTypeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间模板类型分页")
    public CommonResult<PageResult<AppSpaceTemplateTypeRespVO>> getTemplateTypePage(@Valid AppSpaceTemplateTypePageReqVO pageReqVO) {
        SpaceTemplateTypePageReqVO reqVO = BeanUtils.toBean(pageReqVO, SpaceTemplateTypePageReqVO.class);
        PageResult<SpaceTemplateTypeDO> pageResult = templateTypeService.getTemplateTypePage(reqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTemplateTypeRespVO.class));
    }

}
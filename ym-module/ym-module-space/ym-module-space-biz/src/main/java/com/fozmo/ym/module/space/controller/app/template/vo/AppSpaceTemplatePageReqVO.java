package com.fozmo.ym.module.space.controller.app.template.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "App - 空间模板分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSpaceTemplatePageReqVO extends PageParam {

    @Schema(description = "模板中文名称", example = "赵六")
    private String templateCnName;

    @Schema(description = "权益id", example = "10646")
    private Long rightsId;
    
    @Schema(description = "空间模版类别id")
    private String templateTypeCode;
}
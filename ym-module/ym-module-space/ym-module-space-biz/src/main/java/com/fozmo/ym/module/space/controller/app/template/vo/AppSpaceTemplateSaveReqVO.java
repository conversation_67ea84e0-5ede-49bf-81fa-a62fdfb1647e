package com.fozmo.ym.module.space.controller.app.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Schema(description = "App - 空间模板新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppSpaceTemplateSaveReqVO {

    @Schema(description = "模板id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17449")
    private Long templateId;

    @Schema(description = "模板英文名称", example = "芋艿")
    private String templateEnName;

    @Schema(description = "模板中文名称", example = "赵六")
    private String templateCnName;

    @Schema(description = "模板码值")
    private String templateCode;

    @Schema(description = "模板类型id", example = "5241")
    private Long templateTypeId;

    @Schema(description = "模板类型码值")
    private String templateTypeCode;

    @Schema(description = "2d挂载点数")
    private Integer pointNum2;

    @Schema(description = "3d挂载点数")
    private Integer pointNum3;

    @Schema(description = "中文说明", example = "你猜")
    private String cnDescription;

    @Schema(description = "英文说明", example = "随便")
    private String enDescription;

    @Schema(description = "状态 0 初始化 1启用 2 停用 3 删除", example = "1")
    private Integer templateStatus;

    @Schema(description = "空间封面")
    private String templateCover;

    @Schema(description = "模板地址")
    private String templateRes;

    @Schema(description = "文本内容")
    private String templateConfig;

    @Schema(description = "权益id", example = "10646")
    private Long rightsId;

    @Schema(description = "0系统初始化 1 用户自定义 2 其他")
    private Integer createDefault;

    @Schema(description = "template_tags")
    private String templateTags;

    @Schema(description = "创建人id", example = "32353")
    private Long createId;

    @Schema(description = "创建用户名称", example = "芋艿")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "更新人", example = "10626")
    private Long updateId;

    @Schema(description = "更新人", example = "芋艿")
    private String updater;

    @Schema(description = "更新日期")
    private LocalDate updateData;

    @Schema(description = "租户Code")
    private String tenantCode;

}
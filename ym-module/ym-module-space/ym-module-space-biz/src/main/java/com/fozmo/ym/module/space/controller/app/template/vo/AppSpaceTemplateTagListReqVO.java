package com.fozmo.ym.module.space.controller.app.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间模板标签分页 Request VO")
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
public class AppSpaceTemplateTagListReqVO {

    @Schema(description = "名称", example = "张三")
    private String name;

}
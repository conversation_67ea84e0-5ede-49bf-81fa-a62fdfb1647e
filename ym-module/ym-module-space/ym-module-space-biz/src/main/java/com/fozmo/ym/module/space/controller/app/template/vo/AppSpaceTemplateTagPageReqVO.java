package com.fozmo.ym.module.space.controller.app.template.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间模板标签分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSpaceTemplateTagPageReqVO extends PageParam {

    @Schema(description = "名称", example = "张三")
    private String name;

}
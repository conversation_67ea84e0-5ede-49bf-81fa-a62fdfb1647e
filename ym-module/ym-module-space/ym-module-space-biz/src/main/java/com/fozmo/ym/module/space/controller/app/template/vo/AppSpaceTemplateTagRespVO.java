package com.fozmo.ym.module.space.controller.app.template.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间模板标签 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppSpaceTemplateTagRespVO {

    @Schema(description = "id", example = "20845")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "名称", example = "张三")
    @ExcelProperty("名称")
    private String name;

}
package com.fozmo.ym.module.space.controller.app.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间模板标签新增/修改 Request VO")
@Accessors(chain = true)
@Data
public class AppSpaceTemplateTagSaveReqVO {

    @Schema(description = "id", example = "20845")
    private Long id;

    @Schema(description = "名称", example = "张三")
    private String name;
}
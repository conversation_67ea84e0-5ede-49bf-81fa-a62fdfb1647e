package com.fozmo.ym.module.space.controller.app.template.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间模板类型 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppSpaceTemplateTypeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19813")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "空间模板名称", example = "王五")
    @ExcelProperty("空间模板名称")
    private String name;
}

package com.fozmo.ym.module.space.controller.app.type;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.type.vo.SpaceTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.type.vo.AppSpaceTypePageReqVO;
import com.fozmo.ym.module.space.controller.app.type.vo.AppSpaceTypeRespVO;
import com.fozmo.ym.module.space.dal.dataobject.type.SpaceTypeDO;
import com.fozmo.ym.module.space.service.type.SpaceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块 - 空间类型")
@RestController
@RequestMapping("/space/type")
@Validated
public class AppSpaceTypeController {

    @Resource
    private SpaceTypeService typeService;

    @GetMapping("/get")
    @Operation(summary = "获得空间类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppSpaceTypeRespVO> getType(@RequestParam("id") Long id) {
        SpaceTypeDO type = typeService.getType(id);
        return success(BeanUtils.toBean(type, AppSpaceTypeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间类型分页")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppSpaceTypeRespVO>> getTypePage(@Valid AppSpaceTypePageReqVO pageReqVO) {
        SpaceTypePageReqVO reqVO = BeanUtils.toBean(pageReqVO, SpaceTypePageReqVO.class);
        PageResult<SpaceTypeDO> pageResult = typeService.getTypePage(reqVO);
        return success(BeanUtils.toBean(pageResult, AppSpaceTypeRespVO.class));
    }

}
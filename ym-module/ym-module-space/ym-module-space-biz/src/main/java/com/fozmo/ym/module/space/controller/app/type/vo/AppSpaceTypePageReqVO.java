package com.fozmo.ym.module.space.controller.app.type.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
@Schema(description = "管理后台 - 空间类型分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSpaceTypePageReqVO extends PageParam {

    @Schema(description = "类型名称", example = "芋艿")
    private String name;

}

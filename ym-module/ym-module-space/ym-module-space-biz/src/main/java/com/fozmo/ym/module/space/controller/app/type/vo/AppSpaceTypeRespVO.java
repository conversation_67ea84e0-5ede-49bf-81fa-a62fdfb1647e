package com.fozmo.ym.module.space.controller.app.type.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 空间类型 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppSpaceTypeRespVO {

    @Schema(description = "id", example = "9172")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "类型名称", example = "芋艿")
    @ExcelProperty("类型名称")
    private String name;
}

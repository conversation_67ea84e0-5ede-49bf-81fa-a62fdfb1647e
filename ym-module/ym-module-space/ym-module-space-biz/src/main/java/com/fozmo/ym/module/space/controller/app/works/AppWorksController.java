package com.fozmo.ym.module.space.controller.app.works;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksSaveReqVO;
import com.fozmo.ym.module.space.controller.app.works.vo.*;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.service.works.WorksService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP-空间模块-作品部分")
@RestController
@RequestMapping("/space/works")
@Validated
public class AppWorksController {

    @Resource
    private WorksService worksService;

    @PostMapping("/create")
    @Operation(summary = "创建作品", description = "用户单次创建作品使用")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Long> createWorks(@Valid @RequestBody AppWorksSaveReqVO createReqVO) {
        WorksSaveReqVO reqVO = BeanUtils.toBean(createReqVO, WorksSaveReqVO.class);
        return success(worksService.createWorks(reqVO));
    }

    @GetMapping("/createCheck")
    @Operation(summary = "创建作品前检查", description = "用户创建作品前检查，是否允许创建")
    @PermitAll
    @ApiAccessLog
    public CommonResult<WorksCheckVO> createCheck() {
        return success(worksService.createCheck());
    }

    @PostMapping("/batchCreate")
    @Operation(summary = "批量创建作品", description = "用户批量创建作品使用")
    @PermitAll
    @ApiAccessLog
    public CommonResult<List<BatchSaveDTO>> batchCreate(@Valid @RequestBody AppWorksBatchSaveReqVO createReqVO) {
        
        if (CollUtil.isNotEmpty(createReqVO.getBatchSaveList())){
//            List<WorksDO> reqVO = BeanUtils.toBean(createReqVO.getBatchSaveList(), WorksDO.class);
            return success(worksService.batchCreate(createReqVO.getBatchSaveList()));
        }
        return success(new ArrayList<>());
        
    }

    @PutMapping("/update")
    @Operation(summary = "修改作品", description = "用户修改作品使用")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> updateWorks(@Valid @RequestBody AppWorksSaveReqVO updateReqVO) {
        WorksSaveReqVO reqVO = BeanUtils.toBean(updateReqVO, WorksSaveReqVO.class);
        worksService.updateWorks(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除作品", description = "用户删除作品使用,作品立即删除，有专门的删除队列执行后续关联数据删除")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> deleteWorks(@RequestParam("id") Long id) {
        worksService.deleteWorks(id);
        return success(true);
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除作品", description = "用户批量删除作品使用,作品立即删除，有专门的删除队列执行后续关联数据删除")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> batchDelete(@RequestBody WorksBatchDeleteVO deleteVO) {
        worksService.batchDelete(deleteVO.getIds());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得作品详情", description = "用户获得作品详情使用-作品账户数据、权益、评论、点赞、空间占用等")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppWorksPageRespVO> getWorks(@RequestParam("id") Long id) {
        WorksDO works = worksService.getWorksInfo(id);
        return success(BeanUtils.toBean(works, AppWorksPageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "作品分页查询", description = "用户作品分页查询，需要指定账户数据，不传则查自己")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppWorksPageRespVO>> getWorksPage(@Valid AppWorksPageReqVO pageReqVO) {
        WorksPageReqVO reqVO = BeanUtils.toBean(pageReqVO, WorksPageReqVO.class);
        if (ObjectUtil.isEmpty(reqVO.getAccountId())) {
            reqVO.setAccountId( WebFrameworkUtils.getLoginUserId());
        }
        PageResult<WorksDO> pageResult = worksService.getAccountWorksPage(reqVO);
        
        return success(BeanUtils.toBean(pageResult, AppWorksPageRespVO.class));
    }

    @GetMapping("/queryMyPage")
    @Operation(summary = "作品分页查询-查询他人作品和他人指定空间挂载作品", description = "用户作品分页查询，需要指定账户数据，传入spaceId时 查询spaceId下的所有作品")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<AppWorksPageRespVO>> queryMyPage(@Valid AppMyPageReqVO pageReqVO) {

        PageResult<WorksDO> pageResult = worksService.queryMyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppWorksPageRespVO.class));
    }


}
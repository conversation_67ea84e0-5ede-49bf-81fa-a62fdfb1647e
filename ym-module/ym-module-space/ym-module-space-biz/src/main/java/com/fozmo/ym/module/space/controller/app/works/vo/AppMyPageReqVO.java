package com.fozmo.ym.module.space.controller.app.works.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 空间作品分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppMyPageReqVO extends PageParam {

    @Schema(description = "空间id")
    private Long spaceId;

    @Schema(description = "查询他人必传，不传查自己")
    private Long accountId;


}

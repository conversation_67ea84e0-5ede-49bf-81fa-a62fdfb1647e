package com.fozmo.ym.module.space.controller.app.works.vo;


import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
@Schema(description = "管理后台 - 空间作品分页 Request VO")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWorksPageReqVO extends PageParam {

    private String worksName;

    @Schema(description = "挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字", example = "2")
    private Integer pointType;
    
    @Schema(description = "查询他人必传，不传查自己")
    private Long accountId;


}


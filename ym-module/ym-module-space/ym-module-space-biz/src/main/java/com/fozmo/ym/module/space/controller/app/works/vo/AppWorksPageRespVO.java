package com.fozmo.ym.module.space.controller.app.works.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fozmo.ym.module.space.api.dto.WorksUsageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "App - 空间作品 Response VO")
@Accessors(chain = true)
@Data
@ExcelIgnoreUnannotated
public class AppWorksPageRespVO {

    @Schema(description = "id", example = "27445")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "作品状态： 0初始化 1布置  9占用状态", example = "2")
    @ExcelProperty("作品状态： 0初始化 1布置  9占用状态")
    private Integer worksStatus;

    @Schema(description = "高精度路径", example = "https://www.iocoder.cn")
    @ExcelProperty("高精度路径")
    private String hignUrl;

    @Schema(description = "标精路径", example = "https://www.iocoder.cn")
    @ExcelProperty("标精路径")
    private String lowUrl;

    @Schema(description = "创建人", example = "31674")
    @ExcelProperty("创建人")
    private Long accountId;

    @Schema(description = "挂载点")
    @ExcelProperty("挂载点")
    private Integer point;

    @Schema(description = "挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字", example = "2")
    @ExcelProperty("挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字")
    private Integer pointType;

    @Schema(description = "文本内容")
    @ExcelProperty("文本内容")
    private String textContent;
    
    @Schema(description = "中文名称", example = "30195")
    private String worksCnName;
    
    @Schema(description = "英文名称", example = "30195")
    private String worksEnName;
    
    @Schema(description = "作品封面", example = "30195")
    private String worksCover;

    @Schema(description = "评论数")
    private Long commentCount;
    @Schema(description = "评论状态")
    private Boolean commentStatus;
    @Schema(description = "点赞数")
    private Long likeCount;
    @Schema(description = "点赞状态")
    private Boolean likeStatus;
    @Schema(description = "分享数")
    private Long shareCount;
    @Schema(description = "分享状态")
    private Boolean shareStatus;
    @Schema(description = "收藏数")
    private Long favoriteCount;
    @Schema(description = "收藏状态")
    private Boolean favoriteStatus;
    @Schema(description = "账户名称")
    private String accountName;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "用户头像")
    private String avatar;
    @Schema(description = "权益ID")
    private Long rightsId;
    @Schema(description = "权益名称")
    private String rightsName;

    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;

    private String worksAuthor;
    
    private Integer worksWidth;
    
    private Integer worksHeight;


    private List<WorksUsageDTO> usage;
    
    private Integer usageCount;

    private Integer sort;

    private boolean fansStatus;
    
    
}


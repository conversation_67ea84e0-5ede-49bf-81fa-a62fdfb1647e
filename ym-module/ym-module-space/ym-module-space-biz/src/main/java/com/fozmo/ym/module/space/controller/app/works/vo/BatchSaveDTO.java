package com.fozmo.ym.module.space.controller.app.works.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BatchSaveDTO {
	@Schema(description = "挂载点")
	private Integer pointType;
	@Schema(description = "高清URL")
	private String hignUrl;
	@Schema(description = "标清URL")
	private String lowUrl;
	@Schema(description = "作者")
	private String worksAuthor;
	@Schema(description = "作品长度")
	private Integer worksWidth;
	@Schema(description = "作品宽度")
	private Integer worksHeight;
	@Schema(description = "作品封面")
	private String worksCover;

	@Schema(description = "返回创建状态")
	private boolean createStatus;

	@Schema(description = "返回创建返回")
	private String createMsg;
	@Schema(description = "返回创建id")
	private Long id;
	@Schema(description = "返回创建名称")
	private String worksName;
}

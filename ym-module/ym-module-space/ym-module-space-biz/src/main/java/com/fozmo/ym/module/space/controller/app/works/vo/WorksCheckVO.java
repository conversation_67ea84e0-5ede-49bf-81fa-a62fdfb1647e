package com.fozmo.ym.module.space.controller.app.works.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WorksCheckVO {

	@Schema(description = "个人可使用数量")
	private Integer totalNum;
	
	@Schema(description = "已使用数量")
	private Integer currentNum;
	
	@Schema(description = "剩余可用数量")
	private Integer availableNum;
	
	@Schema(description = "权限Id")
	private Long rightsId;
}

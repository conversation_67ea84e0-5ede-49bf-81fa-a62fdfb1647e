package com.fozmo.ym.module.space.controller.app.works.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class WorksInfoVO {
    @Schema(description = "id", example = "27445")
    private Long id;

    @Schema(description = "作品状态： 0初始化 1布置  9占用状态", example = "2")
    private Integer worksStatus;

    @Schema(description = "高精度路径", example = "https://www.iocoder.cn")
    private String hignUrl;

    @Schema(description = "标精路径", example = "https://www.iocoder.cn")
    private String lowUrl;

    @Schema(description = "创建人", example = "31674")
    private Long accountId;

    @Schema(description = "挂载点")
    private Integer point;

    @Schema(description = "挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字", example = "2")
    private Integer pointType;

    @Schema(description = "文本内容")
    private String textContent;
    @Schema(description = "占用作品空间")
    private List<WorksUsageVO> usage;
    @Schema(description = "占用数量")
    private Integer usageCount;

    @Schema(description = "中文名称", example = "30195")
    private String worksCnName;
    @Schema(description = "英文名称", example = "30195")
    private String worksEnName;
    @Schema(description = "作品封面", example = "30195")
    private String worksCover;

    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;

    private String worksAuthor;
    
    private Integer worksWidth;
    
    private Integer worksHeight;
    
    
    
}

package com.fozmo.ym.module.space.controller.app.works.vo;

import com.fozmo.ym.module.space.api.dto.WorksInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class WorksVO {
    @Schema(description = "个人可使用数量")
    private Integer totalNum;

    @Schema(description = "已使用数量")
    private Integer currentNum;

    @Schema(description = "剩余可用数量")
    private Integer availableNum;

    @Schema(description = "作品列表")
    private List<WorksInfoDTO> worksInfoVOList;

    @Schema(description = "个人作品数量-前端分页使用")
    private Integer total;
}

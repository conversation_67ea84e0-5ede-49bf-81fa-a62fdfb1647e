package com.fozmo.ym.module.space.core.mq.space;


import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import com.fozmo.ym.module.social.api.space.SpaceCommentApi;
import com.fozmo.ym.module.social.api.space.SpaceLikeApi;
import com.fozmo.ym.module.social.api.space.SpaceShareApi;
import com.fozmo.ym.module.space.core.mq.space.message.SpaceDeleteMessage;
import com.fozmo.ym.module.space.service.editing.EditingService;
import com.fozmo.ym.module.space.service.hot.SpaceHotService;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.LOCK_SPACE_DELETE_KEY;

/**
 *  空间删除队列消费者
 */

@Slf4j
@Component
public class SpaceDeleteConsumer extends AbstractRedisStreamMessageListener<SpaceDeleteMessage> {

	// 使用线程池实现
	private final ExecutorService deleteExecutor = new ThreadPoolExecutor(
			4, // 核心线程数 (根据实际情况调整)
			16, // 最大线程数 (根据实际情况调整)
			60L, TimeUnit.SECONDS,
			new LinkedBlockingQueue<>(200),
			new ThreadFactory() {
				private final AtomicInteger threadCount = new AtomicInteger(1);

				@Override
				public Thread newThread(Runnable r) {
					return new Thread(r, "space-del-pool-" + threadCount.getAndIncrement());
				}
			},
			new ThreadPoolExecutor.CallerRunsPolicy()
	);
	@Resource
	private RedissonClient redissonClient;
	@Resource
	@Lazy
	private SpaceCommentApi spaceCommentApi;
	@Resource
	@Lazy
	private SpaceLikeApi spaceLikeApi;
	@Resource
	@Lazy
	private SpaceShareApi spaceShareApi;
	@Resource
	@Lazy
	private EditingService editingService;
	@Resource
	@Lazy
	private MountWorksService mountWorksService;
	@Resource
	@Lazy
	private SpaceHotService spaceHotService;

	@PreDestroy
	public void destroy() {
		log.info("Shutting down space delete executor");
		deleteExecutor.shutdown();
		try {
			if (!deleteExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
				log.warn("Force shutdown space delete executor");
				deleteExecutor.shutdownNow();
			}
		} catch (InterruptedException e) {
			deleteExecutor.shutdownNow();
			Thread.currentThread().interrupt();
		}
	}

	@Override
	public void onMessage(SpaceDeleteMessage message) {
		Long spaceId = message.getSpaceId();
		String lockKey = LOCK_SPACE_DELETE_KEY + spaceId;
		RLock lock = redissonClient.getLock(lockKey);

		// 尝试获取锁（带超时）
		try {
			if (!lock.tryLock(0, 30, TimeUnit.SECONDS)) {
				log.warn("空间删除处理中，跳过. spaceId: {}", spaceId);
				return;
			}
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			log.warn("锁获取中断. spaceId: {}", spaceId);
			return;
		}

		try {
			log.info("开始空间删除处理. spaceId: {}", spaceId);
			long startTime = System.currentTimeMillis();
			List<CompletableFuture<Void>> futures = Arrays.asList(
					// 删除空间评论
					runAsync(() -> spaceCommentApi.deleteBySpaceId(spaceId)),
					// 删除空间点赞
					runAsync(() -> spaceLikeApi.deleteBySpaceId(spaceId)),
					// 删除空间分享
					runAsync(() -> spaceShareApi.deleteBySpaceId(spaceId)),
					// 删除编辑信息
					runAsync(() -> editingService.deleteAllEditing(spaceId)),
					// 删除挂载信息
					runAsync(() -> mountWorksService.deleteAllMount(spaceId)),
					// 删除热门空间
					runAsync(() -> spaceHotService.deleteHotSpace(spaceId))
			);

			// 组合所有任务并等待完成（带超时）
			CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
					.exceptionally(ex -> {
						log.error("空间删除子任务异常. spaceId: {}", spaceId, ex);
						return null;
					});

			// 设置全局超时（30秒）
			try {
				allTasks.get(30, TimeUnit.SECONDS);
			} catch (TimeoutException e) {
				log.error("空间删除超时. spaceId: {}", spaceId, e);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				log.warn("空间删除中断. spaceId: {}", spaceId, e);
			} catch (ExecutionException e) {
				log.error("空间删除执行异常. spaceId: {}", spaceId, e);
			}

			long duration = System.currentTimeMillis() - startTime;
			if (duration > 5000) {
				log.warn("空间删除完成但耗时较长. spaceId: {}, 耗时: {}ms", spaceId, duration);
			} else {
				log.info("空间删除完成. spaceId: {}, 耗时: {}ms", spaceId, duration);
			}
		} catch (Exception e) {
			log.error("空间删除处理异常. spaceId: {}", spaceId, e);
		} finally {
			try {
				if (lock.isHeldByCurrentThread() && lock.isLocked()) {
					lock.unlock();
				}
			} catch (IllegalMonitorStateException ex) {
				log.warn("锁释放异常. spaceId: {}", spaceId, ex);
			}
		}
	}

	private CompletableFuture<Void> runAsync(Runnable task) {
		return CompletableFuture.runAsync(
				() -> {
					try {
						long start = System.currentTimeMillis();
						task.run();
						long duration = System.currentTimeMillis() - start;
						if (duration > 1000) {
							log.debug("子任务执行耗时: {}ms, task: {}", duration, task.getClass().getSimpleName());
						}
					} catch (Exception e) {
						log.error("子任务执行失败", e);
						throw new CompletionException(e);
					}
				},
				deleteExecutor
		);
	}
}
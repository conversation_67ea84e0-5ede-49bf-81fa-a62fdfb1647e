package com.fozmo.ym.module.space.core.mq.space;

import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.space.core.mq.space.message.SpaceDeleteMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
/***
 * 空间删除队列
 */
@Slf4j
@Component
public class SpaceDeleteProduce {

	@Resource
	private RedisMQTemplate redisMQTemplate; // 重点：注入 RedisMQTemplate 对象

	public void sendSpaceDelete(Long spaceId,Long accountId) {
		SpaceDeleteMessage message = new SpaceDeleteMessage().setSpaceId(spaceId).setAccountId(accountId);
		redisMQTemplate.send(message);
	}
}

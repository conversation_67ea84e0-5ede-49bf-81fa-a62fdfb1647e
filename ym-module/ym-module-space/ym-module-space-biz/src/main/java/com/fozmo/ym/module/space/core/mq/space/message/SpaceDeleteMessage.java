package com.fozmo.ym.module.space.core.mq.space.message;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
/***
 * 空间删除队列
 */
public class SpaceDeleteMessage extends AbstractRedisStreamMessage {

	private Long spaceId;
	
	private Long accountId;
	
}

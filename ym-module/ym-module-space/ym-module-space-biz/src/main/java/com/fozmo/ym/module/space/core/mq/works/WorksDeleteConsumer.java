package com.fozmo.ym.module.space.core.mq.works;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import com.fozmo.ym.module.social.api.works.WorksCommentApi;
import com.fozmo.ym.module.social.api.works.WorksLikeApi;
import com.fozmo.ym.module.social.api.works.WorksShareApi;
import com.fozmo.ym.module.space.core.mq.works.message.WorksDeleteMessage;
import com.fozmo.ym.module.space.service.editing.EditingService;
import com.fozmo.ym.module.space.service.hot.SpaceHotService;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.LOCK_WORKS_DELETE_KEY;

/**
 * 作品删除队列消费者
 */

@Slf4j
@Component
public class WorksDeleteConsumer extends AbstractRedisStreamMessageListener<WorksDeleteMessage> {
    // 使用线程池实现
    private final ExecutorService deleteWorksExecutor = new ThreadPoolExecutor(
            4, // 核心线程数 (根据实际情况调整)
            16, // 最大线程数 (根据实际情况调整)
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(200),
            new ThreadFactory() {
                private final AtomicInteger threadCount = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "works-del-pool-" + threadCount.getAndIncrement());
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
    );


    @Resource
    private RedissonClient redissonClient;

    @Resource
    @Lazy
    private WorksCommentApi worksCommentApi;

    @Resource
    @Lazy
    private WorksLikeApi worksLikeApi;

    @Resource
    @Lazy
    private WorksShareApi worksShareApi;

    @Resource
    @Lazy
    private EditingService editingService;
    @Resource
    @Lazy
    private MountWorksService mountWorksService;
    @Resource
    @Lazy
    private SpaceHotService spaceHotService;

    @PreDestroy
    public void destroy() {
        log.info("Shutting down works delete executor");
        deleteWorksExecutor.shutdown();
        try {
            if (!deleteWorksExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("Force shutdown works delete executor");
                deleteWorksExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            deleteWorksExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 处理消息
     *
     * @param message 消息
     */
    @Override
    public void onMessage(WorksDeleteMessage message) {
        Long worksId = message.getWorksId();

        String lockKey = LOCK_WORKS_DELETE_KEY + worksId;

        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock(0, 30, TimeUnit.SECONDS)) {
                log.warn("作品删除处理中，跳过. worksId: {}", worksId);
                return;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("锁获取中断. worksId: {}", worksId);
            return;
        }
        try {
            log.info("开始作品删除处理. worksId: {}", worksId);
            long startTime = System.currentTimeMillis();
            List<CompletableFuture<Void>> futures = Arrays.asList(
                    // 删除作品评论
                    runAsync(() -> worksCommentApi.deleteByWorksId(worksId)),
                    // 删除作品点赞
                    runAsync(() -> worksLikeApi.deleteByWorksId(worksId)),
                    // 删除作品分享
                    runAsync(() -> worksShareApi.deleteByWorksId(worksId)),
                    // 删除 作品挂载
                    runAsync(() -> mountWorksService.deleteByWorksId(worksId)),
                    // 删除作品编辑
                    runAsync(() -> editingService.deleteByWorkId(worksId)),
                    // 删除作品投票信息 TODO

                    // 删除热点作品
                    runAsync(() -> spaceHotService.deleteHotWork(worksId))

            );
            // 组合所有任务并等待完成（带超时）
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .exceptionally(ex -> {
                        log.error("作品删除子任务异常. worksId: {}", worksId, ex);
                        return null;
                    });

            // 设置全局超时（30秒）
            try {
                allTasks.get(30, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("作品删除超时. worksId: {}", worksId, e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("作品删除中断. worksId: {}", worksId, e);
            } catch (ExecutionException e) {
                log.error("删除执行异常. spaceId: {}", worksId, e);
            }

            long duration = System.currentTimeMillis() - startTime;
            if (duration > 5000) {
                log.warn("作品删除完成但耗时较长. worksId: {}, 耗时: {}ms", worksId, duration);
            } else {
                log.info("作品删除完成. worksId: {}, 耗时: {}ms", worksId, duration);
            }
        } catch (Exception e) {
            log.error("作品删除处理异常. worksId: {}", worksId, e);
        } finally {
            try {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            } catch (IllegalMonitorStateException ex) {
                log.warn("锁释放异常. worksId: {}", worksId, ex);
            }
        }
    }

    private CompletableFuture<Void> runAsync(Runnable task) {
        return CompletableFuture.runAsync(
                () -> {
                    try {
                        long start = System.currentTimeMillis();
                        task.run();
                        long duration = System.currentTimeMillis() - start;
                        if (duration > 1000) {
                            log.debug("子任务执行耗时: {}ms, task: {}", duration, task.getClass().getSimpleName());
                        }
                    } catch (Exception e) {
                        log.error("子任务执行失败", e);
                        throw new CompletionException(e);
                    }
                },
                deleteWorksExecutor
        );
    }
}
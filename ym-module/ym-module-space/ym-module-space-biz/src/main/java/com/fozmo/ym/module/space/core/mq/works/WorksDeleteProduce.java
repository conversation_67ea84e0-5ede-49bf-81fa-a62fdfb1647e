package com.fozmo.ym.module.space.core.mq.works;

import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.space.core.mq.works.message.WorksDeleteMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/***
 * 作品删除队列
 */
@Slf4j
@Component
public class WorksDeleteProduce {

    @Resource
    private RedisMQTemplate redisMQTemplate; // 重点：注入 RedisMQTemplate 对象

    public void sendSpaceDelete(Long worksId, Long accountId) {
        WorksDeleteMessage message = new WorksDeleteMessage().setWorksId(worksId).setAccountId(accountId);
        redisMQTemplate.send(message);
    }
}

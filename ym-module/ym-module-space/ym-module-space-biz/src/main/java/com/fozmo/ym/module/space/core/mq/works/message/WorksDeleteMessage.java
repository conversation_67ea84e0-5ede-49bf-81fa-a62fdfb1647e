package com.fozmo.ym.module.space.core.mq.works.message;

import com.fozmo.ym.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
/***
 * 空间删除队列
 */
public class WorksDeleteMessage extends AbstractRedisStreamMessage {

    private Long worksId;

    private Long accountId;

}

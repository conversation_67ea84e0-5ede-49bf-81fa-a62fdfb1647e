package com.fozmo.ym.module.space.dal.dataobject.file;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("space_file")
@KeySequence("space_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceFileDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long fileId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private Integer fileSize;
    /**
     * 文件类型id
     */
    private Long fileTypeId;
    /**
     * 说明
     */
    private String description;
    /**
     * 类型编码
     */
    private String fileTypeCode;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件后缀
     */
    private String fileSuffix;
    /**
     * 文件状态
     */
    private Integer fileStatus;
    /**
     * 文件属性
     */
    private String fileAttribute;
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    /**
     * 文件桶
     */
    private String fileBucket;
    /**
     * 文件url
     */
    private String fileUrl;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    @TableField(exist = false)
    private String uploadFileUrl;
    @TableField(exist = false)
    private String  manageUrl;

    private Long configId;
    @TableField(exist = false)
    private Boolean uploadStatus;

    private Long fileUpload;

    private String fileMd5;
    
    private Long rightsId;
}

package com.fozmo.ym.module.space.dal.dataobject.filetype;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 空间资源类型 DO
 *
 * <AUTHOR>
 */
@TableName("space_file_type")
@KeySequence("space_file_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileTypeDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long typeId;
    /**
     * 资源类别中文名称
     */
    private String typeCnName;
    /**
     * 资源类别英文名称
     */
    private String typeEnName;
    /**
     * 空间编码
     */
    private String typeCode;
    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;
    /**
     * 状态 0 初始化 1启用 2 停用 3 删除
     */
    private Integer typeStatus;
    /**
     * 创建标识 0系统初始化 1 用户自定义 2 其他
     */
    private Integer createDefault;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

}
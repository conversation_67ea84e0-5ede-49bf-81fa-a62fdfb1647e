package com.fozmo.ym.module.space.dal.dataobject.mount;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 空间挂在作品 DO
 *
 * <AUTHOR>
 */
@TableName("space_mount_works")
@KeySequence("space_mount_works_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MountWorksDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    private Long spaceId;

    private Integer point;

    private Integer mountSort;

    private Long mountId;

    private Integer mountType;
    @TableField(exist = false)
    private Long accountId;

    private String extraData;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

}
package com.fozmo.ym.module.space.dal.dataobject.space;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 空间 DO
 *
 * <AUTHOR>
 */
@TableName("space")
@KeySequence("space_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 空间模板id
     */
    /**
     * loading 使用
     */
    private String spaceLoading;

    private Long spaceTemplateId;
    /**
     * 空间中文名称
     */
    private String spaceCnName;
    /**
     * 空间英文名称
     */
    private String spaceEnName;
    /**
     * 空间码值
     */
    private String spaceCode;
    /**
     * 空间类型
     */
    private Long spaceTypeId;
    /**
     * 2d挂载点
     */
    private Integer spaceNum2;
    /**
     * 3d挂载点
     */
    private Integer spaceNum3;
    /**
     * 账户id
     */
    private Long accountId;
    /**
     * 账户编码
     */
    private String accountCode;
    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;
    /**
     * 状态 0 初始化 1启用 2 停用 3 删除
     */
    private Integer spaceStatus;
    /**
     * 空间封面
     */
    private String spaceCover;
    /**
     * 一键复制 0 不允许 1运行
     */
    private Integer copyFlag;
    /**
     * 0 全部可见 
     */
    private Integer privacyFlag;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建用户名称
     */
    private String creator;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    /**
     * 评论数
     */
    @TableField(exist = false)
    private Long commentCount;

    /**
     * 点赞数
     */
    @TableField(exist = false)
    private Boolean commentStatus;
    @TableField(exist = false)
    private Long likeCount;
    @TableField(exist = false)
    private Boolean likeStatus;
    @TableField(exist = false)
    private Long shareCount;
    @TableField(exist = false)
    private Boolean shareStatus;

    @TableField(exist = false)
    private Boolean fansStatus;
    @TableField(exist = false)
    private Long favoriteCount;
    @TableField(exist = false)
    private Boolean favoriteStatus;
    @TableField(exist = false)
    private String accountName;
    @TableField(exist = false)
    private String nickname;
    @TableField(exist = false)
    private String avatar;
    @TableField(exist = false)
    private Long rightsId;
    @TableField(exist = false)
    private String rightsName;
    @TableField(exist = false)
    private Long worksCount;
    @TableField(exist = false)
    private Long filesCount;
    private String sponsor;
    
    private LocalDateTime exhibitionStartTime;
    
    private LocalDateTime exhibitionEndTime;
    
    private Integer exhibitionTimeStatus;

}
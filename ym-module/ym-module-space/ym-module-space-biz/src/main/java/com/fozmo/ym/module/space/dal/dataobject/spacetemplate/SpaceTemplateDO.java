package com.fozmo.ym.module.space.dal.dataobject.spacetemplate;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 空间模板 DO
 *
 * <AUTHOR>
 */
@TableName("space_template")
@KeySequence("space_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceTemplateDO extends TenantBaseDO {

    /**
     * 模板id
     */
    @TableId
    private Long templateId;
    /**
     * 模板英文名称
     */
    private String templateEnName;
    /**
     * 模板中文名称
     */
    private String templateCnName;
    /**
     * 模板码值
     */
    private String templateCode;
    /**
     * 模板类型id
     */
    private Long templateTypeId;
    /**
     * 模板类型码值
     */
    private String templateTypeCode;
    /**
     * 2d挂载点数
     */
    private Integer pointNum2;
    /**
     * 3d挂载点数
     */
    private Integer pointNum3;
    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;
    /**
     * 状态 0 初始化 1启用 2 停用 3 删除
     */
    private Integer templateStatus;
    /**
     * 空间封面
     */
    private String templateCover;
    /**
     * 模板地址
     */
    private String templateRes;
    /**
     * 文本内容
     */
    private String templateConfig;
    /**
     * 权益id
     */
    private Long rightsId;
    /**
     * 0系统初始化 1 用户自定义 2 其他
     */
    private Integer createDefault;
    /**
     * template_tags
     */
    private String templateTags;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建用户名称
     */
    private String creator;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    @TableField(exist = false)
    private List<SpaceTemplateTypeDO> typeList;
    @TableField(exist = false)
    private List<SpaceTemplateTagDO> tagList;
    

}
package com.fozmo.ym.module.space.dal.dataobject.templateaccount;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 指定账户模板 DO
 *
 * <AUTHOR>
 */
@TableName("space_template_account")
@KeySequence("space_template_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateAccountDO extends BaseDO {

    /**
     * 账户id
     */
    private Long accountId;
    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 空间id
     */
    private Long templateId;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

}
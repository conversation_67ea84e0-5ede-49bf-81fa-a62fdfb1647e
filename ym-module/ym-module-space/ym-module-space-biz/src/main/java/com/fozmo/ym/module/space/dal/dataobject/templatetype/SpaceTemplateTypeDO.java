package com.fozmo.ym.module.space.dal.dataobject.templatetype;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 空间模板类型 DO
 *
 * <AUTHOR>
 */
@TableName("space_template_type")
@KeySequence("space_template_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceTemplateTypeDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 空间模板名称
     */
    private String name;
    /**
     * 创建人id
     */
    private Long createId;

    private LocalDate createData;
    /**
     * 创建用户名称
     */
    private String creator;
    /**
     * 更新人
     */
    private Long updateId;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

}
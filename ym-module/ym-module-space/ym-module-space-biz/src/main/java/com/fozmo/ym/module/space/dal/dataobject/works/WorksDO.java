package com.fozmo.ym.module.space.dal.dataobject.works;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import com.fozmo.ym.module.space.api.dto.WorksUsageDTO;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 空间作品 DO
 *
 * <AUTHOR>
 */
@TableName("space_works")
@KeySequence("space_works_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorksDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 作品状态： 0初始化 1布置  9占用状态
     */
    private Integer worksStatus;
    /**
     * 高精度路径
     */
    private String hignUrl;
    /**
     * 标精路径
     */
    private String lowUrl;
    /**
     * 创建人
     */
    private Long accountId;
    /**
     * 挂载点
     */
    private Integer point;
    /**
     * 挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字
     */
    private Integer pointType;
    /**
     * 文本内容
     */
    private String textContent;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    
    private String worksCnName;
    
    private String worksEnName;
    
    private String worksCover;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;


    private String worksAuthor;
    
    private Integer worksWidth;
    
    private Integer worksHeight;

    @TableField(exist = false)
    private Long commentCount;
    @TableField(exist = false)
    private Boolean commentStatus;
    @TableField(exist = false)
    private Long likeCount;
    @TableField(exist = false)
    private Boolean likeStatus;
    @TableField(exist = false)
    private Long shareCount;
    @TableField(exist = false)
    private Boolean shareStatus;
    @TableField(exist = false)
    private Long favoriteCount;
    @TableField(exist = false)
    private Boolean favoriteStatus;
    @TableField(exist = false)
    private String accountName;
    @TableField(exist = false)
    private String nickname;
    @TableField(exist = false)
    private String avatar;
    @TableField(exist = false)
    private Long rightsId;
    @TableField(exist = false)
    private String rightsName;

    @TableField(exist = false)
    private List<WorksUsageDTO> usage;
    @TableField(exist = false)
    private Integer usageCount;
    @TableField(exist = false)
    private Integer sort;
    @TableField(exist = false)
    private boolean fansStatus;

}

package com.fozmo.ym.module.space.dal.mysql.editing;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.editing.vo.EditingPageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.editing.EditingDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 空间编辑 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EditingMapper extends BaseMapperX<EditingDO> {

    default PageResult<EditingDO> selectPage(EditingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EditingDO>()
                .eqIfPresent(EditingDO::getSpaceId, reqVO.getSpaceId())
                .eqIfPresent(EditingDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(EditingDO::getCreateData, reqVO.getCreateData())
                .eqIfPresent(EditingDO::getUpdateId, reqVO.getUpdateId())
                .eqIfPresent(EditingDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(EditingDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(EditingDO::getId));
    }

    default List<EditingDO> selectBySpaceId(Long spaceId) {
        return selectList(EditingDO::getSpaceId, spaceId, EditingDO::getDeleted, false);
    }
}
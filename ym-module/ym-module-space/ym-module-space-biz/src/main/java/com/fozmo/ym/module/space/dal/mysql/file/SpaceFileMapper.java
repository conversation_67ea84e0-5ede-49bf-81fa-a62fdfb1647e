package com.fozmo.ym.module.space.dal.mysql.file;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.file.vo.FilePageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SpaceFileMapper extends BaseMapperX<SpaceFileDO> {

    default PageResult<SpaceFileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceFileDO>()
                .eqIfPresent(SpaceFileDO::getRightsId, reqVO.getRightsId())
                .inIfPresent(SpaceFileDO::getFileTypeId, reqVO.getFileTypeId())
                .orderByDesc(SpaceFileDO::getFileId));
    }

}

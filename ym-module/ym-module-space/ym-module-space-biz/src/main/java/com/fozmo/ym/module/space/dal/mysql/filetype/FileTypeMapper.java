package com.fozmo.ym.module.space.dal.mysql.filetype;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.filetype.vo.FileTypePageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间资源类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileTypeMapper extends BaseMapperX<FileTypeDO> {

    default PageResult<FileTypeDO> selectPage(FileTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileTypeDO>()
                .eqIfPresent(FileTypeDO::getTypeId, reqVO.getTypeId())
                .likeIfPresent(FileTypeDO::getTypeCnName, reqVO.getTypeCnName())
                .likeIfPresent(FileTypeDO::getTypeEnName, reqVO.getTypeEnName())
                .eqIfPresent(FileTypeDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(FileTypeDO::getCnDescription, reqVO.getCnDescription())
                .eqIfPresent(FileTypeDO::getEnDescription, reqVO.getEnDescription())
                .eqIfPresent(FileTypeDO::getTypeStatus, reqVO.getTypeStatus())
                .eqIfPresent(FileTypeDO::getCreateDefault, reqVO.getCreateDefault())
                .eqIfPresent(FileTypeDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(FileTypeDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(FileTypeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(FileTypeDO::getUpdateId, reqVO.getUpdateId())
                .eqIfPresent(FileTypeDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(FileTypeDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(FileTypeDO::getTypeId));
    }

}
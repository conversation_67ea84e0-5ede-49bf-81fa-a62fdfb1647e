package com.fozmo.ym.module.space.dal.mysql.hot;

import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.module.space.dal.dataobject.hot.SpaceHotDO;
import org.apache.ibatis.annotations.Mapper;
/***
 * 热度表
 */
@Mapper
public interface SpaceHotMapper extends BaseMapperX<SpaceHotDO> {

//    default PageResult<SpaceLikeDO> selectPage(SpaceLikePageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceLikeDO>()
//                .eqIfPresent(SpaceLikeDO::getSpaceId, reqVO.getSpaceId())
//                .eqIfPresent(SpaceLikeDO::getAccountId, reqVO.getAccountId())
//                .likeIfPresent(SpaceLikeDO::getAccountName, reqVO.getAccountName())
//                .betweenIfPresent(SpaceLikeDO::getLikeTime, reqVO.getLikeTime())
//                .eqIfPresent(SpaceLikeDO::getCreateId, reqVO.getCreateId())
//                .likeIfPresent(SpaceLikeDO::getCreator, reqVO.getCreator())
//                .eqIfPresent(SpaceLikeDO::getCreateData, reqVO.getCreateData())
//                .betweenIfPresent(SpaceLikeDO::getCreateTime, reqVO.getCreateTime())
//                .eqIfPresent(SpaceLikeDO::getUpdateId, reqVO.getUpdateId())
//                .likeIfPresent(SpaceLikeDO::getUpdater, reqVO.getUpdater())
//                .eqIfPresent(SpaceLikeDO::getUpdateData, reqVO.getUpdateData())
//                .eqIfPresent(SpaceLikeDO::getTenantCode, reqVO.getTenantCode())
//                .orderByDesc(SpaceLikeDO::getId));
//    }
}

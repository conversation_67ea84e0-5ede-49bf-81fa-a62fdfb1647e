package com.fozmo.ym.module.space.dal.mysql.mountworks;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksPageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.mount.MountWorksDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间挂在作品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MountWorksMapper extends BaseMapperX<MountWorksDO> {

    default PageResult<MountWorksDO> selectPage(MountWorksPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MountWorksDO>()
                .eqIfPresent(MountWorksDO::getMountId, reqVO.getWorksId())
                .eqIfPresent(MountWorksDO::getSpaceId, reqVO.getSpaceId())
                .inIfPresent(MountWorksDO::getMountType,reqVO.getMountTypes())
                .orderByDesc(MountWorksDO::getMountSort));
    }

}
package com.fozmo.ym.module.space.dal.mysql.space;

import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 空间 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceMapper extends BaseMapperX<SpaceDO> {

    default PageResult<SpaceDO> selectPage(SpacePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceDO>()
                .eqIfPresent(SpaceDO::getSpaceTemplateId, reqVO.getSpaceTemplateId())
                .likeIfPresent(SpaceDO::getSpaceCnName, reqVO.getSpaceCnName())
                .likeIfPresent(SpaceDO::getSpaceEnName, reqVO.getSpaceEnName())
                .eqIfPresent(SpaceDO::getSpaceCode, reqVO.getSpaceCode())
                .eqIfPresent(SpaceDO::getSpaceTypeId, reqVO.getSpaceTypeId())
                .eqIfPresent(SpaceDO::getSpaceNum2, reqVO.getSpaceNum2())
                .eqIfPresent(SpaceDO::getSpaceNum3, reqVO.getSpaceNum3())
                .eqIfPresent(SpaceDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(SpaceDO::getAccountCode, reqVO.getAccountCode())
                .eqIfPresent(SpaceDO::getCnDescription, reqVO.getCnDescription())
                .eqIfPresent(SpaceDO::getEnDescription, reqVO.getEnDescription())
                .eqIfPresent(SpaceDO::getSpaceStatus, reqVO.getSpaceStatus())
                .eqIfPresent(SpaceDO::getSpaceCover, reqVO.getSpaceCover())
                .eqIfPresent(SpaceDO::getCopyFlag, reqVO.getCopyFlag())
                .eqIfPresent(SpaceDO::getPrivacyFlag, reqVO.getPrivacyFlag())
                .eqIfPresent(SpaceDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceDO::getTenantCode, reqVO.getTenantCode())
                .inIfPresent(SpaceDO::getId,reqVO.getSpaceIds())
                .orderByDesc(SpaceDO::getId));
    }

    List<SpaceDO> selectSpacePage(SpacePageReqVO reqVO);

}
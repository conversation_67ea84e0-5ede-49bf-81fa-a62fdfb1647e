package com.fozmo.ym.module.space.dal.mysql.spacetemplate;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceTemplateMapper extends BaseMapperX<SpaceTemplateDO> {

    default PageResult<SpaceTemplateDO> selectPage(SpaceTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceTemplateDO>()
                .likeIfPresent(SpaceTemplateDO::getTemplateEnName, reqVO.getTemplateEnName())
                .likeIfPresent(SpaceTemplateDO::getTemplateCnName, reqVO.getTemplateCnName())
                .likeIfPresent(SpaceTemplateDO::getTemplateCode, reqVO.getTemplateCode())
                .eqIfPresent(SpaceTemplateDO::getTemplateTypeId, reqVO.getTemplateTypeId())
                .likeIfPresent(SpaceTemplateDO::getTemplateTypeCode, reqVO.getTemplateTypeCode())
                
                .eqIfPresent(SpaceTemplateDO::getPointNum2, reqVO.getPointNum2())
                .eqIfPresent(SpaceTemplateDO::getPointNum3, reqVO.getPointNum3())
                .eqIfPresent(SpaceTemplateDO::getCnDescription, reqVO.getCnDescription())
                .eqIfPresent(SpaceTemplateDO::getEnDescription, reqVO.getEnDescription())
                .eqIfPresent(SpaceTemplateDO::getTemplateStatus, reqVO.getTemplateStatus())
                .eqIfPresent(SpaceTemplateDO::getTemplateCover, reqVO.getTemplateCover())
                .eqIfPresent(SpaceTemplateDO::getTemplateRes, reqVO.getTemplateRes())
                .eqIfPresent(SpaceTemplateDO::getTemplateConfig, reqVO.getTemplateConfig())
                .eqIfPresent(SpaceTemplateDO::getRightsId, reqVO.getRightsId())
                .eqIfPresent(SpaceTemplateDO::getCreateDefault, reqVO.getCreateDefault())
                .likeIfPresent(SpaceTemplateDO::getTemplateTags, reqVO.getTemplateTags())
                .eqIfPresent(SpaceTemplateDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceTemplateDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceTemplateDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceTemplateDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceTemplateDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceTemplateDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceTemplateDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceTemplateDO::getTenantCode, reqVO.getTenantCode())
                .inIfPresent(SpaceTemplateDO::getTemplateId,reqVO.getIds())
                .orderByDesc(SpaceTemplateDO::getTemplateId));
    }

}
package com.fozmo.ym.module.space.dal.mysql.tag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.module.space.dal.dataobject.tag.SpaceTagDO;
import org.apache.ibatis.annotations.Mapper;
import com.fozmo.ym.module.space.controller.admin.tag.vo.*;

/**
 * 空间标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceTagMapper extends BaseMapperX<SpaceTagDO> {

    default PageResult<SpaceTagDO> selectPage(SpaceTagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceTagDO>()
                .likeIfPresent(SpaceTagDO::getName, reqVO.getName())
                .eqIfPresent(SpaceTagDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceTagDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceTagDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceTagDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceTagDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceTagDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceTagDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceTagDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SpaceTagDO::getId));
    }

}
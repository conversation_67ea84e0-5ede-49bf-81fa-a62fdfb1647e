package com.fozmo.ym.module.space.dal.mysql.templateaccount;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.templateaccount.vo.TemplateAccountPageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templateaccount.TemplateAccountDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 指定账户模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TemplateAccountMapper extends BaseMapperX<TemplateAccountDO> {

    default PageResult<TemplateAccountDO> selectPage(TemplateAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TemplateAccountDO>()
                .eqIfPresent(TemplateAccountDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(TemplateAccountDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(TemplateAccountDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(TemplateAccountDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(TemplateAccountDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(TemplateAccountDO::getUpdateId, reqVO.getUpdateId())
                .eqIfPresent(TemplateAccountDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(TemplateAccountDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(TemplateAccountDO::getId));
    }

}
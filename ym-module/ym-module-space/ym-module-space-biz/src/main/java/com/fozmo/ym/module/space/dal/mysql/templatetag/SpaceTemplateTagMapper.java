package com.fozmo.ym.module.space.dal.mysql.templatetag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.templatetag.vo.SpaceTemplateTagPageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间模板标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceTemplateTagMapper extends BaseMapperX<SpaceTemplateTagDO> {

    default PageResult<SpaceTemplateTagDO> selectPage(SpaceTemplateTagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceTemplateTagDO>()
                .likeIfPresent(SpaceTemplateTagDO::getName, reqVO.getName())
                .eqIfPresent(SpaceTemplateTagDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceTemplateTagDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceTemplateTagDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceTemplateTagDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceTemplateTagDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceTemplateTagDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceTemplateTagDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceTemplateTagDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SpaceTemplateTagDO::getId));
    }

}
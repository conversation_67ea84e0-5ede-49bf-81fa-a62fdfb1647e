package com.fozmo.ym.module.space.dal.mysql.templatetype;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间模板类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceTemplateTypeMapper extends BaseMapperX<SpaceTemplateTypeDO> {

    default PageResult<SpaceTemplateTypeDO> selectPage(SpaceTemplateTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceTemplateTypeDO>()
                .likeIfPresent(SpaceTemplateTypeDO::getName, reqVO.getName())
                .eqIfPresent(SpaceTemplateTypeDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceTemplateTypeDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceTemplateTypeDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceTemplateTypeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceTemplateTypeDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceTemplateTypeDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceTemplateTypeDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceTemplateTypeDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SpaceTemplateTypeDO::getId));
    }

}
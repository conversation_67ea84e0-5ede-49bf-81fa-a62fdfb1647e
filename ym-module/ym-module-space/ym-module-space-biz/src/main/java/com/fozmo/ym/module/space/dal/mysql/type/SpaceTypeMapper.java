package com.fozmo.ym.module.space.dal.mysql.type;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.type.vo.SpaceTypePageReqVO;
import com.fozmo.ym.module.space.dal.dataobject.type.SpaceTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 空间类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpaceTypeMapper extends BaseMapperX<SpaceTypeDO> {

    default PageResult<SpaceTypeDO> selectPage(SpaceTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpaceTypeDO>()
                .likeIfPresent(SpaceTypeDO::getName, reqVO.getName())
                .eqIfPresent(SpaceTypeDO::getCreateId, reqVO.getCreateId())
                .likeIfPresent(SpaceTypeDO::getCreator, reqVO.getCreator())
                .eqIfPresent(SpaceTypeDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(SpaceTypeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SpaceTypeDO::getUpdateId, reqVO.getUpdateId())
                .likeIfPresent(SpaceTypeDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(SpaceTypeDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(SpaceTypeDO::getTenantCode, reqVO.getTenantCode())
                .orderByDesc(SpaceTypeDO::getId));
    }

}
package com.fozmo.ym.module.space.dal.mysql.works;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.mybatis.core.mapper.BaseMapperX;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
import com.fozmo.ym.module.space.controller.app.works.vo.SpaceMountDTO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 空间作品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorksMapper extends BaseMapperX<WorksDO> {

    default PageResult<WorksDO> selectPage(WorksPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WorksDO>()
                .eqIfPresent(WorksDO::getWorksStatus, reqVO.getWorksStatus())
                .likeIfPresent(WorksDO::getWorksCnName, reqVO.getWorksCnName())
                .eqIfPresent(WorksDO::getHignUrl, reqVO.getHignUrl())
                .eqIfPresent(WorksDO::getLowUrl, reqVO.getLowUrl())
                .eqIfPresent(WorksDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(WorksDO::getPoint, reqVO.getPoint())
                .eqIfPresent(WorksDO::getPointType, reqVO.getPointType())
                .eqIfPresent(WorksDO::getTextContent, reqVO.getTextContent())
                .eqIfPresent(WorksDO::getCreateId, reqVO.getCreateId())
                .eqIfPresent(WorksDO::getCreateData, reqVO.getCreateData())
                .betweenIfPresent(WorksDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(WorksDO::getUpdaterId, reqVO.getUpdaterId())
                .eqIfPresent(WorksDO::getUpdateData, reqVO.getUpdateData())
                .eqIfPresent(WorksDO::getTenantCode, reqVO.getTenantCode())
                .inIfPresent(WorksDO::getId,reqVO.getWorksIds())
                .orderByDesc(WorksDO::getId));
    }
    List<SpaceMountDTO> selectMountsSpace(List<Long> ids);


}

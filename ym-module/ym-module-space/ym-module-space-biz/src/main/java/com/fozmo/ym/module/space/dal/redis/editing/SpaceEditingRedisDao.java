package com.fozmo.ym.module.space.dal.redis.editing;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.module.space.dal.dataobject.editing.EditingDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.SPACE_EDITING_KEY;
import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.WORKS_EDITING_KEY;

@Repository
@Slf4j
public class SpaceEditingRedisDao {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    public void addSpaceEditing(EditingDO editingDO) {
        String editingKey = SPACE_EDITING_KEY + editingDO.getSpaceId();
        String hk = editingDO.getEditId().toString();

        if (hasSpaceEditing(editingDO.getEditId(), editingDO.getSpaceId())) {
            // 存在则不保存
        } else {
            redisTemplate.opsForHash().put(editingKey, hk, editingDO);
        }

    }

    public void removeSpaceEditing(Long editId, Long spaceId) {
        String editingKey = SPACE_EDITING_KEY + spaceId;
        String hk = editId.toString();
        redisTemplate.opsForHash().delete(editingKey, hk);
    }

    public List<EditingDO> getSpaceEditingList(Long spaceId) {
        String editingKey = SPACE_EDITING_KEY + spaceId;
        HashOperations<String, String, EditingDO> hashOps = redisTemplate.opsForHash();
        // 创建扫描选项
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(1000)      // 每次扫描数量（优化性能）
                .build();

        // 创建结果列表
        List<Map.Entry<String, EditingDO>> allResult = new ArrayList<>();
        // 扫描Hash
        try (Cursor<Map.Entry<String, EditingDO>> cursor = hashOps.scan(editingKey, scanOptions)) {
            while (cursor.hasNext()) {
                allResult.add(cursor.next());
            }
        }
        // 创建空间列表
        List<EditingDO> spaceDOList = new ArrayList<>();
        // 判断结果是否为空
        if (CollUtil.isNotEmpty(allResult)) {
            // 将子结果转换为空间列表
            spaceDOList = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        }
        return spaceDOList;
    }

    public EditingDO getSpaceEditing(Long editId, Long spaceId) {
        String editingKey = SPACE_EDITING_KEY + spaceId;
        String hk = editId.toString();
        return (EditingDO) redisTemplate.opsForHash().get(editingKey, hk);
    }

    public boolean hasSpaceEditing(Long editId, Long spaceId) {
        String editingKey = SPACE_EDITING_KEY + spaceId;
        String hk = editId.toString();
        return redisTemplate.opsForHash().hasKey(editingKey, hk);
    }


    public boolean hasSpaceEditingList(Long spaceId) {
        String editingKey = SPACE_EDITING_KEY + spaceId;
        return redisTemplate.hasKey(editingKey);
    }

    public void removeSpaceEditList(Long spaceId) {
        String editingKey = SPACE_EDITING_KEY + spaceId;
        redisTemplate.delete(editingKey);
    }


    public void saveWorksEdit(Long worksId, Long spaceId) {
        String worksEditKey = WORKS_EDITING_KEY + worksId;
        redisTemplate.opsForSet().add(worksEditKey, spaceId);
    }

    public void removeWorksEdit(Long worksId, Long spaceId) {
        String worksEditKey = WORKS_EDITING_KEY + worksId;
        redisTemplate.opsForSet().remove(worksEditKey, spaceId);
    }

    public boolean hasWorksEdit(Long worksId, Long spaceId) {
        String worksEditKey = WORKS_EDITING_KEY + worksId;
        return redisTemplate.opsForSet().isMember(worksEditKey, spaceId);
    }

    public List<Long> getWorksEditList(Long worksId) {
        String worksEditKey = WORKS_EDITING_KEY + worksId;
        return redisTemplate.opsForSet().members(worksEditKey).stream().map(obj -> (Long) obj).collect(Collectors.toList());
    }

    public boolean hasWorksList(Long worksId) {
        String worksEditKey = WORKS_EDITING_KEY + worksId;
        return redisTemplate.hasKey(worksEditKey);
    }

    public void removeWorksEditList(Long worksId) {
        String worksEditKey = WORKS_EDITING_KEY + worksId;
        redisTemplate.delete(worksEditKey);
    }
}

package com.fozmo.ym.module.space.dal.redis.file;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.FILE_INFO_KEY;

@Repository
@Slf4j
public class SpaceFileRedisDao {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;


    public void saveFile(SpaceFileDO spaceFileDO) {
        String hk = spaceFileDO.getFileId().toString();
        if (!hasFile(spaceFileDO.getFileId())) {
            redisTemplate.opsForHash().put(FILE_INFO_KEY, hk, spaceFileDO);
        }

    }

    public void deleteFile(Long fileId) {
        String hk = fileId.toString();
        redisTemplate.opsForHash().delete(FILE_INFO_KEY, hk);
    }

    public boolean hasFile(Long fileId) {
        String hk = fileId.toString();
        return redisTemplate.opsForHash().hasKey(FILE_INFO_KEY, hk);
    }

    public PageResult<SpaceFileDO> getFilePage(Long type, Long rightsId, int pageNo, int pageSize) {


        PageResult<SpaceFileDO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);

        HashOperations<String, String, SpaceFileDO> hashOps = redisTemplate.opsForHash();

        // 创建扫描选项
        ScanOptions scanOptions = scanOptions = ScanOptions.scanOptions()
                .count(1000)      // 每次扫描数量（优化性能）
                .build();


        // 创建结果列表
        List<Map.Entry<String, SpaceFileDO>> allResult = new ArrayList<>();
        // 扫描Hash
        try (Cursor<Map.Entry<String, SpaceFileDO>> cursor = hashOps.scan(FILE_INFO_KEY, scanOptions)) {
            while (cursor.hasNext()) {
                allResult.add(cursor.next());
            }
        }
        // 创建空间列表
        List<SpaceFileDO> spaceDOList = new ArrayList<>();

        if (CollUtil.isNotEmpty(allResult)) {
            spaceDOList = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        }
        // 判断结果是否为空
        if (CollUtil.isNotEmpty(spaceDOList)) {

            if (ObjectUtil.isNotEmpty(type)) {
                spaceDOList = spaceDOList.stream()
                        .filter(spaceDO -> ObjectUtil.isNotEmpty(spaceDO.getFileTypeId()) && spaceDO.getFileTypeId().equals(type))
                        .collect(Collectors.toList());
            }

            if (ObjectUtil.isNotEmpty(rightsId)) {
                spaceDOList = spaceDOList.stream()
                        .filter(spaceDO -> spaceDO.getRightsId().equals(rightsId))
                        .collect(Collectors.toList());
            }

        }

        if (CollUtil.isNotEmpty(spaceDOList)) {
            // 计算起始位置和结束位置
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, spaceDOList.size());

            pageResult.setTotal((long) spaceDOList.size());
            pageResult.setList(spaceDOList.subList(start, end));
        }
        // 设置分页结果

        return pageResult;
    }

    public List<SpaceFileDO> getFileList() {
        List<Object> fileList = redisTemplate.opsForHash().values(FILE_INFO_KEY);
        if (CollUtil.isEmpty(fileList)) {
            return Collections.emptyList();
        } else {
            return fileList.stream().map(file -> file instanceof SpaceFileDO ? (SpaceFileDO) file : new SpaceFileDO()).collect(Collectors.toList());
        }
    }

    public SpaceFileDO getFIleInfo(Long id) {
        return (SpaceFileDO) redisTemplate.opsForHash().get(FILE_INFO_KEY, id.toString());
    }
}

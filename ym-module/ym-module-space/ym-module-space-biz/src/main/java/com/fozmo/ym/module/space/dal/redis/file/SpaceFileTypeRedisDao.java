package com.fozmo.ym.module.space.dal.redis.file;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.FILE_TYPE_KEY;

@Repository
@Slf4j
public class SpaceFileTypeRedisDao {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;


    public void saveFileType(FileTypeDO fileTypeDO) {
        String hk = fileTypeDO.getTypeId().toString();
        redisTemplate.opsForHash().put(FILE_TYPE_KEY, hk, fileTypeDO);
    }

    public void deleteFileType(Long typeId) {
        String hk = typeId.toString();
        redisTemplate.opsForHash().delete(FILE_TYPE_KEY, hk);
    }


    public boolean hasFileType(Long typeId) {
        String hk = typeId.toString();
        return redisTemplate.opsForHash().hasKey(FILE_TYPE_KEY, hk);
    }

    public PageResult<FileTypeDO> getTypePage(int pageNo, int pageSize) {


        PageResult<FileTypeDO> pageResult = new PageResult<>();
        Long total = redisTemplate.opsForHash().size(FILE_TYPE_KEY);

        HashOperations<String, String, FileTypeDO> hashOps = redisTemplate.opsForHash();
        // 创建扫描选项

        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(100)      // 每次扫描数量（优化性能）
                .build();

        // 创建结果列表
        List<Map.Entry<String, FileTypeDO>> allResult = new ArrayList<>();
        // 扫描Hash
        try (Cursor<Map.Entry<String, FileTypeDO>> cursor = hashOps.scan(FILE_TYPE_KEY, scanOptions)) {
            while (cursor.hasNext()) {
                allResult.add(cursor.next());
            }
        }
        // 创建空间列表
        List<FileTypeDO> spaceDOList = new ArrayList<>();
        // 判断结果是否为空
        if (CollUtil.isNotEmpty(allResult)) {
            // 计算起始位置和结束位置
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, allResult.size());

            // 获取子结果
            List<Map.Entry<String, FileTypeDO>> subResult = new ArrayList<>(allResult.subList(start, end));
            // 将子结果转换为空间列表
            spaceDOList = subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        }
        // 设置分页结果
        pageResult.setTotal(total);
        pageResult.setList(spaceDOList);
        return pageResult;
    }

    public List<FileTypeDO> getTypeList() {
        List<Object> fileList = redisTemplate.opsForHash().values(FILE_TYPE_KEY);
        if (CollUtil.isEmpty(fileList)) {
            return Collections.emptyList();
        } else {
            return fileList.stream().map(file -> file instanceof FileTypeDO ? (FileTypeDO) file : new FileTypeDO()).collect(Collectors.toList());
        }
    }

    public FileTypeDO getFileType(Long id) {
        return (FileTypeDO) redisTemplate.opsForHash().get(FILE_TYPE_KEY, id.toString());
    }
}

package com.fozmo.ym.module.space.dal.redis.hot;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.hot.SpaceHotDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.HOT_INFO_KEY;

@Repository
public class HotRedisDao {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;


    public void saveHot(SpaceHotDO hotDO) {
        String hotKey = HOT_INFO_KEY + hotDO.getType();
        String hk = hotDO.getId().toString();
        redisTemplate.opsForHash().put(hotKey, hk, hotDO);
    }

    public void deleteHot(Long id, Integer type) {
        String hotKey = HOT_INFO_KEY + type;
        String hk = id.toString();
        redisTemplate.opsForHash().delete(hotKey, hk);
    }

    public boolean hasHot(Long id, Integer type) {
        String hotKey = HOT_INFO_KEY + type;
        String hk = id.toString();
        return redisTemplate.opsForHash().hasKey(hotKey, hk);
    }

    public SpaceHotDO getHot(Long id, Integer type) {
        String hotKey = HOT_INFO_KEY + type;
        String hk = id.toString();
        return (SpaceHotDO) redisTemplate.opsForHash().get(hotKey, hk);
    }

    public List<SpaceHotDO> getHotList(Integer type) {
        // 1. Validate input
        if (type == null) {
            return Collections.emptyList();
        }

        // 2. Construct key safely
        String hotKey = HOT_INFO_KEY + type;

        try {
            // 3. Get entries with type safety
            List<SpaceHotDO> result = new ArrayList<>();
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(hotKey);

            // 4. Process entries with stream API
            entries.values().stream()
                    .filter(Objects::nonNull)
                    .forEach(value -> {
                        if (value instanceof SpaceHotDO) {
                            result.add((SpaceHotDO) value);
                        }
                    });

            return result;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public PageResult<SpaceHotDO> getHotPage(Integer type, Integer pageNo, Integer pageSize) {
        String hotKey = HOT_INFO_KEY + type;

        PageResult<SpaceHotDO> pageResult = new PageResult<>();
        // 查询所有模板
        // 构建Redis 查询 条件
        HashOperations<String, String, SpaceHotDO> hashOps = redisTemplate.opsForHash();
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(1000)      // 每次扫描数量（优化性能）
                .build();

        List<Map.Entry<String, SpaceHotDO>> allResult = new ArrayList<>();
        try (Cursor<Map.Entry<String, SpaceHotDO>> cursor = hashOps.scan(hotKey, scanOptions)) {
            while (cursor.hasNext()) {
                allResult.add(cursor.next());
            }
        }

        List<SpaceHotDO> hotDOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(allResult)) {
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, allResult.size());

            List<Map.Entry<String, SpaceHotDO>> subResult = new ArrayList<>(allResult.subList(start, end));
            hotDOS = subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        }
        pageResult.setTotal((long) allResult.size());
        pageResult.setList(hotDOS);
        return pageResult;
    }
}

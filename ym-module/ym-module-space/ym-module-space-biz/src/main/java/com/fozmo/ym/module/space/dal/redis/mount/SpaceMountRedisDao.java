package com.fozmo.ym.module.space.dal.redis.mount;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.module.space.constans.SpaceRedisConstants;
import com.fozmo.ym.module.space.dal.dataobject.mount.MountWorksDO;
import com.fozmo.ym.module.space.enums.MountType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.SPACE_MOUNT_KEY;

@Repository
@Slf4j
public class SpaceMountRedisDao {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    /***
     * 保存 空间作品挂载关系
     */
    public void saveSpaceMount(MountWorksDO mountWorksDO) {

        String mountKey = SPACE_MOUNT_KEY + mountWorksDO.getSpaceId();
        String hk = mountWorksDO.getMountId().toString();

        if (hasSpaceMount(mountWorksDO.getMountId(), mountWorksDO.getSpaceId())) {
        } else {
            redisTemplate.opsForHash().put(mountKey, hk, mountWorksDO);
        }

    }

    /***
     *
     * 维护作品 空间 搭载关系
     */
    public void saveWorksMount(Long worksId, Long spaceId) {
        String mountKey = SpaceRedisConstants.WORKS_MOUNT_KEY + worksId;
        redisTemplate.opsForZSet().add(mountKey, spaceId, spaceId);
    }

    /***
     *
     * 批量保存空间挂载关系
     */
    public void saveSpaceMountList(List<MountWorksDO> mountWorksDO) {
        for (MountWorksDO worksDO : mountWorksDO) {
            saveSpaceMount(worksDO);
            if (MountType.isWorksType(worksDO.getMountType())) {
                saveWorksMount(worksDO.getMountId(), worksDO.getSpaceId());
            }
        }
    }

    /***
     * 删除空间挂载关系
     */
    public void deleteSpaceMount(Long mountId, Long spaceId) {
        String mountKey = SPACE_MOUNT_KEY + spaceId;
        String hk = mountId.toString();
        redisTemplate.opsForHash().delete(mountKey, hk);
    }

    /***
     * 获取空间挂载关系
     */
    public MountWorksDO getSpaceMount(Long mountId, Long spaceId) {
        String mountKey = SPACE_MOUNT_KEY + spaceId;
        String hk = mountId.toString();
        return (MountWorksDO) redisTemplate.opsForHash().get(mountKey, hk);
    }

    /***
     * 判断空间是否挂载关系
     */
    public boolean hasSpaceMount(Long mountId, Long spaceId) {
        String mountKey = SPACE_MOUNT_KEY + spaceId;
        String hk = mountId.toString();
        return redisTemplate.opsForHash().hasKey(mountKey, hk);
    }

    /**
     * 判断空间是否存在挂载关系
     */
    public boolean haseSpaceMountList(Long spaceId) {
        String mountKey = SPACE_MOUNT_KEY + spaceId;
        return redisTemplate.opsForHash().size(mountKey) > 0;
    }
    /***
     *  删除空间挂载关系
     */
    public void deleteMountBySpaceId(Long spaceId) {
        String mountKey = SPACE_MOUNT_KEY + spaceId;
        redisTemplate.delete(mountKey);
    }

    /**
     *  获取空间挂载列表
     */
    public List<MountWorksDO> getSpaceMountList(Long spaceId) {
        String mountKey = SPACE_MOUNT_KEY + spaceId;
        HashOperations<String, String, MountWorksDO> hashOps = redisTemplate.opsForHash();
        // 创建扫描选项
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .count(1000)      // 每次扫描数量（优化性能）
                .build();

        // 创建结果列表
        List<Map.Entry<String, MountWorksDO>> allResult = new ArrayList<>();
        // 扫描Hash
        try (Cursor<Map.Entry<String, MountWorksDO>> cursor = hashOps.scan(mountKey, scanOptions)) {
            while (cursor.hasNext()) {
                allResult.add(cursor.next());
            }
        }
        // 创建空间列表
        List<MountWorksDO> spaceDOList = new ArrayList<>();
        // 判断结果是否为空
        if (CollUtil.isNotEmpty(allResult)) {
            // 将子结果转换为空间列表
            spaceDOList = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        }
        return spaceDOList;
    }

    public List<Long> getWorksMountList(Long worksId) {
        // 1. 参数校验
        if (worksId == null || worksId <= 0) {
            return Collections.emptyList();
        }

        // 2. 构建缓存键
        final String worksMountKey = SpaceRedisConstants.WORKS_MOUNT_KEY + worksId;
        final String nullMarkerKey = worksMountKey + ":null";

        // 3. 检查空值标记（防穿透）
        if (redisTemplate.hasKey(nullMarkerKey)) {
            return Collections.emptyList();
        }

        // 4. 获取ZSet数据
        Set<Object> mountSet = redisTemplate.opsForZSet().range(worksMountKey, 0, -1);

        // 5. 处理空结果
        if (CollUtil.isEmpty(mountSet)) {
            // 设置空值标记（短时间过期）
            redisTemplate.opsForValue().set(nullMarkerKey, "1", 1, TimeUnit.MINUTES);
            return Collections.emptyList();
        }

        // 6. 转换结果集（带异常处理和无效数据清理）
        List<Long> result = new ArrayList<>(mountSet.size());
        Set<Object> invalidElements = new HashSet<>();

        for (Object element : mountSet) {
            try {
                // 尝试转换为Long
                Long id = convertToLong(element);
                if (id != null) {
                    result.add(id);
                } else {
                    invalidElements.add(element);
                }
            } catch (NumberFormatException e) {
                log.warn("空间挂载ID格式错误: key={}, value={}", worksMountKey, element);
                invalidElements.add(element);
            }
        }

        // 7. 异步清理无效数据
        if (!invalidElements.isEmpty()) {
            log.info("清理无效挂载数据: key={}, count={}", worksMountKey, invalidElements.size());
            redisTemplate.opsForZSet().remove(worksMountKey, invalidElements.toArray());
        }

        return result;
    }

    public void deleteWorksInfo(Long mountId, Long spaceId) {
        String worksMountKey = SpaceRedisConstants.WORKS_MOUNT_KEY + mountId;
        redisTemplate.opsForSet().remove(worksMountKey, spaceId);

    }

    public boolean hasWorksMountInfo(Long mountId, Long spaceId) {
        String worksMountKey = SpaceRedisConstants.WORKS_MOUNT_KEY + mountId;
        return redisTemplate.opsForSet().isMember(worksMountKey, spaceId);
    }

    public boolean hasWorksMountList(Long mountId) {
        String worksMountKey = SpaceRedisConstants.WORKS_MOUNT_KEY + mountId;
        return redisTemplate.hasKey(worksMountKey);
    }

    // 安全转换方法
    private Long convertToLong(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }

        if (obj instanceof String) {
            try {
                return Long.parseLong((String) obj);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        return null;
    }
}

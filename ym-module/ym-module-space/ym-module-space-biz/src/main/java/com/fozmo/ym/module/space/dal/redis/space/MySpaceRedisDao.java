package com.fozmo.ym.module.space.dal.redis.space;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.SPACE_ACCOUNT_KEY;


/***
 * 我的空间redis 操作
 */
@Repository
public class MySpaceRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	public void saveMySpaceId(Long accountId,Long spaceId){
		String spaceAccountKey = SPACE_ACCOUNT_KEY+accountId;
		if (hasMySpaceId(accountId, spaceId)) {

		}else {
			redisTemplate.opsForList().leftPush(spaceAccountKey,spaceId);
		}


	}
	
	public void deleteMySpaceId(Long accountId,Long spaceId){
		String spaceAccountKey = SPACE_ACCOUNT_KEY+accountId;
		redisTemplate.opsForList().remove(spaceAccountKey,0,spaceId);
	}

	public boolean hasMySpaceId(Long accountId,Long spaceId){
		String spaceAccountKey = SPACE_ACCOUNT_KEY+accountId;
		List<Object> list = redisTemplate.opsForList().range(spaceAccountKey,0,-1);

		return list.contains(spaceId);
	}
	
	public List<Long> getAllMysqlSpaceId(Long accountId){
		String spaceAccountKey = SPACE_ACCOUNT_KEY+accountId;
		List<Object> list = redisTemplate.opsForList().range(spaceAccountKey, 0, -1);
		List<Long> resultList = list.stream()
				                        .filter(Objects ::nonNull) // 过滤null
				                        .map(obj -> {
					                        if (obj instanceof Number num) {
						                        return num.longValue();
					                        } else if (obj instanceof String str) {
						                        try {
							                        return Long.parseLong(str);
						                        } catch (NumberFormatException e) {
							                        return null; // 转换失败返回null
						                        }
					                        }
					                        return null;
				                        })
				                        .filter(Objects::nonNull) // 移除转换失败项
				                        .distinct() // 去重（可选）
				                        .collect(Collectors.toList());
		return resultList;
	}
	
	
	public PageResult<Long> getMyspaceIdPage(Long accountId,Integer pageNo,Integer pageSize){
		PageResult<Long> pageResult = new PageResult<>();
		String spaceAccountKey = SPACE_ACCOUNT_KEY+accountId;
		Long total = redisTemplate.opsForList().size(spaceAccountKey);
		
		int start = (pageNo - 1) * pageSize;
		int end = Math.toIntExact(Math.min(start + pageSize, total));
		List<Object> list = redisTemplate.opsForList().range(spaceAccountKey, start, end);
		List<Long> resultList = list.stream()
				                        .filter(Objects ::nonNull) // 过滤null
				                        .map(obj -> {
					                        if (obj instanceof Number num) {
						                        return num.longValue();
					                        } else if (obj instanceof String str) {
						                        try {
							                        return Long.parseLong(str);
						                        } catch (NumberFormatException e) {
							                        return null; // 转换失败返回null
						                        }
					                        }
					                        return null;
				                        })
				                        .filter(Objects::nonNull) // 移除转换失败项
				                        .distinct() // 去重（可选）
				                        .collect(Collectors.toList());
		
		pageResult.setTotal(total);
		pageResult.setList(resultList);
		return pageResult;
	}

	public void deleteAll(Long accountId) {
		redisTemplate.delete(SPACE_ACCOUNT_KEY + accountId);
	}

	public void saveMyWorksList(Long accountId, List<SpaceDO> space) {
		String spaceAccountKey = SPACE_ACCOUNT_KEY + accountId;
		for (SpaceDO spaceDO : space) {
			redisTemplate.opsForList().leftPush(spaceAccountKey, spaceDO.getId());
		}
	}
}

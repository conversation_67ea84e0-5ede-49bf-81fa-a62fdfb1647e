package com.fozmo.ym.module.space.dal.redis.space;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.SPACE_INFO_KEY;

@Repository
@Slf4j
public class SpaceRedisDao {

	// 注入RedisTemplate
	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	// 注入RedissonClient
	@Resource
	private RedissonClient redissonClient;
	
	
	// 保存空间信息
	public void saveSpace (SpaceDO spaceDO) {
		// 获取空间ID
		String hk = spaceDO.getId().toString();
		// 将空间信息存入Redis
		if (hasSpaceKey(spaceDO.getId())){

		}else {
			redisTemplate.opsForHash().put(SPACE_INFO_KEY,hk,spaceDO);
		}

	}
	
	// 判断空间是否存在
	public Boolean hasSpaceKey(Long spaceId){
		// 获取空间ID
		String hk = spaceId.toString();
		// 判断空间是否存在
		return redisTemplate.opsForHash().hasKey(SPACE_INFO_KEY,hk);
	}
	
	// 删除空间信息
	public void delSpaceKey(Long spaceId){
		// 获取空间ID
		String hk = spaceId.toString();
		// 删除空间信息
		redisTemplate.opsForHash().delete(SPACE_INFO_KEY,hk);
	}
	
	// 获取空间分页信息
//	public PageResult<SpaceDO> getSpacePage(Integer pageNo, Integer pageSize, String name, Long spaceType) {
//
//		// 创建分页结果对象
//		PageResult<SpaceDO> pageResult = new PageResult<>();
//
//		// 获取Hash操作对象
//		HashOperations<String, String, SpaceDO> hashOps = redisTemplate.opsForHash();
//		// 创建扫描选项
//		ScanOptions scanOptions = ScanOptions.scanOptions()
//				                          .count(10000)      // 每次扫描数量（优化性能）
//				                          .build();
//
//		// 创建结果列表
//		List<Map.Entry<String, SpaceDO>> allResult = new ArrayList<>();
//		// 扫描Hash
//		try (Cursor<Map.Entry<String, SpaceDO>> cursor = hashOps.scan(SPACE_INFO_KEY, scanOptions)) {
//			while (cursor.hasNext()) {
//				allResult.add(cursor.next());
//			}
//		}
//		// 创建空间列表
//		List<SpaceDO> spaceDOList = new ArrayList<>();
//		if (CollUtil.isNotEmpty(allResult)) {
//			spaceDOList = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
//			if (CollUtil.isNotEmpty(spaceDOList)) {
//				spaceDOList = spaceDOList.stream()
//					.filter(spaceDO -> {
//						if (ObjectUtil.isEmpty(spaceDO.getExhibitionStartTime()) && beforeNow(spaceDO.getExhibitionStartTime())){
//							return true;
//						}
//						if (ObjectUtil.isEmpty(spaceDO.getExhibitionEndTime()) && afterNow(spaceDO.getExhibitionEndTime())){
//							return true;
//						}
//						if (ObjectUtil.isEmpty(spaceDO.getExhibitionStartTime()) && ObjectUtil.isEmpty(spaceDO.getExhibitionEndTime())){
//                            return afterNow(spaceDO.getExhibitionStartTime()) && beforeNow(spaceDO.getExhibitionEndTime());
//						}
//                        return false;
//                    })
//						.collect(Collectors.toList());
//			}
//		}
//		// 判断结果是否为空
//		if (CollUtil.isNotEmpty(spaceDOList)) {
//
//			if (name != null) {
//				spaceDOList = spaceDOList.stream()
//						.filter(spaceDO -> spaceDO.getSpaceCnName().contains(name))
//						.collect(Collectors.toList());
//			}
//
//			if (spaceType != null) {
//				spaceDOList = spaceDOList.stream()
//						.filter(spaceDO -> spaceDO.getSpaceTypeId().equals(spaceType))
//						.collect(Collectors.toList());
//			}
//			pageResult.setTotal((long) spaceDOList.size());
//		}
//		if (ObjectUtil.isNotEmpty(spaceDOList)) {
//			int start = (pageNo - 1) * pageSize;
//			int end = Math.min(start + pageSize, spaceDOList.size());
//			spaceDOList = spaceDOList.subList(start, end);
//		}
//		// 设置分页结果
//
//		pageResult.setList(spaceDOList);
//		return  pageResult;
//	}

	public PageResult<SpaceDO> getSpacePage(Integer pageNo, Integer pageSize, String name, Long spaceType) {
		// 0. 参数校验

		// 1. 获取当前时间基准（考虑时区）
		ZoneId zone = ZoneId.systemDefault();
		LocalDateTime now = LocalDateTime.now(zone);

		// 2. 使用流式处理避免全量加载
		List<SpaceDO> filteredSpaces = new ArrayList<>();

		HashOperations<String, String, SpaceDO> hashOps = redisTemplate.opsForHash();

		// 3. 优化扫描参数
		ScanOptions scanOptions = ScanOptions.scanOptions()
				.count(1000) // 减少每批数量，降低内存压力
				.build();

		try (Cursor<Map.Entry<String, SpaceDO>> cursor = hashOps.scan(SPACE_INFO_KEY, scanOptions)) {
			while (cursor.hasNext()) {
				SpaceDO space = cursor.next().getValue();

				// 4. 重构时间过滤逻辑
				if (isWithinExhibitionTime(space, now)) {
					// 5. 安全处理名称过滤
					boolean nameMatch = true;
					if (StringUtils.isNotEmpty(name)) {
						// 处理可能的空值并忽略大小写
						nameMatch = space.getSpaceCnName() != null &&
								space.getSpaceCnName().toLowerCase().contains(name.toLowerCase());
					}

					// 6. 安全处理类型过滤
					boolean typeMatch = true;
					if (spaceType != null) {
						// 处理可能的空值
						typeMatch = space.getSpaceTypeId() != null &&
								spaceType.equals(space.getSpaceTypeId());
					}

					if (nameMatch && typeMatch) {
						filteredSpaces.add(space);
					}
				}
			}
		} catch (Exception e) {
//			log.error("空间数据扫描异常: {}", e.getMessage(), e);
//			// 监控上报
//			monitor.reportError("SPACE_SCAN_ERROR");
			log.error("空间数据扫描异常: {}", e.getMessage());
		}

		// 7. 分页处理
		return paginateResults(filteredSpaces, pageNo, pageSize);
	}

	// 重构的时间判断方法（支持跨天场景）
	private boolean isWithinExhibitionTime(SpaceDO space, LocalDateTime now) {
		LocalDateTime start = space.getExhibitionStartTime();
		LocalDateTime end = space.getExhibitionEndTime();

		// 情况1：无时间限制
		if (start == null && end == null) {
			return true;
		}

		// 情况2：只有开始时间
		if (start != null && end == null) {
			return !now.isBefore(start);
		}

		// 情况3：只有结束时间
		if (start == null && end != null) {
			return !now.isAfter(end);
		}

		// 情况4：完整时间区间（支持跨天）
		if (start.isBefore(end) || start.equals(end)) {
			// 普通区间 [start, end]
			return !now.isBefore(start) && !now.isAfter(end);
		} else {
			// 跨天区间 [start, 24:00) ∪ [00:00, end]
			return !now.isBefore(start) || !now.isAfter(end);
		}
	}

	// 分页处理方法
	private PageResult<SpaceDO> paginateResults(List<SpaceDO> spaces, int pageNo, int pageSize) {
		PageResult<SpaceDO> result = new PageResult<>();
		int total = spaces.size();

		if (total == 0) {
			result.setTotal(0L);
			result.setList(Collections.emptyList());
			return result;
		}

		int start = Math.max(0, (pageNo - 1) * pageSize);
		int end = Math.min(start + pageSize, total);

		result.setTotal((long) total);
		result.setList(spaces.subList(start, end));
		return result;
	}
	
	// 获取空间信息
	public SpaceDO getSpaceInfo(Long spaceId){
		// 获取空间ID
		String hk = spaceId.toString();
		// 获取空间信息
		return (SpaceDO) redisTemplate.opsForHash().get(SPACE_INFO_KEY,hk);
	}

	public Long getSpaceCount() {
		return redisTemplate.opsForHash().size(SPACE_INFO_KEY);
	}

	public void delAllSpace() {
		redisTemplate.delete(SPACE_INFO_KEY);
	}

	public void saveAllSpace(List<SpaceDO> spaceDOList) {
		if (CollUtil.isNotEmpty(spaceDOList)) {
			String spaceKey = SPACE_INFO_KEY;
			Map<String, SpaceDO> map = spaceDOList.stream().collect(Collectors.toMap(spaceDO -> spaceDO.getId().toString(), spaceDO -> spaceDO));
			redisTemplate.opsForHash().putAll(spaceKey, map);
		}
	}
}

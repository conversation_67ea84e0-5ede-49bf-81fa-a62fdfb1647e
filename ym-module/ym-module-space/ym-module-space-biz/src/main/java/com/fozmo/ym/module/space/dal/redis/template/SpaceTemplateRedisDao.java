package com.fozmo.ym.module.space.dal.redis.template;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.TEMPLATE_INFO_KEY;

@Repository
public class SpaceTemplateRedisDao {
	
	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	public void saveTemplate(SpaceTemplateDO templateDO) {
		String templateKey = TEMPLATE_INFO_KEY;
		
		String hk = String.valueOf(templateDO.getTemplateId());
		
		if (hasTemplate(templateDO.getTemplateId())){
  
		}else {
			redisTemplate.opsForHash().put(templateKey, hk, templateDO);
		}
	}
	
	public Boolean hasTemplate(Long templateId) {
		String templateKey = TEMPLATE_INFO_KEY;
		String hk = String.valueOf(templateId);
		return redisTemplate.opsForHash().hasKey(templateKey, hk);
	}
	
	public SpaceTemplateDO getTemplate(Long templateId) {
		String templateKey = TEMPLATE_INFO_KEY;
		String hk = String.valueOf(templateId);
		return (SpaceTemplateDO) redisTemplate.opsForHash().get(templateKey, hk);
	}
	
	public void deleteTemplate(Long templateId) {
		String templateKey = TEMPLATE_INFO_KEY;
		String hk = String.valueOf(templateId);
		redisTemplate.opsForHash().delete(templateKey, hk);
	}

	public PageResult<SpaceTemplateDO> getTemplatePage(Integer pageNo, Integer pageSize, String templateTypeCode) {
		String templateKey = TEMPLATE_INFO_KEY;
		
		PageResult<SpaceTemplateDO> pageResult = new PageResult<>();
		pageResult.setTotal(0L);
		// 查询所有模板
		// 构建Redis 查询 条件
		HashOperations<String, String, SpaceTemplateDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = scanOptions = ScanOptions.scanOptions()
				.count(10000)      // 每次扫描数量（优化性能）
				.build();
		
		List<Map.Entry<String, SpaceTemplateDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SpaceTemplateDO>> cursor = hashOps.scan(templateKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		List<SpaceTemplateDO> templateDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult) && StringUtils.isNotBlank(templateTypeCode)) {
			templateDOS = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
			templateDOS = templateDOS.stream().filter(templateDO -> ObjectUtil.isNotEmpty(templateDO.getTemplateTypeCode()) && templateDO.getTemplateTypeCode().contains(templateTypeCode)).collect(Collectors.toList());
		} else {
			templateDOS = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}


		if (CollUtil.isNotEmpty(templateDOS)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, templateDOS.size());
			templateDOS = templateDOS.subList(start, end);
		} else {
			return pageResult;
		}
		pageResult.setTotal((long) templateDOS.size());
		pageResult.setList(templateDOS);
		return  pageResult;
	}
	
}

package com.fozmo.ym.module.space.dal.redis.template;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.TEMPLATE_TAG_KEY;

@Repository
public class SpaceTemplateTagRedisDao {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	public void saveTemplateTag(SpaceTemplateTagDO templateDO) {
		String templateKey = TEMPLATE_TAG_KEY;
        String hk = String.valueOf(templateDO.getId());
		if (hasTemplateTag(templateDO.getId())){

		}else {redisTemplate.opsForHash().put(templateKey, hk, templateDO);}
	}

	public boolean hasTemplateTag(Long id) {
		
		String templateKey = TEMPLATE_TAG_KEY;
		String hk = id.toString();
		return redisTemplate.opsForHash().hasKey(templateKey, hk);
	}
	
	public SpaceTemplateTagDO getTemplateTag(Long id) {
		String templateKey = TEMPLATE_TAG_KEY;
		String hk = id.toString();
		return (SpaceTemplateTagDO) redisTemplate.opsForHash().get(templateKey, hk);
	}
	
	public void deleteTemplateTag(Long id) {
		String templateKey = TEMPLATE_TAG_KEY;
		String hk = id.toString();
		redisTemplate.opsForHash().delete(templateKey, hk);
	}
	
	public PageResult<SpaceTemplateTagDO> getTemplateTagPage(Integer pageNo, Integer pageSize){
		String templateKey = TEMPLATE_TAG_KEY;
		
		PageResult<SpaceTemplateTagDO> pageResult = new PageResult<>();
		// 查询所有模板
		// 构建Redis 查询 条件
		HashOperations<String, String, SpaceTemplateTagDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()// 匹配 tagId
				              .count(1000)      // 每次扫描数量（优化性能）
				              .build();
		
		List<Map.Entry<String, SpaceTemplateTagDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SpaceTemplateTagDO>> cursor = hashOps.scan(templateKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<SpaceTemplateTagDO> templateTagDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, SpaceTemplateTagDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			templateTagDOS= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal((long) allResult.size());
		pageResult.setList(templateTagDOS);
		return  pageResult;
	}

	public List<SpaceTemplateTagDO> getTemplateTagList(String name) {
		String templateKey = TEMPLATE_TAG_KEY;
		HashOperations<String, String, SpaceTemplateTagDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.NONE;
	    if (ObjectUtil.isNotEmpty(name)) {
			scanOptions = ScanOptions.scanOptions()
                    .match("name:" + name)      // 匹配 tagId
                    .count(10000)
					.build();// 每次扫描数量（优化性能）
	    }else {
		    scanOptions = ScanOptions.scanOptions()
				                  .count(10000)
				                  .build();// 每次扫描数量（优化性能
	    }
		List<Map.Entry<String, SpaceTemplateTagDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SpaceTemplateTagDO>> cursor = hashOps.scan(templateKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<SpaceTemplateTagDO> templateDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			templateDOS = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		
		return templateDOS;
	}
}

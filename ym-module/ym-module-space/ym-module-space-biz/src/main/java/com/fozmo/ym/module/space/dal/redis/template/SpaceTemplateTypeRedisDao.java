package com.fozmo.ym.module.space.dal.redis.template;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.TEMPLATE_TYPE_KEY;

@Repository
public class SpaceTemplateTypeRedisDao {

@Resource
private RedisTemplate<String, Object> redisTemplate;

@Resource
private RedissonClient redissonClient;

	public void saveTemplateType(SpaceTemplateTypeDO templateDO) {
		String templateKey = TEMPLATE_TYPE_KEY;
		String hk = String.valueOf(templateDO.getId());
		if (hasTemplateType(templateDO.getId())){

		}else {
			redisTemplate.opsForHash().put(templateKey, hk, templateDO);
		}
	}
	
	public boolean hasTemplateType(Long id) {
		
		String templateKey = TEMPLATE_TYPE_KEY;
		String hk = id.toString();
		return redisTemplate.opsForHash().hasKey(templateKey, hk);
	}
	
	public SpaceTemplateTypeDO getTemplateType(Long id) {
		String templateKey = TEMPLATE_TYPE_KEY;
		String hk = id.toString();
		return (SpaceTemplateTypeDO) redisTemplate.opsForHash().get(templateKey, hk);
	}
	
	public void deleteTemplateType(Long id) {
		String templateKey = TEMPLATE_TYPE_KEY;
		String hk = id.toString();
		redisTemplate.opsForHash().delete(templateKey, hk);
	}
	
	public PageResult<SpaceTemplateTypeDO> getTemplateTypePage(Integer pageNo, Integer pageSize){
		String templateKey = TEMPLATE_TYPE_KEY;
		
		PageResult<SpaceTemplateTypeDO> pageResult = new PageResult<>();
		// 查询所有模板
		// 构建Redis 查询 条件
		HashOperations<String, String, SpaceTemplateTypeDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()// 匹配 tagId
				                          .count(1000)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, SpaceTemplateTypeDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SpaceTemplateTypeDO>> cursor = hashOps.scan(templateKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<SpaceTemplateTypeDO> templateTagDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, allResult.size());
			
			List<Map.Entry<String, SpaceTemplateTypeDO>> subResult = new ArrayList<>(allResult.subList(start, end));
			templateTagDOS= subResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		pageResult.setTotal((long) allResult.size());
		pageResult.setList(templateTagDOS);
		return  pageResult;
	}

	public List<SpaceTemplateTypeDO> getTemplateTypeList(){
		String templateKey = TEMPLATE_TYPE_KEY;
		// 查询所有模板
		// 构建Redis 查询 条件
		HashOperations<String, String, SpaceTemplateTypeDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()// 匹配 tagId
				                          .count(1000)      // 每次扫描数量（优化性能）
				                          .build();
		
		List<Map.Entry<String, SpaceTemplateTypeDO>> allResult = new ArrayList<>();
		try (Cursor<Map.Entry<String, SpaceTemplateTypeDO>> cursor = hashOps.scan(templateKey, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}
		
		List<SpaceTemplateTypeDO> templateTagDOS = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			templateTagDOS= allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}
		return  templateTagDOS;
	}
	
	
}

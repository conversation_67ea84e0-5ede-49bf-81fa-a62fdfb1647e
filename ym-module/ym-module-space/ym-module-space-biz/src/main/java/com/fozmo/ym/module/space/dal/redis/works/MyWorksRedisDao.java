package com.fozmo.ym.module.space.dal.redis.works;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.WORKS_ACCOUNT_KEY;

/***
 *  <AUTHOR>
 *  我的作品RedisDao
 */
@Repository
public class MyWorksRedisDao {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;


    public void saveMyWorksId(Long accountId,Long worksId){
        // 将worksId添加到accountId对应的列表中
        String worksKey = WORKS_ACCOUNT_KEY+accountId;
        if (!hasWorks(accountId, worksId)) {
            // 存储我的作品Id
            redisTemplate.opsForList().leftPush(worksKey, worksId);
        }

    }

    public void deleteMyWorksId(Long accountId,Long worksId){
        String worksKey = WORKS_ACCOUNT_KEY+accountId;
        redisTemplate.opsForList().remove(worksKey,0,worksId);
    }

    public List<Long> getAllMysqlWorksId(Long accountId){
        String worksKey = WORKS_ACCOUNT_KEY+accountId;
        List<Object> list = redisTemplate.opsForList().range(worksKey, 0, -1);
        assert list != null;
        // 过滤null
        // 转换失败返回null
        // 移除转换失败项
        // 去重（可选）
        return list.stream()
                .filter(Objects::nonNull) // 过滤null
                .map(obj -> {
                    if (obj instanceof Number num) {
                        return num.longValue();
                    } else if (obj instanceof String str) {
                        try {
                            return Long.parseLong(str);
                        } catch (NumberFormatException e) {
                            return null; // 转换失败返回null
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull) // 移除转换失败项
                .distinct() // 去重（可选）
                .collect(Collectors.toList());

    }

    public PageResult<Long> getMyWorksIdPage(Long accountId, Integer pageNo, Integer pageSize){
        PageResult<Long> pageResult = new PageResult<>();
        String worksKey = WORKS_ACCOUNT_KEY+accountId;
        Long total = redisTemplate.opsForList().size(worksKey);

        int start = (pageNo - 1) * pageSize;
        int end = Math.toIntExact(Math.min(start + pageSize, total));
        List<Object> list = redisTemplate.opsForList().range(worksKey, start, end);
        assert list != null;
        List<Long> resultList = list.stream()
                .filter(Objects ::nonNull) // 过滤null
                .map(obj -> {
                    if (obj instanceof Number num) {
                        return num.longValue();
                    } else if (obj instanceof String str) {
                        try {
                            return Long.parseLong(str);
                        } catch (NumberFormatException e) {
                            return null; // 转换失败返回null
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull) // 移除转换失败项
                .distinct() // 去重（可选）
                .collect(Collectors.toList());

        pageResult.setTotal(total);
        pageResult.setList(resultList);
        return pageResult;
    }

    public boolean hasWorks(Long loginAccountId, Long id) {
        String worksKey = WORKS_ACCOUNT_KEY + loginAccountId;
        if (redisTemplate.hasKey(worksKey)) {
            Long result = redisTemplate.opsForList().indexOf(worksKey, id);
            return ObjectUtil.isNotEmpty(result) && result > -1;
        }
        return false;

    }

    public Long selectCount(Long accountId) {
        String worksKey = WORKS_ACCOUNT_KEY + accountId;
        if (redisTemplate.hasKey(worksKey)) {
            return redisTemplate.opsForList().size(worksKey);
        }
        return 0L;
    }

    public void deleteMyWorks(Long accountId) {
        redisTemplate.delete(WORKS_ACCOUNT_KEY + accountId);
    }

    public void saveMyWorksList(Long accountId, List<WorksDO> works) {
        String worksKey = WORKS_ACCOUNT_KEY + accountId;
        for (WorksDO worksDO : works) {
            redisTemplate.opsForList().leftPush(worksKey, worksDO.getId());
        }
    }
}

package com.fozmo.ym.module.space.dal.redis.works;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.WORKS_INFO_KEY;

@Repository
@Slf4j
public class WorksRedisDao {
	
	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;

	/***
	 * 作品存储
	 */
	public void saveWorks(WorksDO worksDO){
		String worksKey = WORKS_INFO_KEY;
		String hk = worksDO.getId().toString();
		if (!haseWorksKey(worksDO.getId())){
			redisTemplate.opsForHash().put(worksKey,hk,worksDO);
		}

	}

	/**
	 *  判断作品是否存在
	 */
	public boolean haseWorksKey(Long worksId){
		String worksKey = WORKS_INFO_KEY;
		String hk = worksId.toString();

		return redisTemplate.opsForHash().hasKey(worksKey,hk);
	}

	/***
	 * 获取作品详情
	 */
	public WorksDO getWorksInfo(Long worksId){
		String worksKey = WORKS_INFO_KEY;
		String hk = worksId.toString();

		return (WorksDO) redisTemplate.opsForHash().get(worksKey,hk);
	}


	/**
	 *  获取作品分页
	 */
	public PageResult<WorksDO> getWorksPage(Integer pageNo, Integer pageSize, String name, Integer pointType) {
		// 参数校验
		if (pageNo == null || pageNo < 1) pageNo = 1;
		if (pageSize == null || pageSize < 1) pageSize = 10;
		if (pageSize > 100) pageSize = 100; // 限制最大页面大小

		// 构建缓存键
		String cacheKey = buildCacheKey(name, pointType);

		try {
			// 尝试从缓存获取
			PageResult<WorksDO> cachedResult = getCachedPageResult(cacheKey, pageNo, pageSize);
			if (cachedResult != null) {
				return cachedResult;
			}

			// 缓存未命中，执行查询
			PageResult<WorksDO> result = executeOptimizedQuery(pageNo, pageSize, name, pointType);

			// 缓存结果（异步）
			cachePageResult(cacheKey, result);

			return result;
		} catch (Exception e) {
			log.error("Redis分页查询失败, pageNo: {}, pageSize: {}, name: {}, pointType: {}",
					pageNo, pageSize, name, pointType, e);
			return PageResult.empty();
		}
	}

	/**
	 * 优化后的查询执行方法
	 */
	private PageResult<WorksDO> executeOptimizedQuery(Integer pageNo, Integer pageSize, String name, Integer pointType) {
		PageResult<WorksDO> pageResult = new PageResult<>();

		// 使用流式处理，避免全量加载到内存
		HashOperations<String, String, WorksDO> hashOps = redisTemplate.opsForHash();
		ScanOptions scanOptions = ScanOptions.scanOptions()
				.count(500) // 减少每次扫描数量，提高响应性
				.build();

		List<WorksDO> filteredWorks = new ArrayList<>();
		int totalCount = 0;
		int targetSize = pageNo * pageSize; // 目标数据量

		try (Cursor<Map.Entry<String, WorksDO>> cursor = hashOps.scan(WORKS_INFO_KEY, scanOptions)) {
			while (cursor.hasNext() && filteredWorks.size() < targetSize) {
				Map.Entry<String, WorksDO> entry = cursor.next();
				WorksDO worksDO = entry.getValue();

				// 应用过滤条件
				if (matchesFilter(worksDO, name, pointType)) {
					totalCount++;

					// 只收集当前页需要的数据
					int startIndex = (pageNo - 1) * pageSize;
					if (totalCount > startIndex) {
						filteredWorks.add(worksDO);
					}
				}
			}

			// 如果还没扫描完，继续统计总数
			if (cursor.hasNext()) {
				while (cursor.hasNext()) {
					Map.Entry<String, WorksDO> entry = cursor.next();
					WorksDO worksDO = entry.getValue();
					if (matchesFilter(worksDO, name, pointType)) {
						totalCount++;
					}
				}
			}
		}

		pageResult.setTotal((long) totalCount);
		pageResult.setList(filteredWorks);
		return pageResult;
	}

	/**
	 * 过滤条件匹配
	 */
	private boolean matchesFilter(WorksDO worksDO, String name, Integer pointType) {
		if (worksDO == null) return false;

		// 名称过滤
		if (StringUtils.isNotBlank(name)) {
			if (worksDO.getWorksCnName() == null || !worksDO.getWorksCnName().contains(name)) {
				return false;
			}
		}

		// 类型过滤
		if (pointType != null) {
			return Objects.equals(worksDO.getPointType(), pointType);
		}

		return true;
	}

	/**
	 * 构建缓存键
	 */
	private String buildCacheKey(String name, Integer pointType) {
		StringBuilder keyBuilder = new StringBuilder("works_page:");
		if (StringUtils.isNotBlank(name)) {
			keyBuilder.append("name_").append(name.hashCode()).append(":");
		}
		if (pointType != null) {
			keyBuilder.append("type_").append(pointType).append(":");
		}
		return keyBuilder.toString();
	}

	/**
	 * 从缓存获取分页结果
	 */
	private PageResult<WorksDO> getCachedPageResult(String cacheKey, Integer pageNo, Integer pageSize) {
		try {
			String pageKey = cacheKey + "page_" + pageNo + "_" + pageSize;
			Object cached = redisTemplate.opsForValue().get(pageKey);
			if (cached instanceof PageResult) {
				return (PageResult<WorksDO>) cached;
			}
		} catch (Exception e) {
			log.warn("获取缓存分页结果失败: {}", cacheKey, e);
		}
		return null;
	}

	/**
	 * 缓存分页结果（异步）
	 */
	private void cachePageResult(String cacheKey, PageResult<WorksDO> result) {
		CompletableFuture.runAsync(() -> {
			try {
				String pageKey = cacheKey + "page_" + result.getList().size();
				redisTemplate.opsForValue().set(pageKey, result, Duration.ofMinutes(5)); // 缓存5分钟
			} catch (Exception e) {
				log.warn("缓存分页结果失败: {}", cacheKey, e);
			}
		});
	}

	/**
	 * 高性能版本：使用Sorted Set索引
	 */
	public PageResult<WorksDO> getWorksPageOptimized(Integer pageNo, Integer pageSize, String name, Integer pointType) {
		// 参数校验
		if (pageNo == null || pageNo < 1) pageNo = 1;
		if (pageSize == null || pageSize < 1) pageSize = 10;
		if (pageSize > 100) pageSize = 100;

		try {
			// 构建索引键
			String indexKey = buildIndexKey(pointType);

			// 使用Sorted Set进行分页查询
			long start = (long) (pageNo - 1) * pageSize;
			long end = start + pageSize - 1;

			// 获取分页的作品ID
			Set<Object> worksIds = redisTemplate.opsForZSet().reverseRange(indexKey, start, end);

			if (CollUtil.isEmpty(worksIds)) {
				return PageResult.empty();
			}

			// 批量获取作品详情
			List<WorksDO> worksList = batchGetWorks(worksIds);

			// 应用名称过滤（如果需要）
			if (StringUtils.isNotBlank(name)) {
				worksList = worksList.stream()
						.filter(works -> works.getWorksCnName() != null && works.getWorksCnName().contains(name))
						.collect(Collectors.toList());
			}

			// 获取总数
			Long total = redisTemplate.opsForZSet().zCard(indexKey);

			PageResult<WorksDO> result = new PageResult<>();
			result.setList(worksList);
			result.setTotal(total != null ? total : 0L);

			return result;

		} catch (Exception e) {
			log.error("优化分页查询失败", e);
			// 降级到原始方法
			return executeOptimizedQuery(pageNo, pageSize, name, pointType);
		}
	}

	/**
	 * 构建索引键
	 */
	private String buildIndexKey(Integer pointType) {
		if (pointType != null) {
			return "works_index:type_" + pointType;
		}
		return "works_index:all";
	}

	/**
	 * 批量获取作品信息
	 */
	private List<WorksDO> batchGetWorks(Set<Object> worksIds) {
		if (CollUtil.isEmpty(worksIds)) {
			return new ArrayList<>();
		}
		Set<String> ids = worksIds.stream()
				.map(Object::toString)
				.collect(Collectors.toSet());

		HashOperations<String, String, WorksDO> hashOps = redisTemplate.opsForHash();
		List<WorksDO> worksList = hashOps.multiGet(WORKS_INFO_KEY, ids);

		return worksList.stream()
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
	}

	/**
	 * 维护索引：添加作品时调用
	 */
	public void addToIndex(WorksDO worksDO) {
		if (worksDO == null || worksDO.getId() == null) return;

		try {
			// 添加到全局索引
			redisTemplate.opsForZSet().add("works_index:all",
					worksDO.getId().toString(), System.currentTimeMillis());

			// 添加到类型索引
			if (worksDO.getPointType() != null) {
				redisTemplate.opsForZSet().add("works_index:type_" + worksDO.getPointType(),
						worksDO.getId().toString(), System.currentTimeMillis());
			}
		} catch (Exception e) {
			log.warn("添加索引失败, worksId: {}", worksDO.getId(), e);
		}
	}

	/**
	 * 维护索引：删除作品时调用
	 */
	public void removeFromIndex(Long worksId, Integer pointType) {
		if (worksId == null) return;

		try {
			String worksIdStr = worksId.toString();

			// 从全局索引删除
			redisTemplate.opsForZSet().remove("works_index:all", worksIdStr);

			// 从类型索引删除
			if (pointType != null) {
				redisTemplate.opsForZSet().remove("works_index:type_" + pointType, worksIdStr);
			}
		} catch (Exception e) {
			log.warn("删除索引失败, worksId: {}", worksId, e);
		}
	}

	/**
	 * 删除方法
	 */
	public void delWorksKey(Long worksId){
		// 定义worksKey
		String worksKey = WORKS_INFO_KEY;
		// 将worksId转换为字符串
		String hk = worksId.toString();
		// 从redis中删除worksKey对应的hk
		redisTemplate.opsForHash().delete(worksKey,hk);
	}

	public Long getWorksCount() {
		String worksKey = WORKS_INFO_KEY;
		return redisTemplate.opsForHash().size(worksKey);
	}

	public void saveWorksList(List<WorksDO> worksDOList) {
		if (CollUtil.isNotEmpty(worksDOList)) {
			String worksKey = WORKS_INFO_KEY;
			Map<String, WorksDO> map = worksDOList.stream().collect(Collectors.toMap(worksDO -> worksDO.getId().toString(), worksDO -> worksDO));
			redisTemplate.opsForHash().putAll(worksKey, map);
		}
	}

	public void delAllWorks() {
		redisTemplate.delete(WORKS_INFO_KEY);
	}
}

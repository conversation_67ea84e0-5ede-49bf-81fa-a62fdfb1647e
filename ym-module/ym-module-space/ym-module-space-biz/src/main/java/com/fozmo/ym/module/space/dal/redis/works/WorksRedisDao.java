package com.fozmo.ym.module.space.dal.redis.works;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.WORKS_INFO_KEY;

@Repository
public class WorksRedisDao {
	
	@Resource
	private RedisTemplate<String, Object> redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;

	/***
	 * 作品存储
	 */
	public void saveWorks(WorksDO worksDO){
		String worksKey = WORKS_INFO_KEY;
		String hk = worksDO.getId().toString();
		if (!haseWorksKey(worksDO.getId())){
			redisTemplate.opsForHash().put(worksKey,hk,worksDO);
		}

	}

	/**
	 *  判断作品是否存在
	 */
	public boolean haseWorksKey(Long worksId){
		String worksKey = WORKS_INFO_KEY;
		String hk = worksId.toString();

		return redisTemplate.opsForHash().hasKey(worksKey,hk);
	}

	/***
	 * 获取作品详情
	 */
	public WorksDO getWorksInfo(Long worksId){
		String worksKey = WORKS_INFO_KEY;
		String hk = worksId.toString();

		return (WorksDO) redisTemplate.opsForHash().get(worksKey,hk);
	}


	/**
	 *  获取作品分页
	 */
	public PageResult<WorksDO> getWorksPage(Integer pageNo, Integer pageSize, String name, Integer pointType) {
		// 构建作品分页
		PageResult<WorksDO> pageResult = new PageResult<>();
		pageResult.setTotal(0L);

		// 获取Hash操作对象
		HashOperations<String, String, WorksDO> hashOps = redisTemplate.opsForHash();
		// 创建扫描选项
		ScanOptions scanOptions = ScanOptions.scanOptions()
				.count(1000)      // 每次扫描数量（优化性能）
				.build();

		// 创建结果列表
		List<Map.Entry<String, WorksDO>> allResult = new ArrayList<>();

		// 扫描Hash
		try (Cursor<Map.Entry<String, WorksDO>> cursor = hashOps.scan(WORKS_INFO_KEY, scanOptions)) {
			while (cursor.hasNext()) {
				allResult.add(cursor.next());
			}
		}

		// 创建空间列表
		List<WorksDO> worksDOList = new ArrayList<>();
		if (CollUtil.isNotEmpty(allResult)) {
			worksDOList = allResult.stream().map(Map.Entry::getValue).collect(Collectors.toList());
		}

		if (CollUtil.isNotEmpty(worksDOList)) {
			if (StringUtils.isNotBlank(name)) {
				worksDOList = worksDOList.stream().filter(worksDO -> worksDO.getWorksCnName().contains(name)).collect(Collectors.toList());
			}
			if (pointType != null) {
				worksDOList = worksDOList.stream().filter(worksDO -> worksDO.getPointType().equals(pointType)).collect(Collectors.toList());
			}

			pageResult.setTotal((long) worksDOList.size());
		}
		// 判断结果是否为空
		if (CollUtil.isNotEmpty(worksDOList)) {
			// 计算起始位置和结束位置
			int start = (pageNo - 1) * pageSize;
			int end = Math.min(start + pageSize, worksDOList.size());
			// 设置分页结果
			pageResult.setList(worksDOList.subList(start, end));
		}
		return  pageResult;
	}

	/**
	 * 删除方法
	 */
	public void delWorksKey(Long worksId){
		// 定义worksKey
		String worksKey = WORKS_INFO_KEY;
		// 将worksId转换为字符串
		String hk = worksId.toString();
		// 从redis中删除worksKey对应的hk
		redisTemplate.opsForHash().delete(worksKey,hk);
	}

	public Long getWorksCount() {
		String worksKey = WORKS_INFO_KEY;
		return redisTemplate.opsForHash().size(worksKey);
	}

	public void saveWorksList(List<WorksDO> worksDOList) {
		if (CollUtil.isNotEmpty(worksDOList)) {
			String worksKey = WORKS_INFO_KEY;
			Map<String, WorksDO> map = worksDOList.stream().collect(Collectors.toMap(worksDO -> worksDO.getId().toString(), worksDO -> worksDO));
			redisTemplate.opsForHash().putAll(worksKey, map);
		}
	}

	public void delAllWorks() {
		redisTemplate.delete(WORKS_INFO_KEY);
	}
}

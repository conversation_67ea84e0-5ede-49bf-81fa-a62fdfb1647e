package com.fozmo.ym.module.space.service.editing;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.editing.vo.EditingPageReqVO;
import com.fozmo.ym.module.space.controller.admin.editing.vo.EditingSaveReqVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingInfoRespVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingRespVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingSaveReqVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.AppEditingUpdateReqVO;
import com.fozmo.ym.module.space.dal.dataobject.editing.EditingDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间编辑 Service 接口
 *
 * <AUTHOR>
 */
public interface EditingService {

    /**
     * 创建空间编辑
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEditing(@Valid EditingSaveReqVO createReqVO);

    /**
     * 更新空间编辑
     *
     * @param updateReqVO 更新信息
     */
    void updateEditing(@Valid AppEditingUpdateReqVO updateReqVO);

    /**
     * 删除空间编辑
     *
     * @param editId
     * @param spaceId
     */
    void deleteEditing(Long editId, Long spaceId);

    void deleteAllEditing(Long spaceId);

    /**
     * 获得空间编辑
     * @return 空间编辑
     */
    AppEditingInfoRespVO getEditing(Long id);

    AppEditingInfoRespVO getEditingInfo(Long editId, Long spaceId);

    /**
     * 获得空间编辑分页
     *
     * @param pageReqVO 分页查询
     * @return 空间编辑分页
     */
    PageResult<EditingDO> getEditingPage(EditingPageReqVO pageReqVO);

    Long createEditingApp(@Valid List<AppEditingSaveReqVO> createReqVO);

    void pushEditing(Long spaceId);

    AppEditingRespVO getList(Long spaceId);

    void deleteByWorkId(Long worksId);
}
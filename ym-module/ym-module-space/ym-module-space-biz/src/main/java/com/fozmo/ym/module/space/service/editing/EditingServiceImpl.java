package com.fozmo.ym.module.space.service.editing;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.account.api.account.dto.AccountRightsInfoDTO;
import com.fozmo.ym.module.space.controller.admin.editing.vo.EditingPageReqVO;
import com.fozmo.ym.module.space.controller.admin.editing.vo.EditingSaveReqVO;
import com.fozmo.ym.module.space.controller.app.editing.vo.*;
import com.fozmo.ym.module.space.dal.dataobject.editing.EditingDO;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import com.fozmo.ym.module.space.dal.dataobject.mount.MountWorksDO;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.dal.mysql.editing.EditingMapper;
import com.fozmo.ym.module.space.dal.redis.editing.SpaceEditingRedisDao;
import com.fozmo.ym.module.space.enums.MountType;
import com.fozmo.ym.module.space.service.file.SpaceFileService;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import com.fozmo.ym.module.space.service.space.SpaceService;
import com.fozmo.ym.module.space.service.works.WorksService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.EDITING_NOT_EXISTS;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.SPACE_NOT_EXISTS;
import static com.fozmo.ym.module.space.enums.MountType.*;

/**
 * 空间编辑 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EditingServiceImpl implements EditingService {

    @Resource
    private EditingMapper editingMapper;

    @Resource
    @Lazy
    private SpaceService spaceService;

    @Resource
    @Lazy
    private WorksService worksService;

    @Resource
    @Lazy
    private SpaceFileService spaceFileService;

    @Resource
    private SpaceEditingRedisDao spaceEditingRedisDao;

    @Resource
    private IdService idService;

    @Resource
    private AccountApi accountApi;

    @Resource
    @Lazy
    private MountWorksService mountWorksService;


    @Override
    public Long createEditing(EditingSaveReqVO createReqVO) {


        // 1 校验空间是否存在
        Long spaceId = createReqVO.getSpaceId();
        // 校验空间是否存在
        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        // 校验空间是否存在
        SpaceDO spaceDO = spaceService.getSpaceInfo(spaceId);
        if (ObjectUtil.isEmpty(spaceDO)) {
            throw exception(SPACE_NOT_EXISTS);
        }

        // 2校验账户信息
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginUserId)) {
            throw exception(ACCOUNT_NOT);
        }

        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginUserId);
        if (ObjectUtil.isEmpty(accountRightsInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }

        if (spaceDO.getAccountId().longValue() != loginUserId.longValue()) {
            throw exception(SPACE_NOT_EXISTS, "当前用户不是该空间的所有者");
        }
        // 3 校验当前空间是否存在编辑信息

        Integer point = createReqVO.getPoint();
        if (spaceEditingRedisDao.hasSpaceEditing(createReqVO.getEditId(), spaceId)) {
            return spaceEditingRedisDao.getSpaceEditing(createReqVO.getEditId(), spaceId).getId();
        }else {
            EditingDO editingDO = BeanUtils.toBean(createReqVO, EditingDO.class);
            editingDO.setId(idService.nextId("editing"));
            editingDO.setCreateId(loginUserId);
            editingDO.setCreateTime(LocalDateTime.now());
            editingDO.setCreateData(LocalDate.now());
            editingDO.setTenantId(accountRightsInfoDTO.getTenantId());
            editingDO.setTenantCode(accountRightsInfoDTO.getTenantCode());
            
            editingMapper.insert(editingDO);
            spaceEditingRedisDao.addSpaceEditing(editingDO);
            spaceEditingRedisDao.saveWorksEdit(editingDO.getEditId(), editingDO.getSpaceId());
            return editingDO.getId();
        }
    }

    @Override
    public void updateEditing(AppEditingUpdateReqVO updateReqVO) {

        // 2校验账户信息
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginUserId)) {
            throw exception(ACCOUNT_NOT);
        }

        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginUserId);
        if (ObjectUtil.isEmpty(accountRightsInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }
        
        // 校验存在
        if (ObjectUtil.isEmpty(updateReqVO.getId())) {
            throw exception(EDITING_NOT_EXISTS);
        }

        Long spaceId = updateReqVO.getSpaceId();
        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        // 校验 是否是挂点编辑
        Integer point = updateReqVO.getPoint();
        Long editId = updateReqVO.getEditId();
        /* 对象转换*/
        EditingDO updateObj = BeanUtils.toBean(updateReqVO, EditingDO.class);
        updateObj.setUpdateId(loginUserId);
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdateData(LocalDate.now());

        if (ObjectUtil.isEmpty(editId)) {
            throw exception(EDITING_NOT_EXISTS, "编辑ID不能为空");
        }
        if (ObjectUtil.isNotEmpty(point)) {
            if (spaceEditingRedisDao.hasSpaceEditing(Long.valueOf(point), spaceId)) {
                // 存在则说明 当前挂点是默认初始化数据 先删除当期挂点 在插入
                spaceEditingRedisDao.removeSpaceEditing(Long.valueOf(point), spaceId);
                spaceEditingRedisDao.addSpaceEditing(updateObj);
                editingMapper.updateById(updateObj);
            } else {
                // 说明已经修改过 此时 editId 存在 必定存在 且不等于 point
                if (spaceEditingRedisDao.hasSpaceEditing(editId, spaceId)) {
                    spaceEditingRedisDao.removeSpaceEditing(editId, spaceId);
                    spaceEditingRedisDao.addSpaceEditing(updateObj);
                    editingMapper.updateById(updateObj);
                }

            }
        } else {
            // 当pointId 不存在时 说明是非挂点数据
            if (spaceEditingRedisDao.hasSpaceEditing(editId, spaceId)) {
                spaceEditingRedisDao.removeSpaceEditing(editId, spaceId);
                spaceEditingRedisDao.addSpaceEditing(updateObj);
                editingMapper.updateById(updateObj);
            }
        }
     
    }

    @Override
    public void deleteEditing(Long editId, Long spaceId) {
        // 2校验账户信息
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginUserId)) {
            throw exception(ACCOUNT_NOT);
        }

        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginUserId);
        if (ObjectUtil.isEmpty(accountRightsInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }


        Long id = 0L;
        if (spaceEditingRedisDao.hasSpaceEditing(editId, spaceId)) {
            EditingDO editingDO = spaceEditingRedisDao.getSpaceEditing(editId, spaceId);
            id = editingDO.getId();
            spaceEditingRedisDao.removeSpaceEditing(editId, spaceId);
        } else {
            throw exception(EDITING_NOT_EXISTS);
        }
        if (id.longValue() != 0L) {
            validateEditingExists(id);
            // 删除
            editingMapper.deleteById(id);
        }
    }

    /**
     * @param spaceId
     */
    @Override
    public void deleteAllEditing(Long spaceId) {

        if (spaceEditingRedisDao.hasSpaceEditingList(spaceId)) {
            spaceEditingRedisDao.removeSpaceEditList(spaceId);
        }
        editingMapper.delete(new LambdaQueryWrapperX<EditingDO>().eq(EditingDO::getSpaceId, spaceId).eq(EditingDO::getDeleted, false));
    }

    private void validateEditingExists(Long id) {
        if (editingMapper.selectById(id) == null) {
            throw exception(EDITING_NOT_EXISTS);
        }
    }


    @Override
    public AppEditingInfoRespVO getEditing(Long id) {

        AppEditingInfoRespVO editingInfoRespVO = new AppEditingInfoRespVO();
        // 查看缓存是否存在数据
        EditingDO editingDO = editingMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(editingDO)) {

            if (editingDO.getDeleted()) {
                // 假如编辑信息已被删除则抛出异常
                throw exception(EDITING_NOT_EXISTS);
            }
            spaceEditingRedisDao.addSpaceEditing(editingDO);
            editingInfoRespVO = addEditingInfo(editingDO);
            return editingInfoRespVO;
        }else {
            return editingInfoRespVO;
        }
    }

    /**
     * @param editId
     * @param spaceId
     * @return
     */
    @Override
    public AppEditingInfoRespVO getEditingInfo(Long editId, Long spaceId) {
        AppEditingInfoRespVO appEditingInfoRespVO = new AppEditingInfoRespVO();
        if (ObjectUtil.isEmpty(editId) || ObjectUtil.isEmpty(spaceId)) {
            throw exception(EDITING_NOT_EXISTS);
        }
        if (spaceEditingRedisDao.hasSpaceEditing(editId, spaceId)) {
            EditingDO editingDO = spaceEditingRedisDao.getSpaceEditing(editId, spaceId);
            appEditingInfoRespVO = addEditingInfo(editingDO);

        }
        return appEditingInfoRespVO;
    }

    @Override
    public PageResult<EditingDO> getEditingPage(EditingPageReqVO pageReqVO) {
        return editingMapper.selectPage(pageReqVO);
    }

    /**
     * @param createReqVO
     * @return
     */
    @Override
    public Long createEditingApp(List<AppEditingSaveReqVO> createReqVO) {

        return 0L;
    }

    /**
     *
     * 发布空间
     * @param spaceId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushEditing(Long spaceId) {

        if (spaceId == null) {
            throw exception(SPACE_NOT_EXISTS);
        }

        if (!spaceEditingRedisDao.hasSpaceEditingList(spaceId)) {
            throw exception(EDITING_NOT_EXISTS);
        }
        // 1 发布时 需要校验账户信息
//        Long loginUserId = WebFrameworkUtils.getLoginUserId();
//        if (ObjectUtil.isEmpty(loginUserId)) {
//            throw exception(ACCOUNT_NOT);
//        }
//        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginUserId);
//        if (ObjectUtil.isEmpty(accountRightsInfoDTO)) {
//            throw exception(ACCOUNT_NOT);
//        }

        // 校验空间是否存在
        SpaceDO spaceDO = spaceService.getBaseSpace(spaceId);
        if (ObjectUtil.isEmpty(spaceDO)) {
            throw exception(SPACE_NOT_EXISTS);
        }

        List<EditingDO> editingDOList = spaceEditingRedisDao.getSpaceEditingList(spaceId);

        if (CollUtil.isEmpty(editingDOList)) {
            throw exception(EDITING_NOT_EXISTS);
            }

        // 数据转换

        List<MountWorksDO> mountWorksDOList = convertToMountWorksRespVO(editingDOList);

        boolean pullState = mountWorksService.batchAll(mountWorksDOList, spaceId);

        if (pullState) {
            deleteAllEditing(spaceId);
        }
    }

    private List<MountWorksDO> convertToMountWorksRespVO(List<EditingDO> appEditingRespVO) {
        List<MountWorksDO> mountWorksRespVO = new ArrayList<>();
        for (EditingDO editInfoDTO : appEditingRespVO) {
            MountWorksDO mountInfoDTO = BeanUtils.toBean(editInfoDTO, MountWorksDO.class);
            mountInfoDTO.setId(editInfoDTO.getId());
            mountInfoDTO.setMountId(editInfoDTO.getEditId());
            mountInfoDTO.setMountType(editInfoDTO.getEditType());
            mountInfoDTO.setExtraData(editInfoDTO.getExtraData());
            mountInfoDTO.setPoint(editInfoDTO.getPoint());
            mountInfoDTO.setMountSort(editInfoDTO.getEditSort());

            mountWorksRespVO.add(mountInfoDTO);
        }
        return mountWorksRespVO;
    }

    /**
     * @param spaceId
     * @return
     */
    @Override
    public AppEditingRespVO getList(Long spaceId) {

        List<EditingDO> editingList = getEditList(spaceId);
        if (CollUtil.isNotEmpty(editingList)) {
            if (!spaceEditingRedisDao.hasSpaceEditingList(spaceId)) {
                saveEditBatch(editingList);
            }
            AppEditingRespVO respVO = addEditingList(editingList, spaceId);
            return respVO;
        } else {
            return null;
        }
    }

    private List<EditingDO> getEditList(Long spaceId) {

        List<EditingDO> editInfoList = new ArrayList<>();
        if (spaceEditingRedisDao.hasSpaceEditingList(spaceId)) {
            editInfoList = spaceEditingRedisDao.getSpaceEditingList(spaceId);
        } else {
            // 拉取最新的挂载关系
            List<MountWorksDO> mountList = mountWorksService.getMountListBySpaceId(spaceId);
            if (ObjectUtil.isNotEmpty(mountList)) {
                for (MountWorksDO mountWorksDO : mountList) {
                    EditingDO editingDO = BeanUtils.toBean(mountWorksDO, EditingDO.class);
                    editingDO.setId(idService.nextId("editing"));
                    editingDO.setEditId(mountWorksDO.getMountId());
                    editingDO.setEditType(mountWorksDO.getMountType());
                    editingDO.setEditSort(mountWorksDO.getMountSort());
                    editingDO.setExtraData(mountWorksDO.getExtraData());
                    editingDO.setPoint(mountWorksDO.getPoint());
                    editInfoList.add(editingDO);
                }
            }
        }
        return editInfoList;
    }

    private void saveEditBatch(List<EditingDO> saveReqVOS) {

        if (CollUtil.isNotEmpty(saveReqVOS)) {
            for (EditingDO editingDO : saveReqVOS) {
                spaceEditingRedisDao.addSpaceEditing(editingDO);
                if (isWorksType(editingDO.getEditType())) {
                    spaceEditingRedisDao.saveWorksEdit(editingDO.getEditId(), editingDO.getSpaceId());
                }
            }

            editingMapper.insertBatch(saveReqVOS);
        }

    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorkId(Long worksId) {
        if (spaceEditingRedisDao.hasWorksList(worksId)) {
            // 获取 空间Id
            List<Long> spaceIdList = spaceEditingRedisDao.getWorksEditList(worksId);
            if (CollUtil.isNotEmpty(spaceIdList)) {
                spaceIdList.forEach(spaceId -> {
                    if (spaceEditingRedisDao.hasSpaceEditing(worksId, spaceId)) {
                        // 删除空间编辑信息
                        spaceEditingRedisDao.removeSpaceEditing(worksId, spaceId);
                    }

                });
            }
            spaceEditingRedisDao.removeWorksEditList(worksId);
        }
        // 删除数据库空间编辑信息
        editingMapper.delete(new LambdaQueryWrapperX<EditingDO>()
                .eq(EditingDO::getEditId, worksId)
                .eq(EditingDO::getDeleted, false)
        );
    }

    // 添加草稿数据
    private AppEditingInfoRespVO addEditingInfo(EditingDO editingDO) {
        // 添加草稿数据

        AppEditingInfoRespVO appEditingInfoRespVO = new AppEditingInfoRespVO();
        // 接入空间数据
        Long spaceId = editingDO.getSpaceId();
        SpaceDO spaceDO = spaceService.getBaseSpace(spaceId);
        if (ObjectUtil.isNotEmpty(spaceDO)) {
            appEditingInfoRespVO = (BeanUtils.toBean(spaceDO, AppEditingInfoRespVO.class));
        }
        EditInfoDTO editInfoDTO = new EditInfoDTO();
        editInfoDTO = BeanUtils.toBean(editingDO, EditInfoDTO.class);
        // 获取作品信息 或者资源信息
        Integer editType = editingDO.getEditType();
        Long editId = editingDO.getEditId();
        if (editType == UNKNOWN.getCode()) {
            return appEditingInfoRespVO;
        }

        if (isWorksType(editType)) {
            // 获取作品信息
            WorksDO worksDO = worksService.getWorksInfo(editId);
            if (ObjectUtil.isNotEmpty(worksDO)) {
                editInfoDTO.setEditName(worksDO.getWorksCnName());
                editInfoDTO.setEditDescription(worksDO.getCnDescription());
                editInfoDTO.setEditHeight(worksDO.getWorksHeight());
                editInfoDTO.setEditWidth(worksDO.getWorksWidth());
                editInfoDTO.setEditCover(worksDO.getWorksCover());
                editInfoDTO.setRightsId(worksDO.getRightsId());
                editInfoDTO.setAccountName(worksDO.getAccountName());
                editInfoDTO.setNickname(worksDO.getNickname());
                editInfoDTO.setHignUrl(worksDO.getHignUrl());
                editInfoDTO.setLowUrl(worksDO.getLowUrl());
                editInfoDTO.setAuthor(worksDO.getNickname());
            }
        }

        if (isResourceType(editType)) {
            SpaceFileDO fileDO = spaceFileService.getFile(editId);
            if (ObjectUtil.isNotEmpty(fileDO)) {
                Long accountId = WebFrameworkUtils.getLoginUserId();
                AccountRightsInfoDTO accountDO = new AccountRightsInfoDTO();
                if (ObjectUtil.isNotEmpty(accountId)) {
                    accountDO = accountApi.queryAccountRightsInfoById(accountId);
                }
                editInfoDTO.setRightsId(fileDO.getRightsId());
                editInfoDTO.setAccountName(accountDO.getName());
                editInfoDTO.setNickname(accountDO.getNickname());
                editInfoDTO.setHignUrl(fileDO.getFileUrl());
                editInfoDTO.setLowUrl(fileDO.getFileAttribute());
                editInfoDTO.setAuthor(accountDO.getNickname());
                editInfoDTO.setEditName(fileDO.getFileName());
                editInfoDTO.setEditDescription(fileDO.getDescription());
            }

        }

        if (isMetaType(editType)) {
            SpaceFileDO fileDO = spaceFileService.getFile(editId);
            if (ObjectUtil.isNotEmpty(fileDO)) {
                Long accountId = WebFrameworkUtils.getLoginUserId();
                AccountRightsInfoDTO accountDO = new AccountRightsInfoDTO();
                if (ObjectUtil.isNotEmpty(accountId)) {
                    accountDO = accountApi.queryAccountRightsInfoById(accountId);
                }
                editInfoDTO.setRightsId(fileDO.getRightsId());
                editInfoDTO.setAccountName(accountDO.getName());
                editInfoDTO.setNickname(accountDO.getNickname());
                editInfoDTO.setHignUrl(fileDO.getFileUrl());
                editInfoDTO.setLowUrl(fileDO.getFileAttribute());
                editInfoDTO.setAuthor(accountDO.getNickname());
                editInfoDTO.setEditName(fileDO.getFileName());
                editInfoDTO.setEditDescription(fileDO.getDescription());
            }
        }
        appEditingInfoRespVO.setEditInfo(editInfoDTO);
        return appEditingInfoRespVO;
    }


    private AppEditingRespVO addEditingList(List<EditingDO> editInfoList, Long spaceId) {
        AppEditingRespVO respVO = createMountResponse(spaceId);
        if (respVO == null) {
            return new AppEditingRespVO();
        }

        // 2. 处理空挂载列表
        if (CollUtil.isEmpty(editInfoList)) {
            return respVO;
        }

        // 3. 预分类容器
        List<EditInfoDTO> fileList = new ArrayList<>();
        List<EditInfoDTO> pointList = new ArrayList<>();
        List<EditInfoDTO> worksList = new ArrayList<>();
        List<EditInfoDTO> metaList = new ArrayList<>();


        // 4. 批量预加载数据
        Map<Long, WorksDO> worksCache = preloadWorksData(editInfoList);
        Map<Long, SpaceFileDO> fileCache = preloadFileData(editInfoList);
        Map<Long, SpaceFileDO> metaCache = preloadMetaData(editInfoList);
        AccountBaseInfoDTO accountInfo = getCurrentAccountInfo();

        // 5. 处理每个挂载项
        for (EditingDO mount : editInfoList) {
            EditInfoDTO dto = convertToEditInfoDTO(mount);

            Integer editType = mount.getEditType();
            if (editType == MountType.UNKNOWN.getCode()) {
                // 处理挂点类型
                if (ObjectUtil.isNotEmpty(dto.getPoint())) {
                    pointList.add(dto);
                }
            } else {
                // c处理作品类型
                if (MountType.isWorksType(editType)) {
                    populateWorksInfo(dto, worksCache.get(mount.getEditId()));
                    if (ObjectUtil.isNotEmpty(dto.getPoint())) {
                        pointList.add(dto);
                    } else {
                        worksList.add(dto);
                    }

                }
                // 处理资源类型
                if (MountType.isResourceType(editType)) {
                    populateFileInfo(dto, fileCache.get(mount.getEditId()), accountInfo);
                    fileList.add(dto);
                }
                if (isMetaType(editType)) {
                    populateMetaInfo(dto, metaCache.get(mount.getEditId()));
                    metaList.add(dto);
                }
            }

        }
        // 6. 设置分类结果
        respVO.setFileList(fileList);
        respVO.setPonitList(pointList);
        respVO.setWorksList(worksList);
        respVO.setMetaList(metaList);
        return respVO;
    }

    private void populateMetaInfo(EditInfoDTO dto, SpaceFileDO file) {
        if (file == null) {
            return;
        }
        dto.setRightsId(file.getRightsId());
        dto.setHignUrl(file.getFileUrl());
        dto.setLowUrl(file.getFileAttribute());
        dto.setEditName(file.getFileName());
        dto.setEditDescription(file.getDescription());
    }

    private Map<Long, SpaceFileDO> preloadMetaData(List<EditingDO> editInfoList) {
        Map<Long, SpaceFileDO> fileMap = new HashMap<>();
        Set<Long> fileIds = editInfoList.stream()
                .filter(m -> MountType.isMetaType(m.getEditType()))
                .map(EditingDO::getEditId)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(fileIds)) {
            return fileMap;
        } else {
            fileMap = fileIds.stream().map(fileId -> spaceFileService.getFile(fileId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SpaceFileDO::getFileId, Function.identity()));
        }
        return fileMap;
    }


    /**
     * 创建挂载响应对象
     */
    private AppEditingRespVO createMountResponse(Long spaceId) {

        AppEditingRespVO appEditingRespVO = new AppEditingRespVO();
        SpaceDO space = spaceService.getBaseSpace(spaceId);
        if (space == null) {
            deleteAllEditing(spaceId);
            return null;
        }
        appEditingRespVO = BeanUtils.toBean(space, AppEditingRespVO.class);
        appEditingRespVO.setSpaceId(space.getId());
        return appEditingRespVO;
    }

    /**
     * 预加载作品数据
     */
    private Map<Long, WorksDO> preloadWorksData(List<EditingDO> mounts) {
        // 1. 提取作品ID集合（使用更简洁的流操作）
        Set<Long> worksIds = mounts.stream()
                .filter(m -> MountType.isWorksType(m.getEditType()))
                .map(EditingDO::getEditId)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(worksIds)) {
            return Collections.emptyMap();
        }

        // 2. 使用批量查询接口（避免N+1问题）
        List<WorksDO> worksList = new ArrayList<>();
        for (Long worksId : worksIds) {
            WorksDO worksDO = worksService.getWorksInfoById(worksId);
            worksList.add(worksDO);
        }

        // 3. 直接构建映射关系（避免中间转换）
        return worksList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        WorksDO::getId,
                        Function.identity(),
                        (existing, replacement) -> existing // 处理键冲突
                ));
    }
    /**
     * 预加载文件数据
     */
    private Map<Long, SpaceFileDO> preloadFileData(List<EditingDO> editingDOS) {

        Map<Long, SpaceFileDO> fileMap = new HashMap<>();
        Set<Long> fileIds = editingDOS.stream()
                .filter(m -> MountType.isResourceType(m.getEditType()))
                .map(EditingDO::getEditId)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(fileIds)) {
            return fileMap;
        } else {
            fileMap = fileIds.stream().map(fileId -> spaceFileService.getFile(fileId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SpaceFileDO::getFileId, file -> file));
        }
        return fileMap;
    }


    /**
     * 获取当前账户信息
     */
    private AccountBaseInfoDTO getCurrentAccountInfo() {
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (accountId == null) {
            return new AccountBaseInfoDTO();
        }
        return accountApi.queryAccountBaseInfoById(accountId);
    }

    /**
     * 转换基础DTO对象
     */
    private EditInfoDTO convertToEditInfoDTO(EditingDO mount) {
        EditInfoDTO dto = new EditInfoDTO();
        BeanUtils.copyProperties(mount, dto);
        return dto;
    }

    /**
     * 填充作品信息
     */
    private void populateWorksInfo(EditInfoDTO dto, WorksDO works) {
        if (works == null) {
            return;
        }

        dto.setEditName(works.getWorksCnName());
        dto.setEditDescription(works.getCnDescription());
        dto.setEditHeight(works.getWorksHeight());
        dto.setEditWidth(works.getWorksWidth());
        dto.setEditCover(works.getWorksCover());
        dto.setRightsId(works.getRightsId());
        dto.setAccountName(works.getAccountName());
        dto.setNickname(works.getNickname());
        dto.setHignUrl(works.getHignUrl());
        dto.setLowUrl(works.getLowUrl());
        dto.setAuthor(works.getNickname());
    }

    /**
     * 填充文件信息
     */
    private void populateFileInfo(EditInfoDTO dto, SpaceFileDO file, AccountBaseInfoDTO accountInfo) {
        if (file == null) {
            return;
        }
        dto.setRightsId(file.getRightsId());
        dto.setAccountName(accountInfo.getName());
        dto.setNickname(accountInfo.getNickname());
        dto.setHignUrl(file.getFileUrl());
        dto.setLowUrl(file.getFileAttribute());
        dto.setAuthor(accountInfo.getNickname());
        dto.setEditName(file.getFileName());
        dto.setEditDescription(file.getDescription());
    }


}
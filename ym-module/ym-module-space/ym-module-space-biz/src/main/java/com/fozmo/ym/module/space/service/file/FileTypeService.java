package com.fozmo.ym.module.space.service.file;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.filetype.vo.FileTypePageReqVO;
import com.fozmo.ym.module.space.controller.admin.filetype.vo.FileTypeSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import jakarta.validation.Valid;

/**
 * 空间资源类型 Service 接口
 *
 * <AUTHOR>
 */
public interface FileTypeService {

    /**
     * 创建空间资源类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFileType(@Valid FileTypeSaveReqVO createReqVO);

    /**
     * 更新空间资源类型
     *
     * @param updateReqVO 更新信息
     */
    void updateFileType(@Valid FileTypeSaveReqVO updateReqVO);

    /**
     * 删除空间资源类型
     *
     * @param id 编号
     */
    void deleteFileType(Long id);

    /**
     * 获得空间资源类型
     *
     * @param id 编号
     * @return 空间资源类型
     */
    FileTypeDO getFileType(Long id);

    /**
     * 获得空间资源类型分页
     *
     * @param pageReqVO 分页查询
     * @return 空间资源类型分页
     */
    PageResult<FileTypeDO> getFileTypePage(FileTypePageReqVO pageReqVO);

}
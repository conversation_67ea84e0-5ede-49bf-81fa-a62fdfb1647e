package com.fozmo.ym.module.space.service.file;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.filetype.vo.FileTypePageReqVO;
import com.fozmo.ym.module.space.controller.admin.filetype.vo.FileTypeSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import com.fozmo.ym.module.space.dal.mysql.filetype.FileTypeMapper;
import com.fozmo.ym.module.space.dal.redis.file.SpaceFileTypeRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.FILE_TYPE_NOT_EXISTS;

/**
 * 空间资源类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileTypeServiceImpl implements FileTypeService {

    @Resource
    private FileTypeMapper fileTypeMapper;

    @Resource
    private IdService idService;

    @Resource
    private SpaceFileTypeRedisDao spaceFileTypeRedisDao;

    @Override
    public Long createFileType(FileTypeSaveReqVO createReqVO) {
        // 插入
        FileTypeDO createObj = BeanUtils.toBean(createReqVO, FileTypeDO.class);

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setTypeId(idService.nextId("fileType"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");

        fileTypeMapper.insert(createObj);
        spaceFileTypeRedisDao.saveFileType(createObj);
        // 返回
        return createObj.getTypeId();
    }

    @Override
    public void updateFileType(FileTypeSaveReqVO updateReqVO) {

        if (spaceFileTypeRedisDao.hasFileType(updateReqVO.getTypeId())) {
            spaceFileTypeRedisDao.deleteFileType(updateReqVO.getTypeId());
        }
        // 校验存在
        validateFileTypeExists(updateReqVO.getTypeId());
        // 更新
        FileTypeDO updateObj = BeanUtils.toBean(updateReqVO, FileTypeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        fileTypeMapper.updateById(updateObj);

        getFileType(updateObj.getTypeId());

    }

    @Override
    public void deleteFileType(Long id) {

        if (spaceFileTypeRedisDao.hasFileType(id)) {
            spaceFileTypeRedisDao.deleteFileType(id);
            fileTypeMapper.deleteById(id);
        } else {
            // 校验存在
            validateFileTypeExists(id);
            // 删除
            fileTypeMapper.deleteById(id);
        }

    }

    private void validateFileTypeExists(Long id) {
        if (fileTypeMapper.selectById(id) == null) {
            throw exception(FILE_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public FileTypeDO getFileType(Long id) {

        FileTypeDO fileTypeDO = new FileTypeDO();
        if (spaceFileTypeRedisDao.hasFileType(id)) {
            fileTypeDO = spaceFileTypeRedisDao.getFileType(id);
        }

        if (ObjectUtil.isNull(fileTypeDO)) {
            fileTypeDO = fileTypeMapper.selectById(id);

            if (ObjectUtil.isNotEmpty(fileTypeDO)) {
                spaceFileTypeRedisDao.saveFileType(fileTypeDO);
            }
        }
        return fileTypeDO;
    }

    @Override
    public PageResult<FileTypeDO> getFileTypePage(FileTypePageReqVO pageReqVO) {

        PageResult<FileTypeDO> pageResult = spaceFileTypeRedisDao.getTypePage(pageReqVO.getPageNo(), pageReqVO.getPageSize());

        if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0 || CollUtil.isNotEmpty(pageResult.getList())) {
            pageResult = fileTypeMapper.selectPage(pageReqVO);
            if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0 && CollUtil.isNotEmpty(pageResult.getList())) {
                pageResult.getList().forEach(fileTypeDO -> {
                    spaceFileTypeRedisDao.saveFileType(fileTypeDO);
                });
            }
        }
        return pageResult;
    }

}
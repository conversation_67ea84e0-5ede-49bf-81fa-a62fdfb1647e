package com.fozmo.ym.module.space.service.file;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.file.vo.FilePageReqVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileSaveReqVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileSaveRespVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileUploadRespVO;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import jakarta.validation.Valid;

import java.util.List;

public interface SpaceFileService {

    /**
     * 创建资源
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    FileSaveRespVO createFile(@Valid FileSaveReqVO createReqVO);

    /**
     * 更新资源
     *
     * @param updateReqVO 更新信息
     * @return
     */
    FileUploadRespVO updateFile(@Valid FileSaveReqVO updateReqVO);

    /**
     * 删除资源
     *
     * @param id 编号
     */
    void deleteFile(Long id);

    /**
     * 获得资源
     *
     * @param id 编号
     * @return 资源
     */
    SpaceFileDO getFile(Long id);

    /**
     * 获得资源分页
     *
     * @param pageReqVO 分页查询
     * @return 资源分页
     */
    PageResult<SpaceFileDO> getFilePage(FilePageReqVO pageReqVO);

    List<SpaceFileDO> getFileList();

}

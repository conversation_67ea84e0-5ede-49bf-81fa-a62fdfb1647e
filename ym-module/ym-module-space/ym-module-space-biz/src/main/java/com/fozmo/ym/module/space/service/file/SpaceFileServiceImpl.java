package com.fozmo.ym.module.space.service.file;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.file.vo.FilePageReqVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileSaveReqVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileSaveRespVO;
import com.fozmo.ym.module.space.controller.admin.file.vo.FileUploadRespVO;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import com.fozmo.ym.module.space.dal.dataobject.filetype.FileTypeDO;
import com.fozmo.ym.module.space.dal.mysql.file.SpaceFileMapper;
import com.fozmo.ym.module.space.dal.redis.file.SpaceFileRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.FILE_NOT_EXISTS;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.FILE_TYPE_NOT_EXISTS;

@Service
@Validated
public class SpaceFileServiceImpl implements SpaceFileService {

    // 定义文件状态常量
    private static final int FILE_STATUS_UPLOADING = 9;
    private static final int FILE_STATUS_UPDATE = 1;
    @Resource
    private SpaceFileMapper spaceFileMapper;
    @Resource
    private IdService idService;
    @Resource
    private FileTypeService fileTypeService;
    @Resource
    private SpaceFileRedisDao spaceFileRedisDao;

    /**
     * @return 文件创建响应列表
     * @throws RuntimeException 用户未登录时抛出异常
     */
    @Override
    public FileSaveRespVO createFile(FileSaveReqVO reqVO) {
        // 1. 验证用户登录状态
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }


        // 4.2 获取文件类型信息
        FileTypeDO fileTypeDO = fileTypeService.getFileType(reqVO.getFileTypeId());
        if (fileTypeDO == null) {
            throw exception(FILE_TYPE_NOT_EXISTS);
        }

        // 4.3 构建文件路径和名称
        SpaceFileDO fileDO = BeanUtils.toBean(reqVO, SpaceFileDO.class);

        fileDO.setFileTypeCode(fileTypeDO.getTypeCode());

        // 设置用户相关信息
        fileDO.setCreateId(loginUser.getId());
        fileDO.setCreator(loginUser.getUsername());
        fileDO.setCreateData(LocalDate.now());

        // 设置系统信息
        fileDO.setFileId(idService.nextId("spaceFile"));
        fileDO.setTenantId(loginUser.getTenantId());
        fileDO.setFileStatus(0);
        fileDO.setTenantCode(String.valueOf(loginUser.getTenantId()));
        spaceFileMapper.insert(fileDO);
        return BeanUtils.toBean(fileDO, FileSaveRespVO.class);
    }

    /**
     * 更新空间资源
     * @param updateReqVO
     * @return
     */
    @Override
    public FileUploadRespVO updateFile(FileSaveReqVO updateReqVO) {

        FileUploadRespVO fileUploadRespVO = new FileUploadRespVO();
        // 1. 用户登录校验（优化异常类型）
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            return fileUploadRespVO;
        }
        if (spaceFileRedisDao.hasFile(updateReqVO.getFileId())) {
            // 删除缓存
            spaceFileRedisDao.deleteFile(updateReqVO.getFileId());
        } else {
            SpaceFileDO fileDO = spaceFileMapper.selectById(updateReqVO.getFileId());
            if (ObjectUtil.isNotEmpty(fileDO)) {
                fileDO = BeanUtils.toBean(updateReqVO, SpaceFileDO.class);
                fileDO.setUpdateId(loginUser.getId());
                fileDO.setUpdater(loginUser.getUsername());
                fileDO.setUploadTime(LocalDateTime.now());
                fileDO.setUpdateData(LocalDate.now()); // 改为LocalDateTime
//                fileDO.setFileStatus(FILE_STATUS_UPDATE);
                fileDO.setFileSize(updateReqVO.getFileSize());
                spaceFileMapper.updateById(fileDO);
                fileUploadRespVO = BeanUtils.toBean(fileDO, FileUploadRespVO.class);
            } else {
                throw exception(FILE_NOT_EXISTS);
            }
        }
        getFile(updateReqVO.getFileId());
        return fileUploadRespVO;
    }


    @Override
    public void deleteFile(Long id) {
        if (spaceFileRedisDao.hasFile(id)) {
            spaceFileRedisDao.deleteFile(id);
            // 删除
            spaceFileMapper.deleteById(id);
        } else {
            validateFileExists(id);
            // 删除
            spaceFileMapper.deleteById(id);
        }
      
    }

    private void validateFileExists(Long id) {
        if (spaceFileMapper.selectById(id) == null) {
            throw exception(FILE_NOT_EXISTS);
        }
    }

    @Override
    public SpaceFileDO getFile(Long id) {
        SpaceFileDO spaceFileDO = new SpaceFileDO();
        if (spaceFileRedisDao.hasFile(id)) {
            spaceFileDO = spaceFileRedisDao.getFIleInfo(id);
        }

        if (ObjectUtil.isEmpty(spaceFileDO)) {
            spaceFileDO = spaceFileMapper.selectById(id);

            if (ObjectUtil.isNotEmpty(spaceFileDO)) {
                spaceFileRedisDao.saveFile(spaceFileDO);
            }

        }
        return spaceFileMapper.selectById(id);
    }

    @Override
    public PageResult<SpaceFileDO> getFilePage(FilePageReqVO pageReqVO) {

        PageResult<SpaceFileDO> pageResult = new PageResult<>();
        pageResult = spaceFileRedisDao.getFilePage(pageReqVO.getFileTypeId(), pageReqVO.getRightsId(), pageReqVO.getPageNo(), pageReqVO.getPageSize());

        if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0L || CollUtil.isEmpty(pageResult.getList())) {
            pageResult = spaceFileMapper.selectPage(pageReqVO);

            if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0 && CollUtil.isNotEmpty(pageResult.getList())) {
                pageResult.getList().forEach(spaceFileDO -> {
                    spaceFileRedisDao.saveFile(spaceFileDO);
                });
            }
        }
        return pageResult;
    }

    /**
     * @return 
     */
    @Override
    public List<SpaceFileDO> getFileList() {

        List<SpaceFileDO> spaceFileDOList = spaceFileRedisDao.getFileList();
        if (CollUtil.isEmpty(spaceFileDOList)) {
            spaceFileDOList = spaceFileMapper.selectList();
            if (CollUtil.isNotEmpty(spaceFileDOList)) {
                spaceFileDOList.forEach(spaceFileDO -> {
                    spaceFileRedisDao.saveFile(spaceFileDO);
                });
            }
        }
        return spaceFileDOList;
    }

}
package com.fozmo.ym.module.space.service.hot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.module.space.dal.dataobject.hot.SpaceHotDO;
import com.fozmo.ym.module.space.dal.mysql.hot.SpaceHotMapper;
import com.fozmo.ym.module.space.dal.redis.hot.HotRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class SpaceHotServiceImpl implements SpaceHotService {

    @Resource
    private SpaceHotMapper spaceHotMapper;
    @Resource
    private HotRedisDao spaceHotRedisDao;
    /**
     * @param type
     * @return
     */
    @Override
    public List<SpaceHotDO> findByType(Integer type) {
        // 先查缓存
        List<SpaceHotDO> spaceHotDOS = spaceHotRedisDao.getHotList(type);

        if (CollUtil.isEmpty(spaceHotDOS)) {
            LambdaQueryWrapperX<SpaceHotDO> wrapper = new LambdaQueryWrapperX<>();
            wrapper.eq(SpaceHotDO::getType, type);
            wrapper.eq(SpaceHotDO::getDeleted, false);
            wrapper.orderByAsc(SpaceHotDO::getId);
            spaceHotDOS = spaceHotMapper.selectList(wrapper);

            if (CollUtil.isNotEmpty(spaceHotDOS)) {
                spaceHotDOS.forEach(spaceHotDO -> spaceHotRedisDao.saveHot(spaceHotDO));
            }
        }

        return spaceHotDOS;
    }

    /**
     * @param typeId
     */
    @Override
    public void deleteHotSpace(Long typeId) {
        // 空间类型 type =2
        Integer type = 2;
        if (spaceHotRedisDao.hasHot(typeId, type)) {

            SpaceHotDO spaceHotDO = spaceHotRedisDao.getHot(typeId, type);
            if (ObjectUtil.isNotEmpty(spaceHotDO)) {
                spaceHotMapper.deleteById(spaceHotDO.getId());
            }

            spaceHotRedisDao.deleteHot(typeId, type);
        }
    }

    /**
     * @param typeId
     */
    @Override
    public void deleteHotWork(Long typeId) {
        // 作品类型 type =3
        Integer type = 3;
        // 判断是否有热榜作品
        if (spaceHotRedisDao.hasHot(typeId, type)) {

            // 获取热榜作品
            SpaceHotDO spaceHotDO = spaceHotRedisDao.getHot(typeId, type);
            // 判断作品是否为空
            if (ObjectUtil.isNotEmpty(spaceHotDO)) {
                // 删除数据库中的热榜作品
                spaceHotMapper.deleteById(spaceHotDO.getId());
            }

            // 删除缓存中的热榜作品
            spaceHotRedisDao.deleteHot(typeId, type);
        }
    }

    /**
     * @param typeId
     */
    @Override
    public void deleteHotTemplate(Long typeId) {
        // 模板类型 type =1
        Integer type = 1;
        // 判断是否存在热模板
        if (spaceHotRedisDao.hasHot(typeId, type)) {

            // 获取热模板
            SpaceHotDO spaceHotDO = spaceHotRedisDao.getHot(typeId, type);
            // 判断热模板是否为空
            if (ObjectUtil.isNotEmpty(spaceHotDO)) {
                // 删除数据库中的热模板
                spaceHotMapper.deleteById(spaceHotDO.getId());
            }

            // 删除缓存中的热模板
            spaceHotRedisDao.deleteHot(typeId, type);
        }
    }

    /**
     * @param typeId
     */
    @Override
    public void deleteHotAccount(Long typeId) {
        // 账号类型 type =4
        Integer type = 4;
        // 判断是否存在热点账号
        if (spaceHotRedisDao.hasHot(typeId, type)) {

            // 获取热点账号
            SpaceHotDO spaceHotDO = spaceHotRedisDao.getHot(typeId, type);
            // 判断热点账号是否为空
            if (ObjectUtil.isNotEmpty(spaceHotDO)) {
                // 删除热点账号
                spaceHotMapper.deleteById(spaceHotDO.getId());
            }

            // 删除热点账号
            spaceHotRedisDao.deleteHot(typeId, type);
        }
    }


}

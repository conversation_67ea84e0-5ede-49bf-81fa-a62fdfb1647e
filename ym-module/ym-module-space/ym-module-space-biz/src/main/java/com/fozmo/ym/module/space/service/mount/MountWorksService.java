package com.fozmo.ym.module.space.service.mount;

import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksInfoRespVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksRespVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.mount.MountWorksDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间挂在作品 Service 接口
 *
 * <AUTHOR>
 */
public interface MountWorksService {

    /**
     * 创建空间挂在作品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMountWorks(@Valid MountWorksSaveReqVO createReqVO);
    /**
     * 更新空间挂在作品
     *
     * @param updateReqVO 更新信息
     */
    void updateMountWorks(@Valid MountWorksSaveReqVO updateReqVO);

    /**
     * 删除空间挂在作品
     *
     * @param id 编号
     */
    void deleteMountWorks(Long id);

    MountWorksRespVO getMountList(Long spaceId);

    List<MountWorksDO> getMountListBySpaceId(Long spaceId);

    boolean pushMount(MountWorksRespVO respVO);

    MountWorksInfoRespVO getMountInfo(Long id);

    boolean initMount(Long spaceId, Integer spaceNum);

    void deleteAllMount(Long spaceId);


    void deleteByWorksId(Long worksId);

    List<Long> getWorksMountList(Long id);

    Boolean batchMount(MountWorksRespVO reqVO);

    /***
     *  草稿箱 一键发布使用
     */
    Boolean batchAll(List<MountWorksDO> reqVO, Long spaceId);

    Boolean batchUnMount(List<Long> ids);

    Boolean unMountType(Long spaceId, Integer type);
}
package com.fozmo.ym.module.space.service.mount;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountRightsInfoDTO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountInfoDTO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksInfoRespVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksRespVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.file.SpaceFileDO;
import com.fozmo.ym.module.space.dal.dataobject.mount.MountWorksDO;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.dal.mysql.mountworks.MountWorksMapper;
import com.fozmo.ym.module.space.dal.redis.mount.SpaceMountRedisDao;
import com.fozmo.ym.module.space.enums.MountType;
import com.fozmo.ym.module.space.service.editing.EditingService;
import com.fozmo.ym.module.space.service.file.SpaceFileService;
import com.fozmo.ym.module.space.service.space.SpaceService;
import com.fozmo.ym.module.space.service.works.WorksService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.rights.enums.ErrorCodeConstants.RIGHTS_NOT;
import static com.fozmo.ym.module.space.constans.SpaceRedisConstants.LOCK_MOUNT_KEY;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.*;
import static com.fozmo.ym.module.space.enums.MountType.isMetaType;

/**
 * 空间挂在作品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MountWorksServiceImpl implements MountWorksService {

    @Resource
    private MountWorksMapper mountWorksMapper;

    @Resource
    @Lazy
    private WorksService worksService;

    @Resource
    @Lazy
    private SpaceFileService spaceFileService;

    @Resource
    private SpaceMountRedisDao spaceMountRedisDao;

    @Resource
    private AccountApi accountApi;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    @Lazy
    private SpaceService spaceService;

    @Resource
    private IdService idService;

    @Resource
    @Lazy
    private EditingService editingService;

    @Override
    public Long createMountWorks(MountWorksSaveReqVO createReqVO) {
        // 2校验账户信息
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginUserId)) {
            throw exception(UNAUTHORIZED);
        }

        // 1 校验空间是否存在
        Long spaceId = createReqVO.getSpaceId();
        // 校验空间是否存在
        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        // 校验空间是否存在
        SpaceDO spaceDO = spaceService.getBaseSpace(spaceId);
        if (ObjectUtil.isEmpty(spaceDO)) {
            throw exception(SPACE_NOT_EXISTS);
        }



        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginUserId);
        if (ObjectUtil.isEmpty(accountRightsInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }

        if (spaceDO.getAccountId().longValue() != loginUserId.longValue()) {
            throw exception(SPACE_NOT_EXISTS, "当前用户不是该空间的所有者");
        }

        // 3 校验当前空间是否存在编辑信息

        if (spaceMountRedisDao.hasSpaceMount(createReqVO.getMountId(), spaceId)) {
            return spaceMountRedisDao.getSpaceMount(createReqVO.getMountId(), spaceId).getId();
        } else {
            MountWorksDO editingDO = BeanUtils.toBean(createReqVO, MountWorksDO.class);
            editingDO.setId(idService.nextId("mount"));
            editingDO.setAccountId(loginUserId);
            editingDO.setCreateId(loginUserId);
            editingDO.setCreateTime(LocalDateTime.now());
            editingDO.setCreateData(LocalDate.now());
            editingDO.setTenantId(accountRightsInfoDTO.getTenantId());
            editingDO.setTenantCode(accountRightsInfoDTO.getTenantCode());
            mountWorksMapper.insert(editingDO);
            spaceMountRedisDao.saveSpaceMount(editingDO);
            if (MountType.isWorksType(editingDO.getMountType())) {
                spaceMountRedisDao.saveWorksMount(editingDO.getMountId(), editingDO.getSpaceId());
            }
            return editingDO.getId();
        }
    }

    @Override
    public void updateMountWorks(MountWorksSaveReqVO updateReqVO) {

        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginUserId)) {
            throw exception(UNAUTHORIZED);
        }

        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginUserId);
        if (ObjectUtil.isEmpty(accountRightsInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }

        Long userRightsId = accountRightsInfoDTO.getRightsId();
        if (ObjectUtil.isEmpty(userRightsId)) {
            throw exception(RIGHTS_NOT);
        } else {
            if (userRightsId < 0L) {
                throw exception(RIGHTS_NOT);
            }
        }
        // 1 校验存在 查询数据库文件是否存在
        MountWorksDO mountWorksDO = mountWorksMapper.selectById(updateReqVO.getId());
        if (ObjectUtil.isEmpty(mountWorksDO)) {
            throw exception(MOUNT_WORKS_NOT_EXISTS);
        }
        // 校验文件 或者素材权益
        if (MountType.isMetaType(mountWorksDO.getMountType()) || MountType.isResourceType(mountWorksDO.getMountType())) {
            // 校验素材是否存在
            SpaceFileDO spaceFileDO = spaceFileService.getFile(mountWorksDO.getMountId());
            if (ObjectUtil.isEmpty(spaceFileDO)) {
                throw exception(FILE_NOT_EXISTS);
            }
            if (spaceFileDO.getDeleted().equals(Boolean.TRUE)) {
                throw exception(FILE_NOT_EXISTS);
            }
            if (ObjectUtil.isEmpty(spaceFileDO.getRightsId()) || spaceFileDO.getRightsId() > userRightsId) {
                throw exception(RIGHTS_NOT);
            }
        }
        // 2 校验当前空间是否存在编辑信息
        if (spaceMountRedisDao.hasSpaceMount(mountWorksDO.getMountId(), updateReqVO.getSpaceId())) {
            // 先删除
            spaceMountRedisDao.deleteSpaceMount(mountWorksDO.getMountId(), updateReqVO.getSpaceId());
        } else {
            throw exception(MOUNT_WORKS_NOT_EXISTS);
        }


        mountWorksDO = BeanUtils.toBean(updateReqVO, MountWorksDO.class);

        if (MountType.isWorksType(mountWorksDO.getMountType())) {
            spaceMountRedisDao.saveWorksMount(mountWorksDO.getMountId(), mountWorksDO.getSpaceId());
        }
        spaceMountRedisDao.saveSpaceMount(mountWorksDO);
        mountWorksMapper.updateById(mountWorksDO);
    }

    @Override
    public void deleteMountWorks(Long id) {
        // 先查询  数据
        MountWorksDO mountWorksDO = mountWorksMapper.selectById(id);
        if (ObjectUtil.isEmpty(mountWorksDO)) {
            throw exception(MOUNT_WORKS_NOT_EXISTS);
        }
        spaceMountRedisDao.deleteSpaceMount(id, mountWorksDO.getSpaceId());
        mountWorksMapper.deleteById(id);

    }

    private void validateMountWorksExists(Long id) {
        if (mountWorksMapper.selectById(id) == null) {
            throw exception(MOUNT_WORKS_NOT_EXISTS);
        }
    }
    /**
     * @param spaceId
     * @return
     */
    @Override
    public MountWorksRespVO getMountList(Long spaceId) {
        List<MountWorksDO> mountWorksDOList = getList(spaceId);
        if (CollUtil.isNotEmpty(mountWorksDOList)) {
            return addMountList(mountWorksDOList, spaceId);
        }
        return null;
    }

    /**
     * @param spaceId
     * @return
     */
    @Override
    public List<MountWorksDO> getMountListBySpaceId(Long spaceId) {
        return getList(spaceId);
    }

    private List<MountWorksDO> getList(Long spaceId) {
        if (spaceMountRedisDao.haseSpaceMountList(spaceId)) {
            return spaceMountRedisDao.getSpaceMountList(spaceId);
        } else {
            LambdaQueryWrapperX<MountWorksDO> query = new LambdaQueryWrapperX<MountWorksDO>()
                    .eq(MountWorksDO::getSpaceId, spaceId)
                    .eq(MountWorksDO::getDeleted, false)
                    .orderByDesc(MountWorksDO::getMountSort);
            List<MountWorksDO> mountWorksDOList = mountWorksMapper.selectList(query);
            if (CollUtil.isNotEmpty(mountWorksDOList)) {
                spaceMountRedisDao.saveSpaceMountList(mountWorksDOList);
                return mountWorksDOList;
            }

        }
        return List.of();
    }

    /**
     * @param respVO
     * @return
     */
    @Override
    public boolean pushMount(MountWorksRespVO respVO) {
        // 获取本次需要执行的所有挂载关系
        List<MountInfoDTO> mountPointList = respVO.getPonitList();
        // 需要删除的非挂点数据
        List<MountInfoDTO> mountWorksList = respVO.getWorksList();

        if (ObjectUtil.isNotEmpty(mountWorksList)) {
            for (MountInfoDTO mountInfoDTO : mountWorksList) {
                if (MountType.isWorksType(mountInfoDTO.getMountType()) && ObjectUtil.isEmpty(mountInfoDTO.getPoint())) {
                    spaceMountRedisDao.deleteSpaceMount(mountInfoDTO.getId(), respVO.getSpaceId());
                    spaceMountRedisDao.deleteWorksInfo(mountInfoDTO.getId(), respVO.getSpaceId());
                    mountWorksMapper.deleteById(mountInfoDTO.getId());
                } else {
                    throw exception(MOUNT_INIT_NOT_DELETE);
                }
            }
        }

        Long spaceId = respVO.getSpaceId();


        List<Long> mountIds = mountPointList.stream().map(MountInfoDTO::getId).collect(Collectors.toList());
        // 定义获取所有点位数据的映射关系
        List<MountWorksDO> validMountWorksDOList = mountWorksMapper.selectByIds(mountIds);
        Map<Long, MountWorksDO> mountMap = validMountWorksDOList.stream().collect(Collectors.toMap(MountWorksDO::getId, Function.identity()));

        // 遍历内存中所有的点位数据
        if (ObjectUtil.isNotEmpty(mountPointList) && CollUtil.isNotEmpty(mountMap)) {
            List<MountWorksDO> updateMountWorksDOList = new ArrayList<>();
            mountPointList.forEach(mountWorksDO -> {
                if (mountMap.containsKey(mountWorksDO.getId())) {
                    MountWorksDO mountWorks = mountMap.get(mountWorksDO.getId());
                    if (mountWorks.getMountId() != mountWorksDO.getMountId()) {
                        // 当原始挂点和现在挂点不一致的时候，则进行数据更新
                        spaceMountRedisDao.deleteSpaceMount(mountWorks.getMountId(), spaceId);
                        mountWorks.setMountId(mountWorksDO.getMountId());
                        mountWorks.setMountType(mountWorksDO.getMountType());
                        mountWorks.setMountSort(mountWorksDO.getMountSort());
                        mountWorks.setExtraData(mountWorksDO.getExtraData());
                        // 删除原缓存数据

                        spaceMountRedisDao.saveSpaceMount(mountWorks);
                        if (MountType.isWorksType(mountWorksDO.getMountType())) {
                            spaceMountRedisDao.saveWorksMount(mountWorksDO.getMountId(), spaceId);
                        }

                        updateMountWorksDOList.add(mountWorks);
                    }

                }

            });
            if (CollUtil.isNotEmpty(updateMountWorksDOList)) {
                mountWorksMapper.updateBatch(updateMountWorksDOList);
                // 删除草稿箱
                editingService.deleteAllEditing(spaceId);
            }


        } else {
            return false;
        }


        return true;
    }

    /**
     * @param id
     * @return
     */
    @Override
    public MountWorksInfoRespVO getMountInfo(Long id) {
        MountWorksDO mountWorksDO = mountWorksMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(mountWorksDO)) {

            if (!spaceMountRedisDao.hasSpaceMount(mountWorksDO.getMountId(), mountWorksDO.getSpaceId())) {
                spaceMountRedisDao.saveSpaceMount(mountWorksDO);
            }
            return addMountInfo(mountWorksDO);
        }
        return null;
    }

    /**
     * @param spaceId
     * @param spaceNum
     * @return
     */
    @Override
    public boolean initMount(Long spaceId, Integer spaceNum) {
        // 当 空间创建完成时 初始化挂点数据
        List<MountWorksDO> mountWorksDOList = new ArrayList<>();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        for (int i = 1; i <= spaceNum; i++) {
            MountWorksDO mountWorksDO = new MountWorksDO();
            mountWorksDO.setSpaceId(spaceId);
            mountWorksDO.setId(idService.nextId("mount"));
            mountWorksDO.setPoint(i);
            mountWorksDO.setMountId(Long.valueOf(i));
            mountWorksDO.setMountType(0);
            mountWorksDO.setMountSort(i);
            mountWorksDO.setCreateId(userId);
            mountWorksDOList.add(mountWorksDO);
        }
        mountWorksMapper.insertBatch(mountWorksDOList);
        spaceMountRedisDao.saveSpaceMountList(mountWorksDOList);
        return true;
    }

    /**
     * @param spaceId
     */

    @Override
    public void deleteAllMount(Long spaceId) {
        if (spaceId == null) {
            log.warn("尝试删除挂载时传入空spaceId");
            return;
        }

        // 1. 使用批量操作减少锁竞争
        List<Long> mountIds = spaceMountRedisDao.getWorksMountList(spaceId);
        if (CollUtil.isEmpty(mountIds)) {
            log.info("空间无挂载记录, spaceId: {}", spaceId);
            deleteSpaceRecords(spaceId);
            return;
        }

//        // 2. 并行处理挂载点删除
//        deleteSpaceMountsInParallel(spaceId, mountIds);

        // 3. 删除空间挂载列表
        spaceMountRedisDao.deleteMountBySpaceId(spaceId);

        // 4. 删除数据库记录
        deleteSpaceRecords(spaceId);
    }

    /**
     * @param worksId
     */
    @Override
    public void deleteByWorksId(Long worksId) {
        if (ObjectUtil.isEmpty(worksId)) {
            return;
        }

        if (!spaceMountRedisDao.hasWorksMountList(worksId)) {
            log.info("作品无挂载记录, worksId: {}", worksId);
            return;
        }
        // 获取 这个 作品 挂载的所有的空间
        List<Long> spaceIdList = spaceMountRedisDao.getWorksMountList(worksId);
        if (CollUtil.isNotEmpty(spaceIdList)) {
            spaceIdList.forEach(spaceId -> {
                // 删除空间下的所有挂载
                if (spaceMountRedisDao.hasSpaceMount(worksId, spaceId)) {
                    MountWorksDO mountWorksDO = spaceMountRedisDao.getSpaceMount(worksId, spaceId);
                    if (ObjectUtil.isNotEmpty(mountWorksDO.getPoint())) {
                        // 先删除缓存
                        spaceMountRedisDao.deleteSpaceMount(worksId, spaceId);
                        // 在更新 挂点数据
                        mountWorksDO.setMountType(0);
                        mountWorksDO.setMountId(Long.valueOf(mountWorksDO.getPoint()));
                        mountWorksMapper.updateById(mountWorksDO);
                        spaceMountRedisDao.saveSpaceMount(mountWorksDO);
                    } else {
                        spaceMountRedisDao.deleteSpaceMount(worksId, spaceId);
                        mountWorksMapper.deleteById(mountWorksDO.getId());
                    }

                }
            });
        }
    }

    /**
     * @param id
     * @return
     */
    @Override
    public List<Long> getWorksMountList(Long id) {
        if (spaceMountRedisDao.hasWorksMountList(id)) {
            return spaceMountRedisDao.getWorksMountList(id);
        }
        return List.of();
    }

    /**
     * @param reqVO
     * @return
     */
    @Override
    public Boolean batchMount(MountWorksRespVO reqVO) {

        if (ObjectUtil.isEmpty(reqVO)) {
            throw exception(MOUNT_WORKS_NOT_EXISTS);
        } else {
            //  获取挂点数据
            List<MountInfoDTO> worksList = reqVO.getWorksList();

//            List<MountInfoDTO> fileList = reqVO.getFileList();
//
//            List<MountInfoDTO> metaList = reqVO.getMetaList();

            List<MountInfoDTO> ponitList = reqVO.getPonitList();


            // 如果pointList不为空 则进行批量保存
            if (CollUtil.isNotEmpty(ponitList)) {

            }

            // 如果无挂点作品不为空说明 新增作品
            if (CollUtil.isNotEmpty(worksList)) {

            }
        }
        return null;
    }

    /**
     * @param reqVO
     * @return
     */
    @Override
    public Boolean batchAll(List<MountWorksDO> reqVO, Long spaceId) {

        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        if (spaceMountRedisDao.haseSpaceMountList(spaceId)) {
            // 删除
            deleteAllMount(spaceId);
            mountWorksMapper.insertBatch(reqVO);
            spaceMountRedisDao.saveSpaceMountList(reqVO);

        } else {
            throw exception(SPACE_NOT_EXISTS);
        }
        return true;
    }

    private boolean saveBatchMount(List<MountWorksSaveReqVO> reqVOList) {
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate nowDate = LocalDate.now();
        for (MountWorksSaveReqVO reqVO : reqVOList) {
            MountWorksDO editingDO = BeanUtils.toBean(reqVO, MountWorksDO.class);
            editingDO.setId(idService.nextId("mount"));
            editingDO.setAccountId(loginUserId);
            editingDO.setCreateId(loginUserId);
            editingDO.setCreateTime(nowTime);
            editingDO.setCreateData(nowDate);
            editingDO.setTenantId(1L);
            editingDO.setTenantCode("1");

            mountWorksMapper.insert(editingDO);
            spaceMountRedisDao.saveSpaceMount(editingDO);
            if (MountType.isWorksType(editingDO.getMountType())) {
                spaceMountRedisDao.saveWorksMount(editingDO.getMountId(), editingDO.getSpaceId());
            }
        }
        return true;
    }

    /**
     * @param ids
     * @return
     */
    @Override
    public Boolean batchUnMount(List<Long> ids) {
        return null;
    }

    /**
     * @param spaceId
     * @param type
     * @return
     */
    @Override
    public Boolean unMountType(Long spaceId, Integer type) {

        // 获取 当前所有的 空间挂载
        List<MountWorksDO> mountWorksDOList = spaceMountRedisDao.getSpaceMountList(spaceId);
        if (type == MountType.UNKNOWN.getCode()) {
        }

        return null;
    }

    private MountWorksInfoRespVO addMountInfo(MountWorksDO mountWorksDO) {
        // 添加草稿数据

        MountWorksInfoRespVO mountWorksInfoRespVO = new MountWorksInfoRespVO();
        // 接入空间数据
        Long spaceId = mountWorksDO.getSpaceId();
        SpaceDO spaceDO = spaceService.getBaseSpace(spaceId);
        if (ObjectUtil.isNotEmpty(spaceDO)) {
            mountWorksInfoRespVO = (BeanUtils.toBean(spaceDO, MountWorksInfoRespVO.class));
        }


        MountInfoDTO dto = convertToMountInfoDTO(mountWorksDO);

        Integer editType = mountWorksDO.getMountType();
        if (editType == MountType.UNKNOWN.getCode()) {
            // 处理挂点类型

        } else {
            // c处理作品类型
            if (MountType.isWorksType(editType)) {
                WorksDO worksDO = worksService.getWorksInfo(mountWorksDO.getMountId());
                populateWorksInfo(dto, worksDO);
            }
            // 处理资源类型
            if (MountType.isResourceType(editType)) {
                populateFileInfo(dto, spaceFileService.getFile(mountWorksDO.getMountId()), getCurrentAccountInfo());
            }

            if (isMetaType(editType)) {
                populateMetaInfo(dto, spaceFileService.getFile(mountWorksDO.getMountId()));
            }
        }
        mountWorksInfoRespVO.setMountInfo(dto);
        return mountWorksInfoRespVO;
    }


    private MountWorksRespVO addMountList(List<MountWorksDO> mountWorksDOList, Long spaceId) {
        // 1. 创建响应对象并处理空间数据
        MountWorksRespVO respVO = createMountResponse(spaceId);
        if (respVO == null) {
            return new MountWorksRespVO();
        }

        // 2. 处理空挂载列表
        if (CollUtil.isEmpty(mountWorksDOList)) {
            return respVO;
        }

        // 3. 预分类容器
        List<MountInfoDTO> fileList = new ArrayList<>();
        List<MountInfoDTO> pointList = new ArrayList<>();
        List<MountInfoDTO> worksList = new ArrayList<>();
        List<MountInfoDTO> metaList = new ArrayList<>();

        // 4. 批量预加载数据
        Map<Long, WorksDO> worksCache = preloadWorksData(mountWorksDOList);
        Map<Long, SpaceFileDO> fileCache = preloadFileData(mountWorksDOList);
        Map<Long, SpaceFileDO> metaCache = preloadMetaFileData(mountWorksDOList);
        AccountRightsInfoDTO accountInfo = getCurrentAccountInfo();

        // 5. 处理每个挂载项
        for (MountWorksDO mount : mountWorksDOList) {
            MountInfoDTO dto = convertToMountInfoDTO(mount);

            Integer editType = mount.getMountType();
            if (editType == MountType.UNKNOWN.getCode()) {
                // 处理挂点类型
                if (ObjectUtil.isNotEmpty(dto.getPoint())) {
                    pointList.add(dto);
                }
            } else {
                // c处理作品类型
                if (MountType.isWorksType(editType)) {
                    populateWorksInfo(dto, worksCache.get(mount.getMountId()));
                    if (ObjectUtil.isNotEmpty(dto.getPoint())) {
                        pointList.add(dto);
                    } else {
                        worksList.add(dto);
                    }

                }
                // 处理资源类型
                if (MountType.isResourceType(editType)) {
                    populateFileInfo(dto, fileCache.get(mount.getMountId()), accountInfo);
                    fileList.add(dto);
                }

                if (isMetaType(editType)) {
                    populateMetaInfo(dto, metaCache.get(mount.getMountId()));
                    metaList.add(dto);
                }
            }


        }

        // 6. 设置分类结果
        respVO.setFileList(fileList);
        respVO.setPonitList(pointList);
        respVO.setWorksList(worksList);
        respVO.setMetaList(metaList);

        return respVO;
    }

    private void populateMetaInfo(MountInfoDTO dto, SpaceFileDO file) {
        if (file == null) {
            return;
        }
        dto.setRightsId(file.getRightsId());
        dto.setHignUrl(file.getFileUrl());
        dto.setLowUrl(file.getFileAttribute());
        dto.setMountName(file.getFileName());
        dto.setMountDescription(file.getDescription());
        dto.setMountType(Math.toIntExact(file.getFileTypeId()));
        dto.setMountId(file.getFileId());
    }

    private Map<Long, SpaceFileDO> preloadMetaFileData(List<MountWorksDO> mountWorksDOList) {
        Map<Long, SpaceFileDO> fileMap = new HashMap<>();
        Set<Long> fileIds = mountWorksDOList.stream()
                .filter(m -> isMetaType(m.getMountType()))
                .map(MountWorksDO::getMountId)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(fileIds)) {
            return fileMap;
        } else {
            fileMap = fileIds.stream().map(fileId -> spaceFileService.getFile(fileId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SpaceFileDO::getFileId, file -> file));
        }
        return fileMap;
    }


    /***
     * 通过空间删除挂载关系时 并行处理
     */
    private void deleteSpaceMountsInParallel(Long spaceId, List<Long> mountIds) {
        // 使用并行流处理，限制并发数
        Map<Boolean, List<Long>> results = mountIds.parallelStream()
                .collect(Collectors.partitioningBy(mountId -> deleteSingleSpace(spaceId, mountId)));

        // 处理失败项
        List<Long> failedIds = results.get(false);
        if (CollUtil.isNotEmpty(failedIds)) {
            log.error("部分挂载点删除失败, spaceId: {}, mountIds: {}", spaceId, failedIds);
            // 重试机制（可选）
            retrySpaceMounts(spaceId, failedIds);
        }
    }


    /**
     * 删除单个空间下的所有挂载点
     */
    private boolean deleteSingleSpace(Long spaceId, Long mountId) {
        String lockKey = LOCK_MOUNT_KEY + mountId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，带超时时间
            if (!lock.tryLock(1, 10, TimeUnit.SECONDS)) {
                log.warn("挂载点锁获取超时, mountId: {}, spaceId: {}", mountId, spaceId);
                return false;
            }

            try {
                // 检查并删除挂载信息
                if (spaceMountRedisDao.hasWorksMountInfo(mountId, spaceId)) {
                    spaceMountRedisDao.deleteWorksInfo(mountId, spaceId);
                    log.debug("成功删除挂载点, mountId: {}, spaceId: {}", mountId, spaceId);
                    return true;
                }
                return true; // 无记录也算成功
            } finally {
                lock.unlock();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("挂载点删除中断, mountId: {}, spaceId: {}", mountId, spaceId, e);
            return false;
        } catch (Exception e) {
            log.error("删除挂载点异常, mountId: {}, spaceId: {}", mountId, spaceId, e);
            return false;
        }
    }


    /****
     *
     *  删除空间下挂载点 重试
     */
    private void retrySpaceMounts(Long spaceId, List<Long> failedIds) {
        // 简单重试机制
        for (Long mountId : failedIds) {
            try {
                if (deleteSingleSpace(spaceId, mountId)) {
                    log.info("重试删除成功, mountId: {}", mountId);
                }
            } catch (Exception e) {
                log.error("重试删除挂载点失败, mountId: {}", mountId, e);
            }
        }
    }


    /**
     * 删除数据哭记录
     */
    private void deleteSpaceRecords(Long spaceId) {
        try {
            int deleted = mountWorksMapper.delete(new LambdaQueryWrapperX<MountWorksDO>()
                    .eq(MountWorksDO::getSpaceId, spaceId)
                    .eq(MountWorksDO::getDeleted, false));

            log.info("删除数据库挂载记录, spaceId: {}, 数量: {}", spaceId, deleted);
        } catch (Exception e) {
            log.error("删除数据库挂载记录失败, spaceId: {}", spaceId, e);
            // 可添加补偿机制
        }
    }


    // =============== addMountList 数据拼装 ===============

    /**
     * 创建挂载响应对象
     */
    private MountWorksRespVO createMountResponse(Long spaceId) {
        SpaceDO space = spaceService.getBaseSpace(spaceId);
        if (space == null) {
            deleteMountWorks(spaceId);
            return null;
        }
        return BeanUtils.toBean(space, MountWorksRespVO.class);
    }

    /**
     * 预加载作品数据
     */
    private Map<Long, WorksDO> preloadWorksData(List<MountWorksDO> mounts) {
        Map<Long, WorksDO> worksMap = new HashMap<>();
        Set<Long> worksIds = mounts.stream()
                .filter(m -> MountType.isWorksType(m.getMountType()))
                .map(MountWorksDO::getMountId)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(worksIds)) {
            return Collections.emptyMap();
        } else {
            worksMap = worksIds.stream()
                    .map(worksId -> {
                        WorksDO works = worksService.getWorksInfoById(worksId);
                        return (works != null) ? new AbstractMap.SimpleEntry<>(worksId, works) : null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue
                    ));

        }

        return worksMap;
    }

    /**
     * 预加载文件数据
     */
    private Map<Long, SpaceFileDO> preloadFileData(List<MountWorksDO> mounts) {

        Map<Long, SpaceFileDO> fileMap = new HashMap<>();
        Set<Long> fileIds = mounts.stream()
                .filter(m -> MountType.isResourceType(m.getMountType()))
                .map(MountWorksDO::getMountId)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(fileIds)) {
            return fileMap;
        } else {
            fileMap = fileIds.stream().map(fileId -> spaceFileService.getFile(fileId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SpaceFileDO::getFileId, file -> file));
        }
        return fileMap;
    }


    /**
     * 获取当前账户信息
     */
    private AccountRightsInfoDTO getCurrentAccountInfo() {
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (accountId == null) {
            return new AccountRightsInfoDTO();
        }
        return accountApi.queryAccountRightsInfoById(accountId);
    }

    /**
     * 转换基础DTO对象
     */
    private MountInfoDTO convertToMountInfoDTO(MountWorksDO mount) {
        MountInfoDTO dto = new MountInfoDTO();
        BeanUtils.copyProperties(mount, dto);
        return dto;
    }

    /**
     * 填充作品信息
     */
    private void populateWorksInfo(MountInfoDTO dto, WorksDO works) {
        if (works == null) {
            return;
        }

        dto.setMountName(works.getWorksCnName());
        dto.setMountDescription(works.getCnDescription());
        dto.setMountHeight(works.getWorksHeight());
        dto.setMountWidth(works.getWorksWidth());
        dto.setMountCover(works.getWorksCover());
        dto.setRightsId(works.getRightsId());
        dto.setAccountName(works.getAccountName());
        dto.setNickname(works.getNickname());
        dto.setHignUrl(works.getHignUrl());
        dto.setLowUrl(works.getLowUrl());
        dto.setAuthor(works.getNickname());
    }

    /**
     * 填充文件信息
     */
    private void populateFileInfo(MountInfoDTO dto, SpaceFileDO file, AccountRightsInfoDTO accountInfo) {
        if (file == null) {
            return;
        }
        dto.setRightsId(file.getRightsId());
        dto.setAccountName(accountInfo.getName());
        dto.setNickname(accountInfo.getNickname());
        dto.setHignUrl(file.getFileUrl());
        dto.setLowUrl(file.getFileAttribute());
        dto.setAuthor(accountInfo.getNickname());
        dto.setMountName(file.getFileName());
        dto.setMountDescription(file.getDescription());
    }


}
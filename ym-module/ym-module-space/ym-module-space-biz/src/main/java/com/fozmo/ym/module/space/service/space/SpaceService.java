package com.fozmo.ym.module.space.service.space;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceSaveReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceUpdateTempReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceVO;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import jakarta.validation.Valid;

/**
 * 空间 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceService {

    /**
     * 创建空间
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSpace(@Valid SpaceSaveReqVO createReqVO);

    /**
     * 更新空间
     *
     * @param updateReqVO 更新信息
     */
    void updateSpace(@Valid SpaceSaveReqVO updateReqVO);

    /**
     * 删除空间
     * @param id 编号
     */
    void deleteSpace(Long id);
    /**
     * 获取空间详情
     */
    SpaceDO getSpaceInfo(Long spaceId);

    /**
     * 获取空间基础信息
     */
    SpaceDO getBaseSpace(Long spaceId);

    /**
     * 获得空间分页 按条件检索
     *
     * @param pageReqVO 分页查询
     * @return 空间分页
     */

    PageResult<SpaceDO> getSpacePage(SpacePageReqVO pageReqVO);

    /**
     * 获取当前或者其他用户空间分页
     *
     * @param pageReqVO 分页查询
     */
    PageResult<SpaceDO> getAccountSpacePage(SpacePageReqVO pageReqVO);

    PageResult<SpaceDO> getMyAccountSpacePage(SpacePageReqVO pageReqVO);

    AppSpaceVO getMySpacePage(SpacePageReqVO pageReqVO);
    /**
     * 查询我的空间使用数量
     */
    Long pageCount(Long accountId);

    boolean hasSpace(Long spaceId);

    Long updateTemp(@Valid AppSpaceUpdateTempReqVO reqVO);

    PageResult<WorksDO> getMountWoks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize);

    PageResult<WorksDO> getUnMountWorks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize);

    PageResult<WorksDO> spaceWorks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize);
}
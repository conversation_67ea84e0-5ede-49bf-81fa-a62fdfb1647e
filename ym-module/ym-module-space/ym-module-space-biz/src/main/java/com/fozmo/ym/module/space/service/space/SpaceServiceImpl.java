package com.fozmo.ym.module.space.service.space;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.account.api.account.dto.AccountRightsInfoDTO;
import com.fozmo.ym.module.social.api.fans.SocialFansApi;
import com.fozmo.ym.module.social.api.space.SpaceCommentApi;
import com.fozmo.ym.module.social.api.space.SpaceLikeApi;
import com.fozmo.ym.module.social.api.space.SpaceShareApi;
import com.fozmo.ym.module.space.api.dto.SpaceInfoDTO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO;
import com.fozmo.ym.module.space.controller.admin.space.vo.SpaceSaveReqVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksRespVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceUpdateTempReqVO;
import com.fozmo.ym.module.space.controller.app.space.vo.AppSpaceVO;
import com.fozmo.ym.module.space.core.mq.space.SpaceDeleteProduce;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.dal.mysql.space.SpaceMapper;
import com.fozmo.ym.module.space.dal.redis.space.MySpaceRedisDao;
import com.fozmo.ym.module.space.dal.redis.space.SpaceRedisDao;
import com.fozmo.ym.module.space.enums.MountType;
import com.fozmo.ym.module.space.service.editing.EditingService;
import com.fozmo.ym.module.space.service.file.SpaceFileService;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import com.fozmo.ym.module.space.service.spacetemplate.SpaceTemplateService;
import com.fozmo.ym.module.space.service.works.WorksService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.rights.enums.ErrorCodeConstants.RIGHTS_NOT;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.*;

/**
 * 空间 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SpaceServiceImpl implements SpaceService {

    @Resource
     @Lazy
    private IdService idService;

    @Resource
    private SpaceMapper spaceMapper;
    
    @Resource
    private WorksService worksService;

    @Resource
    @Lazy
    private MountWorksService mountWorksService;

    @Resource
    @Lazy
    private EditingService editingService;

    @Resource
    private SpaceTemplateService spaceTemplateService;

    @Resource
    @Lazy
    private AccountApi accountApi;

    @Autowired
    private SpaceFileService spaceFileService;
    
    @Resource
    private SpaceRedisDao spaceRedisDao;
    
    @Resource
    private MySpaceRedisDao mySpaceRedisDao;
    
    @Resource
    private SpaceCommentApi spaceCommentApi;
    
    @Resource
    private SpaceLikeApi spaceLikeApi;
    
    @Resource
    private SpaceShareApi spaceShareApi;

    @Resource
    private SpaceDeleteProduce spaceDeleteProduce;

    @Resource
    private SocialFansApi socialFansApi;

    @Override
    public Long createSpace(SpaceSaveReqVO createReqVO) {
      // 空间创建校验逻辑
       checkSpace(createReqVO);
        // 插入
        SpaceDO createObj = BeanUtils.toBean(createReqVO, SpaceDO.class);

        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginUserId)) {
            throw exception(ACCOUNT_NOT);
        }
        AccountRightsInfoDTO loginUser = accountApi.queryAccountRightsInfoById(loginUserId);
        if (ObjectUtil.isEmpty(loginUser)) {
            throw exception(ACCOUNT_NOT);
        }
        String spaceCover = createObj.getSpaceCover();
        Long templateId = createObj.getSpaceTemplateId();
        SpaceTemplateDO spaceTemplateDO = spaceTemplateService.getTemplate(templateId);
        if (StringUtils.isEmpty(spaceCover)) {
            createObj.setSpaceCover(spaceTemplateDO.getTemplateCover());
        }
        createObj.setSpaceNum2(spaceTemplateDO.getPointNum2());
        createObj.setSpaceNum3(spaceTemplateDO.getPointNum3());
        Long rightsId = loginUser.getRightsId();

        Integer copyFlag = createObj.getCopyFlag();
        if (ObjectUtil.isNotEmpty(copyFlag) && copyFlag == 0) {
            if (ObjectUtil.isNotEmpty(rightsId) && rightsId == 2) {
                copyFlag = 0;
            } else {
                throw exception(RIGHTS_NOT);
            }
        } else {
            copyFlag = 1;
        }
        createObj.setCopyFlag(copyFlag);
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getName());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("space"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        createObj.setAccountId(loginUser.getId());
        // 新增时 往缓存 写入空间
        spaceRedisDao.saveSpace(createObj);
        mySpaceRedisDao.saveMySpaceId(loginUser.getId(), createObj.getId());
        spaceMapper.insert(createObj);
        // 初始化挂点数据
        mountWorksService.initMount(createObj.getId(), createObj.getSpaceNum2());
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateSpace(SpaceSaveReqVO updateReqVO) {
        // 校验存在
        validateSpaceExists(updateReqVO.getId());
        // 更新
        SpaceDO updateObj = BeanUtils.toBean(updateReqVO, SpaceDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        // 更新缓存时 删除原记录 在插入新的记录
        spaceRedisDao.delSpaceKey(updateObj.getId());
        spaceMapper.updateById(updateObj);
        getSpaceInfo(updateObj.getId());
        
    }

    @Override
    public void deleteSpace(Long id) {
        Long accountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isNull(accountId)) {
            throw exception(ACCOUNT_NOT);
        }

        if (spaceRedisDao.hasSpaceKey( id)){
            spaceMapper.deleteById(id);
            // 删除空间列表
            spaceRedisDao.delSpaceKey(id);
            // 删除我的账户列表
            mySpaceRedisDao.deleteMySpaceId(accountId,id);
        }else {
            // 校验存在
            validateSpaceExists(id);
            spaceMapper.deleteById(id);
        }

        // 启动空间删除队列
        spaceDeleteProduce.sendSpaceDelete(id, accountId);
        
        
    }

    private void validateSpaceExists(Long id) {

        SpaceDO spaceDO = spaceMapper.selectById(id);
        if (ObjectUtil.isEmpty(spaceDO)) {
            throw exception(SPACE_NOT_EXISTS);
        }
       
    }


    /**
     * @param spaceId
     * @return
     */
    @Override
    public SpaceDO getSpaceInfo(Long spaceId) {

        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        SpaceDO spaceDO = new SpaceDO();
        if (spaceRedisDao.hasSpaceKey(spaceId)){
            spaceDO=spaceRedisDao.getSpaceInfo(spaceId);
        }else {
            spaceDO = spaceMapper.selectById(spaceId);
            if (ObjectUtil.isNotEmpty(spaceDO)) {
                spaceRedisDao.saveSpace(spaceDO);
                mySpaceRedisDao.saveMySpaceId(spaceDO.getAccountId(),spaceDO.getId());
            }
        }
        if (ObjectUtil.isNotEmpty(spaceDO)) {
            // 处理空间数据
            addSpaceInfo(spaceDO,loginUserId);

            // 处理关注状态
            if (ObjectUtil.isEmpty(loginUserId)) {
                spaceDO.setFansStatus(false);
            } else {
                spaceDO.setFansStatus(socialFansApi.isFans(spaceDO.getAccountId(), loginUserId));
            }

        }
        return spaceDO;
    }

    /**
     * 获取空间基础信息
     *
     * @param spaceId
     */
    @Override
    public SpaceDO getBaseSpace(Long spaceId) {
        return spaceRedisDao.getSpaceInfo(spaceId);
    }

    @Override
    public PageResult<SpaceDO> getSpacePage(SpacePageReqVO pageReqVO) {
        checkCount();
        PageResult<SpaceDO> pageResult = new PageResult<>();
        
        // 获取 当前用户id
        Long userId = WebFrameworkUtils.getLoginUserId();
        pageReqVO.setPrivacyFlag(0);
        pageResult = spaceRedisDao.getSpacePage(pageReqVO.getPageNo(), pageReqVO.getPageSize(), pageReqVO.getSpaceCnName(), pageReqVO.getSpaceTypeId());

        if(ObjectUtil.isNotEmpty(pageResult) && CollUtil.isNotEmpty(pageResult.getList())){

            pageResult.getList().forEach(spaceDO -> {
                addSpaceList(spaceDO, userId);
            });
        }else {
            pageResult=spaceMapper.selectPage(pageReqVO);

            if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0 && CollUtil.isNotEmpty(pageResult.getList())) {
                pageResult.getList().forEach(spaceDO -> {
                    addSpaceList(spaceDO, userId);
                    spaceRedisDao.saveSpace(spaceDO);
                    mySpaceRedisDao.saveMySpaceId(spaceDO.getAccountId(),spaceDO.getId());
                });
            }
        }
        return pageResult;
    }

    private void checkCount() {
        LambdaQueryWrapperX queryWrapperX = new LambdaQueryWrapperX<SpaceDO>().eq(SpaceDO::getDeleted, false);
        // 查询空间模板数量
        Long redisCount = spaceRedisDao.getSpaceCount();
        Long dbCount = spaceMapper.selectCount(queryWrapperX);
        if (dbCount > redisCount) {
            List<SpaceDO> spaceDOList = spaceMapper.selectList(queryWrapperX);
            if (CollectionUtil.isNotEmpty(spaceDOList)) {
                spaceRedisDao.delAllSpace();
                spaceRedisDao.saveAllSpace(spaceDOList);
                Map<Long, List<SpaceDO>> accountWorksMap = spaceDOList.stream().collect(Collectors.groupingBy(SpaceDO::getAccountId));
                if (MapUtil.isNotEmpty(accountWorksMap)) {
                    accountWorksMap.forEach((accountId, space) -> {
                        mySpaceRedisDao.deleteAll(accountId);
                        mySpaceRedisDao.saveMyWorksList(accountId, space);
                    });
                }
            }
        }
    }

/**
 * @param pageReqVO
 *
 * @return
 */
    @Override
    public PageResult<SpaceDO> getAccountSpacePage(SpacePageReqVO pageReqVO) {

//        PageResult<SpaceDO> pageResult = new PageResult<>();
//      // 获取 当前登录人Id
//        Long loginAccountId = WebFrameworkUtils.getLoginUserId();
//
//
//        // 查询 账户Id 为 pageReqVO.getAccountId() 的空间
//        Long accountId = pageReqVO.getAccountId();
//
//        // 查询该账户的空间数据
//        PageResult<Long> spaceIds = mySpaceRedisDao.getMyspaceIdPage(accountId, pageReqVO.getPageNo(), pageReqVO.getPageSize());
//        if (ObjectUtil.isEmpty(spaceIds) || spaceIds.getTotal() == 0 || CollUtil.isEmpty(spaceIds.getList())) {
//            // 查询空间列表
//            PageResult<SpaceDO> spaceDOList = spaceMapper.selectPage(pageReqVO);
//            if (ObjectUtil.isEmpty(spaceDOList) || spaceDOList.getTotal() == 0 || CollUtil.isEmpty(spaceDOList.getList())) {
//                return PageResult.empty();
//            }
//            for (SpaceDO spaceDO : spaceDOList.getList()) {
//                // 拼装账户信息
//                addSpaceList(spaceDO, loginAccountId);
//
//                spaceRedisDao.saveSpace(spaceDO);
//                mySpaceRedisDao.saveMySpaceId(spaceDO.getAccountId(), spaceDO.getId());
//            }
//            pageResult.setList(spaceDOList.getList());
//            pageResult.setTotal(spaceDOList.getTotal());
//            return pageResult;
//
//        } else {
//            List<Long> ids = spaceIds.getList();
//
//            List<SpaceDO> spaceDOList = new ArrayList<>();
//            for (Long spaceId : ids) {
//                SpaceDO spaceDO = spaceRedisDao.getSpaceInfo(spaceId);
//                spaceDOList.add(spaceDO);
//            }
//            // 拼装数据
//            if (CollUtil.isNotEmpty(spaceDOList)) {
//                for (SpaceDO spaceDO : spaceDOList) {
//                    // 拼装账户信息
//                    spaceDO = addSpaceList(spaceDO, loginAccountId);
//                }
//            }
//            pageResult.setList(spaceDOList);
//            pageResult.setTotal(spaceIds.getTotal());
//            return pageResult;
//        }


        PageResult<SpaceDO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);
        pageReqVO.setPrivacyFlag(0);
        List<Long> spaceIds = mySpaceRedisDao.getAllMysqlSpaceId(pageReqVO.getAccountId());
        List<SpaceDO> spaceDOList = new ArrayList<>();

        List<SpaceDO> subList = new ArrayList<>();
        if (CollUtil.isNotEmpty(spaceIds)) {
            LocalDateTime now = LocalDateTime.now();
            for (Long spaceId : spaceIds) {
                SpaceDO spaceDO = spaceRedisDao.getSpaceInfo(spaceId);
                if (ObjectUtil.isEmpty(spaceDO)) {
                    continue;
                }
                if (isWithinExhibitionTime(spaceDO, now)) {
                    if (ObjectUtil.isNotEmpty(spaceDO.getPrivacyFlag()) && spaceDO.getPrivacyFlag() == 0) {
                        spaceDOList.add(spaceDO);
                    }


                }
            }

        }

        if (CollUtil.isNotEmpty(spaceDOList)) {
            if (ObjectUtil.isNotEmpty(pageReqVO.getSpaceTypeId())) {
                spaceDOList = spaceDOList.stream()
                        .filter(spaceDO -> spaceDO.getSpaceTypeId().equals(pageReqVO.getSpaceTypeId()))
                        .collect(Collectors.toList());
            }
            pageResult.setTotal((long) spaceDOList.size());
        }


        if (CollUtil.isNotEmpty(spaceDOList)) {
            int start = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
            int end = Math.min(start + pageReqVO.getPageSize(), spaceDOList.size());
            subList = spaceDOList.subList(start, end);
        }
        if (CollUtil.isNotEmpty(subList)) {
            for (SpaceDO spaceDO : subList) {
                addSpaceList(spaceDO, pageReqVO.getAccountId());
            }

            pageResult.setTotal((long) spaceDOList.size());
            pageResult.setList(subList);
        }

        return pageResult;
    }

    /**
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SpaceDO> getMyAccountSpacePage(SpacePageReqVO pageReqVO) {
        PageResult<SpaceDO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);
        List<Long> spaceIds = mySpaceRedisDao.getAllMysqlSpaceId(pageReqVO.getAccountId());
        List<SpaceDO> spaceDOList = new ArrayList<>();

        List<SpaceDO> subList = new ArrayList<>();
        if (CollUtil.isNotEmpty(spaceIds)) {
            for (Long spaceId : spaceIds) {
                SpaceDO spaceDO = spaceRedisDao.getSpaceInfo(spaceId);
                spaceDOList.add(spaceDO);
            }

        }

        if (CollUtil.isNotEmpty(spaceDOList)) {
            if (ObjectUtil.isNotEmpty(pageReqVO.getSpaceTypeId())) {
                spaceDOList = spaceDOList.stream()
                        .filter(spaceDO -> spaceDO.getSpaceTypeId().equals(pageReqVO.getSpaceTypeId()))
                        .collect(Collectors.toList());
            }
            pageResult.setTotal((long) spaceDOList.size());
        }


        if (CollUtil.isNotEmpty(spaceDOList)) {
            int start = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
            int end = Math.min(start + pageReqVO.getPageSize(), spaceDOList.size());
            subList = spaceDOList.subList(start, end);
        }
        if (CollUtil.isNotEmpty(subList)) {
            for (SpaceDO spaceDO : subList) {
                addSpaceList(spaceDO, pageReqVO.getAccountId());
            }

            pageResult.setTotal((long) spaceDOList.size());
            pageResult.setList(subList);
        }

        return pageResult;
    }

    public SpaceDO addSpaceList(SpaceDO spaceDO, Long accountId) {
        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        Future<AccountBaseInfoDTO> accountFuture = executorService.submit(() -> accountApi.queryAccountBaseInfoById(spaceDO.getAccountId()));

        // 拼装账户信息
        AccountBaseInfoDTO spaceAccount = null;
        try {
            spaceAccount = accountFuture.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        if (ObjectUtil.isNotEmpty(spaceAccount)) {
            spaceDO.setAccountName(spaceAccount.getName());
            spaceDO.setRightsId(spaceAccount.getRightsId());
            spaceDO.setAvatar(spaceAccount.getAvatar());
            spaceDO.setNickname(spaceAccount.getNickname());
            if (ObjectUtil.isEmpty(spaceDO.getSponsor())) {
                spaceDO.setSponsor(spaceAccount.getNickname());
            }
        }

        Future<Long> likeCountFuture = executorService.submit(() -> spaceLikeApi.countBySpaceId(spaceDO.getId()));
        try {
            spaceDO.setLikeCount(likeCountFuture.get());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        Future<Boolean> likeStatusFuture = executorService.submit(() -> {
            if (ObjectUtil.isNotEmpty(accountId)) {
                return spaceLikeApi.isLike(spaceDO.getId(), accountId);
            } else {
                return false;
            }
        });
        try {
            spaceDO.setLikeStatus(likeStatusFuture.get());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        // 关闭线程池
        executorService.shutdown();

        // 拼装空间模板信息
        return spaceDO;
    }

    
    /**
     *
     *  空间信息拼装
     */

    public SpaceDO addSpaceInfo(SpaceDO spaceDO,Long accountId){

        // 拼装账户信息
        AccountBaseInfoDTO spaceAccount = accountApi.queryAccountBaseInfoById(spaceDO.getAccountId());
        if (ObjectUtil.isNotEmpty(spaceAccount)) {

            spaceDO.setAccountName(spaceAccount.getName());
            spaceDO.setRightsId(spaceAccount.getRightsId());
            spaceDO.setAvatar(spaceAccount.getAvatar());
            spaceDO.setNickname(spaceAccount.getNickname());

            if (ObjectUtil.isEmpty(spaceDO.getSponsor())) {
                spaceDO.setSponsor(spaceAccount.getNickname());
            }

        }
        // 拼装 作品信息 todo

        // 拼装 评论信息
        spaceDO.setCommentCount(spaceCommentApi.countBySpaceId(spaceDO.getId()));
        if (ObjectUtil.isNotEmpty(accountId)){
            spaceDO.setCommentStatus(spaceCommentApi.isComment(spaceDO.getId(), accountId));
        }else {
            spaceDO.setCommentStatus(false);
        }
        spaceDO.setLikeCount(spaceLikeApi.countBySpaceId(spaceDO.getId()));
        if (ObjectUtil.isNotEmpty(accountId)){
            spaceDO.setLikeStatus(spaceLikeApi.isLike(spaceDO.getId(), accountId));
        }else {
            spaceDO.setLikeStatus(false);
        }

        // 拼装 分享信息
        spaceDO.setShareCount(spaceShareApi.countBySpaceId(spaceDO.getId()));
        if (ObjectUtil.isNotEmpty(accountId)){
            spaceDO.setShareStatus(spaceShareApi.isShare(spaceDO.getId(), accountId));
        }else {
            spaceDO.setShareStatus(false);
        }

        // 拼装空间模板信息
        return spaceDO;
    }

    /**
     * @param pageReqVO
     * @return
     */
    @Override
    public AppSpaceVO getMySpacePage(SpacePageReqVO pageReqVO) {
        AppSpaceVO appSpaceVO = new AppSpaceVO();
        // 获取当前用户 所有的空间信息
        Long loginAccountId = WebFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginAccountId)){
            throw exception(ACCOUNT_NOT);
        }
        AccountRightsInfoDTO accountRightsInfoDTO = accountApi.queryAccountRightsInfoById(loginAccountId);
        Integer totalNum = accountRightsInfoDTO.getRightsInfo().getWorksRights();
        if (ObjectUtil.isEmpty(accountRightsInfoDTO)){
            throw exception(ACCOUNT_NOT);
        }

        Integer currentNum = mySpaceRedisDao.getAllMysqlSpaceId(loginAccountId).size();

        appSpaceVO.setTotalNum(totalNum);
        appSpaceVO.setCurrentNum(currentNum);

        if (ObjectUtil.isNotEmpty(totalNum) && ObjectUtil.isNotEmpty(currentNum) && totalNum > currentNum){
            appSpaceVO.setAvailableNum(totalNum - currentNum);
        }else {
            appSpaceVO.setAvailableNum(0);
        }
        PageResult<Long> spaceIds = mySpaceRedisDao.getMyspaceIdPage(loginAccountId, pageReqVO.getPageNo(), pageReqVO.getPageSize());
        if (ObjectUtil.isEmpty(spaceIds) || spaceIds.getTotal() == 0 || CollUtil.isEmpty(spaceIds.getList())) {
            return appSpaceVO;
        }
        List<Long> ids = spaceIds.getList();
        List<SpaceDO> spaceDOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(ids)){
            for (Long spaceId : ids){
                SpaceDO spaceDO = spaceRedisDao.getSpaceInfo(spaceId);
                spaceDOList.add(spaceDO);
            }
        }

        if (CollUtil.isNotEmpty(spaceDOList)){
            for (SpaceDO spaceDO : spaceDOList){
                spaceDO = addSpaceInfo(spaceDO,loginAccountId);
            }
            appSpaceVO.setSpaceInfoList(BeanUtils.toBean(spaceDOList, SpaceInfoDTO.class));
            appSpaceVO.setTotal(Math.toIntExact(spaceIds.getTotal()));

            appSpaceVO.setAvailableNum(accountRightsInfoDTO.getRightsInfo().getSpaceRights() - spaceDOList.size());
        }


        return appSpaceVO;
    }

/**
 * 查询我的空间使用数量
 *
 * @param accountId
 */
    @Override
    public Long pageCount(Long accountId) {

        Long num = (long) mySpaceRedisDao.getAllMysqlSpaceId(accountId).size();

        if (ObjectUtil.isEmpty(num) || num <= 0L) {
            num = spaceMapper.selectCount(new LambdaQueryWrapperX<SpaceDO>().eq(SpaceDO::getAccountId, accountId).eq(SpaceDO::getDeleted, false));
        }
        return num;
    }

    /**
     * @param spaceId
     */
    @Override
    public boolean hasSpace(Long spaceId) {
        if (ObjectUtil.isEmpty(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        if (!spaceRedisDao.hasSpaceKey(spaceId)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        return true;
    }

    /**
     * @param reqVO
     * @return
     */
    @Override
    public Long updateTemp(AppSpaceUpdateTempReqVO reqVO) {
        // 1 校验当前用户是否可以编辑
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)) {
            throw exception(ACCOUNT_NOT);
        }
        AccountRightsInfoDTO accountInfoDTO = accountApi.queryAccountRightsInfoById(accountId);
        if (ObjectUtil.isEmpty(accountInfoDTO)) {
            throw exception(ACCOUNT_NOT);
        }
        Long userRightsId = accountInfoDTO.getRightsInfo().getRightsId();
        // 2 查询要更改的 空间模板信息
        SpaceTemplateDO spaceTemplateDO = spaceTemplateService.getTemplate(reqVO.getTemplateId());
        if (ObjectUtil.isEmpty(spaceTemplateDO)) {
            throw exception(TEMPLATE_NOT_EXISTS);
        }
        Long templateRightsId = spaceTemplateDO.getRightsId();
        if (userRightsId < templateRightsId) {
            throw exception(RIGHTS_NOT);
        }
        // 3 获取空间信息
        SpaceDO spaceDO = getSpaceInfo(reqVO.getSpaceId());
        if (ObjectUtil.isEmpty(spaceDO)) {
            throw exception(SPACE_NOT_EXISTS);
        }
        if (!spaceDO.getAccountId().equals(accountId)) {
            throw exception(SPACE_NOT_OWNER, "当前用户无权编辑该空间");
        }

        // 4 更新空间信息
        spaceDO.setSpaceTemplateId(reqVO.getTemplateId());
        spaceDO.setUpdateTime(LocalDateTime.now());
        spaceDO.setSpaceNum2(spaceTemplateDO.getPointNum2());
        spaceDO.setSpaceNum3(spaceTemplateDO.getPointNum3());
        spaceMapper.updateById(spaceDO);
        // 4.1 删除空间缓存
        spaceRedisDao.delSpaceKey(spaceDO.getId());
        // 4.2重新写入缓存数据
        spaceRedisDao.saveSpace(spaceDO);
        // 4.3删除挂载信息
        mountWorksService.deleteAllMount(spaceDO.getId());
        // 4.4初始化挂点信息
        mountWorksService.initMount(spaceDO.getId(), spaceDO.getSpaceNum2());
        // 4.5删除编辑信息
        editingService.deleteAllEditing(spaceDO.getId());
        // 4.6初始化编辑信息
//        editingService.initEditing(spaceDO.getId(), spaceDO.getSpaceNum2());
        return spaceDO.getId();
    }

    /**
     * @param spaceId
     * @return
     */
    @Override
    public PageResult<WorksDO> getMountWoks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {

        PageResult<WorksDO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);

        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (currentUserId == null) {
            return PageResult.empty();
        }

        // 2. 并行获取数据
        CompletableFuture<Set<Long>> mountWorksIdsFuture = CompletableFuture.supplyAsync(() ->
                getMountWorksIds(spaceId));

        // 3. 等待并合并结果
        try {
            Set<Long> mountWorksIds = mountWorksIdsFuture.get();
            if (CollUtil.isEmpty(mountWorksIds)) {
                return PageResult.empty();
            }
            // 4. 批量查询作品信息（解决N+1问题）
            Map<Long, WorksDO> worksMap = worksService.batchGetWorksInfo(mountWorksIds.stream().collect(Collectors.toList()));

            // 5. 应用过滤条件
            List<WorksDO> filteredWorks = worksMap.values().stream()
                    .filter(worksDO -> {
                        if (pointType == null) {
                            return true;
                        }
                        return worksDO.getPointType().equals(pointType);
                    })
                    .collect(Collectors.toList());

            // 6. 内存分页（已优化：只在必要时进行）
            return createPageResult(filteredWorks, pageNo, pageSize);

        } catch (Exception e) {
            log.error("查询空间作品失败, spaceId: {}, userId: {}", spaceId, currentUserId, e);
            return PageResult.empty();
        }

    }

    /**
     * @param spaceId
     * @return
     */
    @Override
    public PageResult<WorksDO> getUnMountWorks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {
        PageResult<WorksDO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);

        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (currentUserId == null) {
            return PageResult.empty();
        }

        // 2. 并行获取数据
        CompletableFuture<Set<Long>> mountWorksIdsFuture = CompletableFuture.supplyAsync(() ->
                getMountWorksIds(spaceId));

        CompletableFuture<Set<Long>> userWorksIdsFuture = CompletableFuture.supplyAsync(() ->
                new HashSet<>(worksService.getAllWorksByAccountId(currentUserId)));

        // 3. 等待并合并结果
        try {
            Set<Long> mountWorksIds = mountWorksIdsFuture.get();
            Set<Long> userWorksIds = userWorksIdsFuture.get();

            if (CollUtil.isEmpty(userWorksIds)) {
                return PageResult.empty();
            }
            Set<Long> unMountedWorksIds = new HashSet<>();
            if (CollUtil.isEmpty(mountWorksIds)) {
                unMountedWorksIds = userWorksIds;
            } else {
                unMountedWorksIds = userWorksIds.stream()
                        .filter(id -> !mountWorksIds.contains(id))
                        .collect(Collectors.toSet());
            }


            // 从用户作品中移除已挂载的作品ID


            if (CollUtil.isEmpty(unMountedWorksIds)) {
                return PageResult.empty();
            }

            // 4. 批量查询作品信息（解决N+1问题）
            Map<Long, WorksDO> worksMap = worksService.batchGetWorksInfo(unMountedWorksIds.stream().collect(Collectors.toList()));

            // 5. 应用过滤条件
            List<WorksDO> filteredWorks = worksMap.values().stream()
                    .filter(worksDO -> {
                        if (pointType == null) {
                            return true;
                        }
                        return worksDO.getPointType().equals(pointType);
                    })
                    .collect(Collectors.toList());

            // 6. 内存分页（已优化：只在必要时进行）
            return createPageResult(filteredWorks, pageNo, pageSize);

        } catch (Exception e) {
            log.error("查询空间作品失败, spaceId: {}, userId: {}", spaceId, currentUserId, e);
            return PageResult.empty();
        }
    }

    /**
     * @param spaceId
     * @return
     */
//    @Override
//    public PageResult<WorksDO> spaceWorks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {
//        PageResult pageResult = new PageResult();
//        pageResult.setTotal(0L);
//
//
//        MountWorksRespVO mountWorks = mountWorksService.getMountList(spaceId);
//        List<MountInfoDTO> mountList = new ArrayList<>();
//        List<WorksDO> worksDOList = new ArrayList<>();
//        Map<Long, WorksDO> mountMap = new HashMap<>();
//
//        if (ObjectUtil.isNotEmpty(mountWorks)) {
//            if (CollUtil.isNotEmpty(mountWorks.getPonitList())) {
//                mountList.addAll(mountWorks.getPonitList());
//            }
//            if (CollUtil.isNotEmpty(mountWorks.getWorksList())) {
//                mountList.addAll(mountWorks.getWorksList());
//            }
//        }
//
//
//        if (CollUtil.isNotEmpty(mountList)) {
//            mountList.forEach(mountWorksDO -> {
//                if (MountType.isWorksType(mountWorksDO.getMountType())) {
//                    WorksDO worksDO = worksService.getWorksInfoById(mountWorksDO.getMountId());
//                    if (ObjectUtil.isNotEmpty(worksDO)) {
//                        worksDO.setPoint(mountWorksDO.getPoint());
//                        worksDO.setSort(mountWorksDO.getMountSort());
//                        mountMap.put(mountWorksDO.getMountId(), worksDO);
//                        worksDOList.add(worksDO);
//                    }
//
//                }
//            });
//        }
//        // 查询 个人所有作品
//        List<Long> worksIdList = worksService.getAllWorksByAccountId(SecurityFrameworkUtils.getLoginUserId());
//        if (CollUtil.isNotEmpty(worksIdList)) {
//            if (CollUtil.isNotEmpty(mountMap)) {
//                worksIdList.forEach(worksId -> {
//                    if (!mountMap.containsKey(worksId)) {
//                        WorksDO worksDO = worksService.getWorksInfoById(worksId);
//                        worksDOList.add(worksDO);
//                    }
//                });
//            } else {
//                worksIdList.forEach(worksId -> {
//                    WorksDO worksDO = worksService.getWorksInfoById(worksId);
//                    worksDOList.add(worksDO);
//                });
//            }
//        }
//        if (CollUtil.isNotEmpty(worksDOList)) {
//            List<WorksDO> workList = new ArrayList<>();
//            if (ObjectUtil.isNotEmpty(pointType)) {
//                workList = worksDOList.stream().filter(worksDO -> worksDO.getPointType().equals(pointType)).collect(Collectors.toList());
//            } else {
//                workList = worksDOList;
//            }
//
//            pageResult.setTotal(Long.valueOf(workList.size()));
//            int start = (pageNo - 1) * pageSize;
//            int end = Math.min(start + pageSize, workList.size());
//            pageResult.setList(workList.subList(start, end));
//        }
//        return pageResult;
//    }

    /**
     * 优化后的空间作品查询方法
     */
    @Override
    public PageResult<WorksDO> spaceWorks(Long spaceId, Integer pointType, Integer pageNo, Integer pageSize) {
        // 1. 参数校验
        if (spaceId == null || pageNo == null || pageSize == null || pageNo < 1 || pageSize < 1) {
            return PageResult.empty();
        }

        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (currentUserId == null) {
            return PageResult.empty();
        }

        // 2. 并行获取数据
        CompletableFuture<Set<Long>> mountWorksIdsFuture = CompletableFuture.supplyAsync(() ->
                getMountWorksIds(spaceId));

        CompletableFuture<Set<Long>> userWorksIdsFuture = CompletableFuture.supplyAsync(() ->
                new HashSet<>(worksService.getAllWorksByAccountId(currentUserId)));

        // 3. 等待并合并结果
        try {
            Set<Long> mountWorksIds = mountWorksIdsFuture.get();
            Set<Long> userWorksIds = userWorksIdsFuture.get();

            // 合并作品ID（挂载的作品优先）
            List<Long> allWorksIds = mergeWorksIds(mountWorksIds, userWorksIds);

            if (CollUtil.isEmpty(allWorksIds)) {
                return PageResult.empty();
            }

            // 4. 批量查询作品信息（解决N+1问题）
            Map<Long, WorksDO> worksMap = batchGetWorksInfo(allWorksIds);

            // 5. 应用过滤条件
            List<WorksDO> filteredWorks = applyFilters(worksMap, allWorksIds, pointType);

            // 6. 内存分页（已优化：只在必要时进行）
            return createPageResult(filteredWorks, pageNo, pageSize);

        } catch (Exception e) {
            log.error("查询空间作品失败, spaceId: {}, userId: {}", spaceId, currentUserId, e);
            return PageResult.empty();
        }
    }

    /**
     * 获取挂载的作品ID集合
     */
    private Set<Long> getMountWorksIds(Long spaceId) {
        try {
            MountWorksRespVO mountWorks = mountWorksService.getMountList(spaceId);
            if (mountWorks == null) {
                return Collections.emptySet();
            }

            Set<Long> mountWorksIds = new HashSet<>();

            // 合并点位列表和作品列表
            Stream.of(mountWorks.getPonitList(), mountWorks.getWorksList())
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .filter(mount -> MountType.isWorksType(mount.getMountType()))
                    .forEach(mount -> mountWorksIds.add(mount.getMountId()));

            return mountWorksIds;
        } catch (Exception e) {
            log.error("获取挂载作品失败, spaceId: {}", spaceId, e);
            return Collections.emptySet();
        }
    }

    /**
     * 合并作品ID列表（挂载的作品优先）
     */
    private List<Long> mergeWorksIds(Set<Long> mountWorksIds, Set<Long> userWorksIds) {
        List<Long> result = new ArrayList<>();

        // 先添加挂载的作品
        result.addAll(mountWorksIds);

        // 再添加用户的其他作品（排除已挂载的）
        userWorksIds.stream()
                .filter(id -> !mountWorksIds.contains(id))
                .forEach(result::add);

        return result;
    }

    /**
     * 批量获取作品信息（解决N+1查询问题）
     */
    private Map<Long, WorksDO> batchGetWorksInfo(List<Long> worksIds) {
        if (CollUtil.isEmpty(worksIds)) {
            return Collections.emptyMap();
        }

        // 分批查询，避免单次查询数据量过大
        int batchSize = 100;
        Map<Long, WorksDO> result = new HashMap<>();

        for (int i = 0; i < worksIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, worksIds.size());
            List<Long> batchIds = worksIds.subList(i, endIndex);

            // 批量查询作品信息
            Map<Long, WorksDO> batchResult = worksService.batchGetWorksInfo(batchIds);
            result.putAll(batchResult);
        }

        return result;
    }

    /**
     * 应用过滤条件
     */
    private List<WorksDO> applyFilters(Map<Long, WorksDO> worksMap, List<Long> worksIds, Integer pointType) {
        return worksIds.stream()
                .map(worksMap::get)
                .filter(Objects::nonNull)
                .filter(works -> pointType == null || Objects.equals(works.getPointType(), pointType))
                .collect(Collectors.toList());
    }

    /**
     * 创建分页结果
     */
    private PageResult<WorksDO> createPageResult(List<WorksDO> allWorks, Integer pageNo, Integer pageSize) {
        if (CollUtil.isEmpty(allWorks)) {
            return PageResult.empty();
        }

        int total = allWorks.size();
        int start = (pageNo - 1) * pageSize;

        if (start >= total) {
            return new PageResult<>(Collections.emptyList(), (long) total);
        }

        int end = Math.min(start + pageSize, total);
        List<WorksDO> pageData = allWorks.subList(start, end);

        return new PageResult<>(pageData, (long) total);
    }
    private boolean checkSpace(SpaceSaveReqVO reqVO) {
        // 1 查询当前用户的登录信息 校验当前用户的权限数据
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        
        if (ObjectUtil.isNotEmpty(accountId)) {
            AccountRightsInfoDTO accountInfoDTO = accountApi.queryAccountRightsInfoById(accountId);
            
            if (ObjectUtil.isEmpty(accountInfoDTO)){
                throw exception(SPACE_CREATE_NO,"非法用户");
            }
            Long userRightsId = accountInfoDTO.getRightsInfo().getRightsId();
            if (ObjectUtil.isEmpty(userRightsId) || userRightsId<0L){
                throw exception(SPACE_CREATE_NO,"当前用户为游客权益");
            }
            
            // 校验是否可以创建 空间
            Long currenSpaceNum = pageCount(accountInfoDTO.getId());
            
            Long userSpaceNum = Long.valueOf(accountInfoDTO.getRightsInfo().getSpaceRights());
            if (currenSpaceNum>= userSpaceNum){
                throw exception(SPACE_CREATE_NO,"当前用户空间数：{},权益空间数{}，无法创建",userSpaceNum,currenSpaceNum);
            }
            
            Long spaceTemplateId = reqVO.getSpaceTemplateId();
            
            
            // 2 校验 当前模版是否为空
            if (ObjectUtil.isNotEmpty(spaceTemplateId)) {
                SpaceTemplateDO spaceTemplateInfo = spaceTemplateService.getTemplate(spaceTemplateId);
                
                if (ObjectUtil.isEmpty(spaceTemplateInfo)) {
                    throw exception(TEMPLATE_NOT_EXISTS);
                }else {
                    Long templateRightsId = spaceTemplateInfo.getRightsId();
                    if (templateRightsId>userRightsId){
                        throw exception(SPACE_CREATE_NO,"当前用户权益：{},模版权益为{}，无法创建",userRightsId,spaceTemplateId);
                    }
                }
            }else {
                throw exception(TEMPLATE_NOT_NULL);
            }
        }else {
            return false;
        }
       
      
        return true;
    }
    
    private AccountBaseInfoDTO getAccountInfo(Long accountId){
        AccountBaseInfoDTO accountInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
        return accountInfoDTO;
    }
    
    private Map<Long,AccountBaseInfoDTO> getAccountByIds(Set<Long> accountIds){
        List<AccountBaseInfoDTO> accountInfoDTOS = new ArrayList<>();
        for (Long accountId : accountIds) {
            AccountBaseInfoDTO accountInfoDTO = getAccountInfo(accountId);
            if (ObjectUtil.isNotEmpty(accountInfoDTO)) {
                accountInfoDTOS.add(accountInfoDTO);
            }
            
        }
        if (CollUtil.isNotEmpty(accountInfoDTOS)) {
            return accountInfoDTOS.stream().collect(Collectors.toMap(AccountBaseInfoDTO::getId,Function.identity()));
        }
        return Collections.emptyMap();
    }

    private boolean isWithinExhibitionTime(SpaceDO space, LocalDateTime now) {
        LocalDateTime start = space.getExhibitionStartTime();
        LocalDateTime end = space.getExhibitionEndTime();

        // 情况1：无时间限制
        if (start == null && end == null) {
            return true;
        }

        // 情况2：只有开始时间
        if (start != null && end == null) {
            return !now.isBefore(start);
        }

        // 情况3：只有结束时间
        if (start == null && end != null) {
            return !now.isAfter(end);
        }

        // 情况4：完整时间区间（支持跨天）
        if (start.isBefore(end) || start.equals(end)) {
            // 普通区间 [start, end]
            return !now.isBefore(start) && !now.isAfter(end);
        } else {
            // 跨天区间 [start, 24:00) ∪ [00:00, end]
            return !now.isBefore(start) || !now.isAfter(end);
        }
    }
}
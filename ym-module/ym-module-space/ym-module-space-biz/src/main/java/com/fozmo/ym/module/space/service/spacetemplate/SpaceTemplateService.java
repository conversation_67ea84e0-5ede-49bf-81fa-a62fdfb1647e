package com.fozmo.ym.module.space.service.spacetemplate;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplateSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import jakarta.validation.Valid;

/**
 * 空间模板 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceTemplateService {

    /**
     * 创建空间模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplate(@Valid SpaceTemplateSaveReqVO createReqVO);

    /**
     * 更新空间模板
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplate(@Valid SpaceTemplateSaveReqVO updateReqVO);

    /**
     * 删除空间模板
     *
     * @param id 编号
     */
    void deleteTemplate(Long id);

    /**
     * 获得空间模板
     *
     * @param id 编号
     * @return 空间模板
     */
    SpaceTemplateDO getTemplate(Long id);

    /**
     * 获得空间模板分页
     *
     * @param pageReqVO 分页查询
     * @return 空间模板分页
     */
    PageResult<SpaceTemplateDO> getTemplatePage(SpaceTemplatePageReqVO pageReqVO);

    PageResult<SpaceTemplateDO> getTemplateInfoPage(SpaceTemplatePageReqVO pageReqVO);

}
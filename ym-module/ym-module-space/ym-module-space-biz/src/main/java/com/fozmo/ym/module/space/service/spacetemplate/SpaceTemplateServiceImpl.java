package com.fozmo.ym.module.space.service.spacetemplate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplatePageReqVO;
import com.fozmo.ym.module.space.controller.admin.spacetemplate.vo.SpaceTemplateSaveReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTagListReqVO;
import com.fozmo.ym.module.space.dal.dataobject.spacetemplate.SpaceTemplateDO;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import com.fozmo.ym.module.space.dal.mysql.spacetemplate.SpaceTemplateMapper;
import com.fozmo.ym.module.space.dal.redis.template.SpaceTemplateRedisDao;
import com.fozmo.ym.module.space.service.templatetag.SpaceTemplateTagService;
import com.fozmo.ym.module.space.service.templatetype.SpaceTemplateTypeService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.TEMPLATE_NOT_EXISTS;

/**
 * 空间模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceTemplateServiceImpl implements SpaceTemplateService {
    
    @Resource
    private SpaceTemplateMapper templateMapper;
    @Resource
    private SpaceTemplateTypeService spaceTemplateTypeService;
    
    @Resource
    private SpaceTemplateTagService spaceTemplateTagService;
    @Resource
    private IdService idService;
    
    @Resource
    private SpaceTemplateRedisDao spaceTemplateRedisDao;

@Override
public Long createTemplate(SpaceTemplateSaveReqVO createReqVO) {
    // 插入
    SpaceTemplateDO createObj = BeanUtils.toBean(createReqVO, SpaceTemplateDO.class);
    
    LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
    if (ObjectUtil.isNull(loginUser)) {
        throw new RuntimeException("用户未登录");
    }
    createObj.setCreateId(loginUser.getId());
    createObj.setCreator(loginUser.getUsername());
    createObj.setCreateData(LocalDate.now());
    createObj.setTemplateId(idService.nextId("spaceTemplate"));
    createObj.setTenantId(loginUser.getTenantId());
    createObj.setTenantCode(loginUser.getTenantId() + "");
    
    templateMapper.insert(createObj);
    spaceTemplateRedisDao.saveTemplate(createObj);
    // 返回
    return createObj.getTemplateId();
}

@Override
public void updateTemplate(SpaceTemplateSaveReqVO updateReqVO) {
    // 校验存在
    validateTemplateExists(updateReqVO.getTemplateId());
    // 更新
    SpaceTemplateDO updateObj = BeanUtils.toBean(updateReqVO, SpaceTemplateDO.class);
    LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
    if (ObjectUtil.isNull(loginUser)) {
        throw new RuntimeException("用户未登录");
    }
    updateObj.setUpdateId(loginUser.getId());
    updateObj.setUpdater(loginUser.getUsername());
    updateObj.setUpdateData(LocalDate.now());
    templateMapper.updateById(updateObj);
    spaceTemplateRedisDao.deleteTemplate(updateObj.getTemplateId());
    spaceTemplateRedisDao.saveTemplate(updateObj);
}

@Override
public void deleteTemplate(Long id) {
    // 校验存在
    if (spaceTemplateRedisDao.hasTemplate(id)){
        spaceTemplateRedisDao.deleteTemplate(id);
        templateMapper.deleteById(id);
    }else{
        validateTemplateExists(id);
        templateMapper.deleteById(id);
    }
   
}

private void validateTemplateExists(Long id) {
    if (templateMapper.selectById(id) == null) {
        throw exception(TEMPLATE_NOT_EXISTS);
    }
}

@Override
public SpaceTemplateDO getTemplate(Long id) {
    // 查询空间模板详情
    SpaceTemplateDO spaceTemplateDO = new SpaceTemplateDO();
    if (spaceTemplateRedisDao.hasTemplate(id)){
        spaceTemplateDO = spaceTemplateRedisDao.getTemplate(id);
    }else {
        spaceTemplateDO = templateMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(spaceTemplateDO)){
            spaceTemplateRedisDao.saveTemplate(spaceTemplateDO);
        }
    }
    if (ObjectUtil.isNotEmpty(spaceTemplateDO)){
        spaceTemplateDO = addTemplate(spaceTemplateDO);
    }
    
    return spaceTemplateDO;
}

@Override
public PageResult<SpaceTemplateDO> getTemplatePage(SpaceTemplatePageReqVO pageReqVO) {
    PageResult<SpaceTemplateDO> pageResult = new PageResult<>();
    pageResult = spaceTemplateRedisDao.getTemplatePage(pageReqVO.getPageNo(), pageReqVO.getPageSize(), pageReqVO.getTemplateTypeCode());
    if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0) {
        pageResult = templateMapper.selectPage(pageReqVO);
        
        if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0) {
            pageResult.getList().forEach(templateDO -> {
                spaceTemplateRedisDao.saveTemplate(templateDO);
            });
            
        }
    }
    
    addTemplateList(pageResult.getList());
    return pageResult;
}


    /**
     * @param pageReqVO
     *
     * @return
     */
    @Override
    public PageResult<SpaceTemplateDO> getTemplateInfoPage(SpaceTemplatePageReqVO pageReqVO) {
        PageResult<SpaceTemplateDO> pageResult = new PageResult<>();

        pageResult = spaceTemplateRedisDao.getTemplatePage(pageReqVO.getPageNo(), pageReqVO.getPageSize(), pageReqVO.getTemplateTypeCode());
        return pageResult;
    }
    
    
    private SpaceTemplateDO addTemplate(SpaceTemplateDO spaceTemplateDO) {
        String tags = spaceTemplateDO.getTemplateTags();
        // 处理tags
        if (StringUtils.isNotEmpty(tags)) {
            List<SpaceTemplateTagDO> tagList = new ArrayList<>();
            List<SpaceTemplateTagDO> templateTagDOList = spaceTemplateTagService.getTemplateTagList(new AppSpaceTemplateTagListReqVO());
            Map<Long, SpaceTemplateTagDO> templateTagDOMap = templateTagDOList.stream().collect(Collectors.toMap(SpaceTemplateTagDO::getId, tag -> tag));
            List<Long> tagsIds = Arrays.stream(tags.split(","))
                                         .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            
            for (Long tagId : tagsIds) {
                SpaceTemplateTagDO spaceTemplateTagDO = templateTagDOMap.get(tagId);
                tagList.add(spaceTemplateTagDO);
            }
            
            spaceTemplateDO.setTagList(tagList);
        }
        
        if (StringUtils.isNotEmpty(spaceTemplateDO.getTemplateTypeCode())) {
            String typeCode = spaceTemplateDO.getTemplateTypeCode();
            
            List<SpaceTemplateTypeDO>typeList = new ArrayList<>();
            List<SpaceTemplateTypeDO> templateTypeDOList = spaceTemplateTypeService.getTemplateTypeList();
            Map<Long, SpaceTemplateTypeDO> templateTypeDOMap = templateTypeDOList.stream().collect(Collectors.toMap(SpaceTemplateTypeDO::getId, type -> type));
            
            List<Long> typeIds = Arrays.stream(typeCode.split(","))
                                         .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            
            for (Long typeId : typeIds) {
                SpaceTemplateTypeDO spaceTemplateTypeDO = templateTypeDOMap.get(typeId);
                typeList.add(spaceTemplateTypeDO);
            }
            
            spaceTemplateDO.setTypeList(typeList);
        }
        
        return spaceTemplateDO;
    }


    private List<SpaceTemplateDO> addTemplateList( List<SpaceTemplateDO>  list) {
        if (CollUtil.isNotEmpty(list)) {
            List<SpaceTemplateTagDO> templateTagDOList = spaceTemplateTagService.getTemplateTagList(new AppSpaceTemplateTagListReqVO());
            Map<Long, SpaceTemplateTagDO> templateTagDOMap = templateTagDOList.stream().collect(Collectors.toMap(SpaceTemplateTagDO::getId, tag -> tag));
            
            List<SpaceTemplateTypeDO> templateTypeDOList = spaceTemplateTypeService.getTemplateTypeList();
            Map<Long, SpaceTemplateTypeDO> templateTypeDOMap = templateTypeDOList.stream().collect(Collectors.toMap(SpaceTemplateTypeDO::getId, type -> type));
            
            
            for (SpaceTemplateDO spaceTemplateDO : list) {
                String tags = spaceTemplateDO.getTemplateTags();
                // 处理tags
                if (StringUtils.isNotEmpty(tags)) {
                    List<SpaceTemplateTagDO> tagList = new ArrayList<>();
                    
                    List<Long> tagsIds = Arrays.stream(tags.split(","))
                                                 .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    
                    for (Long tagId : tagsIds) {
                        SpaceTemplateTagDO spaceTemplateTagDO = templateTagDOMap.get(tagId);
                        tagList.add(spaceTemplateTagDO);
                    }
                    
                    spaceTemplateDO.setTagList(tagList);
                }
                
                if (StringUtils.isNotEmpty(spaceTemplateDO.getTemplateTypeCode())) {
                    String typeCode = spaceTemplateDO.getTemplateTypeCode();
                    
                    List<SpaceTemplateTypeDO>typeList = new ArrayList<>();
                    
                    
                    List<Long> typeIds = Arrays.stream(typeCode.split(","))
                                                 .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    
                    for (Long typeId : typeIds) {
                        SpaceTemplateTypeDO spaceTemplateTypeDO = templateTypeDOMap.get(typeId);
                        typeList.add(spaceTemplateTypeDO);
                    }
                    
                    spaceTemplateDO.setTypeList(typeList);
                }
                
            }
            
            
        }
        return list;
    }
}
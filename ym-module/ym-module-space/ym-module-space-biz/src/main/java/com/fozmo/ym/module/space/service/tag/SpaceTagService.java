package com.fozmo.ym.module.space.service.tag;

import jakarta.validation.*;
import com.fozmo.ym.module.space.controller.admin.tag.vo.*;
import com.fozmo.ym.module.space.dal.dataobject.tag.SpaceTagDO;
import com.fozmo.ym.framework.common.pojo.PageResult;

/**
 * 空间标签 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceTagService {

    /**
     * 创建空间标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSpaceTag(@Valid SpaceTagSaveReqVO createReqVO);

    /**
     * 更新空间标签
     *
     * @param updateReqVO 更新信息
     */
    void updateSpaceTag(@Valid SpaceTagSaveReqVO updateReqVO);

    /**
     * 删除空间标签
     *
     * @param id 编号
     */
    void deleteSpaceTag(Long id);

    /**
     * 获得空间标签
     *
     * @param id 编号
     * @return 空间标签
     */
    SpaceTagDO getSpaceTag(Long id);

    /**
     * 获得空间标签分页
     *
     * @param pageReqVO 分页查询
     * @return 空间标签分页
     */
    PageResult<SpaceTagDO> getSpaceTagPage(SpaceTagPageReqVO pageReqVO);

}
package com.fozmo.ym.module.space.service.tag;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.fozmo.ym.module.space.controller.admin.tag.vo.*;
import com.fozmo.ym.module.space.dal.dataobject.tag.SpaceTagDO;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;

import com.fozmo.ym.module.space.dal.mysql.tag.SpaceTagMapper;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.*;

/**
 * 空间标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceTagServiceImpl implements SpaceTagService {

    @Resource
    private SpaceTagMapper spaceTagMapper;

    @Resource
    private IdService idService;

    @Override
    public Long createSpaceTag(SpaceTagSaveReqVO createReqVO) {
        // 插入
        SpaceTagDO createObj = BeanUtils.toBean(createReqVO, SpaceTagDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("spaceTag"));

        spaceTagMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateSpaceTag(SpaceTagSaveReqVO updateReqVO) {
        // 校验存在
        validateSpaceTagExists(updateReqVO.getId());
        // 更新
        SpaceTagDO updateObj = BeanUtils.toBean(updateReqVO, SpaceTagDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        spaceTagMapper.updateById(updateObj);
    }

    @Override
    public void deleteSpaceTag(Long id) {
        // 校验存在
        validateSpaceTagExists(id);
        // 删除
        spaceTagMapper.deleteById(id);
    }

    private void validateSpaceTagExists(Long id) {
        if (spaceTagMapper.selectById(id) == null) {
            throw exception(SPACE_TAG_NOT_EXISTS);
        }
    }

    @Override
    public SpaceTagDO getSpaceTag(Long id) {
        return spaceTagMapper.selectById(id);
    }

    @Override
    public PageResult<SpaceTagDO> getSpaceTagPage(SpaceTagPageReqVO pageReqVO) {
        return spaceTagMapper.selectPage(pageReqVO);
    }

}
package com.fozmo.ym.module.space.service.templateaccount;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.templateaccount.vo.TemplateAccountPageReqVO;
import com.fozmo.ym.module.space.controller.admin.templateaccount.vo.TemplateAccountSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templateaccount.TemplateAccountDO;
import jakarta.validation.Valid;

/**
 * 指定账户模板 Service 接口
 *
 * <AUTHOR>
 */
public interface TemplateAccountService {

    /**
     * 创建指定账户模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplateAccount(@Valid TemplateAccountSaveReqVO createReqVO);

    /**
     * 更新指定账户模板
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplateAccount(@Valid TemplateAccountSaveReqVO updateReqVO);

    /**
     * 删除指定账户模板
     *
     * @param id 编号
     */
    void deleteTemplateAccount(Long id);

    /**
     * 获得指定账户模板
     *
     * @param id 编号
     * @return 指定账户模板
     */
    TemplateAccountDO getTemplateAccount(Long id);

    /**
     * 获得指定账户模板分页
     *
     * @param pageReqVO 分页查询
     * @return 指定账户模板分页
     */
    PageResult<TemplateAccountDO> getTemplateAccountPage(TemplateAccountPageReqVO pageReqVO);

}
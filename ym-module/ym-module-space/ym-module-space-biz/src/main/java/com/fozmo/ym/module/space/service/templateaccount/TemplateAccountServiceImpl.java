package com.fozmo.ym.module.space.service.templateaccount;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.space.controller.admin.templateaccount.vo.TemplateAccountPageReqVO;
import com.fozmo.ym.module.space.controller.admin.templateaccount.vo.TemplateAccountSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templateaccount.TemplateAccountDO;
import com.fozmo.ym.module.space.dal.mysql.templateaccount.TemplateAccountMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.TEMPLATE_ACCOUNT_NOT_EXISTS;

/**
 * 指定账户模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TemplateAccountServiceImpl implements TemplateAccountService {

    @Resource
    private TemplateAccountMapper templateAccountMapper;

    @Override
    public Long createTemplateAccount(TemplateAccountSaveReqVO createReqVO) {
        // 插入
        TemplateAccountDO templateAccount = BeanUtils.toBean(createReqVO, TemplateAccountDO.class);
        templateAccountMapper.insert(templateAccount);
        // 返回
        return templateAccount.getId();
    }

    @Override
    public void updateTemplateAccount(TemplateAccountSaveReqVO updateReqVO) {
        // 校验存在
        validateTemplateAccountExists(updateReqVO.getId());
        // 更新
        TemplateAccountDO updateObj = BeanUtils.toBean(updateReqVO, TemplateAccountDO.class);
        templateAccountMapper.updateById(updateObj);
    }

    @Override
    public void deleteTemplateAccount(Long id) {
        // 校验存在
        validateTemplateAccountExists(id);
        // 删除
        templateAccountMapper.deleteById(id);
    }

    private void validateTemplateAccountExists(Long id) {
        if (templateAccountMapper.selectById(id) == null) {
            throw exception(TEMPLATE_ACCOUNT_NOT_EXISTS);
        }
    }

    @Override
    public TemplateAccountDO getTemplateAccount(Long id) {
        return templateAccountMapper.selectById(id);
    }

    @Override
    public PageResult<TemplateAccountDO> getTemplateAccountPage(TemplateAccountPageReqVO pageReqVO) {
        return templateAccountMapper.selectPage(pageReqVO);
    }

}
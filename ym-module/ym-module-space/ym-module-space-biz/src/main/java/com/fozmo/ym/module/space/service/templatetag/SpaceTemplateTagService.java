package com.fozmo.ym.module.space.service.templatetag;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.templatetag.vo.SpaceTemplateTagPageReqVO;
import com.fozmo.ym.module.space.controller.admin.templatetag.vo.SpaceTemplateTagSaveReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTagListReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间模板标签 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceTemplateTagService {

    /**
     * 创建空间模板标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplateTag(@Valid SpaceTemplateTagSaveReqVO createReqVO);

    /**
     * 更新空间模板标签
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplateTag(@Valid SpaceTemplateTagSaveReqVO updateReqVO);

    /**
     * 删除空间模板标签
     *
     * @param id 编号
     */
    void deleteTemplateTag(Long id);

    /**
     * 获得空间模板标签
     *
     * @param id 编号
     * @return 空间模板标签
     */
    SpaceTemplateTagDO getTemplateTag(Long id);

    /**
     * 获得空间模板标签分页
     *
     * @param pageReqVO 分页查询
     * @return 空间模板标签分页
     */
    PageResult<SpaceTemplateTagDO> getTemplateTagPage(SpaceTemplateTagPageReqVO pageReqVO);

    List<SpaceTemplateTagDO> getTemplateTagList(AppSpaceTemplateTagListReqVO pageReqVO);
}
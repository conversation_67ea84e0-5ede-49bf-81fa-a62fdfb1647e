package com.fozmo.ym.module.space.service.templatetag;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.templatetag.vo.SpaceTemplateTagPageReqVO;
import com.fozmo.ym.module.space.controller.admin.templatetag.vo.SpaceTemplateTagSaveReqVO;
import com.fozmo.ym.module.space.controller.app.template.vo.AppSpaceTemplateTagListReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetag.SpaceTemplateTagDO;
import com.fozmo.ym.module.space.dal.mysql.templatetag.SpaceTemplateTagMapper;
import com.fozmo.ym.module.space.dal.redis.template.SpaceTemplateTagRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.TEMPLATE_TAG_NOT_EXISTS;

/**
 * 空间模板标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceTemplateTagServiceImpl implements SpaceTemplateTagService {

    @Resource
    private SpaceTemplateTagMapper templateTagMapper;
    
    @Resource
    private SpaceTemplateTagRedisDao templateTagRedisDao;

    @Resource
    private IdService idService;

    @Override
    public Long createTemplateTag(SpaceTemplateTagSaveReqVO createReqVO) {
        // 插入
        SpaceTemplateTagDO createObj = BeanUtils.toBean(createReqVO, SpaceTemplateTagDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("templateTag"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        templateTagRedisDao.saveTemplateTag(createObj);
        templateTagMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateTemplateTag(SpaceTemplateTagSaveReqVO updateReqVO) {
        // 校验存在
        validateTemplateTagExists(updateReqVO.getId());
        // 更新
        SpaceTemplateTagDO updateObj = BeanUtils.toBean(updateReqVO, SpaceTemplateTagDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        templateTagMapper.updateById(updateObj);
        
        templateTagRedisDao.deleteTemplateTag(updateObj.getId());
        templateTagRedisDao.saveTemplateTag(updateObj);
    }

    @Override
    public void deleteTemplateTag(Long id) {
        
        if (templateTagRedisDao.hasTemplateTag(id)){
           templateTagRedisDao.deleteTemplateTag(id);
            templateTagMapper.deleteById(id);
        }else {
            // 校验存在
            validateTemplateTagExists(id);
            // 删除
            templateTagMapper.deleteById(id);
        }
      
    }

    private void validateTemplateTagExists(Long id) {
        if (templateTagMapper.selectById(id) == null) {
            throw exception(TEMPLATE_TAG_NOT_EXISTS);
        }
    }

    @Override
    public SpaceTemplateTagDO getTemplateTag(Long id) {
        
        // 判断缓存是否存在
        SpaceTemplateTagDO spaceTemplateTagDO = new SpaceTemplateTagDO();
        if (templateTagRedisDao.hasTemplateTag(id)) {
            spaceTemplateTagDO = templateTagRedisDao.getTemplateTag(id);
        } else {
            // 校验存在
            validateTemplateTagExists(id);
            // 返回
            spaceTemplateTagDO =  templateTagMapper.selectById(id);
            
            if (ObjectUtil.isNotEmpty(spaceTemplateTagDO)) {
                templateTagRedisDao.saveTemplateTag(spaceTemplateTagDO);
            }
        }
        return spaceTemplateTagDO;
    }

    @Override
    public PageResult<SpaceTemplateTagDO> getTemplateTagPage(SpaceTemplateTagPageReqVO pageReqVO) {
        
        PageResult<SpaceTemplateTagDO> pageResult = new PageResult<>();
        pageResult = templateTagRedisDao.getTemplateTagPage(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        if (ObjectUtil.isEmpty(pageResult) || pageResult.getTotal() == 0) {
            pageResult = templateTagMapper.selectPage(pageReqVO);
            if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0) {
                pageResult.getList().forEach(spaceTemplateTagDO -> {
                    templateTagRedisDao.saveTemplateTag(spaceTemplateTagDO);
                });
            }
        }
        
        return pageResult;
    }

    /**
     * @param pageReqVO
     *
     * @return
     */
    @Override
    public List<SpaceTemplateTagDO> getTemplateTagList(AppSpaceTemplateTagListReqVO pageReqVO) {
        
        List<SpaceTemplateTagDO>  list = templateTagRedisDao.getTemplateTagList(pageReqVO.getName());
        
        if (CollUtil.isEmpty(list)) {
            LambdaQueryWrapperX<SpaceTemplateTagDO> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(SpaceTemplateTagDO :: getDeleted, false);
            if (StrUtil.isNotBlank(pageReqVO.getName())) {
                queryWrapperX.like(SpaceTemplateTagDO :: getName, pageReqVO.getName());
            }
            list = templateTagMapper.selectList(queryWrapperX);
            
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(spaceTemplateTagDO -> {
                    templateTagRedisDao.saveTemplateTag(spaceTemplateTagDO);
                });
            }
        }
        return list;
    }

}
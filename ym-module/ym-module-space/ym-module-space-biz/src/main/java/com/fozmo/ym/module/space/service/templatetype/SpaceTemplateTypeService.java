package com.fozmo.ym.module.space.service.templatetype;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypeSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间模板类型 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceTemplateTypeService {

    /**
     * 创建空间模板类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplateType(@Valid SpaceTemplateTypeSaveReqVO createReqVO);

    /**
     * 更新空间模板类型
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplateType(@Valid SpaceTemplateTypeSaveReqVO updateReqVO);

    /**
     * 删除空间模板类型
     *
     * @param id 编号
     */
    void deleteTemplateType(Long id);

    /**
     * 获得空间模板类型
     *
     * @param id 编号
     * @return 空间模板类型
     */
    SpaceTemplateTypeDO getTemplateType(Long id);

    /**
     * 获得空间模板类型分页
     *
     * @param pageReqVO 分页查询
     * @return 空间模板类型分页
     */
    PageResult<SpaceTemplateTypeDO> getTemplateTypePage(SpaceTemplateTypePageReqVO pageReqVO);
    
    List<SpaceTemplateTypeDO> getTemplateTypeList();
}
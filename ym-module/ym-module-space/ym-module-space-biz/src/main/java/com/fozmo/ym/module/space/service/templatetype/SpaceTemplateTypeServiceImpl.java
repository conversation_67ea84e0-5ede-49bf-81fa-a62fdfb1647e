package com.fozmo.ym.module.space.service.templatetype;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypePageReqVO;
import com.fozmo.ym.module.space.controller.admin.templatetype.vo.SpaceTemplateTypeSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.templatetype.SpaceTemplateTypeDO;
import com.fozmo.ym.module.space.dal.mysql.templatetype.SpaceTemplateTypeMapper;
import com.fozmo.ym.module.space.dal.redis.template.SpaceTemplateTypeRedisDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.TEMPLATE_TYPE_NOT_EXISTS;

/**
 * 空间模板类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceTemplateTypeServiceImpl implements SpaceTemplateTypeService {

    @Resource
    private SpaceTemplateTypeMapper templateTypeMapper;

    @Resource
    private IdService idService;
    
    @Resource
    private SpaceTemplateTypeRedisDao templateTypeRedisDao;

    @Override
    public Long createTemplateType(SpaceTemplateTypeSaveReqVO createReqVO) {
        // 插入
        SpaceTemplateTypeDO createObj = BeanUtils.toBean(createReqVO, SpaceTemplateTypeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("templateType"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");
        
        templateTypeMapper.insert(createObj);
        templateTypeRedisDao.saveTemplateType(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateTemplateType(SpaceTemplateTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateTemplateTypeExists(updateReqVO.getId());
        // 更新
        SpaceTemplateTypeDO updateObj = BeanUtils.toBean(updateReqVO, SpaceTemplateTypeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        templateTypeMapper.updateById(updateObj);
        templateTypeRedisDao.deleteTemplateType(updateObj.getId());
        templateTypeRedisDao.saveTemplateType(updateObj);
    }

    @Override
    public void deleteTemplateType(Long id) {
        // 校验存在
        
        if (templateTypeRedisDao.hasTemplateType(id)){
            templateTypeRedisDao.deleteTemplateType(id);
            templateTypeMapper.deleteById(id);
        }else{
            validateTemplateTypeExists(id);
            templateTypeMapper.deleteById(id);
        }
    }

    private void validateTemplateTypeExists(Long id) {
        if (templateTypeMapper.selectById(id) == null) {
            throw exception(TEMPLATE_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public SpaceTemplateTypeDO getTemplateType(Long id) {
        SpaceTemplateTypeDO templateTypeDO = new SpaceTemplateTypeDO();
        if (templateTypeRedisDao.hasTemplateType(id)){
            templateTypeDO = templateTypeRedisDao.getTemplateType(id);
        }else{
            templateTypeDO = templateTypeMapper.selectById(id);
            
            if (ObjectUtil.isNotEmpty(templateTypeDO)){
                templateTypeRedisDao.saveTemplateType(templateTypeDO);
            }
        }
        return templateTypeDO;
    }

    @Override
    public PageResult<SpaceTemplateTypeDO> getTemplateTypePage(SpaceTemplateTypePageReqVO pageReqVO) {
        
        PageResult<SpaceTemplateTypeDO> pageResult = new PageResult<>();
        pageResult = templateTypeRedisDao.getTemplateTypePage(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        
        
        if (ObjectUtil.isEmpty(pageResult) && pageResult.getTotal() == 0) {
            pageResult = templateTypeMapper.selectPage(pageReqVO);
            if (ObjectUtil.isNotEmpty(pageResult) && pageResult.getTotal() > 0) {
                pageResult.getList().forEach(templateTypeDO -> {
                    templateTypeRedisDao.saveTemplateType(templateTypeDO);
                });
            }
        }
        return pageResult;
    }

    /**
     * @return
     */
    @Override
    public List<SpaceTemplateTypeDO> getTemplateTypeList() {
       
        List<SpaceTemplateTypeDO> templateTypeDOList = templateTypeRedisDao.getTemplateTypeList();
        if (ObjectUtil.isEmpty(templateTypeDOList) || templateTypeDOList.isEmpty()) {
            templateTypeDOList = templateTypeMapper.selectList();
            if (ObjectUtil.isNotEmpty(templateTypeDOList) && !templateTypeDOList.isEmpty()) {
                templateTypeDOList.forEach(templateTypeDO -> {
                    templateTypeRedisDao.saveTemplateType(templateTypeDO);
                });
            }
        }
        return templateTypeDOList;
    }
    
}
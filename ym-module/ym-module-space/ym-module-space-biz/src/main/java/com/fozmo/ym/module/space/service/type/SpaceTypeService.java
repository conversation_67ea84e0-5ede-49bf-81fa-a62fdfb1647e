package com.fozmo.ym.module.space.service.type;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.type.vo.SpaceTypePageReqVO;
import com.fozmo.ym.module.space.controller.admin.type.vo.SpaceTypeSaveReqVO;
import com.fozmo.ym.module.space.dal.dataobject.type.SpaceTypeDO;
import jakarta.validation.Valid;

/**
 * 空间类型 Service 接口
 *
 * <AUTHOR>
 */
public interface SpaceTypeService {

    /**
     * 创建空间类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createType(@Valid SpaceTypeSaveReqVO createReqVO);

    /**
     * 更新空间类型
     *
     * @param updateReqVO 更新信息
     */
    void updateType(@Valid SpaceTypeSaveReqVO updateReqVO);

    /**
     * 删除空间类型
     *
     * @param id 编号
     */
    void deleteType(Long id);

    /**
     * 获得空间类型
     *
     * @param id 编号
     * @return 空间类型
     */
    SpaceTypeDO getType(Long id);

    /**
     * 获得空间类型分页
     *
     * @param pageReqVO 分页查询
     * @return 空间类型分页
     */
    PageResult<SpaceTypeDO> getTypePage(SpaceTypePageReqVO pageReqVO);

}
package com.fozmo.ym.module.space.service.type;

import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import com.fozmo.ym.module.space.controller.admin.type.vo.*;
import com.fozmo.ym.module.space.dal.dataobject.type.SpaceTypeDO;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;

import com.fozmo.ym.module.space.dal.mysql.type.SpaceTypeMapper;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.*;

/**
 * 空间类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpaceTypeServiceImpl implements SpaceTypeService {

    @Resource
    private SpaceTypeMapper typeMapper;

    @Resource
    private IdService idService;

    @Override
    public Long createType(SpaceTypeSaveReqVO createReqVO) {
        // 插入
        SpaceTypeDO createObj = BeanUtils.toBean(createReqVO, SpaceTypeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        createObj.setCreateId(loginUser.getId());
        createObj.setCreator(loginUser.getUsername());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("spaceType"));
        createObj.setTenantId(loginUser.getTenantId());
        createObj.setTenantCode(loginUser.getTenantId()+"");

        typeMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }

    @Override
    public void updateType(SpaceTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateTypeExists(updateReqVO.getId());
        // 更新
        SpaceTypeDO updateObj = BeanUtils.toBean(updateReqVO, SpaceTypeDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateData(LocalDate.now());
        typeMapper.updateById(updateObj);
    }

    @Override
    public void deleteType(Long id) {
        // 校验存在
        validateTypeExists(id);
        // 删除
        typeMapper.deleteById(id);
    }

    private void validateTypeExists(Long id) {
        if (typeMapper.selectById(id) == null) {
            throw exception(TYPE_NOT_EXISTS);
        }
    }

    @Override
    public SpaceTypeDO getType(Long id) {
        return typeMapper.selectById(id);
    }

    @Override
    public PageResult<SpaceTypeDO> getTypePage(SpaceTypePageReqVO pageReqVO) {
        return typeMapper.selectPage(pageReqVO);
    }

}
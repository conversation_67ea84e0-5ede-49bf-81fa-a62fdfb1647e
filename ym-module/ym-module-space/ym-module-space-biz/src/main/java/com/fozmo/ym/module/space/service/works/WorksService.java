package com.fozmo.ym.module.space.service.works;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksSaveReqVO;
import com.fozmo.ym.module.space.controller.app.works.vo.AppMyPageReqVO;
import com.fozmo.ym.module.space.controller.app.works.vo.BatchSaveDTO;
import com.fozmo.ym.module.space.controller.app.works.vo.WorksCheckVO;
import com.fozmo.ym.module.space.controller.app.works.vo.WorksVO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 空间作品 Service 接口
 *
 * <AUTHOR>
 */
public interface WorksService {

    /**
     * 创建空间作品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWorks(@Valid WorksSaveReqVO createReqVO);

    /**
     * 更新空间作品
     *
     * @param updateReqVO 更新信息
     */
    void updateWorks(@Valid WorksSaveReqVO updateReqVO);

    /**
     * 删除空间作品
     *
     * @param id 编号
     */
    void deleteWorks(Long id);

    /***
     * 获取作品详情
     */
    WorksDO getWorksInfo(Long worksId);

    WorksDO getWorksInfoById(Long worksId);

    /**
     * 获得空间作品分页
     *
     * @param pageReqVO 分页查询
     * @return 空间作品分页
     */
    PageResult<WorksDO> getWorksPage(WorksPageReqVO pageReqVO);

    /***
     * 查看我的作品
     */
    WorksVO queryMyWorksPage(WorksPageReqVO pageReqVO);
    
    Long worksCountByAccountId(Long accountId);

    /**
     * 批量创建作品
     */
    List<BatchSaveDTO> batchCreate(List<BatchSaveDTO> reqVO);

    /***
     * 创建前检查
     */
    WorksCheckVO createCheck();

    /**
     * 批量删作品
     *
     * @param ids
     */
    void batchDelete(List<Long> ids);

    /***
     * 分页查询自己/他人作品
     */
    PageResult<WorksDO> getAccountWorksPage(WorksPageReqVO reqVO);

    boolean hasWork(Long worksId);

    List<Long> getAllWorksByAccountId(Long accountId);

    PageResult<WorksDO> queryMyPage(@Valid AppMyPageReqVO pageReqVO);

    Long worksCount(Long id);
}

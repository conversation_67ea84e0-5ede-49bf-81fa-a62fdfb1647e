package com.fozmo.ym.module.space.service.works;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.account.api.account.dto.AccountRightsInfoDTO;
import com.fozmo.ym.module.social.api.fans.SocialFansApi;
import com.fozmo.ym.module.social.api.works.WorksCommentApi;
import com.fozmo.ym.module.social.api.works.WorksLikeApi;
import com.fozmo.ym.module.social.api.works.WorksShareApi;
import com.fozmo.ym.module.space.api.dto.WorksInfoDTO;
import com.fozmo.ym.module.space.api.dto.WorksUsageDTO;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksPageReqVO;
import com.fozmo.ym.module.space.controller.admin.works.vo.WorksSaveReqVO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountInfoDTO;
import com.fozmo.ym.module.space.controller.app.mount.vo.MountWorksRespVO;
import com.fozmo.ym.module.space.controller.app.works.vo.AppMyPageReqVO;
import com.fozmo.ym.module.space.controller.app.works.vo.BatchSaveDTO;
import com.fozmo.ym.module.space.controller.app.works.vo.WorksCheckVO;
import com.fozmo.ym.module.space.controller.app.works.vo.WorksVO;
import com.fozmo.ym.module.space.core.mq.works.WorksDeleteProduce;
import com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO;
import com.fozmo.ym.module.space.dal.dataobject.works.WorksDO;
import com.fozmo.ym.module.space.dal.mysql.works.WorksMapper;
import com.fozmo.ym.module.space.dal.redis.works.MyWorksRedisDao;
import com.fozmo.ym.module.space.dal.redis.works.WorksRedisDao;
import com.fozmo.ym.module.space.enums.MountType;
import com.fozmo.ym.module.space.service.mount.MountWorksService;
import com.fozmo.ym.module.space.service.space.SpaceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.auth.enums.ErrorCodeConstants.ACCOUNT_NOT;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.WORKS_CREATE_NO;
import static com.fozmo.ym.module.space.enums.ErrorCodeConstants.WORKS_NOT_EXISTS;

/**
 * 空间作品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WorksServiceImpl implements WorksService {

    @Resource
    private WorksMapper worksMapper;

    @Resource
    private IdService idService;

    @Resource
    @Lazy
    private AccountApi accountApi;

    @Resource
    private WorksRedisDao worksRedisDao;
    @Resource
    private MyWorksRedisDao myWorksRedisDao;

    @Resource
    private WorksLikeApi worksLikeApi;

    @Resource
    private WorksCommentApi worksCommentApi;

    @Resource
    private WorksShareApi worksShareApi;

    @Resource
    private WorksDeleteProduce worksDeleteProduce;
    @Resource
    @Lazy
    private MountWorksService mountWorksService;

    @Resource
    @Lazy
    private SpaceService spaceService;

    @Resource
    @Lazy
    private SocialFansApi socialFansApi;

    @Override
    public Long createWorks(WorksSaveReqVO createReqVO) {

        // 插入
        Long loginAccountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isNull(loginAccountId)) {
            throw exception(ACCOUNT_NOT);
        }

        AccountBaseInfoDTO loginAccountInfo = accountApi.queryAccountBaseInfoById(loginAccountId);
        if (ObjectUtil.isEmpty(loginAccountInfo)) {
            throw exception(ACCOUNT_NOT);
        }
        WorksDO createObj = BeanUtils.toBean(createReqVO, WorksDO.class);

        
        // 权益校验 数量校验
        checkWorks(createReqVO);

        createObj.setCreateId(loginAccountInfo.getId());
        createObj.setCreator(loginAccountInfo.getName());
        createObj.setCreateData(LocalDate.now());
        createObj.setId(idService.nextId("works"));
        createObj.setWorksStatus(0);

        if (ObjectUtil.isEmpty(createObj.getWorksCnName())) {
            createObj.setWorksCnName("作品" + RandomUtil.randomString(4));
        }
        createObj.setTenantId(loginAccountInfo.getTenantId());
        createObj.setTenantCode(loginAccountInfo.getTenantCode());
        createObj.setAccountId(loginAccountInfo.getId());

        worksRedisDao.saveWorks(createObj);
        myWorksRedisDao.saveMyWorksId(loginAccountInfo.getId(), createObj.getId());
        worksMapper.insert(createObj);
        // 返回
        return createObj.getId();
    }



    @Override
    public void updateWorks(WorksSaveReqVO updateReqVO) {
        // 校验存在
        Long worksId = updateReqVO.getId();
        Long loginAccountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isNull(loginAccountId)) {
            throw exception(ACCOUNT_NOT);
        }

        AccountBaseInfoDTO loginAccountInfo = accountApi.queryAccountBaseInfoById(loginAccountId);
        if (ObjectUtil.isEmpty(loginAccountInfo)) {
            throw exception(ACCOUNT_NOT);
        }

        if(ObjectUtil.isEmpty(worksId)){
            throw exception(WORKS_NOT_EXISTS);
        }


        if (worksRedisDao.haseWorksKey(worksId)){
            // 缓存存在 删除缓存
            worksRedisDao.delWorksKey(worksId);
        }else {
            validateWorksExists(updateReqVO.getId());
        }

        WorksDO updateObj = BeanUtils.toBean(updateReqVO, WorksDO.class);
        updateObj.setAccountId(loginAccountInfo.getId());
        updateObj.setUpdaterId(loginAccountInfo.getId());
        updateObj.setUpdater(loginAccountInfo.getName());
        updateObj.setUpdateData(LocalDate.now());
        worksMapper.updateById(updateObj);
        // 更新缓存
        worksRedisDao.saveWorks(this.getWorksInfo(worksId));
    }

    @Override
    public void deleteWorks(Long id) {

        // 校验账号
        Long loginAccountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginAccountId)) {
            throw exception(ACCOUNT_NOT);
        }

        AccountBaseInfoDTO loginAccountInfo = accountApi.queryAccountBaseInfoById(loginAccountId);
        if (ObjectUtil.isEmpty(loginAccountInfo)) {
            throw exception(ACCOUNT_NOT);
        }
        // 校验存在
        if (worksRedisDao.haseWorksKey(id)){
            worksRedisDao.delWorksKey(id);
            myWorksRedisDao.deleteMyWorksId(loginAccountInfo.getId(),id);
            worksMapper.deleteById(id);
        }else {
            validateWorksExists(id);
            // 删除
            worksMapper.deleteById(id);
        }
        // 执行删除队列
        worksDeleteProduce.sendSpaceDelete(id, loginAccountId);

    }

// 验证作品是否存在
    private void validateWorksExists(Long id) {
        // 根据id查询作品
        if (worksMapper.selectById(id) == null) {
            // 如果作品不存在，抛出异常
            throw exception(WORKS_NOT_EXISTS);
        }
    }

/**
 * @param worksId
 *
 * @return
 */
@Override
public WorksDO getWorksInfo(Long worksId) {

    WorksDO worksDO = new WorksDO();
    if (worksRedisDao.haseWorksKey(worksId)){
        worksDO= worksRedisDao.getWorksInfo(worksId);

        worksDO = addWorksInfoValue(worksDO);
        return worksDO;
    }else {
        validateWorksExists(worksId);
        worksDO = worksMapper.selectById(worksId);

        worksDO = addWorksInfoValue(worksDO);

        if (ObjectUtil.isNotEmpty(worksDO)){
            worksRedisDao.saveWorks(worksDO);
            myWorksRedisDao.saveMyWorksId(worksDO.getAccountId(),worksDO.getId());
        }
        return worksDO;
    }
}

    /**
     * @param worksId
     * @return
     */
    @Override
    public WorksDO getWorksInfoById(Long worksId) {
        WorksDO worksDO = new WorksDO();
        if (worksRedisDao.haseWorksKey(worksId)) {
            worksDO = worksRedisDao.getWorksInfo(worksId);
            return worksDO;
        } else {
            worksDO = worksMapper.selectById(worksId);

            if (ObjectUtil.isNotEmpty(worksDO)) {
                worksRedisDao.saveWorks(worksDO);
                myWorksRedisDao.saveMyWorksId(worksDO.getAccountId(), worksDO.getId());
            }
            return worksDO;
        }
    }

    @Override
    public PageResult<WorksDO> getWorksPage(WorksPageReqVO pageReqVO) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("作品分页查询-校验长度");
        checkWorksLength();
        stopWatch.stop();
        Long accountId = pageReqVO.getAccountId();
        pageReqVO.setAccountId(accountId);
        pageReqVO.setWorksStatus(0);
        PageResult<WorksDO> result = new PageResult<>();
        // redis queryPage
        stopWatch.start("作品分页查询-查询分页");
//        result = worksRedisDao.getWorksPage(pageReqVO.getPageNo(), pageReqVO.getPageSize(), pageReqVO.getWorksCnName(), pageReqVO.getPointType());
//        if (ObjectUtil.isNotEmpty(pageReqVO) && result.getTotal()>0){
//        }else {
            result = worksMapper.selectPage(pageReqVO);
//        }
        if (ObjectUtil.isNotEmpty(result) && CollUtil.isNotEmpty(result.getList())){
            // 缓存保存
            result.getList().forEach(worksDO -> {
                worksRedisDao.saveWorks(worksDO);
                myWorksRedisDao.saveMyWorksId(worksDO.getAccountId(),worksDO.getId());
            });
        }
        stopWatch.stop();
        stopWatch.start("作品分页查询-添加信息");
        addWorksInfoListValue(result.getList());
        stopWatch.stop();
        StopWatch.TaskInfo[] task = stopWatch.getTaskInfo();
        for (StopWatch.TaskInfo taskInfo : task) {
            System.out.println(taskInfo.getTaskName() + ": " + taskInfo.getTimeMillis());
        }
        return result;
    }

    private void checkWorksLength() {
        LambdaQueryWrapperX queryWrapperX = new LambdaQueryWrapperX<WorksDO>().eq(WorksDO::getDeleted, false);
        // 查询作品数量
        Long dbCount = worksMapper.selectCount(queryWrapperX);
        Long redisCount = worksRedisDao.getWorksCount();
        if (dbCount > redisCount) {
            //
            List<WorksDO> worksDOList = worksMapper.selectList(queryWrapperX);
            if (CollectionUtil.isNotEmpty(worksDOList)) {
                worksRedisDao.delAllWorks();
                worksRedisDao.saveWorksList(worksDOList);
                Map<Long, List<WorksDO>> accountWorksMap = worksDOList.stream().collect(Collectors.groupingBy(WorksDO::getAccountId));
                if (MapUtil.isNotEmpty(accountWorksMap)) {
                    accountWorksMap.forEach((accountId, works) -> {
                        myWorksRedisDao.deleteMyWorks(accountId);
                        myWorksRedisDao.saveMyWorksList(accountId, works);
                    });
                }
            }
        }
    }


    /**
     * 查看作品占用状态
     * @param pageReqVO
     * @return
     */
    @Override
    public WorksVO queryMyWorksPage(WorksPageReqVO pageReqVO) {
//
        WorksVO worksVO = new WorksVO();
        List<WorksInfoDTO> worksInfoVOS = new ArrayList<>();
        // 查询我的作品
        Long accountId = SecurityFrameworkUtils.getLoginUserId();


        AccountRightsInfoDTO accountRightsInfo = accountApi.queryAccountRightsInfoById(accountId);
        if (ObjectUtil.isEmpty(accountRightsInfo)) {
            throw exception(ACCOUNT_NOT);
        }

        Integer allNum = accountRightsInfo.getRightsInfo().getWorksRights();
        worksVO.setTotalNum(allNum);
        // 查询 当前用户作品 分页
        PageResult<Long> worksIdPage = myWorksRedisDao.getMyWorksIdPage(accountId, pageReqVO.getPageNo(), pageReqVO.getPageSize());
        // 假如 查询账户Id为空 则返回空
        if (ObjectUtil.isEmpty(accountId) || ObjectUtil.isEmpty(worksIdPage)) {
            return worksVO;
        }

        // 查询 当前用户作品 分页
        if (ObjectUtil.isNotEmpty(worksIdPage) && worksIdPage.getTotal() > 0 && CollectionUtil.isNotEmpty(worksIdPage.getList())) {
            // 遍历 作品Id
            List<WorksDO> worksDOList = new ArrayList<>();
            for (Long worksId : worksIdPage.getList()) {
                // 获取作品信息
                WorksDO worksDO = worksRedisDao.getWorksInfo(worksId);
                worksDOList.add(worksDO);
            }

            if (CollectionUtil.isNotEmpty(worksDOList)) {
                Integer total = Math.toIntExact(worksIdPage.getTotal());
                // 加工作品信息
                addWorksInfoListValue(worksDOList);
                worksInfoVOS = BeanUtils.toBean(worksDOList, WorksInfoDTO.class);
                worksVO.setTotal(total);
                worksVO.setWorksInfoVOList(worksInfoVOS);
                worksVO.setCurrentNum(total);
                if (allNum - total > 0) {
                    worksVO.setAvailableNum(allNum - total);
                } else {
                    worksVO.setAvailableNum(0);
                }

            }
        }
        return worksVO;
    }

/**
 * @param accountId
 *
 * @return
 */
@Override
public Long worksCountByAccountId(Long accountId) {
    LambdaQueryWrapperX<WorksDO> queryWrapper = new LambdaQueryWrapperX();
    queryWrapper.eq(WorksDO::getAccountId, accountId);
    queryWrapper.eq(WorksDO::getDeleted, Boolean.FALSE);
    queryWrapper.in(WorksDO::getWorksStatus,0,1);
    return worksMapper.selectCount(queryWrapper);
}

/**
 * @param reqVO
 *
 * @return
 */
@Override
@Transactional(rollbackFor = Exception.class)
public List<BatchSaveDTO> batchCreate(List<BatchSaveDTO> reqVO) {

    Integer createNum = reqVO.size();
    if (createNum == 0) {
        throw exception(WORKS_CREATE_NO, "创建作品数量为0");
    }
    Long AccountId = SecurityFrameworkUtils.getLoginUserId();

    AccountRightsInfoDTO accountInfoDTO = accountApi.queryAccountRightsInfoById(AccountId);
    if (ObjectUtil.isEmpty(accountInfoDTO)) {
        throw exception(ACCOUNT_NOT);
    }

    Long userRightsId = accountInfoDTO.getRightsInfo().getRightsId();

    if (ObjectUtil.isNotEmpty(userRightsId) && userRightsId < 0L) {
        throw exception(WORKS_CREATE_NO, "当前用户为游客权益");
    }
    Long rightsWorksNum = Long.valueOf(accountInfoDTO.getRightsInfo().getWorksRights());

    if (CollectionUtil.isNotEmpty(reqVO)) {
        for (BatchSaveDTO saveDTO : reqVO) {
            WorksCheckVO checkVO = createCheck();
            if (ObjectUtil.isEmpty(checkVO)) {
                saveDTO.setCreateStatus(false);
                saveDTO.setCreateMsg("当前用户没有创建权限");
                saveDTO.setId(null);
                continue;
            } else {
                if (checkVO.getAvailableNum() > rightsWorksNum) {
                    saveDTO.setCreateStatus(false);
                    saveDTO.setCreateMsg("可以创建总数超出限制");
                    saveDTO.setId(null);
                    continue;
                } else {
                    WorksDO worksDO = BeanUtils.toBean(saveDTO, WorksDO.class);
                    worksDO.setDeleted(Boolean.FALSE);
                    worksDO.setWorksStatus(0);

                    String name = "作品" + RandomUtil.randomString(4);
                    worksDO.setWorksCnName(name);
                    worksDO.setCreateId(accountInfoDTO.getId());
                    worksDO.setCreator(accountInfoDTO.getName());
                    worksDO.setCreateData(LocalDate.now());
                    Long id = idService.nextId("works");
                    worksDO.setId(id);
                    worksDO.setTenantId(accountInfoDTO.getTenantId());
                    worksDO.setTenantCode(accountInfoDTO.getTenantCode());
                    worksDO.setAccountId(accountInfoDTO.getId());
                    worksRedisDao.saveWorks(worksDO);
                    myWorksRedisDao.saveMyWorksId(worksDO.getAccountId(), id);
                    int add = worksMapper.insert(worksDO);
                    if (add > 0) {
                        saveDTO.setCreateStatus(true);
                        saveDTO.setId(id);
                        saveDTO.setCreateMsg("创建成功");
                        saveDTO.setWorksName(name);
                    }
                }
            }
        }

    }
    return reqVO;
}

/**
 * @return
 */
@Override
public WorksCheckVO createCheck() {
    
    WorksCheckVO worksVO = new WorksCheckVO();
    Long accountId = SecurityFrameworkUtils.getLoginUser().getId();
    if (ObjectUtil.isNull(accountId)) {
        throw exception(WORKS_CREATE_NO,"用户不存在");
    }
    AccountRightsInfoDTO accountInfo = accountApi.queryAccountRightsInfoById(accountId);
    Long total = myWorksRedisDao.selectCount(accountId);
    
    Integer allNum = accountInfo.getRightsInfo().getWorksRights();
    worksVO.setTotalNum(allNum);
    Integer currenNum = Math.toIntExact(total);
    worksVO.setCurrentNum(currenNum);
    Integer availableNum = allNum - currenNum;
    
    worksVO.setRightsId(accountInfo.getRightsId());
    if (availableNum > 0) {
        worksVO.setAvailableNum(availableNum);
    }else {
        worksVO.setAvailableNum(0);
    }
    return worksVO;
}

    /**
     * @param ids
     */
    @Override
    public void batchDelete(List<Long> ids) {

        Long loginAccountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginAccountId)) {
            throw exception(ACCOUNT_NOT);
        }

        if (CollUtil.isEmpty(ids)) {
            throw exception(WORKS_NOT_EXISTS);
        }

        AccountBaseInfoDTO loginAccountInfo = accountApi.queryAccountBaseInfoById(loginAccountId);
        if (ObjectUtil.isEmpty(loginAccountInfo)) {
            throw exception(ACCOUNT_NOT);
        }
        if (CollectionUtil.isEmpty(ids)) {
            throw exception(WORKS_NOT_EXISTS);
        }
        for (Long id : ids) {
            if (worksRedisDao.haseWorksKey(id)) {
                // 删除缓存
                worksRedisDao.delWorksKey(id);
                if (myWorksRedisDao.hasWorks(loginAccountId, id)) {
                    myWorksRedisDao.deleteMyWorksId(loginAccountId, id);
                }
                worksDeleteProduce.sendSpaceDelete(id, loginAccountId);
            }
        }
        // 删除数据库记录
        worksMapper.deleteBatchIds(ids);
    }

    @Override
    public PageResult<WorksDO> getAccountWorksPage(WorksPageReqVO reqVO) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("作品分页查询-校验长度");
        PageResult<WorksDO> pageResult = new PageResult<>();
        // 获取查询 账户Id
        Long queryAccountId = reqVO.getAccountId();
        // 假如 查询账户Id为空 则返回空
        if (ObjectUtil.isEmpty(queryAccountId)) {
            return PageResult.empty();
        }
        stopWatch.stop();
        stopWatch.start("作品分页查询-查询缓存");

        // 查询 当前用户作品 分页
        PageResult<Long> worksIdPage = myWorksRedisDao.getMyWorksIdPage(queryAccountId,reqVO.getPageNo(),reqVO.getPageSize());
        if (ObjectUtil.isNotEmpty(worksIdPage) && worksIdPage.getTotal() > 0  && CollectionUtil.isNotEmpty(worksIdPage.getList())){
            // 遍历 作品Id
            List<WorksDO> worksDOList = new ArrayList<>();
            for (Long worksId : worksIdPage.getList()) {
                // 获取作品信息
                WorksDO worksDO = worksRedisDao.getWorksInfo(worksId);
                if (ObjectUtil.isNotEmpty(worksDO)) {
                    if (ObjectUtil.isNotEmpty(reqVO.getPointType())) {
                        if (worksDO.getPointType().equals(reqVO.getPointType())) {
                            worksDOList.add(worksDO);
                        }
                    } else {
                        worksDOList.add(worksDO);
                    }
                }

            }
            stopWatch.stop();

            stopWatch.start("作品分页查询-添加作品信息");
            if (CollectionUtil.isNotEmpty(worksDOList)){
                pageResult.setTotal((long) worksDOList.size());
                // 加工作品信息 - 使用多线程优化版本
                addMyWorksInfoListValueOptimized(worksDOList, queryAccountId);

                pageResult.setList(worksDOList);
            }
            stopWatch.stop();

            StopWatch.TaskInfo[] taskInfos = stopWatch.getTaskInfo();
            for (StopWatch.TaskInfo taskInfo : taskInfos) {
                System.out.println(taskInfo.getTaskName() + ": " + taskInfo.getTimeMillis());
            }
        }

        return pageResult;
    }

    /**
     * @param worksId
     * @return
     */
    @Override
    public boolean hasWork(Long worksId) {
        return worksRedisDao.haseWorksKey(worksId);
    }

    /**
     * @param accountId
     * @return
     */
    @Override
    public List<Long> getAllWorksByAccountId(Long accountId) {
        return myWorksRedisDao.getAllMysqlWorksId(accountId);
    }

    /**
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<WorksDO> queryMyPage(AppMyPageReqVO pageReqVO) {
        PageResult<WorksDO> pageResult = new PageResult<>();
        List<Long> worksIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(pageReqVO)) {
            Long accountId = pageReqVO.getAccountId();
            Long spaceId = pageReqVO.getSpaceId();
            if (ObjectUtil.isNotEmpty(accountId) && ObjectUtil.isNotEmpty(spaceId)) {
                // 查询挂载关系
                MountWorksRespVO mountWorksRespVO = mountWorksService.getMountList(spaceId);
                if (ObjectUtil.isNotEmpty(mountWorksRespVO)) {
                    if (CollectionUtil.isNotEmpty(mountWorksRespVO.getWorksList())) {
                        List<Long> mountWorksIds = mountWorksRespVO.getWorksList().stream().filter(e -> MountType.isWorksType(e.getMountType())).map(MountInfoDTO::getMountId).collect(Collectors.toList());
                        worksIds.addAll(mountWorksIds);
                    }
                    if (CollectionUtil.isNotEmpty(mountWorksRespVO.getPonitList())) {
                        List<Long> mountPointIds = mountWorksRespVO.getPonitList().stream().filter(e -> MountType.isWorksType(e.getMountType())).map(MountInfoDTO::getMountId).collect(Collectors.toList());
                        worksIds.addAll(mountPointIds);
                    }
                }
            } else {
                worksIds = myWorksRedisDao.getAllMysqlWorksId(accountId);
            }
        }

        if (CollectionUtil.isEmpty(worksIds)) {
            return PageResult.empty();
        } else {

            List<WorksDO> worksDOList = new ArrayList<>();
            for (Long worksId : worksIds) {
                WorksDO worksDO = getWorksInfoById(worksId);
                worksDOList.add(worksDO);
            }
            pageResult.setTotal((long) worksDOList.size());
            Integer pageSize = pageReqVO.getPageSize();
            Integer pageNo = pageReqVO.getPageNo();
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, worksDOList.size());
            pageResult.setList(worksDOList.subList(start, end));

        }
        return pageResult;
    }

    /**
     * @param id
     * @return
     */
    @Override
    public Long worksCount(Long id) {
        return myWorksRedisDao.selectCount(id);
    }

    /**
     * @param batchIds
     * @return
     */
    @Override
    public Map<Long, WorksDO> batchGetWorksInfo(List<Long> batchIds) {
        Map<Long, WorksDO> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(batchIds)) {
            for (Long id : batchIds) {
                WorksDO worksDO = worksRedisDao.getWorksInfo(id);
                result.put(id, worksDO);
                return result;
            }
        }

        return Map.of();
    }

    /**
     * 校验当前用户是否可以 新建作品
     */
    private boolean checkWorks(WorksSaveReqVO createReqVO) {
        // 查询当前用户权益
        
        Long AccountId = SecurityFrameworkUtils.getLoginUserId();
        AccountRightsInfoDTO accountInfoDTO = accountApi.queryAccountRightsInfoById(AccountId);
        Long userRightsId = accountInfoDTO.getRightsInfo().getRightsId();
        
        Long rightsWorksNum = Long.valueOf(accountInfoDTO.getRightsInfo().getWorksRights());
        if (ObjectUtil.isNotEmpty(userRightsId) && userRightsId<0L){
            throw exception(WORKS_CREATE_NO,"当前用户为游客权益");
        }
        // 查询作品数量
        Long worksNum = worksCountByAccountId(accountInfoDTO.getId());
        if (worksNum >= rightsWorksNum){
            throw exception(WORKS_CREATE_NO,"当前用户作品数：{},权益作品数{}，无法创建",worksNum,rightsWorksNum);
        }
        return true;
    }
    
    
    private WorksDO addWorksInfoValue(WorksDO worksDO){
        // 获取当前用户用户id
        Boolean status = Boolean.TRUE;
        Long loginAccountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(loginAccountId)){
            // 当前用户不存在时 说明不用计算当前状态
            status= Boolean.FALSE;
        }
        // 填充用户数据
        Long accountId = worksDO.getAccountId();
        if (ObjectUtil.isNotEmpty(accountId)){
            AccountBaseInfoDTO accountInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
            if (ObjectUtil.isNotEmpty(accountInfoDTO)){
                worksDO.setAccountName(accountInfoDTO.getName());
                worksDO.setNickname(accountInfoDTO.getNickname());
                worksDO.setAvatar(accountInfoDTO.getAvatar());
                worksDO.setRightsId(accountInfoDTO.getRightsId());
            }
        }
        // 处理作品点赞数据
        worksDO.setLikeCount(worksLikeApi.countByWorksId(worksDO.getId()));
        if (status){
            worksDO.setLikeStatus(worksLikeApi.isLike(worksDO.getId(),loginAccountId));
        }else {
            worksDO.setLikeStatus(status);
        }
        // 处理评论数据
        worksDO.setCommentCount(worksCommentApi.countByWorksId(worksDO.getId()));
        if (status){
            worksDO.setCommentStatus(worksCommentApi.isComment(worksDO.getId(),loginAccountId));
        }else {
            worksDO.setCommentStatus(status);
        }
        // 处理挂载数据 todo
        List<Long> spaceIds = mountWorksService.getWorksMountList(worksDO.getId());
        if (CollUtil.isNotEmpty(spaceIds)) {

            List<WorksUsageDTO> usageList = new ArrayList<>();
            for (Long spaceId : spaceIds) {
                SpaceDO spaceDO = spaceService.getBaseSpace(spaceId);
                if (ObjectUtil.isNotEmpty(spaceDO)) {
                    WorksUsageDTO worksUsageDTO = new WorksUsageDTO();
                    worksUsageDTO.setSpaceId(spaceDO.getId());
                    worksUsageDTO.setSpaceName(spaceDO.getSpaceCnName());
                    usageList.add(worksUsageDTO);
                }
            }
            worksDO.setUsage(usageList);
            worksDO.setUsageCount(spaceIds.size());
        } else {
            worksDO.setUsageCount(0);
            worksDO.setUsage(List.of());
        }

        // 处理作品分享数据
        worksDO.setShareCount(worksShareApi.countByWorksId(worksDO.getId()));
        if (status){
            worksDO.setShareStatus(worksShareApi.isShare(worksDO.getId(),loginAccountId));
        }else {
            worksDO.setShareStatus(status);
        }

        // 处理关注状态
        if (status) {
            worksDO.setFansStatus(socialFansApi.isFans(worksDO.getAccountId(), loginAccountId));
        } else {
            worksDO.setFansStatus(status);
        }

        return worksDO;
    }

    private List<WorksDO> addWorksInfoListValue(List<WorksDO> worksDOList){


        Long loginAccountId = SecurityFrameworkUtils.getLoginUserId();
        if (CollUtil.isEmpty(worksDOList)) {
            return worksDOList;
        }

        // 获取当前用户状态
        Boolean needUserStatus = ObjectUtil.isNotEmpty(loginAccountId);

        try {
            // 收集所有用户ID
            List<Long> accountIds = worksDOList.stream().map(WorksDO::getAccountId).filter(Objects::nonNull).collect(Collectors.toList());
            // 收集所有作品ID
            List<Long> worksIds = worksDOList.stream()
                    .map(WorksDO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 并行批量获取统计数据
            CompletableFuture<ConcurrentMap<Long, Number>> likeCountFuture = CompletableFuture.supplyAsync(() ->
                    batchGetLikeCounts(worksIds));

            CompletableFuture<Map<Long, Boolean>> likeStatusFuture = CompletableFuture.supplyAsync(() -> {
                if (needUserStatus) {
                    return batchGetLikeStatus(worksIds, loginAccountId);
                }
                return Collections.emptyMap();
            });

            CompletableFuture<Map<Long, Number>> commentCountFuture = CompletableFuture.supplyAsync(() ->
                    batchGetCommentCounts(worksIds));

            CompletableFuture<Map<Long, Boolean>> commentStatusFuture = CompletableFuture.supplyAsync(() -> {
                if (needUserStatus) {
                    return batchGetCommentStatus(worksIds, loginAccountId);
                }
                return Collections.emptyMap();
            });

            CompletableFuture<Map<Long, AccountBaseInfoDTO>> accountInfoFuture = CompletableFuture.supplyAsync(() ->
                    batchGetAccountInfo(accountIds));

            // 等待所有异步任务完成
            CompletableFuture.allOf(accountInfoFuture, likeCountFuture, likeStatusFuture,
                    commentCountFuture, commentStatusFuture).get();

            // 获取结果
            Map<Long, AccountBaseInfoDTO> accountMap = accountInfoFuture.get();
            Map<Long, Number> likeCountMap = likeCountFuture.get();
            Map<Long, Boolean> likeStatusMap = likeStatusFuture.get();
            Map<Long, Number> commentCountMap = commentCountFuture.get();
            Map<Long, Boolean> commentStatusMap = commentStatusFuture.get();
            Map<Long, AccountBaseInfoDTO> accountInfoMap = accountInfoFuture.get();

            // 填充数据
            return fillWorksData(worksDOList, accountInfoMap, likeCountMap, likeStatusMap,
                    commentCountMap, commentStatusMap, needUserStatus);

        } catch (Exception e) {
            log.error("批量填充作品信息失败", e);
            // 降级处理：使用原有的同步方式
            return worksDOList;
        }
    }

    private List<WorksDO> fillWorksData(List<WorksDO> worksDOList, Map<Long, AccountBaseInfoDTO> accountInfoMap, Map<Long, Number> likeCountMap, Map<Long, Boolean> likeStatusMap, Map<Long, Number> commentCountMap, Map<Long, Boolean> commentStatusMap, Boolean needUserStatus) {
        return worksDOList.stream().map(worksDO -> {
            try {
                // 填充用户数据
                if (ObjectUtil.isNotEmpty(worksDO.getAccountId()) && ObjectUtil.isNotEmpty(accountInfoMap)) {
                    if (accountInfoMap.containsKey(worksDO.getAccountId())) {
                        worksDO.setAccountName(accountInfoMap.get(worksDO.getAccountId()).getName());
                        worksDO.setNickname(accountInfoMap.get(worksDO.getAccountId()).getNickname());
                        worksDO.setAvatar(accountInfoMap.get(worksDO.getAccountId()).getAvatar());
                        worksDO.setRightsId(accountInfoMap.get(worksDO.getAccountId()).getRightsId());
                    }
                }

                Long worksId = worksDO.getId();
                // 填充点赞数据
                worksDO.setLikeCount(likeCountMap.getOrDefault(worksId, 0).longValue());
                worksDO.setLikeStatus(needUserStatus ? likeStatusMap.getOrDefault(worksId, false) : false);

                // 填充评论数据
                worksDO.setCommentCount(commentCountMap.getOrDefault(worksId, 0).longValue());
                worksDO.setCommentStatus(needUserStatus ? commentStatusMap.getOrDefault(worksId, false) : false);

                return worksDO;
            } catch (Exception e) {
                log.warn("填充作品数据失败, worksId: {}", worksDO.getId(), e);
                return worksDO;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 多线程优化版本：批量填充作品信息
     */
    private List<WorksDO> addMyWorksInfoListValueOptimized(List<WorksDO> worksDOList, Long loginAccountId) {
        if (CollUtil.isEmpty(worksDOList)) {
            return worksDOList;
        }

        // 获取当前用户状态
        Boolean needUserStatus = ObjectUtil.isNotEmpty(loginAccountId);

        try {
            // 收集所有作品ID
            List<Long> worksIds = worksDOList.stream()
                    .map(WorksDO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 并行执行数据获取任务
            CompletableFuture<AccountBaseInfoDTO> accountInfoFuture = CompletableFuture.supplyAsync(() -> {
                if (needUserStatus) {
                    try {
                        return accountApi.queryAccountBaseInfoById(loginAccountId);
                    } catch (Exception e) {
                        log.warn("获取用户信息失败, accountId: {}", loginAccountId, e);
                        return null;
                    }
                }
                return null;
            });

            // 并行批量获取统计数据
            CompletableFuture<ConcurrentMap<Long, Number>> likeCountFuture = CompletableFuture.supplyAsync(() ->
                    batchGetLikeCounts(worksIds));

            CompletableFuture<Map<Long, Boolean>> likeStatusFuture = CompletableFuture.supplyAsync(() -> {
                if (needUserStatus) {
                    return batchGetLikeStatus(worksIds, loginAccountId);
                }
                return Collections.emptyMap();
            });

            CompletableFuture<Map<Long, Number>> commentCountFuture = CompletableFuture.supplyAsync(() ->
                    batchGetCommentCounts(worksIds));

            CompletableFuture<Map<Long, Boolean>> commentStatusFuture = CompletableFuture.supplyAsync(() -> {
                if (needUserStatus) {
                    return batchGetCommentStatus(worksIds, loginAccountId);
                }
                return Collections.emptyMap();
            });

            // 等待所有异步任务完成
            CompletableFuture.allOf(accountInfoFuture, likeCountFuture, likeStatusFuture,
                    commentCountFuture, commentStatusFuture).get();

            // 获取结果
            AccountBaseInfoDTO accountInfo = accountInfoFuture.get();
            Map<Long, Number> likeCountMap = likeCountFuture.get();
            Map<Long, Boolean> likeStatusMap = likeStatusFuture.get();
            Map<Long, Number> commentCountMap = commentCountFuture.get();
            Map<Long, Boolean> commentStatusMap = commentStatusFuture.get();

            // 填充数据
            return fillWorksDataOptimized(worksDOList, accountInfo, likeCountMap, likeStatusMap,
                    commentCountMap, commentStatusMap, needUserStatus);

        } catch (Exception e) {
            log.error("批量填充作品信息失败", e);
            // 降级处理：使用原有的同步方式
            return worksDOList;
        }
    }

    /**
     * 批量获取点赞数量
     */
    private ConcurrentMap<Long, Number> batchGetLikeCounts(List<Long> worksIds) {
        try {
            return worksIds.parallelStream()
                    .collect(Collectors.toConcurrentMap(
                            Function.identity(),
                            worksId -> {
                                try {
                                    return worksLikeApi.countByWorksId(worksId);
                                } catch (Exception e) {
                                    log.warn("获取作品点赞数失败, worksId: {}", worksId, e);
                                    return 0;
                                }
                            }
                    ));
        } catch (Exception e) {
            log.error("批量获取点赞数失败", e);
            return new ConcurrentSkipListMap<>();
        }
    }

    /**
     * 批量获取点赞状态
     */
    private Map<Long, Boolean> batchGetLikeStatus(List<Long> worksIds, Long loginAccountId) {
        try {
            return worksIds.parallelStream()
                    .collect(Collectors.toConcurrentMap(
                            Function.identity(),
                            worksId -> {
                                try {
                                    return worksLikeApi.isLike(worksId, loginAccountId);
                                } catch (Exception e) {
                                    log.warn("获取作品点赞状态失败, worksId: {}, accountId: {}", worksId, loginAccountId, e);
                                    return false;
                                }
                            }
                    ));
        } catch (Exception e) {
            log.error("批量获取点赞状态失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 批量获取评论数量
     */
    private ConcurrentMap<Long, Number> batchGetCommentCounts(List<Long> worksIds) {
        try {
            return worksIds.parallelStream()
                    .collect(Collectors.toConcurrentMap(
                            Function.identity(),
                            worksId -> {
                                try {
                                    return worksCommentApi.countByWorksId(worksId);
                                } catch (Exception e) {
                                    log.warn("获取作品评论数失败, worksId: {}", worksId, e);
                                    return 0;
                                }
                            }
                    ));
        } catch (Exception e) {
            log.error("批量获取评论数失败", e);
            return new ConcurrentSkipListMap<>();
        }
    }

    private ConcurrentMap<Long, AccountBaseInfoDTO> batchGetAccountInfo(List<Long> accountIds) {
        return accountIds.parallelStream()
                .collect(Collectors.toConcurrentMap(
                        Function.identity(),
                        accountId -> {
                            try {
                                return accountApi.queryAccountBaseInfoById(accountId);
                            } catch (Exception e) {
                                log.warn("获取用户信息失败, accountId: {}", accountId, e);
                                return null;
                            }
                        }
                ));
    }

    /**
     * 批量获取评论状态
     */
    private Map<Long, Boolean> batchGetCommentStatus(List<Long> worksIds, Long loginAccountId) {
        try {
            return worksIds.parallelStream()
                    .collect(Collectors.toConcurrentMap(
                            Function.identity(),
                            worksId -> {
                                try {
                                    return worksCommentApi.isComment(worksId, loginAccountId);
                                } catch (Exception e) {
                                    log.warn("获取作品评论状态失败, worksId: {}, accountId: {}", worksId, loginAccountId, e);
                                    return false;
                                }
                            }
                    ));
        } catch (Exception e) {
            log.error("批量获取评论状态失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 填充作品数据
     */
    private List<WorksDO> fillWorksDataOptimized(List<WorksDO> worksDOList,
                                                 AccountBaseInfoDTO accountInfo,
                                                 Map<Long, Number> likeCountMap,
                                                 Map<Long, Boolean> likeStatusMap,
                                                 Map<Long, Number> commentCountMap,
                                                 Map<Long, Boolean> commentStatusMap,
                                                 Boolean needUserStatus) {

        return worksDOList.stream().map(worksDO -> {
            try {
                // 填充用户数据
                if (ObjectUtil.isNotEmpty(worksDO.getAccountId()) && ObjectUtil.isNotEmpty(accountInfo)) {
                    worksDO.setAccountName(accountInfo.getName());
                    worksDO.setNickname(accountInfo.getNickname());
                    worksDO.setAvatar(accountInfo.getAvatar());
                    worksDO.setRightsId(accountInfo.getRightsId());
                }

                Long worksId = worksDO.getId();

                // 填充点赞数据
                worksDO.setLikeCount(likeCountMap.getOrDefault(worksId, 0).longValue());
                worksDO.setLikeStatus(needUserStatus ? likeStatusMap.getOrDefault(worksId, false) : false);

                // 填充评论数据
                worksDO.setCommentCount(commentCountMap.getOrDefault(worksId, 0).longValue());
                worksDO.setCommentStatus(needUserStatus ? commentStatusMap.getOrDefault(worksId, false) : false);

                return worksDO;
            } catch (Exception e) {
                log.warn("填充作品数据失败, worksId: {}", worksDO.getId(), e);
                return worksDO;
            }
        }).collect(Collectors.toList());
    }

}

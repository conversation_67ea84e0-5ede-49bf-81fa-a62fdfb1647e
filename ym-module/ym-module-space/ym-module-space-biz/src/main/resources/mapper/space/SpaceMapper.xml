<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fozmo.ym.module.space.dal.mysql.space.SpaceMapper">
    <select id="selectSpacePage" parameterType="com.fozmo.ym.module.space.controller.admin.space.vo.SpacePageReqVO" resultType="com.fozmo.ym.module.space.dal.dataobject.space.SpaceDO">
        SELECT
            s.*,
            COUNT(DISTINCT l.id) AS likeCount,
            COUNT(DISTINCT sh.id) AS shareCount,
            COUNT(DISTINCT f.id) AS favoriteCount,
            COUNT(DISTINCT c.id) AS commentCount
        FROM
            space s
                LEFT JOIN space_like l ON s.id = l.space_id
                LEFT JOIN space_share sh ON s.id = sh.space_id
                LEFT JOIN space_favorite f ON s.id = f.space_id
                LEFT JOIN space_comment c ON s.id = c.space_id
        <where>
            <if test="spaceTemplateId != null">
                AND s.space_template_id = #{spaceTemplateId}
            </if>
            <if test="spaceCnName != null and spaceCnName != ''">
                AND s.space_cn_name LIKE CONCAT('%', #{spaceCnName}, '%')
            </if>
            <if test="spaceEnName != null and spaceEnName != ''">
                AND s.space_en_name LIKE CONCAT('%', #{spaceEnName}, '%')
            </if>
            <if test="spaceCode != null">
                AND s.space_code = #{spaceCode}
            </if>
            <if test="spaceTypeId != null">
                AND s.space_type_id = #{spaceTypeId}
            </if>
            <if test="spaceNum2 != null">
                AND s.space_num2 = #{spaceNum2}
            </if>
            <if test="spaceNum3 != null">
                AND s.space_num3 = #{spaceNum3}
            </if>
            <if test="accountId != null">
                AND s.account_id = #{accountId}
            </if>
            <if test="accountCode != null">
                AND s.account_code = #{accountCode}
            </if>
            <if test="cnDescription != null">
                AND s.cn_description = #{cnDescription}
            </if>
            <if test="enDescription != null">
                AND s.en_description = #{enDescription}
            </if>
            <if test="spaceStatus != null">
                AND s.space_status = #{spaceStatus}
            </if>
            <if test="spaceCover != null">
                AND s.space_cover = #{spaceCover}
            </if>
            <if test="copyFlag != null">
                AND s.copy_flag = #{copyFlag}
            </if>
            <if test="privacyFlag != null">
                AND s.privacy_flag = #{privacyFlag}
            </if>
            <if test="createId != null">
                AND s.create_id = #{createId}
            </if>
            <if test="creator != null and creator != ''">
                AND s.creator LIKE CONCAT('%', #{creator}, '%')
            </if>
            <if test="createData != null">
                AND s.create_data = #{createData}
            </if>
            <if test="createTime != null">
                AND s.create_time BETWEEN #{createTime[0]} AND #{createTime[1]}
            </if>
            <if test="updateId != null">
                AND s.update_id = #{updateId}
            </if>
            <if test="updater != null and updater != ''">
                AND s.updater LIKE CONCAT('%', #{updater}, '%')
            </if>
            <if test="updateData != null">
                AND s.update_data = #{updateData}
            </if>
            <if test="tenantCode != null">
                AND s.tenant_code = #{tenantCode}
            </if>
        </where>

        LIMIT #{pageNo}, #{pageSize}
    </select>

</mapper>
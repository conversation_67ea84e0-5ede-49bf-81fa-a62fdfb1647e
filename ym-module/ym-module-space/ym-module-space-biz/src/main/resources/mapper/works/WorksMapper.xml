<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fozmo.ym.module.space.dal.mysql.works.WorksMapper">
    <select id="selectMountsSpace" resultType="com.fozmo.ym.module.space.controller.app.works.vo.SpaceMountDTO">
        select
            a.id id,
            a.works_id worksId,
            a.space_id spaceId,
            b.space_cn_name spaceName
        from
            space_mount_works a
            left join space b
            on a.space_id = b.id
        where a.works_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
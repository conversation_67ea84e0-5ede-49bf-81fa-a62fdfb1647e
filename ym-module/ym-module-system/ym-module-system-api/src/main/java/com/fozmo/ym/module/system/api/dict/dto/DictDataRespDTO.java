package com.fozmo.ym.module.system.api.dict.dto;

import com.fozmo.ym.framework.common.enums.CommonStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 字典数据 Response DTO
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class DictDataRespDTO {

    /**
     * 字典标签
     */
    private String label;
    /**
     * 字典值
     */
    private String value;
    /**
     * 字典类型
     */
    private String dictType;
    /**
     * 状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

}

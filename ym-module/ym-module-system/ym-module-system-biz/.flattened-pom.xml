<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.fozmo</groupId>
  <artifactId>ym-module-system-biz</artifactId>
  <version>1.0</version>
  <name>ym-module-system-biz</name>
  <description>system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等</description>
  <url></url>
  <dependencies>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-module-system-api</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-module-infra-api</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-biz-data-permission</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-biz-tenant</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-biz-ip</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-security</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <version>3.5.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-mybatis</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-redis</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-job</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-mq</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-spring-boot-starter-excel</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
      <version>3.5.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>me.zhyd.oauth</groupId>
      <artifactId>JustAuth</artifactId>
      <version>1.16.7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.xkcoding.justauth</groupId>
      <artifactId>justauth-spring-boot-starter</artifactId>
      <version>1.4.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-mp-spring-boot-starter</artifactId>
      <version>4.7.6-20250704.154059</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
      <version>4.7.6-20250704.154059</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.anji-plus</groupId>
      <artifactId>captcha-spring-boot-starter</artifactId>
      <version>1.4.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.dromara.hutool</groupId>
      <artifactId>hutool-extra</artifactId>
      <version>6.0.0-M22</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-module-sms-api</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fozmo</groupId>
      <artifactId>ym-module-auth-biz</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>huaweicloud</id>
      <name>huawei</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </repository>
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <id>spring-snapshots</id>
      <name>Spring Snapshots</name>
      <url>https://repo.spring.io/snapshot</url>
    </repository>
  </repositories>
</project>

package com.fozmo.ym.module.tenant.api.tenant;

import com.fozmo.ym.module.tenant.api.tenant.dto.TenantDataDTO;

import java.util.List;

/**
 * 多租户的 API 接口
 *
 * <AUTHOR>
 */
public interface TenantApi {

    /**
     * 获得所有租户
     *
     * @return 租户编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 校验租户是否合法
     *
     * @param id 租户编号
     */
    void validateTenant(Long id);
    /**
     *  处理租户信息
     * @param tenantDataDTO 租户信息
     * @param count 租户数量
     */
    void  handleTenantInfo(TenantDataDTO tenantDataDTO,Long count);
    /**
     *  查询租户信息
     * @param tenantId 租户编号
     * @return 租户信息
     */
    TenantDataDTO getTenantInfo(Long tenantId);
    /**
     * 处理租户菜单
     */
    void handleTenantMenu();
}

package com.fozmo.ym.module.tenant.api.tenant.enums;

import cn.hutool.core.util.ArrayUtil;
import com.fozmo.ym.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.fozmo.ym.framework.common.enums.CommonStatusEnum.ARRAYS;

@AllArgsConstructor
@Getter
public enum TenantEnum implements ArrayValuable<Integer> {


    YM(1,"ym","元美");
    private final Integer id;

    private final String code;

    private final String name;


    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static TenantEnum valueOfType(Integer id) {
        return ArrayUtil.firstMatch(o -> o.getId().equals(id), values());
    }
}

package com.fozmo.ym.module.tenant.api.tenant;


import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.tenant.api.tenant.dto.TenantDataDTO;
import com.fozmo.ym.module.tenant.dal.dataobject.tenant.TenantDO;
import com.fozmo.ym.module.tenant.service.tenant.TenantService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.system.enums.ErrorCodeConstants.USER_COUNT_MAX;

/**
 * 多租户的 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Override
    public List<Long> getTenantIdList() {
        return tenantService.getTenantIdList();
    }

    @Override
    public void validateTenant(Long id) {
        tenantService.validTenant(id);
    }

    /**
     * @param dto
     * @param count
     * @return
     */
    @Override
    public void handleTenantInfo(TenantDataDTO dto,Long count) {
        TenantDO tenantDO = BeanUtils.toBean(dto, TenantDO.class);
        tenantService.handleTenantInfo(tenant ->{
            if (count > tenant.getAccountCount()) {
                throw exception(USER_COUNT_MAX, tenant.getAccountCount());
            }
        } );
    }

    /**
     * @param tenantId
     * @return
     */
    @Override
    public TenantDataDTO getTenantInfo(Long tenantId) {
        TenantDO tenantDO = tenantService.getTenant(tenantId);
        if (tenantDO != null) {
            return BeanUtils.toBean(tenantDO, TenantDataDTO.class);
        }
        return null;
    }

    /**
     * 处理租户菜单
     */
    @Override
    public void handleTenantMenu() {

//        tenantService.handleTenantMenu(menuIds -> menus.removeIf(menu -> !CollUtil.contains(menuIds, menu.getId())));
    }

}

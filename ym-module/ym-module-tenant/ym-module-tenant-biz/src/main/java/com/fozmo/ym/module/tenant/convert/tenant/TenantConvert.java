package com.fozmo.ym.module.tenant.convert.tenant;

import org.mapstruct.Mapper;

/**
 * 租户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConvert {
//
//    TenantConvert INSTANCE = Mappers.getMapper(TenantConvert.class);
//
//    default UserSaveReqVO convert02(TenantSaveReqVO bean) {
//        UserSaveReqVO reqVO = new UserSaveReqVO();
//        reqVO.setUsername(bean.getUsername());
//        reqVO.setPassword(bean.getPassword());
//        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile());
//        return reqVO;
//    }

}

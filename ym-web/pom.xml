<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ym-web</artifactId>
    <packaging>jar</packaging>

    <name>ym-web</name>
    <description>
        后端 Server 的主项目，通过引入需要 ym-module-xxx 的依赖，
        从而实现提供 RESTful API 给 ym-ui-admin、ym-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url></url>

    <dependencies>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-system-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-infra-biz</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- 空间模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-space-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-log-biz</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- 内容模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-sms-biz</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- 账户模块-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-account-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-tenant-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-information-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-file-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-market-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-auth-biz</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-social-biz</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-notice-biz</artifactId>
            <version>1.0</version>
        </dependency>



        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-protection</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-rights-biz</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-social-biz</artifactId>
            <version>1.0</version>
        </dependency>


    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>

server:
  port: 11000

--- #################### 数据库相关配置 ####################
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration # 默认 local 环境，不开启 Quartz 的自动配置
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
      #        login-username:
      #        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ****************************************************************************************************************************************************************************
          username: root
          password: Root123456

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: *************** # 地址
      port: 6379 # 端口
      database: 0 # 数据库索引
      password: 123456 # 密码，建议生产环境开启
#  data:
#    redis:
#      cluster:
#        nodes:
#          - **************:6370
#          - **************:6371
#          - **************:6372
#          - **************:6373
#          - **************:6374
#          - **************:6375
#      password: 123456

--- #################### 定时任务相关配置 ####################

# Quartz 配置项，对应 QuartzProperties 配置类
spring:
  quartz:
    auto-startup: true # 本地开发环境，尽量不要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 10 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876 # RocketMQ Namesrv

spring:
  # RabbitMQ 配置项，对应 RabbitProperties 配置类
  rabbitmq:
    host: 127.0.0.1 # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: rabbit # RabbitMQ 服务的账号
    password: rabbit # RabbitMQ 服务的密码
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    bootstrap-servers: 127.0.0.1:9092 # 指定 Kafka Broker 地址，可以设置多个，以逗号分隔

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin/monitor # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.fozmo.ym.module.bpm.dal.mysql: debug
    com.fozmo.ym.module.infra.dal.mysql: debug
    com.fozmo.ym.module.infra.dal.mysql.logger.ApiErrorLogMapper: INFO # 配置 ApiErrorLogMapper 的日志级别为 info，避免和 GlobalExceptionHandler 重复打印
    com.fozmo.ym.module.infra.dal.mysql.job.JobLogMapper: INFO # 配置 JobLogMapper 的日志级别为 info
    com.fozmo.ym.module.infra.dal.mysql.file.FileConfigMapper: INFO # 配置 FileConfigMapper 的日志级别为 info
    com.fozmo.ym.module.pay.dal.mysql: debug
    com.fozmo.ym.module.pay.dal.mysql.notify.PayNotifyTaskMapper: INFO # 配置 PayNotifyTaskMapper 的日志级别为 info
    com.fozmo.ym.module.system.dal.mysql: debug
    com.fozmo.ym.module.system.dal.mysql.sms.com.fozmo.ym.module.sms.dal.mysql.sms.SmsChannelMapper: INFO # 配置 SmsChannelMapper 的日志级别为 info
    com.fozmo.ym.module.tool.dal.mysql: debug
    com.fozmo.ym.module.member.dal.mysql: debug
    com.fozmo.ym.module.trade.dal.mysql: debug
    com.fozmo.ym.module.promotion.dal.mysql: debug
    com.fozmo.ym.module.statistics.dal.mysql: debug
    com.fozmo.ym.module.crm.dal.mysql: debug
    com.fozmo.ym.module.erp.dal.mysql: debug
    com.fozmo.ym.module.iot.dal.mysql: debug
    com.fozmo.ym.module.iot.dal.tdengine: DEBUG
    com.fozmo.ym.module.ai.dal.mysql: debug
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR # TODO 芋艿：先禁用，Spring Boot 3.X 存在部分错误的 WARN 提示

debug: false

--- #################### 微信公众号、小程序相关配置 ####################
wx:
  mp:
    #    liuqian的测试公众号
    app-id: wx47abcd05b88c0141 # 测试号（自己的）
    secret: ccf1b7636d646b4305a919e5ce886723
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wxmp # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
  miniapp:
    #    liuqian 的测试号
    appid: wx01dfa7651fd3908a
    secret: 536ab9524b196c51c68901cbbefdf31b
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wxap # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台

--- #################### 芋道相关配置 ####################

# 芋道配置项，设置当前项目所有自定义的配置
ym:
  captcha:
    enable: false # 本地环境，暂时关闭图片验证码，方便登录等接口的测试；
  security:
    mock-enable: true
  pay:
    order-notify-url: http://ztsmi3.natappfree.cc/admin/pay/notify/order # 支付渠道的【支付】回调地址
    refund-notify-url: http://ztsmi3.natappfree.cc/admin/pay/notify/refund # 支付渠道的【退款】回调地址
    transfer-notify-url: http://yunai.natapp1.cc/admin/pay/notify/transfer # 支付渠道的【转账】回调地址
  access-log: # 访问日志的配置项
    enable: false
  demo: false # 关闭演示模式
  wxa-code:
    env-version: develop # 小程序版本: 正式版为 "release"；体验版为 "trial"；开发版为 "develop"
  wxa-subscribe-message:
    miniprogram-state: developer # 跳转小程序类型：开发版为 “developer”；体验版为 “trial”为；正式版为 “formal”
  tencent-lbs-key: TVDBZ-TDILD-4ON4B-PFDZA-RNLKH-VVF6E # QQ 地图的密钥 https://lbs.qq.com/service/staticV2/staticGuide/staticDoc

justauth:
  enabled: true
  type:
    DINGTALK: # 钉钉
      client-id: dingvrnreaje3yqvzhxg
      client-secret: i8E6iZyDvZj51JIb0tYsYfVQYOks9Cq1lgryEjFRqC79P3iJcrxEwT6Qk2QvLrLI
      ignore-check-redirect-uri: true
    WECHAT_ENTERPRISE: # 企业微信
      client-id: wwd411c69a39ad2e54
      client-secret: 1wTb7hYxnpT2TUbIeHGXGo7T0odav1ic10mLdyyATOw
      agent-id: 1000004
      ignore-check-redirect-uri: true
    # noinspection SpringBootApplicationYaml
    WECHAT_MINI_APP: # 微信小程序
      client-id: ${wx.miniapp.appid}
      client-secret: ${wx.miniapp.secret}
      ignore-check-redirect-uri: true
      ignore-check-state: true # 微信小程序，不会使用到 state，所以不进行校验
    WECHAT_MP: # 微信公众号
      client-id: ${wx.mp.app-id}
      client-secret: ${wx.mp.secret}
      ignore-check-redirect-uri: true
  cache:
    type: REDIS
    prefix: 'social_auth_state:' # 缓存前缀，目前只对 Redis 缓存生效，默认 JUSTAUTH::STATE::
    timeout: 24h # 超时时长，目前只对 Redis 缓存生效，默认 3 分钟

--- #################### iot相关配置 TODO 芋艿【IOT】：再瞅瞅 ####################

#
#---
#spring:
#  redis:
#    # redisson配置
#    redisson:
#      # 如果该值为false，系统将不会创建RedissionClient的bean。
#      enabled: true
#      # mode的可用值为，single/cluster/sentinel/master-slave
##      mode: cluster
#      # single: 单机模式
#      #   address: redis://localhost:6379
#      cluster:
#        address: redis://**************:6370,redis://**************:6371,redis://**************:6372,redis://**************:6373,redis://**************:6374,redis://**************:6375
#      # sentinel:
#      #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
#      #   address: redis://localhost:6379,redis://localhost:6378,...
#      # master-slave:
#      #   每个节点逗号分隔，第一个为主节点，其余为从节点。同时每个节点前必须以redis://开头。
#      #   address: redis://localhost:6379,redis://localhost:6378,...
##      address: redis://127.0.0.1:6001
#      # redis 密码，空可以不填。
#      password: 123456
#      database: 0